# API Documentation

## Overview

The Timing Violation Web Display system provides a comprehensive API for extracting, processing, and serving timing violation data through a web interface. This document describes the core APIs, data structures, and interfaces.

## Core APIs

### DataExporter Class

The main coordinator for data extraction and export operations.

#### Constructor

```python
DataExporter(violation_check_dir: str = "VIOLATION_CHECK", 
             enable_performance_monitoring: bool = True)
```

**Parameters:**
- `violation_check_dir` (str): Path to the VIOLATION_CHECK directory containing Excel files and database
- `enable_performance_monitoring` (bool): Enable performance monitoring and logging

#### Methods

##### export_all_data()

```python
def export_all_data() -> bool
```

Exports all violation data to JSON files for web consumption.

**Returns:**
- `bool`: True if export successful, False otherwise

**Generated Files:**
- `{web_display_dir}/data/index.json`: Metadata and summary statistics
- `{web_display_dir}/data/corners/{corner}_cases.json`: Cases for each corner
- `{web_display_dir}/data/violations/{corner}_{case}.json`: Violation data files
- `{web_display_dir}/data/statistics.json`: Detailed statistics

**Example:**
```python
exporter = DataExporter("VIOLATION_CHECK")
success = exporter.export_all_data()
if success:
    print("Export completed successfully")
```

##### load_violation_data()

```python
def load_violation_data() -> Tuple[List[Dict[str, Any]], List[str]]
```

Loads violation data from available sources (Excel files or database).

**Returns:**
- `Tuple[List[Dict], List[str]]`: (violations_list, data_sources)

**Example:**
```python
violations, sources = exporter.load_violation_data()
print(f"Loaded {len(violations)} violations from {sources}")
```

##### export_web_template()

```python
def export_web_template() -> bool
```

Copies web template files to the output directory.

**Returns:**
- `bool`: True if template export successful

### ExcelParser Class

Parses Excel files containing timing violation data.

#### Constructor

```python
ExcelParser()
```

#### Methods

##### parse_excel_file()

```python
def parse_excel_file(file_path: str) -> List[Dict[str, Any]]
```

Parses a single Excel file and extracts violation data.

**Parameters:**
- `file_path` (str): Path to Excel file

**Returns:**
- `List[Dict[str, Any]]`: List of violation records

**Raises:**
- `ExcelParsingError`: If file cannot be parsed or has invalid format

**Example:**
```python
parser = ExcelParser()
try:
    violations = parser.parse_excel_file("violations.xlsx")
    print(f"Parsed {len(violations)} violations")
except ExcelParsingError as e:
    print(f"Parsing failed: {e}")
```

##### extract_metadata_from_path()

```python
def extract_metadata_from_path(file_path: str) -> Tuple[str, str]
```

Extracts corner and case information from file path.

**Parameters:**
- `file_path` (str): Path to Excel file

**Returns:**
- `Tuple[str, str]`: (corner, case) extracted from path

### DatabaseReader Class

Reads violation data from SQLite database.

#### Constructor

```python
DatabaseReader(db_path: str = None)
```

**Parameters:**
- `db_path` (str, optional): Path to database file. Auto-detects if None.

#### Methods

##### get_confirmed_violations()

```python
def get_confirmed_violations() -> List[Dict[str, Any]]
```

Retrieves all confirmed violations from database.

**Returns:**
- `List[Dict[str, Any]]`: List of confirmed violation records

**Example:**
```python
reader = DatabaseReader()
violations = reader.get_confirmed_violations()
```

##### get_corners_and_cases()

```python
def get_corners_and_cases() -> Tuple[List[str], List[str]]
```

Gets unique corners and cases from database.

**Returns:**
- `Tuple[List[str], List[str]]`: (corners, cases)

## Data Structures

### Violation Record

Core data structure representing a timing violation.

```python
{
    "num": int,                    # Violation number/ID
    "hier": str,                   # Hierarchy path
    "time_ns": float,              # Time in nanoseconds
    "check_info": str,             # Check information
    "status": str,                 # Status (confirmed/pending)
    "confirmer": str,              # Person who confirmed
    "result": str,                 # Confirmation result
    "reason": str,                 # Confirmation reason
    "confirmed_at": str,           # Confirmation timestamp (ISO format)
    "corner": str,                 # Corner name
    "case": str,                   # Case name
    "source": str                  # Data source (excel/database)
}
```

### Index Data Structure

Main metadata file structure for web interface.

```python
{
    "metadata": {
        "generated_at": str,       # Generation timestamp
        "total_violations": int,   # Total violation count
        "total_corners": int,      # Number of corners
        "total_cases": int,        # Number of cases
        "data_sources": List[str]  # Sources used (excel/database)
    },
    "corners": List[str],          # Available corners
    "cases": List[str],            # Available cases
    "summary": {
        "total_violations": int,
        "confirmed_violations": int,
        "pending_violations": int,
        "confirmation_rate": float,
        "latest_confirmation": str
    }
}
```

### Statistics Data Structure

Detailed statistics for analysis.

```python
{
    "overall": {
        "total_violations": int,
        "confirmed_violations": int,
        "pending_violations": int,
        "confirmation_rate": float
    },
    "by_corner": {
        "corner_name": {
            "total": int,
            "confirmed": int,
            "pending": int,
            "rate": float
        }
    },
    "by_case": {
        "case_name": {
            "total": int,
            "confirmed": int,
            "pending": int,
            "rate": float
        }
    },
    "timeline": {
        "first_violation": str,
        "last_violation": str,
        "first_confirmation": str,
        "last_confirmation": str
    }
}
```

## Error Handling

### Exception Classes

#### DataExportError

```python
class DataExportError(Exception):
    """Raised when data export operations fail"""
    pass
```

#### ExcelParsingError

```python
class ExcelParsingError(Exception):
    """Raised when Excel file parsing fails"""
    pass
```

#### DatabaseError

```python
class DatabaseError(Exception):
    """Raised when database operations fail"""
    pass
```

### Error Response Format

All API methods return consistent error information:

```python
{
    "success": bool,
    "error": str,           # Error message
    "error_type": str,      # Error category
    "details": Dict         # Additional error details
}
```

## Performance Considerations

### Large Dataset Handling

For datasets with 10,000+ records:

1. **Automatic Pagination**: Data is split into files of 1000 records each
2. **Lazy Loading**: Only required data is loaded based on filters
3. **Caching**: Intelligent caching with configurable timeout
4. **Memory Management**: Automatic cleanup of expired cache entries

### File Naming Convention

```
data/violations/{corner}_{case}_page{n}.json  # Paginated files
data/violations/{corner}_{case}.json          # Single files
data/corners/{corner}_cases.json              # Corner-specific cases
```

## Configuration

### Environment Variables

- `VIOLATION_CHECK_DIR`: Override default VIOLATION_CHECK directory
- `WEB_DISPLAY_DEBUG`: Enable debug logging
- `MAX_RECORDS_PER_FILE`: Override pagination size (default: 1000)

### Configuration File

Optional `web_display_config.json`:

```json
{
    "pagination_size": 1000,
    "cache_timeout": 300000,
    "enable_compression": true,
    "debug_mode": false,
    "excel_extensions": [".xlsx", ".xls"],
    "database_timeout": 30
}
```

## Integration Examples

### Basic Usage

```python
from web_display.data_exporter import DataExporter

# Initialize and export
exporter = DataExporter("VIOLATION_CHECK")
success = exporter.export_all_data()

if success:
    print("Web data ready at VIOLATION_CHECK/web_display/")
else:
    print("Export failed - check logs")
```

### Custom Processing

```python
from web_display.parsers.excel_parser import ExcelParser
from web_display.parsers.database_reader import DatabaseReader

# Parse specific Excel file
parser = ExcelParser()
violations = parser.parse_excel_file("corner1_case1.xlsx")

# Read from database
reader = DatabaseReader("custom_path/violations.db")
db_violations = reader.get_confirmed_violations()

# Combine data sources
all_violations = violations + db_violations
```

### Error Handling

```python
from web_display.data_exporter import DataExporter, DataExportError

try:
    exporter = DataExporter("VIOLATION_CHECK")
    success = exporter.export_all_data()
    
    if not success:
        print("Export completed with warnings - check logs")
        
except DataExportError as e:
    print(f"Export failed: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Web Interface API

### JavaScript API

The web interface provides a JavaScript API for dynamic interaction:

#### ViolationDataManager Class

```javascript
class ViolationDataManager {
    constructor()
    
    // Data loading methods
    async loadInitialData()
    async loadViolationData(corner, case)
    async loadCornerCaseData(corner, case)
    
    // Filter methods
    async applyFilters()
    async handleCornerFilterChange(corner)
    async handleCaseFilterChange(case)
    
    // UI methods
    updateStatistics()
    updateDataTable()
    showLoading(show)
    showError(message)
}
```

#### Usage Example

```javascript
// Initialize application
const app = new ViolationDataManager();

// Apply custom filters
await app.handleCornerFilterChange('ff');
await app.handleCaseFilterChange('test1');

// Get current statistics
const stats = app.getMemoryStats();
console.log('Memory usage:', stats);
```

## REST API Endpoints (Future Enhancement)

Planned REST API endpoints for server-side integration:

```
GET /api/violations                    # Get all violations
GET /api/violations?corner=ff          # Filter by corner
GET /api/violations?case=test1         # Filter by case
GET /api/statistics                    # Get statistics
GET /api/corners                       # Get available corners
GET /api/cases                         # Get available cases
POST /api/export                       # Trigger data export
```

## Versioning

API version is included in generated data files:

```json
{
    "api_version": "1.0.0",
    "format_version": "1.0.0",
    "generated_at": "2025-08-06T11:23:55Z"
}
```

## Security Considerations

1. **File Access**: Only reads from designated VIOLATION_CHECK directory
2. **Path Traversal**: All file paths are validated and sanitized
3. **Data Sanitization**: All user data is escaped in JSON output
4. **Error Information**: Sensitive paths are not exposed in error messages

## Logging

Comprehensive logging is available at multiple levels:

```python
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('web_display')

# Log levels used:
# DEBUG: Detailed operation information
# INFO: General operation status
# WARNING: Non-fatal issues
# ERROR: Operation failures
# CRITICAL: System-level failures
```

## Testing

API testing is available through the test suite:

```bash
# Run all API tests
python -m pytest tests/test_data_exporter.py -v

# Run specific API test
python -m pytest tests/test_data_exporter.py::TestDataExporter::test_export_all_data -v

# Run with coverage
python -m pytest tests/ --cov=web_display --cov-report=html
```