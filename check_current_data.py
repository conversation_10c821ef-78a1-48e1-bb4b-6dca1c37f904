#!/usr/bin/env python3
"""
Check current database data
"""

import sqlite3

def check_current_data():
    """Check current violations in database"""
    db_path = "VIOLATION_CHECK/timing_violations.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check total violations
        cursor.execute("SELECT COUNT(*) FROM timing_violations")
        total = cursor.fetchone()[0]
        print(f"Total violations in database: {total}")
        
        # Check by corner/case
        cursor.execute("SELECT corner, case_name, COUNT(*) FROM timing_violations GROUP BY corner, case_name")
        data = cursor.fetchall()
        print("\nViolations by corner/case:")
        for row in data:
            print(f"  {row[0]} / {row[1]}: {row[2]} violations")
        
        # Check confirmed violations
        cursor.execute("""
            SELECT v.corner, v.case_name, COUNT(*) 
            FROM timing_violations v
            LEFT JOIN confirmation_records c ON v.id = c.violation_id
            WHERE c.status = 'confirmed' OR c.status = 'Confirmed'
            GROUP BY v.corner, v.case_name
        """)
        confirmed_data = cursor.fetchall()
        print("\nConfirmed violations by corner/case:")
        for row in confirmed_data:
            print(f"  {row[0]} / {row[1]}: {row[2]} confirmed")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_current_data()