# Design Document

## Overview

The Register Table Parser Plugin is designed as a comprehensive GUI application that integrates with the existing plugin architecture. It follows the established patterns of the codebase, using PyQt5 for the user interface and implementing the plugin base class structure. The plugin will parse Excel-based register specification tables and provide an interactive interface for viewing and editing register field values.

## Architecture

### Plugin Architecture Integration

The plugin follows the established plugin pattern:

```
register_table_parser_plugin.py (Main plugin class)
└── register_table_parser/
    ├── main_window.py (Main GUI window)
    ├── models.py (Data models)
    ├── parser.py (Excel parsing logic)
    ├── widgets.py (Custom UI components)
    └── utils.py (Utility functions)
```

### Core Components

1. **RegisterTableParserPlugin**: Main plugin class inheriting from `PluginBase`
2. **RegisterParserMainWindow**: Main GUI window inheriting from `NonModalDialog`
3. **ExcelTableParser**: Excel file parsing and validation
4. **RegisterDataModel**: Data model for register information
5. **RegisterListWidget**: Custom widget for register list display
6. **FieldEditorWidget**: Custom widget for field value editing
7. **NumberFormatConverter**: Utility for number format conversion

## Components and Interfaces

### 1. Plugin Entry Point

```python
class RegisterTableParserPlugin(PluginBase):
    @property
    def name(self) -> str:
        return "寄存器表格解析器"
    
    def initialize(self, main_window):
        # Create menu item and initialize GUI
        
    def cleanup(self):
        # Clean up resources
```

### 2. Main Window Interface

```python
class RegisterParserMainWindow(NonModalDialog):
    def __init__(self, parent=None):
        super().__init__(parent, "寄存器表格解析器")
        self.init_ui()
        
    def init_ui(self):
        # Create header panel, register list, and field editor
```

### 3. Excel Parser Interface

```python
class ExcelTableParser:
    def parse_register_table(self, file_path: str) -> RegisterTableData:
        # Parse Excel file and extract register data
        
    def validate_table_format(self, worksheet) -> bool:
        # Validate Excel table format
        
    def extract_header_info(self, worksheet) -> HeaderInfo:
        # Extract header information (first 4 rows)
        
    def extract_register_data(self, worksheet) -> List[RegisterInfo]:
        # Extract register and field information
```

### 4. Data Models

```python
@dataclass
class HeaderInfo:
    project_name: str
    sub_system: str
    module_name: str
    base_addr: str

@dataclass
class FieldInfo:
    name: str
    bit_range: str
    rw_attribute: str
    reset_value: str
    description: str

@dataclass
class RegisterInfo:
    offset: str
    name: str
    description: str
    width: int
    fields: List[FieldInfo]

@dataclass
class RegisterTableData:
    header: HeaderInfo
    registers: List[RegisterInfo]
```

### 5. GUI Layout Structure

```
Main Window
├── Header Panel (Top)
│   ├── Project Name Display
│   ├── Sub System Display
│   ├── Module Name Display
│   └── Base Address Display
├── Content Panel (Middle)
│   ├── Left Panel
│   │   ├── Search Box
│   │   └── Register List (QTreeWidget)
│   └── Right Panel
│       ├── Register Value Display
│       ├── Field List (QScrollArea)
│       └── Number Format Selector
└── Control Panel (Bottom)
    ├── Load File Button
    ├── Export Button
    └── Status Bar
```

## Data Models

### Register Data Flow

1. **File Loading**: User selects Excel file → Parser validates format
2. **Data Extraction**: Parser extracts header and register data → Creates data models
3. **GUI Population**: Data models populate GUI components
4. **User Interaction**: User selects register → Field editor updates
5. **Value Calculation**: User modifies field → Register value recalculates

### Field Value Management

```python
class FieldValueManager:
    def __init__(self):
        self.field_values = {}  # {field_name: value}
        self.number_format = NumberFormat.HEX
        
    def update_field_value(self, field_name: str, value: str):
        # Update field value and trigger register recalculation
        
    def calculate_register_value(self, register: RegisterInfo) -> int:
        # Combine all field values into register value
        
    def convert_to_format(self, value: int, format: NumberFormat) -> str:
        # Convert value to specified number format
```

## Error Handling

### File Parsing Errors

1. **Invalid File Format**: Display error dialog with format requirements
2. **Missing Required Columns**: Show specific missing column information
3. **Data Validation Errors**: Highlight problematic rows and provide correction guidance

### Runtime Errors

1. **Memory Management**: Implement pagination for large register tables
2. **GUI Responsiveness**: Use background threads for heavy parsing operations
3. **Value Validation**: Validate field values against bit ranges and data types

### Error Recovery

```python
class ErrorHandler:
    def handle_parsing_error(self, error: Exception, file_path: str):
        # Log error and show user-friendly message
        
    def handle_validation_error(self, field_name: str, value: str, error: str):
        # Show field-specific validation error
        
    def handle_calculation_error(self, register_name: str, error: Exception):
        # Handle register value calculation errors
```

## Testing Strategy

### Unit Testing

1. **Parser Testing**: Test Excel parsing with various table formats
2. **Data Model Testing**: Validate data model creation and manipulation
3. **Value Calculation Testing**: Test field value to register value conversion
4. **Number Format Testing**: Test conversion between binary, decimal, and hexadecimal

### Integration Testing

1. **GUI Integration**: Test GUI component interactions
2. **Plugin Integration**: Test plugin loading and menu integration
3. **File Handling**: Test various Excel file formats and edge cases

### Test Data

```python
# Test Excel files with different scenarios:
test_files = [
    "valid_register_table.xlsx",      # Standard format
    "large_register_table.xlsx",      # Performance testing
    "invalid_format.xlsx",            # Error handling
    "missing_columns.xlsx",           # Validation testing
    "special_characters.xlsx"         # Edge case testing
]
```

### Performance Testing

1. **Large File Handling**: Test with register tables containing 1000+ registers
2. **Memory Usage**: Monitor memory consumption during parsing and GUI operations
3. **Response Time**: Ensure GUI remains responsive during heavy operations

### User Acceptance Testing

1. **Workflow Testing**: Test complete user workflows from file loading to value editing
2. **Usability Testing**: Validate GUI usability and user experience
3. **Cross-platform Testing**: Test on different operating systems and screen resolutions

## Implementation Phases

### Phase 1: Core Infrastructure
- Plugin base class implementation
- Excel parser foundation
- Basic data models
- Main window structure

### Phase 2: GUI Implementation
- Header display panel
- Register list with search functionality
- Basic field editor
- File loading dialog

### Phase 3: Advanced Features
- Number format conversion
- Real-time value calculation
- Field validation
- Error handling

### Phase 4: Polish and Testing
- Performance optimization
- Comprehensive testing
- Documentation
- User experience improvements