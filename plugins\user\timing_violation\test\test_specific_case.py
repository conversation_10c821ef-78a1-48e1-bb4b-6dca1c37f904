#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试用户提到的具体问题：
历史数据：setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:7869100 FS, negedge SE:7735500 FS,0.1621 : 262100FS, -0.0241 : -24100 FS )
新数据：  setup( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )

虽然括号内容一致，但是括号前内容不一致，不应该自动匹配
"""

def normalize_check_info(check_info: str) -> str:
    """标准化检查信息，用于模糊匹配"""
    try:
        # 查找括号内容
        start_idx = check_info.find('(')
        end_idx = check_info.rfind(')')
        
        if start_idx == -1 or end_idx == -1 or start_idx >= end_idx:
            # 如果没有找到括号，返回原始信息
            return check_info
        
        # 提取括号前的部分和括号内的部分
        prefix = check_info[:start_idx + 1]  # 包含开括号，这部分必须完全匹配
        bracket_content = check_info[start_idx + 1:end_idx]
        
        # 按逗号分割括号内容
        parts = bracket_content.split(',')
        
        if len(parts) < 3:
            # 如果分割后少于3部分，返回原始信息
            return check_info
        
        normalized_parts = []
        
        # 处理第一部分：移除冒号后的时间信息
        part1 = parts[0].strip()
        colon_idx = part1.find(':')
        if colon_idx != -1:
            part1 = part1[:colon_idx].strip()
        normalized_parts.append(part1)
        
        # 处理第二部分：移除冒号后的时间信息
        part2 = parts[1].strip()
        colon_idx = part2.find(':')
        if colon_idx != -1:
            part2 = part2[:colon_idx].strip()
        normalized_parts.append(part2)
        
        # 第三部分忽略，不添加到标准化结果中
        
        # 重新组装标准化的检查信息
        # 注意：括号前的内容(prefix)保持不变，必须完全匹配
        normalized_bracket_content = ', '.join(normalized_parts)
        normalized_check_info = prefix + normalized_bracket_content + ')'
        
        return normalized_check_info
        
    except Exception as e:
        print(f"标准化检查信息失败: {str(e)}, 原始信息: {check_info}")
        return check_info

def test_specific_case():
    """测试用户提到的具体问题"""
    
    print("=" * 80)
    print("测试用户提到的具体问题")
    print("=" * 80)
    print()
    
    # 用户提到的具体案例
    historical_data = "setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:7869100 FS, negedge SE:7735500 FS,0.1621 : 262100FS, -0.0241 : -24100 FS )"
    new_data = "setup( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )"
    
    print("历史数据:")
    print(f"  {historical_data}")
    print()
    
    print("新数据:")
    print(f"  {new_data}")
    print()
    
    # 标准化处理
    historical_normalized = normalize_check_info(historical_data)
    new_normalized = normalize_check_info(new_data)
    
    print("标准化结果:")
    print(f"  历史数据标准化: {historical_normalized}")
    print(f"  新数据标准化:   {new_normalized}")
    print()
    
    # 检查是否匹配
    is_match = historical_normalized == new_normalized
    
    print("匹配结果:")
    if is_match:
        print("  ❌ 错误：意外匹配了！")
        print("  这是一个问题，不同类型的检查不应该匹配")
        print("  setuphold<hold> 和 setup 是不同的检查类型")
    else:
        print("  ✅ 正确：没有匹配")
        print("  setuphold<hold> 和 setup 是不同的检查类型，正确地没有匹配")
    
    print()
    
    # 分析差异
    print("详细分析:")
    print("1. 括号前内容比较:")
    
    # 提取括号前内容
    hist_prefix = historical_data[:historical_data.find('(') + 1] if '(' in historical_data else historical_data
    new_prefix = new_data[:new_data.find('(') + 1] if '(' in new_data else new_data
    
    print(f"   历史数据括号前: '{hist_prefix}'")
    print(f"   新数据括号前:   '{new_prefix}'")
    print(f"   括号前是否相同: {'是' if hist_prefix == new_prefix else '否'}")
    print()
    
    # 提取括号内容
    if '(' in historical_data and ')' in historical_data:
        hist_bracket = historical_data[historical_data.find('(') + 1:historical_data.rfind(')')]
        hist_parts = [part.strip() for part in hist_bracket.split(',')]
        
        print("2. 历史数据括号内容分析:")
        for i, part in enumerate(hist_parts, 1):
            if ':' in part:
                before_colon = part[:part.find(':')].strip()
                after_colon = part[part.find(':'):].strip()
                print(f"   第{i}部分: '{part}'")
                print(f"     冒号前: '{before_colon}'")
                print(f"     冒号后: '{after_colon}'")
            else:
                print(f"   第{i}部分: '{part}' (无冒号)")
        print()
    
    if '(' in new_data and ')' in new_data:
        new_bracket = new_data[new_data.find('(') + 1:new_data.rfind(')')]
        new_parts = [part.strip() for part in new_bracket.split(',')]
        
        print("3. 新数据括号内容分析:")
        for i, part in enumerate(new_parts, 1):
            if ':' in part:
                before_colon = part[:part.find(':')].strip()
                after_colon = part[part.find(':'):].strip()
                print(f"   第{i}部分: '{part}'")
                print(f"     冒号前: '{before_colon}'")
                print(f"     冒号后: '{after_colon}'")
            else:
                print(f"   第{i}部分: '{part}' (无冒号)")
        print()
    
    print("4. 结论:")
    print("   虽然括号内的信号名称相同，但检查类型不同：")
    print("   - setuphold<hold>：建立保持时间检查的保持部分")
    print("   - setup：建立时间检查")
    print("   这两种是不同的时序检查，不应该匹配")
    
    return not is_match  # 返回True表示测试通过（没有匹配）

def test_additional_cases():
    """测试其他相关案例"""
    
    print("\n" + "=" * 80)
    print("测试其他相关案例")
    print("=" * 80)
    print()
    
    test_cases = [
        {
            'name': '相同类型不同时间戳（应该匹配）',
            'historical': 'setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:1111111 FS, negedge SE:2222222 FS,0.1234 : 123400FS, -0.0567 : -56700 FS )',
            'new': 'setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )',
            'should_match': True
        },
        {
            'name': 'setup vs setuphold<setup>（不应该匹配）',
            'historical': 'setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:1111111 FS, negedge SE:2222222 FS,0.1234 : 123400FS, -0.0567 : -56700 FS )',
            'new': 'setup( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )',
            'should_match': False
        },
        {
            'name': 'setuphold<hold> vs setuphold<setup>（不应该匹配）',
            'historical': 'setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:1111111 FS, negedge SE:2222222 FS,0.1234 : 123400FS, -0.0567 : -56700 FS )',
            'new': 'setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )',
            'should_match': False
        }
    ]
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"测试案例 {i}: {case['name']}")
        
        hist_normalized = normalize_check_info(case['historical'])
        new_normalized = normalize_check_info(case['new'])
        is_match = hist_normalized == new_normalized
        
        print(f"  历史: {case['historical']}")
        print(f"  新的: {case['new']}")
        print(f"  历史标准化: {hist_normalized}")
        print(f"  新的标准化: {new_normalized}")
        print(f"  实际匹配: {'是' if is_match else '否'}")
        print(f"  期望匹配: {'是' if case['should_match'] else '否'}")
        
        if is_match == case['should_match']:
            print(f"  结果: ✅ 通过")
        else:
            print(f"  结果: ❌ 失败")
            all_passed = False
        
        print()
    
    return all_passed

def main():
    """主函数"""
    print("时序违例插件模糊匹配功能 - 特定问题测试")
    
    # 测试用户提到的具体问题
    test1_passed = test_specific_case()
    
    # 测试其他相关案例
    test2_passed = test_additional_cases()
    
    print("=" * 80)
    print("测试总结:")
    if test1_passed and test2_passed:
        print("✅ 所有测试通过！模糊匹配功能工作正常")
        print("✅ 不同类型的检查正确地不会匹配")
        print("✅ 相同类型但时间戳不同的检查正确地会匹配")
    else:
        print("❌ 部分测试失败！需要检查实现")
    print("=" * 80)
    
    return test1_passed and test2_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
