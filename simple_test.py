#!/usr/bin/env python3
"""
简单测试数据导出功能
"""

import json
from pathlib import Path

# 创建测试数据
test_data = [
    {
        "num": 1,
        "hier": "test/path/module1",
        "time_ns": 1.234,
        "check_info": "setup (clk: 2.5ns, data: 1.2ns, slack: -0.1ns)",
        "status": "pending",
        "confirmer": "",
        "result": "",
        "reason": "",
        "confirmed_at": "",
        "corner": "ss_125c_0p81v",
        "case": "test_case_1"
    },
    {
        "num": 2,
        "hier": "test/path/module2", 
        "time_ns": 2.567,
        "check_info": "hold (clk: 2.5ns, data: 2.8ns, slack: -0.3ns)",
        "status": "confirmed",
        "confirmer": "测试用户",
        "result": "pass",
        "reason": "复位期间违例，可忽略",
        "confirmed_at": "2024-01-15 10:30:00",
        "corner": "ss_125c_0p81v",
        "case": "test_case_1"
    }
]

# 确保目录存在
web_dir = Path("VIOLATION_CHECK/web_display")
data_dir = web_dir / "data"
data_dir.mkdir(parents=True, exist_ok=True)

# 写入测试数据
with open(data_dir / "violations.json", 'w', encoding='utf-8') as f:
    json.dump(test_data, f, ensure_ascii=False, indent=2)

print(f"✅ 测试数据已写入: {data_dir / 'violations.json'}")
print(f"数据包含 {len(test_data)} 条违例记录")

# 创建索引文件
index_data = {
    "metadata": {
        "total_violations": len(test_data),
        "export_time": "2024-01-15T10:30:00",
        "version": "1.0"
    },
    "corners": ["ss_125c_0p81v"],
    "cases": ["test_case_1"],
    "violations": test_data
}

with open(data_dir / "index.json", 'w', encoding='utf-8') as f:
    json.dump(index_data, f, ensure_ascii=False, indent=2)

print(f"✅ 索引文件已写入: {data_dir / 'index.json'}")
print("现在可以在浏览器中打开 VIOLATION_CHECK/web_display/offline.html 查看结果")