# 回归目录扫描器功能增强

## 概述

本次更新为回归目录扫描器添加了对更灵活目录结构的支持，以适应不同用户的回归命令和目录组织方式。

## 主要变更

### 1. 双模式扫描支持

#### 标准模式（默认）
- 目录结构：`./regression/<subsys>/.../<case_name>_<corner_name>/<case_name>_<seed_number>/log/vio_summary.log`
- 适用于遵循标准回归目录结构的项目
- 保持与现有功能的完全兼容性

#### 通用模式
- 目录结构：`./regression/任意层级目录/<case_name>_<seed_number>/log/vio_summary.log`
- 适用于自定义回归目录结构的项目
- 通过智能解析提取corner和subsys信息

### 2. 智能信息提取

#### Corner检测
- 支持的corner列表：
  ```python
  VALID_CORNERS = [
      'npg_f1_ssg', 'npg_f2_ssg', 'npg_f3_ssg', 'npg_f4_ssg', 'npg_f5_ssg', 'npg_f6_ssg', 'npg_f7_ssg',
      'npg_f1_ffg', 'npg_f2_ffg', 'npg_f3_ffg', 'npg_f4_ffg', 'npg_f5_ffg', 'npg_f6_ffg', 'npg_f7_ffg',
      'npg_f1_tt', 'npg_f2_tt', 'npg_f3_tt'
  ]
  ```
- 从路径组件中智能匹配corner名称
- 支持格式：`case_name_corner`、`case_name_corner_suffix`等

#### Subsys检测
- 从`$PROJ_ENV`环境变量指定的目录中自动获取有效子系统列表
- 支持以`_sys`结尾的目录和`top`目录
- 在路径中智能匹配子系统名称

### 3. UI界面增强

#### 新增控件
- 添加"标准回归目录结构"复选框
- 默认选中，使用标准扫描模式
- 取消选中时使用通用扫描模式
- 提供工具提示说明两种模式的区别

#### 缓存机制优化
- 缓存键包含扫描模式信息
- 不同模式的扫描结果分别缓存
- 切换模式时自动清空当前结果，提示重新扫描

## 技术实现

### 核心类修改

#### RegressionDirectoryScanner
```python
def __init__(self, use_standard_structure: bool = True):
    # 新增扫描模式参数
    self.use_standard_structure = use_standard_structure
    # 初始化corner和subsys列表
    self.valid_corners = [...]
    self.valid_subsys = self._get_valid_subsys_list()
```

#### 新增方法
- `_parse_flexible_structure()`: 解析通用目录结构
- `_find_corner_in_path()`: 在路径中查找corner
- `_find_subsys_in_path_flexible()`: 在路径中查找子系统
- `_get_valid_subsys_list()`: 从环境变量获取子系统列表

### UI组件修改

#### RegressionBatchDialog
- 添加扫描模式选择复选框
- 更新缓存机制以支持模式区分
- 添加模式切换处理逻辑

## 使用方法

### 标准模式（默认）
1. 保持"标准回归目录结构"复选框选中
2. 选择回归根目录
3. 点击"开始扫描"

### 通用模式
1. 取消选中"标准回归目录结构"复选框
2. 选择回归根目录
3. 点击"开始扫描"

## 兼容性

- 完全向后兼容现有功能
- 默认使用标准模式，不影响现有用户
- 新功能为可选功能，不会破坏现有工作流程

## 测试验证

通过测试验证了以下功能：
- 标准模式正确解析标准目录结构
- 通用模式正确解析灵活目录结构
- Corner检测算法正确识别各种格式
- Subsys检测算法正确匹配子系统名称
- UI界面正确响应模式切换

## 环境要求

- 需要设置`$PROJ_ENV`环境变量以获取子系统列表
- 如果环境变量未设置，将使用默认子系统列表

## 注意事项

1. 通用模式下，如果无法从路径中提取corner或subsys信息，将设置为"unknown"
2. 切换扫描模式时需要重新扫描目录
3. 不同模式的扫描结果会分别缓存，避免混淆