#!/usr/bin/env python3
"""
简化的数据库合并功能测试脚本（不依赖PyQt5）
"""

import os
import sqlite3
import tempfile
import shutil
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any


class SimpleMergeTest:
    """简化的合并测试类"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建表结构
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS timing_violations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_name TEXT NOT NULL,
                corner TEXT,
                num INTEGER NOT NULL,
                hier TEXT NOT NULL,
                time_fs INTEGER NOT NULL,
                time_display TEXT NOT NULL,
                check_info TEXT NOT NULL,
                file_path TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(case_name, corner, num, hier, check_info)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS confirmation_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                violation_id INTEGER NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                confirmer TEXT,
                result TEXT,
                reason TEXT,
                is_auto_confirmed BOOLEAN DEFAULT 0,
                confirmed_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (violation_id) REFERENCES timing_violations(id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS violation_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                hier_pattern TEXT NOT NULL,
                check_pattern TEXT NOT NULL,
                default_confirmer TEXT,
                default_result TEXT,
                default_reason TEXT,
                match_count INTEGER DEFAULT 1,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(hier_pattern, check_pattern)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def backup_database(self) -> str:
        """备份数据库"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = os.path.dirname(self.db_path)
            backup_filename = f"timing_violations_backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            shutil.copy2(self.db_path, backup_path)
            print(f"数据库备份成功: {backup_path}")
            return backup_path
        except Exception as e:
            print(f"数据库备份失败: {str(e)}")
            return ""
    
    def validate_database_schema(self, db_path: str) -> bool:
        """验证数据库schema"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            required_tables = ['timing_violations', 'confirmation_records', 'violation_patterns']
            for table in required_tables:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                if not cursor.fetchone():
                    print(f"数据库缺少必要的表: {table}")
                    return False
            
            conn.close()
            return True
        except Exception as e:
            print(f"验证数据库schema失败: {str(e)}")
            return False
    
    def get_database_statistics(self, db_path: str = None) -> Dict[str, int]:
        """获取数据库统计信息"""
        stats = {
            'total_violations': 0,
            'confirmed_violations': 0,
            'pending_violations': 0,
            'total_patterns': 0,
            'unique_cases': 0
        }
        
        target_db = db_path if db_path else self.db_path
        
        try:
            conn = sqlite3.connect(target_db)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM timing_violations")
            stats['total_violations'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM confirmation_records WHERE status = 'confirmed'")
            stats['confirmed_violations'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM confirmation_records WHERE status = 'pending'")
            stats['pending_violations'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM violation_patterns")
            stats['total_patterns'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT case_name) FROM timing_violations")
            stats['unique_cases'] = cursor.fetchone()[0]
            
            conn.close()
        except Exception as e:
            print(f"获取数据库统计信息失败: {str(e)}")
        
        return stats
    
    def merge_databases(self, source_db_paths: List[str]) -> Dict[str, Any]:
        """合并数据库"""
        merge_stats = {
            'success': False,
            'backup_path': '',
            'total_violations_added': 0,
            'total_confirmations_updated': 0,
            'total_patterns_merged': 0,
            'errors': [],
            'processed_databases': 0
        }
        
        try:
            # 备份当前数据库
            backup_path = self.backup_database()
            if not backup_path:
                merge_stats['errors'].append("数据库备份失败")
                return merge_stats
            merge_stats['backup_path'] = backup_path
            
            # 验证源数据库
            valid_sources = []
            for db_path in source_db_paths:
                if not os.path.exists(db_path):
                    merge_stats['errors'].append(f"数据库文件不存在: {db_path}")
                    continue
                
                if not self.validate_database_schema(db_path):
                    merge_stats['errors'].append(f"数据库schema不兼容: {db_path}")
                    continue
                
                valid_sources.append(db_path)
            
            if not valid_sources:
                merge_stats['errors'].append("没有有效的源数据库")
                return merge_stats
            
            # 执行合并
            for source_db in valid_sources:
                print(f"正在合并数据库: {os.path.basename(source_db)}")
                result = self._merge_single_database(source_db)
                merge_stats['total_violations_added'] += result['violations_added']
                merge_stats['total_confirmations_updated'] += result['confirmations_updated']
                merge_stats['total_patterns_merged'] += result['patterns_merged']
                merge_stats['processed_databases'] += 1
                
                if result['errors']:
                    merge_stats['errors'].extend(result['errors'])
            
            merge_stats['success'] = True
            print("合并完成")
            
        except Exception as e:
            error_msg = f"数据库合并过程中发生错误: {str(e)}"
            print(error_msg)
            merge_stats['errors'].append(error_msg)
        
        return merge_stats
    
    def _merge_single_database(self, source_db_path: str) -> Dict[str, Any]:
        """合并单个数据库"""
        result = {
            'violations_added': 0,
            'confirmations_updated': 0,
            'patterns_merged': 0,
            'errors': []
        }
        
        target_conn = sqlite3.connect(self.db_path)
        source_conn = sqlite3.connect(source_db_path)
        
        try:
            target_cursor = target_conn.cursor()
            source_cursor = source_conn.cursor()
            
            # 合并timing_violations表
            source_cursor.execute("""
                SELECT case_name, corner, num, hier, time_fs, time_display, 
                       check_info, file_path, created_at
                FROM timing_violations
            """)
            
            violations_data = source_cursor.fetchall()
            for violation in violations_data:
                try:
                    target_cursor.execute("""
                        SELECT id FROM timing_violations
                        WHERE case_name=? AND corner=? AND num=? AND hier=? AND check_info=?
                    """, (violation[0], violation[1], violation[2], violation[3], violation[6]))
                    
                    existing = target_cursor.fetchone()
                    if not existing:
                        target_cursor.execute("""
                            INSERT INTO timing_violations
                            (case_name, corner, num, hier, time_fs, time_display, 
                             check_info, file_path, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, violation)
                        
                        new_violation_id = target_cursor.lastrowid
                        result['violations_added'] += 1
                        
                        # 创建确认记录
                        now = datetime.now().isoformat()
                        target_cursor.execute("""
                            INSERT INTO confirmation_records
                            (violation_id, status, created_at, updated_at)
                            VALUES (?, 'pending', ?, ?)
                        """, (new_violation_id, now, now))
                
                except Exception as e:
                    result['errors'].append(f"合并违例记录失败: {str(e)}")
                    continue
            
            target_conn.commit()
            
        except Exception as e:
            target_conn.rollback()
            error_msg = f"合并数据库 {source_db_path} 失败: {str(e)}"
            result['errors'].append(error_msg)
            print(error_msg)
        finally:
            target_conn.close()
            source_conn.close()
        
        return result


def create_test_data(db_path: str, case_prefix: str):
    """创建测试数据"""
    merger = SimpleMergeTest(db_path)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    now = datetime.now().isoformat()
    
    # 插入测试违例
    test_violations = [
        (f"{case_prefix}_1", "npg_f1_ssg", 1, "tb_top.cpu.core", 1500000, "1.5 NS", "setup(posedge clk, data)", "/path/to/log1", now),
        (f"{case_prefix}_1", "npg_f1_ssg", 2, "tb_top.cpu.cache", 2000000, "2.0 NS", "hold(posedge clk, addr)", "/path/to/log1", now),
        (f"{case_prefix}_2", "npg_f2_ffg", 1, "tb_top.mem.ctrl", 800000, "0.8 NS", "setup(posedge clk, we)", "/path/to/log2", now),
    ]
    
    for violation in test_violations:
        cursor.execute('''
            INSERT INTO timing_violations
            (case_name, corner, num, hier, time_fs, time_display, check_info, file_path, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', violation)
        
        violation_id = cursor.lastrowid
        cursor.execute('''
            INSERT INTO confirmation_records
            (violation_id, status, created_at, updated_at)
            VALUES (?, 'pending', ?, ?)
        ''', (violation_id, now, now))
    
    conn.commit()
    conn.close()
    print(f"测试数据创建完成: {db_path}")


def main():
    """主测试函数"""
    print("开始简化数据库合并测试...")
    
    temp_dir = tempfile.mkdtemp(prefix="timing_violation_simple_test_")
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建测试数据库
        main_db_path = os.path.join(temp_dir, "main", "timing_violations.db")
        user1_db_path = os.path.join(temp_dir, "user1", "timing_violations.db")
        user2_db_path = os.path.join(temp_dir, "user2", "timing_violations.db")
        
        create_test_data(main_db_path, "main_case")
        create_test_data(user1_db_path, "user1")
        create_test_data(user2_db_path, "user2")
        
        # 创建合并器
        merger = SimpleMergeTest(main_db_path)
        
        # 获取合并前统计
        print("\n=== 合并前统计 ===")
        main_stats = merger.get_database_statistics()
        print(f"主数据库: {main_stats}")
        
        user1_stats = merger.get_database_statistics(user1_db_path)
        print(f"用户1数据库: {user1_stats}")
        
        user2_stats = merger.get_database_statistics(user2_db_path)
        print(f"用户2数据库: {user2_stats}")
        
        # 执行合并
        print("\n=== 开始合并 ===")
        source_databases = [user1_db_path, user2_db_path]
        merge_result = merger.merge_databases(source_databases)
        
        # 显示结果
        print("\n=== 合并结果 ===")
        print(f"合并成功: {merge_result['success']}")
        print(f"备份文件: {merge_result['backup_path']}")
        print(f"新增违例: {merge_result['total_violations_added']}")
        print(f"更新确认: {merge_result['total_confirmations_updated']}")
        print(f"合并模式: {merge_result['total_patterns_merged']}")
        print(f"处理数据库: {merge_result['processed_databases']}")
        
        if merge_result['errors']:
            print(f"错误信息: {merge_result['errors']}")
        
        # 合并后统计
        print("\n=== 合并后统计 ===")
        final_stats = merger.get_database_statistics()
        print(f"最终数据库: {final_stats}")
        
        # 验证备份
        if merge_result['backup_path'] and os.path.exists(merge_result['backup_path']):
            print(f"备份文件验证: 成功")
        else:
            print("备份文件验证: 失败")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理
        try:
            shutil.rmtree(temp_dir)
            print(f"\n临时目录已清理: {temp_dir}")
        except Exception as e:
            print(f"清理失败: {str(e)}")


if __name__ == "__main__":
    main()
