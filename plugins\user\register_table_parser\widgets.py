"""
寄存器表格解析器自定义控件

包含：
- RegisterListWidget: 寄存器列表控件，支持搜索和过滤
- FieldEditorWidget: 字段编辑器控件，支持字段值编辑和实时计算
- NumberFormatConverter: 数字格式转换工具
"""

from typing import List, Optional, Callable, Dict
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QTreeWidget, 
    QTreeWidgetItem, QLabel, QPushButton, QGroupBox, QHeaderView,
    QScrollArea, QFrame, QGridLayout, QButtonGroup, QRadioButton,
    QSpacerItem, QSizePolicy, QComboBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QIcon, QPalette

try:
    from .models import RegisterInfo, RegisterTableData, FieldInfo, NumberFormat
    from .utils import NumberFormatConverter, FieldValueCalculator
except ImportError:
    from models import RegisterInfo, RegisterTableData, FieldInfo, NumberFormat
    from utils import NumberFormatConverter, FieldValueCalculator


class RegisterListWidget(QWidget):
    """寄存器列表控件
    
    功能：
    - 显示寄存器列表（Offset和RegName列）
    - 提供搜索功能，支持实时过滤
    - 支持十六进制和十进制地址搜索
    - 寄存器选择处理和视觉指示
    """
    
    # 信号定义
    register_selected = pyqtSignal(object)  # RegisterInfo
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 数据
        self.table_data: Optional[RegisterTableData] = None
        self.filtered_registers: List[RegisterInfo] = []
        self.selected_register: Optional[RegisterInfo] = None
        
        # 搜索防抖定时器
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self._perform_search)
        
        # 初始化UI
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化用户界面"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)
        
        # 搜索区域
        search_layout = self.create_search_area()
        layout.addLayout(search_layout)
        
        # 寄存器列表
        self.register_tree = self.create_register_tree()
        layout.addWidget(self.register_tree)
        
        # 状态标签
        self.status_label = QLabel("未加载数据")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 10px;
                padding: 3px 6px;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 3px;
                max-height: 16px;
            }
        """)
        layout.addWidget(self.status_label)
    
    def create_search_area(self) -> QVBoxLayout:
        """创建搜索区域"""
        search_layout = QVBoxLayout()
        search_layout.setSpacing(5)
        
        # 搜索标签
        search_label = QLabel("🔍 搜索寄存器:")
        search_label.setStyleSheet("QLabel { font-weight: bold; color: #333333; }")
        search_layout.addWidget(search_label)
        
        # 搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入寄存器名称或偏移地址 (支持十六进制0x前缀和十进制)")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #e0e0e0;
                border-radius: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                background-color: #f9fff9;
            }
            QLineEdit:hover {
                border-color: #c0c0c0;
            }
        """)
        search_layout.addWidget(self.search_input)
        
        # 搜索提示
        search_hint = QLabel("💡 提示: 支持按寄存器名称或地址搜索，地址可使用十六进制(0x1000)或十进制(4096)格式")
        search_hint.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 10px;
                font-style: italic;
                padding: 2px;
            }
        """)
        search_hint.setWordWrap(True)
        search_layout.addWidget(search_hint)
        
        return search_layout
    
    def create_register_tree(self) -> QTreeWidget:
        """创建寄存器树形控件"""
        tree = QTreeWidget()
        
        # 设置列
        tree.setHeaderLabels(["偏移地址", "寄存器名称", "字段数"])
        tree.setRootIsDecorated(False)  # 不显示根节点装饰
        tree.setAlternatingRowColors(True)  # 交替行颜色
        tree.setSelectionMode(QTreeWidget.SingleSelection)  # 单选模式
        tree.setSortingEnabled(True)  # 启用排序
        
        # 设置列宽
        header = tree.header()
        header.setStretchLastSection(False)  # 最后一列不自动拉伸
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 偏移地址列自适应内容
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 寄存器名称列拉伸
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 字段数列自适应内容
        
        # 设置样式
        tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #4CAF50;
                selection-color: white;
                font-size: 12px;
            }
            QTreeWidget::item {
                padding: 6px;
                border-bottom: 1px solid #e9ecef;
            }
            QTreeWidget::item:hover {
                background-color: #e8f5e8;
            }
            QTreeWidget::item:selected {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
            }
            QHeaderView::section {
                background-color: #f1f3f4;
                padding: 8px;
                border: none;
                border-right: 1px solid #d0d0d0;
                border-bottom: 2px solid #4CAF50;
                font-weight: bold;
                color: #333333;
            }
            QHeaderView::section:hover {
                background-color: #e8f0fe;
            }
        """)
        
        return tree
    
    def setup_connections(self):
        """设置信号连接"""
        # 搜索输入变化时启动防抖定时器
        self.search_input.textChanged.connect(self._on_search_text_changed)
        
        # 寄存器选择变化
        self.register_tree.itemSelectionChanged.connect(self._on_selection_changed)
        
        # 双击寄存器项
        self.register_tree.itemDoubleClicked.connect(self._on_item_double_clicked)
    
    def set_table_data(self, table_data: RegisterTableData):
        """设置表格数据"""
        self.table_data = table_data
        self.filtered_registers = table_data.registers if table_data else []
        self.selected_register = None
        
        # 更新显示
        self._update_register_list()
        self._update_status()
        
        # 清空搜索
        self.search_input.clear()
    
    def _on_search_text_changed(self, text: str):
        """搜索文本变化处理（防抖）"""
        # 停止之前的定时器
        self.search_timer.stop()
        
        # 启动新的定时器（300ms延迟）
        self.search_timer.start(300)
    
    def _perform_search(self):
        """执行搜索"""
        if not self.table_data:
            return
        
        query = self.search_input.text().strip()
        
        if not query:
            # 空查询，显示所有寄存器
            self.filtered_registers = self.table_data.registers
        else:
            # 执行搜索
            self.filtered_registers = self._search_registers(query)
        
        # 更新显示
        self._update_register_list()
        self._update_status()
    
    def _search_registers(self, query: str) -> List[RegisterInfo]:
        """搜索寄存器
        
        支持：
        - 寄存器名称模糊匹配
        - 十六进制地址搜索（带或不带0x前缀）
        - 十进制地址搜索
        """
        query_lower = query.lower()
        results = []
        
        for register in self.table_data.registers:
            # 搜索寄存器名称（模糊匹配）
            if query_lower in register.name.lower():
                results.append(register)
                continue
            
            # 搜索偏移地址（精确匹配）
            if query_lower in register.offset.lower():
                results.append(register)
                continue
            
            # 尝试作为十六进制地址搜索
            try:
                if query_lower.startswith("0x"):
                    # 带0x前缀的十六进制
                    query_int = int(query, 16)
                else:
                    # 尝试作为不带前缀的十六进制
                    query_int = int(query, 16)
                
                if register.offset_int == query_int:
                    results.append(register)
                    continue
            except ValueError:
                pass
            
            # 尝试作为十进制地址搜索
            try:
                query_int = int(query, 10)
                if register.offset_int == query_int:
                    results.append(register)
            except ValueError:
                pass
        
        return results
    
    def _update_register_list(self):
        """更新寄存器列表显示"""
        # 清空现有项目
        self.register_tree.clear()
        
        if not self.filtered_registers:
            return
        
        # 添加寄存器项目
        for register in self.filtered_registers:
            item = QTreeWidgetItem()
            
            # 设置列数据
            item.setText(0, register.offset)  # 偏移地址
            item.setText(1, register.name)    # 寄存器名称
            item.setText(2, str(len(register.non_reserved_fields)))  # 可编辑字段数
            
            # 存储寄存器对象引用
            item.setData(0, Qt.UserRole, register)
            
            # 设置工具提示
            tooltip = f"寄存器: {register.name}\n"
            tooltip += f"偏移地址: {register.offset}\n"
            tooltip += f"总字段数: {len(register.fields)}\n"
            tooltip += f"可编辑字段数: {len(register.non_reserved_fields)}"
            if register.description:
                tooltip += f"\n描述: {register.description}"
            
            item.setToolTip(0, tooltip)
            item.setToolTip(1, tooltip)
            item.setToolTip(2, tooltip)
            
            # 添加到树形控件
            self.register_tree.addTopLevelItem(item)
        
        # 按偏移地址排序
        self.register_tree.sortItems(0, Qt.AscendingOrder)
        
        # 调整列宽
        self.register_tree.resizeColumnToContents(0)
        self.register_tree.resizeColumnToContents(2)
    
    def _update_status(self):
        """更新状态标签"""
        if not self.table_data:
            self.status_label.setText("未加载数据")
            return
        
        total_count = len(self.table_data.registers)
        filtered_count = len(self.filtered_registers)
        
        if filtered_count == total_count:
            self.status_label.setText(f"📊 显示 {total_count} 个寄存器")
        else:
            self.status_label.setText(f"🔍 找到 {filtered_count} 个寄存器 (共 {total_count} 个)")
    
    def _on_selection_changed(self):
        """选择变化处理"""
        selected_items = self.register_tree.selectedItems()
        
        if selected_items:
            item = selected_items[0]
            register = item.data(0, Qt.UserRole)
            
            if register != self.selected_register:
                self.selected_register = register
                # 发射选择信号
                self.register_selected.emit(register)
        else:
            self.selected_register = None
            self.register_selected.emit(None)
    
    def _on_item_double_clicked(self, item: QTreeWidgetItem, column: int):
        """项目双击处理"""
        register = item.data(0, Qt.UserRole)
        if register:
            # 双击时也发射选择信号（可以用于特殊处理）
            self.register_selected.emit(register)
    
    def select_register_by_name(self, name: str) -> bool:
        """根据名称选择寄存器"""
        for i in range(self.register_tree.topLevelItemCount()):
            item = self.register_tree.topLevelItem(i)
            register = item.data(0, Qt.UserRole)
            
            if register and register.name == name:
                self.register_tree.setCurrentItem(item)
                return True
        
        return False
    
    def select_register_by_offset(self, offset: str) -> bool:
        """根据偏移地址选择寄存器"""
        for i in range(self.register_tree.topLevelItemCount()):
            item = self.register_tree.topLevelItem(i)
            register = item.data(0, Qt.UserRole)
            
            if register and register.offset.lower() == offset.lower():
                self.register_tree.setCurrentItem(item)
                return True
        
        return False
    
    def clear_selection(self):
        """清空选择"""
        self.register_tree.clearSelection()
        self.selected_register = None
    
    def get_selected_register(self) -> Optional[RegisterInfo]:
        """获取当前选择的寄存器"""
        return self.selected_register
    
    def set_search_text(self, text: str):
        """设置搜索文本"""
        self.search_input.setText(text)


class FieldEditorWidget(QWidget):
    """字段编辑器控件
    
    功能：
    - 显示寄存器字段信息（字段名、位范围、RW属性）
    - 提供可编辑字段的输入框，显示默认值
    - 禁用只读字段的输入框，跳过保留字段
    - 实时计算并显示寄存器值
    - 支持多种数字格式转换
    """
    
    # 信号定义
    register_value_changed = pyqtSignal(int)  # 寄存器值变化
    field_value_changed = pyqtSignal(str, int)  # 字段值变化 (field_name, value)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 数据
        self.current_register: Optional[RegisterInfo] = None
        self.field_values: Dict[str, int] = {}  # 字段名到值的映射
        self.field_inputs: Dict[str, QLineEdit] = {}  # 字段名到输入框的映射
        self.current_format: NumberFormat = NumberFormat.HEXADECIMAL
        
        # 初始化UI
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 寄存器信息和格式选择区域（同一行）
        self.top_panel = self.create_top_panel()
        main_layout.addWidget(self.top_panel)
        
        # 字段编辑区域（可滚动）
        self.fields_scroll_area = self.create_fields_scroll_area()
        main_layout.addWidget(self.fields_scroll_area)
        
        # 状态标签
        self.status_label = QLabel("请选择一个寄存器")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 10px;
                padding: 4px 8px;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 3px;
                text-align: center;
                max-height: 18px;
            }
        """)
        self.status_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.status_label)
    
    def create_top_panel(self) -> QWidget:
        """创建顶部面板（寄存器信息和数字格式在同一行）"""
        top_widget = QWidget()
        top_layout = QHBoxLayout(top_widget)
        top_layout.setContentsMargins(10, 8, 10, 8)
        top_layout.setSpacing(15)
        
        # 寄存器信息标签（一行显示）
        self.register_info_label = QLabel("未选择寄存器")
        self.register_info_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                font-family: 'Courier New', monospace;
                color: #2E7D32;
                padding: 8px 12px;
                background-color: #f8fff8;
                border: 2px solid #4CAF50;
                border-radius: 6px;
            }
        """)
        top_layout.addWidget(self.register_info_label, 1)  # 占主要空间
        
        # 数字格式下拉框
        format_label = QLabel("格式:")
        format_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #1976D2;
                padding: 2px;
            }
        """)
        top_layout.addWidget(format_label)
        
        self.format_combo = QComboBox()
        self.format_combo.addItems(["十六进制 (0x)", "十进制", "二进制 (0b)"])
        self.format_combo.setCurrentIndex(0)  # 默认十六进制
        self.format_combo.setStyleSheet("""
            QComboBox {
                font-size: 12px;
                font-weight: bold;
                color: #333333;
                padding: 6px 8px;
                background-color: white;
                border: 2px solid #2196F3;
                border-radius: 4px;
                min-width: 120px;
            }
            QComboBox:hover {
                border-color: #1976D2;
                background-color: #f8f9ff;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #1976D2;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                border: 1px solid #2196F3;
                selection-background-color: #e3f2fd;
                selection-color: #1976D2;
                font-size: 12px;
                padding: 4px;
            }
        """)
        top_layout.addWidget(self.format_combo)
        
        # 设置整体面板样式
        top_widget.setStyleSheet("""
            QWidget {
                background-color: #fafafa;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
        """)
        
        return top_widget
    
    def create_fields_scroll_area(self) -> QWidget:
        """创建字段编辑区域"""
        # 主容器
        fields_widget = QWidget()
        fields_main_layout = QVBoxLayout(fields_widget)
        fields_main_layout.setContentsMargins(0, 0, 0, 0)
        fields_main_layout.setSpacing(8)
        
        # 字段标题
        fields_title = QLabel("⚙️ 寄存器字段")
        fields_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #333333;
                padding: 8px 12px;
                background-color: #f1f3f4;
                border: 1px solid #d0d0d0;
                border-radius: 6px 6px 0 0;
                margin: 0;
            }
        """)
        fields_main_layout.addWidget(fields_title)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #d0d0d0;
                border-top: none;
                border-radius: 0 0 6px 6px;
                background-color: white;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
        """)
        
        # 创建字段容器
        self.fields_container = QWidget()
        self.fields_layout = QVBoxLayout(self.fields_container)
        self.fields_layout.setContentsMargins(8, 8, 8, 8)
        self.fields_layout.setSpacing(4)
        
        # 添加占位符标签
        self.placeholder_label = QLabel("请从左侧列表选择一个寄存器")
        self.placeholder_label.setStyleSheet("""
            QLabel {
                color: #999999;
                font-size: 14px;
                font-style: italic;
                padding: 50px;
                text-align: center;
            }
        """)
        self.placeholder_label.setAlignment(Qt.AlignCenter)
        self.fields_layout.addWidget(self.placeholder_label)
        
        # 弹性空间
        self.fields_layout.addStretch()
        
        scroll_area.setWidget(self.fields_container)
        fields_main_layout.addWidget(scroll_area)
        
        return fields_widget
    
    def setup_connections(self):
        """设置信号连接"""
        # 数字格式选择变化
        self.format_combo.currentIndexChanged.connect(self.on_format_changed)
    
    def set_register(self, register: Optional[RegisterInfo]):
        """设置当前寄存器"""
        self.current_register = register
        self.field_values.clear()
        self.field_inputs.clear()
        
        if register:
            # 初始化字段值（使用复位值）
            for field in register.non_reserved_fields:
                self.field_values[field.name] = field.get_reset_value_int()
            
            # 创建字段编辑界面
            self.create_field_editors()
            
            # 计算并显示初始寄存器值
            self.update_register_value()
            
            # 更新状态
            field_count = len(register.non_reserved_fields)
            writable_count = len(register.writable_fields)
            self.status_label.setText(f"📝 {field_count} 个字段 ({writable_count} 个可编辑)")
        else:
            # 清空显示
            self.register_info_label.setText("未选择寄存器")
            self.clear_field_editors(show_placeholder=True)  # 显示占位符
            self.status_label.setText("请选择一个寄存器")
    
    def create_field_editors(self):
        """创建字段编辑器"""
        if not self.current_register:
            return
        
        # 清空现有内容，不显示占位符
        self.clear_field_editors(show_placeholder=False)
        
        # 获取非保留字段
        fields = self.current_register.non_reserved_fields
        if not fields:
            no_fields_label = QLabel("该寄存器没有可编辑的字段")
            no_fields_label.setStyleSheet("""
                QLabel {
                    color: #666666;
                    font-size: 12px;
                    font-style: italic;
                    padding: 20px;
                    text-align: center;
                }
            """)
            no_fields_label.setAlignment(Qt.AlignCenter)
            self.fields_layout.addWidget(no_fields_label)
            return
        
        # 为每个字段创建编辑器
        for field in fields:
            field_widget = self.create_field_widget(field)
            self.fields_layout.addWidget(field_widget)
        
        # 添加弹性空间
        self.fields_layout.addStretch()
    
    def create_field_widget(self, field: FieldInfo) -> QWidget:
        """创建单个字段的编辑控件"""
        field_widget = QFrame()
        field_widget.setStyleSheet("""
            QFrame {
                background-color: #fafafa;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                padding: 4px;
                margin: 1px;
            }
            QFrame:hover {
                background-color: #f5f5f5;
                border-color: #c0c0c0;
            }
        """)
        
        layout = QGridLayout(field_widget)
        layout.setContentsMargins(6, 4, 6, 4)
        layout.setSpacing(4)
        
        # 字段名称标签
        name_label = QLabel(field.name)
        name_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 11px;
                color: #333333;
                background-color: transparent;
                border: none;
                padding: 1px;
            }
        """)
        layout.addWidget(name_label, 0, 0)
        
        # 位范围标签
        range_label = QLabel(f"[{field.bit_range}]")
        range_label.setStyleSheet("""
            QLabel {
                font-family: 'Courier New', monospace;
                font-size: 9px;
                color: #666666;
                background-color: #e8f4f8;
                border: 1px solid #b3d9e6;
                border-radius: 2px;
                padding: 1px 4px;
            }
        """)
        layout.addWidget(range_label, 0, 1)
        
        # 读写属性标签
        rw_label = QLabel(field.rw_attribute)
        if field.is_read_only:
            rw_style = """
                QLabel {
                    font-size: 9px;
                    font-weight: bold;
                    color: #d32f2f;
                    background-color: #ffebee;
                    border: 1px solid #ffcdd2;
                    border-radius: 2px;
                    padding: 1px 4px;
                }
            """
        else:
            rw_style = """
                QLabel {
                    font-size: 9px;
                    font-weight: bold;
                    color: #388e3c;
                    background-color: #e8f5e8;
                    border: 1px solid #c8e6c9;
                    border-radius: 2px;
                    padding: 1px 4px;
                }
            """
        rw_label.setStyleSheet(rw_style)
        layout.addWidget(rw_label, 0, 2)
        
        # 值输入框
        value_input = QLineEdit()
        value_input.setStyleSheet("""
            QLineEdit {
                font-family: 'Courier New', monospace;
                font-size: 11px;
                padding: 3px 6px;
                border: 1px solid #e0e0e0;
                border-radius: 3px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                background-color: #f9fff9;
            }
            QLineEdit:disabled {
                background-color: #f5f5f5;
                color: #999999;
                border-color: #d0d0d0;
            }
        """)
        
        # 设置初始值
        initial_value = self.field_values.get(field.name, field.get_reset_value_int())
        value_input.setText(self.format_value(initial_value, field.bit_width))
        
        # 设置输入框状态
        if field.is_read_only:
            value_input.setEnabled(False)
            value_input.setToolTip(f"只读字段，不可编辑\n复位值: {field.reset_value}")
        else:
            value_input.setToolTip(f"可编辑字段\n位宽: {field.bit_width} 位\n最大值: {(1 << field.bit_width) - 1}")
            # 连接值变化信号
            value_input.textChanged.connect(lambda text, f=field: self.on_field_value_changed(f, text))
        
        layout.addWidget(value_input, 1, 0, 1, 3)
        
        # 保存输入框引用
        self.field_inputs[field.name] = value_input
        
        # 描述标签（如果有）
        if field.description:
            desc_label = QLabel(field.description)
            desc_label.setStyleSheet("""
                QLabel {
                    font-size: 9px;
                    color: #777777;
                    font-style: italic;
                    background-color: transparent;
                    border: none;
                    padding: 1px;
                }
            """)
            desc_label.setWordWrap(True)
            layout.addWidget(desc_label, 2, 0, 1, 3)
        
        return field_widget
    
    def clear_field_editors(self, show_placeholder=True):
        """清空字段编辑器
        
        Args:
            show_placeholder: 是否显示占位符提示
        """
        # 移除所有子控件
        while self.fields_layout.count():
            child = self.fields_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        # 根据参数决定是否添加占位符
        if show_placeholder:
            self.placeholder_label = QLabel("请从左侧列表选择一个寄存器")
            self.placeholder_label.setStyleSheet("""
                QLabel {
                    color: #999999;
                    font-size: 14px;
                    font-style: italic;
                    padding: 50px;
                    text-align: center;
                }
            """)
            self.placeholder_label.setAlignment(Qt.AlignCenter)
            self.fields_layout.addWidget(self.placeholder_label)
        
        # 添加弹性空间
        self.fields_layout.addStretch()
    
    @pyqtSlot()
    def on_format_changed(self):
        """数字格式变化处理"""
        # 根据下拉框选择确定新格式
        index = self.format_combo.currentIndex()
        if index == 0:
            new_format = NumberFormat.HEXADECIMAL
        elif index == 1:
            new_format = NumberFormat.DECIMAL
        elif index == 2:
            new_format = NumberFormat.BINARY
        else:
            new_format = NumberFormat.HEXADECIMAL
        
        if new_format != self.current_format:
            self.current_format = new_format
            self.update_all_field_displays()
            self.update_register_value()
    
    def on_field_value_changed(self, field: FieldInfo, text: str):
        """字段值变化处理"""
        try:
            # 解析输入值
            if not text.strip():
                value = 0
            else:
                value = NumberFormatConverter.from_string(text.strip())
            
            # 验证值范围
            if not field.validate_value(value):
                # 值超出范围，恢复之前的值
                old_value = self.field_values.get(field.name, field.get_reset_value_int())
                input_widget = self.field_inputs.get(field.name)
                if input_widget:
                    input_widget.blockSignals(True)
                    input_widget.setText(self.format_value(old_value, field.bit_width))
                    input_widget.blockSignals(False)
                return
            
            # 更新字段值
            self.field_values[field.name] = value
            
            # 实时计算寄存器值
            self.update_register_value()
            
            # 发射字段值变化信号
            self.field_value_changed.emit(field.name, value)
            
        except ValueError:
            # 输入格式错误，恢复之前的值
            old_value = self.field_values.get(field.name, field.get_reset_value_int())
            input_widget = self.field_inputs.get(field.name)
            if input_widget:
                input_widget.blockSignals(True)
                input_widget.setText(self.format_value(old_value, field.bit_width))
                input_widget.blockSignals(False)
    
    def update_register_value(self):
        """更新寄存器值显示"""
        if not self.current_register:
            self.register_info_label.setText("未选择寄存器")
            return
        
        # 计算寄存器值
        register_value = self.current_register.calculate_register_value(self.field_values)
        
        # 格式化显示
        formatted_value = self.format_register_value(register_value)
        
        # 更新寄存器信息标签（一行显示）
        info_text = f"{self.current_register.name} @ {self.current_register.offset}: [{formatted_value}]"
        self.register_info_label.setText(info_text)
        
        # 发射寄存器值变化信号
        self.register_value_changed.emit(register_value)
    
    def update_all_field_displays(self):
        """更新所有字段的显示格式"""
        if not self.current_register:
            return
        
        for field in self.current_register.non_reserved_fields:
            if field.name in self.field_inputs and not field.is_read_only:
                input_widget = self.field_inputs[field.name]
                current_value = self.field_values.get(field.name, field.get_reset_value_int())
                
                # 阻止信号以避免递归
                input_widget.blockSignals(True)
                input_widget.setText(self.format_value(current_value, field.bit_width))
                input_widget.blockSignals(False)
    
    def format_value(self, value: int, bit_width: int = None) -> str:
        """格式化字段值"""
        return NumberFormatConverter.convert_format(value, self.current_format, bit_width)
    
    def format_register_value(self, value: int) -> str:
        """格式化寄存器值（32位）"""
        if self.current_format == NumberFormat.BINARY:
            return f"0b{value:032b}"
        elif self.current_format == NumberFormat.DECIMAL:
            return str(value)
        else:  # HEXADECIMAL
            return f"0x{value:08X}"
    
    def get_current_register_value(self) -> int:
        """获取当前寄存器值"""
        if not self.current_register:
            return 0
        return self.current_register.calculate_register_value(self.field_values)
    
    def get_field_value(self, field_name: str) -> Optional[int]:
        """获取指定字段的值"""
        return self.field_values.get(field_name)
    
    def set_field_value(self, field_name: str, value: int):
        """设置指定字段的值"""
        if not self.current_register:
            return
        
        field = self.current_register.get_field_by_name(field_name)
        if not field or field.is_reserved or not field.validate_value(value):
            return
        
        # 更新字段值
        self.field_values[field_name] = value
        
        # 更新输入框显示
        if field_name in self.field_inputs:
            input_widget = self.field_inputs[field_name]
            input_widget.blockSignals(True)
            input_widget.setText(self.format_value(value, field.bit_width))
            input_widget.blockSignals(False)
        
        # 更新寄存器值
        self.update_register_value()
    
    def reset_to_default_values(self):
        """重置所有字段到默认值"""
        if not self.current_register:
            return
        
        # 重置字段值
        for field in self.current_register.non_reserved_fields:
            default_value = field.get_reset_value_int()
            self.field_values[field.name] = default_value
            
            # 更新输入框显示
            if field.name in self.field_inputs:
                input_widget = self.field_inputs[field.name]
                input_widget.blockSignals(True)
                input_widget.setText(self.format_value(default_value, field.bit_width))
                input_widget.blockSignals(False)
        
        # 更新寄存器值
        self.update_register_value()