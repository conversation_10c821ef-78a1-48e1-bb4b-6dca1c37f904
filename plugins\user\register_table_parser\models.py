"""
寄存器表格解析器数据模型

定义了寄存器表格解析所需的核心数据结构，包括：
- HeaderInfo: 表头信息（项目名称、子系统、模块名称、基地址）
- FieldInfo: 字段信息（字段名、位范围、读写属性、复位值、描述）
- RegisterInfo: 寄存器信息（偏移地址、名称、描述、宽度、字段列表）
- RegisterTableData: 完整的寄存器表格数据
"""

from dataclasses import dataclass
from typing import List, Optional
from enum import Enum


class NumberFormat(Enum):
    """数字格式枚举"""
    BINARY = "binary"
    DECIMAL = "decimal"
    HEXADECIMAL = "hexadecimal"


class FieldType(Enum):
    """字段类型枚举"""
    READ_WRITE = "RW"
    READ_ONLY = "RO"
    WRITE_ONLY = "WO"
    RESERVED = "Reserved"


@dataclass
class HeaderInfo:
    """表头信息数据类
    
    包含寄存器表格的基本信息，通常位于Excel文件的前4行
    """
    project_name: str = ""
    sub_system: str = ""
    module_name: str = ""
    base_addr: str = ""
    
    def is_valid(self) -> bool:
        """检查表头信息是否有效"""
        return bool(self.project_name and self.sub_system and 
                   self.module_name and self.base_addr)
    
    def __str__(self) -> str:
        return f"Project: {self.project_name}, SubSystem: {self.sub_system}, Module: {self.module_name}, BaseAddr: {self.base_addr}"


@dataclass
class FieldInfo:
    """字段信息数据类
    
    描述寄存器中单个字段的详细信息
    """
    name: str = ""
    bit_range: str = ""  # 例如: "31:24", "15", "7:0"
    rw_attribute: str = ""  # RW, RO, WO
    reset_value: str = "0"
    description: str = ""
    
    def __post_init__(self):
        """初始化后处理"""
        # 标准化读写属性
        if self.rw_attribute:
            self.rw_attribute = self.rw_attribute.upper().strip()
    
    @property
    def is_reserved(self) -> bool:
        """判断是否为保留字段"""
        return self.name.lower().strip() == "reserved"
    
    @property
    def is_read_only(self) -> bool:
        """判断是否为只读字段"""
        return self.rw_attribute == "RO"
    
    @property
    def is_writable(self) -> bool:
        """判断是否可写"""
        return self.rw_attribute in ["RW", "WO"]
    
    @property
    def bit_width(self) -> int:
        """获取字段位宽"""
        try:
            if ":" in self.bit_range:
                # 范围格式，如 "31:24"
                high, low = self.bit_range.split(":")
                return int(high.strip()) - int(low.strip()) + 1
            else:
                # 单个位，如 "15"
                return 1
        except (ValueError, AttributeError):
            return 0
    
    @property
    def bit_position(self) -> tuple:
        """获取字段位置 (high_bit, low_bit)"""
        try:
            if ":" in self.bit_range:
                high, low = self.bit_range.split(":")
                return (int(high.strip()), int(low.strip()))
            else:
                bit = int(self.bit_range.strip())
                return (bit, bit)
        except (ValueError, AttributeError):
            return (0, 0)
    
    def get_reset_value_int(self) -> int:
        """获取复位值的整数表示"""
        try:
            if self.reset_value.startswith("0x") or self.reset_value.startswith("0X"):
                return int(self.reset_value, 16)
            elif self.reset_value.startswith("0b") or self.reset_value.startswith("0B"):
                return int(self.reset_value, 2)
            else:
                return int(self.reset_value)
        except (ValueError, AttributeError):
            return 0
    
    def validate_value(self, value: int) -> bool:
        """验证值是否在字段范围内"""
        max_value = (1 << self.bit_width) - 1
        return 0 <= value <= max_value
    
    def __str__(self) -> str:
        return f"{self.name}[{self.bit_range}] ({self.rw_attribute}): {self.reset_value}"


@dataclass
class RegisterInfo:
    """寄存器信息数据类
    
    描述单个寄存器的完整信息，包括其所有字段
    """
    offset: str = ""  # 偏移地址，如 "0x0000"
    name: str = ""
    description: str = ""
    width: int = 32  # 寄存器位宽，默认32位
    fields: List[FieldInfo] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.fields is None:
            self.fields = []
    
    @property
    def offset_int(self) -> int:
        """获取偏移地址的整数表示"""
        try:
            if self.offset.startswith("0x") or self.offset.startswith("0X"):
                return int(self.offset, 16)
            else:
                return int(self.offset)
        except (ValueError, AttributeError):
            return 0
    
    @property
    def non_reserved_fields(self) -> List[FieldInfo]:
        """获取非保留字段列表"""
        return [field for field in self.fields if not field.is_reserved]
    
    @property
    def writable_fields(self) -> List[FieldInfo]:
        """获取可写字段列表"""
        return [field for field in self.fields if field.is_writable and not field.is_reserved]
    
    def get_field_by_name(self, name: str) -> Optional[FieldInfo]:
        """根据名称获取字段"""
        for field in self.fields:
            if field.name == name:
                return field
        return None
    
    def calculate_reset_value(self) -> int:
        """计算寄存器的复位值"""
        reset_value = 0
        for field in self.fields:
            if not field.is_reserved:
                high_bit, low_bit = field.bit_position
                field_value = field.get_reset_value_int()
                # 将字段值放置到正确的位置
                reset_value |= (field_value << low_bit)
        return reset_value
    
    def calculate_register_value(self, field_values: dict) -> int:
        """根据字段值计算寄存器值
        
        Args:
            field_values: 字段名到值的映射字典
            
        Returns:
            计算得到的寄存器值
        """
        register_value = 0
        for field in self.fields:
            if field.is_reserved:
                # 保留字段使用复位值
                field_value = field.get_reset_value_int()
            else:
                # 使用提供的值，如果没有提供则使用复位值
                field_value = field_values.get(field.name, field.get_reset_value_int())
            
            high_bit, low_bit = field.bit_position
            # 确保值在字段范围内
            max_value = (1 << field.bit_width) - 1
            field_value = min(field_value, max_value)
            
            # 将字段值放置到正确的位置
            register_value |= (field_value << low_bit)
        
        return register_value
    
    def __str__(self) -> str:
        return f"{self.name} @ {self.offset} ({len(self.fields)} fields)"


@dataclass
class RegisterTableData:
    """完整的寄存器表格数据
    
    包含表头信息和所有寄存器信息
    """
    header: HeaderInfo = None
    registers: List[RegisterInfo] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.header is None:
            self.header = HeaderInfo()
        if self.registers is None:
            self.registers = []
    
    @property
    def register_count(self) -> int:
        """获取寄存器数量"""
        return len(self.registers)
    
    @property
    def total_field_count(self) -> int:
        """获取总字段数量"""
        return sum(len(reg.fields) for reg in self.registers)
    
    @property
    def non_reserved_field_count(self) -> int:
        """获取非保留字段数量"""
        return sum(len(reg.non_reserved_fields) for reg in self.registers)
    
    def get_register_by_name(self, name: str) -> Optional[RegisterInfo]:
        """根据名称获取寄存器"""
        for register in self.registers:
            if register.name == name:
                return register
        return None
    
    def get_register_by_offset(self, offset: str) -> Optional[RegisterInfo]:
        """根据偏移地址获取寄存器"""
        for register in self.registers:
            if register.offset.lower() == offset.lower():
                return register
        return None
    
    def search_registers(self, query: str) -> List[RegisterInfo]:
        """搜索寄存器
        
        Args:
            query: 搜索关键词，可以是寄存器名称或偏移地址
            
        Returns:
            匹配的寄存器列表
        """
        query = query.lower().strip()
        if not query:
            return self.registers
        
        results = []
        for register in self.registers:
            # 搜索寄存器名称
            if query in register.name.lower():
                results.append(register)
                continue
            
            # 搜索偏移地址
            if query in register.offset.lower():
                results.append(register)
                continue
            
            # 尝试作为十六进制地址搜索
            try:
                if query.startswith("0x"):
                    query_int = int(query, 16)
                else:
                    query_int = int(query, 16)  # 尝试作为十六进制解析
                
                if register.offset_int == query_int:
                    results.append(register)
                    continue
            except ValueError:
                pass
            
            # 尝试作为十进制地址搜索
            try:
                query_int = int(query, 10)
                if register.offset_int == query_int:
                    results.append(register)
            except ValueError:
                pass
        
        return results
    
    def is_valid(self) -> bool:
        """检查数据是否有效"""
        return (self.header.is_valid() and 
                len(self.registers) > 0 and
                all(reg.name and reg.offset for reg in self.registers))
    
    def __str__(self) -> str:
        return f"RegisterTable: {self.header.module_name} ({self.register_count} registers, {self.total_field_count} fields)"


class ParseError(Exception):
    """解析错误异常类"""
    
    def __init__(self, message: str, row: int = None, column: str = None):
        self.message = message
        self.row = row
        self.column = column
        super().__init__(self.format_message())
    
    def format_message(self) -> str:
        """格式化错误消息"""
        if self.row is not None and self.column is not None:
            return f"解析错误 (行 {self.row}, 列 {self.column}): {self.message}"
        elif self.row is not None:
            return f"解析错误 (行 {self.row}): {self.message}"
        else:
            return f"解析错误: {self.message}"


class ValidationError(Exception):
    """验证错误异常类"""
    
    def __init__(self, message: str, field_name: str = None, register_name: str = None):
        self.message = message
        self.field_name = field_name
        self.register_name = register_name
        super().__init__(self.format_message())
    
    def format_message(self) -> str:
        """格式化错误消息"""
        if self.register_name and self.field_name:
            return f"验证错误 (寄存器 {self.register_name}, 字段 {self.field_name}): {self.message}"
        elif self.register_name:
            return f"验证错误 (寄存器 {self.register_name}): {self.message}"
        else:
            return f"验证错误: {self.message}"