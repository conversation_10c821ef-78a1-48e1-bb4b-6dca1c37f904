"""
Main Window Integration Adapter

Provides seamless integration of performance components into the existing TimingViolationWindow
with backward compatibility and graceful degradation.
"""

import time
from typing import Dict, List, Optional, Any, Callable
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import <PERSON>Widge<PERSON>, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar, QPushButton

try:
    from .performance_integration_system import PerformanceIntegrationSystem
except ImportError:
    try:
        from performance_integration_system import PerformanceIntegrationSystem
    except ImportError:
        print("Warning: Performance Integration System not available")
        PerformanceIntegrationSystem = None


class MainWindowIntegrationAdapter(QObject):
    """
    Adapter that integrates performance components into the existing TimingViolationWindow
    without breaking existing functionality.
    """
    
    # Signals for main window
    status_message_changed = pyqtSignal(str)  # Status bar message
    progress_updated = pyqtSignal(int, str)   # Progress bar update
    table_widget_ready = pyqtSignal(QWidget)  # New optimized table widget
    performance_alert = pyqtSignal(str, str)  # Alert type, message
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        
        # Integration system
        self.integration_system = None
        self.integration_enabled = False
        
        # Current state
        self.current_violations = []
        self.current_file_path = ""
        self.performance_session_active = False
        
        # UI components for performance monitoring
        self.performance_status_widget = None
        self.performance_progress_bar = None
        
        # Backward compatibility flags
        self.use_legacy_parsing = False
        self.use_legacy_table = False
        
        # Initialize integration
        self._initialize_integration()
    
    def _initialize_integration(self):
        """Initialize the performance integration system"""
        try:
            if PerformanceIntegrationSystem:
                self.integration_system = PerformanceIntegrationSystem(self.main_window)
                
                # Connect signals
                self.integration_system.performance_status_changed.connect(
                    self._handle_performance_status_change
                )
                self.integration_system.optimization_applied.connect(
                    self._handle_optimization_applied
                )
                self.integration_system.error_occurred.connect(
                    self._handle_integration_error
                )
                self.integration_system.parsing_progress.connect(
                    self.progress_updated.emit
                )
                self.integration_system.ui_mode_changed.connect(
                    self._handle_ui_mode_change
                )
                
                self.integration_enabled = True
                print("✓ Main Window Integration Adapter initialized")
                
            else:
                print("⚠ Performance Integration System not available, using legacy mode")
                self.use_legacy_parsing = True
                self.use_legacy_table = True
                
        except Exception as e:
            print(f"✗ Failed to initialize integration adapter: {e}")
            self.use_legacy_parsing = True
            self.use_legacy_table = True
    
    def integrate_file_parsing(self, original_parse_method: Callable) -> Callable:
        """
        Integrate performance-optimized file parsing with the existing parse method.
        
        Args:
            original_parse_method: The original file parsing method
            
        Returns:
            Callable: Enhanced parsing method
        """
        def enhanced_parse_method(file_path: str, progress_callback: Callable = None):
            try:
                # Store current file path
                self.current_file_path = file_path
                
                # Start performance session if integration is enabled
                if self.integration_enabled and self.integration_system:
                    session_started = self.integration_system.start_performance_session(
                        'file_parsing', file_path
                    )
                    self.performance_session_active = session_started
                    
                    if session_started:
                        # Use optimized parsing
                        violations = self.integration_system.parse_file_with_optimization(
                            file_path, progress_callback
                        )
                        self.current_violations = violations
                        return violations
                
                # Fallback to original method
                print("Using legacy parsing method")
                violations = original_parse_method(file_path, progress_callback)
                self.current_violations = violations
                return violations
                
            except Exception as e:
                print(f"Enhanced parsing failed, falling back to original: {e}")
                # Always fallback to original method on error
                return original_parse_method(file_path, progress_callback)
        
        return enhanced_parse_method
    
    def integrate_table_creation(self, original_table_creation: Callable) -> Callable:
        """
        Integrate performance-optimized table creation with existing table creation.
        
        Args:
            original_table_creation: The original table creation method
            
        Returns:
            Callable: Enhanced table creation method
        """
        def enhanced_table_creation(violations_data: List[Dict], parent_widget: QWidget = None):
            try:
                # Use optimized table if integration is enabled
                if (self.integration_enabled and self.integration_system and 
                    not self.use_legacy_table):
                    
                    optimized_table = self.integration_system.create_optimized_table_view(
                        violations_data, parent_widget
                    )
                    
                    if optimized_table:
                        # Emit signal that new table is ready
                        self.table_widget_ready.emit(optimized_table)
                        return optimized_table
                
                # Fallback to original method
                print("Using legacy table creation")
                return original_table_creation(violations_data, parent_widget)
                
            except Exception as e:
                print(f"Enhanced table creation failed, falling back to original: {e}")
                return original_table_creation(violations_data, parent_widget)
        
        return enhanced_table_creation
    
    def integrate_batch_operations(self, original_batch_method: Callable) -> Callable:
        """
        Integrate performance-optimized batch operations.
        
        Args:
            original_batch_method: The original batch processing method
            
        Returns:
            Callable: Enhanced batch processing method
        """
        def enhanced_batch_method(violations: List[Dict], operation_type: str = 'batch_confirm',
                                progress_callback: Callable = None):
            try:
                # Use optimized batch processing if available
                if self.integration_enabled and self.integration_system:
                    processed_violations = self.integration_system.process_batch_operations(
                        violations, operation_type, progress_callback
                    )
                    return processed_violations
                
                # Fallback to original method
                return original_batch_method(violations, progress_callback)
                
            except Exception as e:
                print(f"Enhanced batch processing failed, falling back to original: {e}")
                return original_batch_method(violations, progress_callback)
        
        return enhanced_batch_method
    
    def handle_violation_count_change(self, new_count: int):
        """
        Handle changes in violation count and adapt performance settings.
        
        Args:
            new_count: New violation count
        """
        if self.integration_enabled and self.integration_system:
            try:
                adaptations_applied = self.integration_system.handle_violation_count_change(
                    new_count, self.current_violations
                )
                
                if adaptations_applied:
                    self.status_message_changed.emit(
                        f"Performance settings adapted for {new_count:,} violations"
                    )
                    
            except Exception as e:
                print(f"Failed to handle violation count change: {e}")
    
    def create_performance_status_widget(self, parent: QWidget = None) -> Optional[QWidget]:
        """
        Create performance status widget for display in main window.
        
        Args:
            parent: Parent widget
            
        Returns:
            Optional[QWidget]: Performance status widget
        """
        try:
            if self.integration_enabled and self.integration_system:
                status_widget = self.integration_system.get_performance_status_widget()
                if status_widget:
                    return status_widget
            
            # Create basic status widget as fallback
            return self._create_basic_status_widget(parent)
            
        except Exception as e:
            print(f"Failed to create performance status widget: {e}")
            return self._create_basic_status_widget(parent)
    
    def get_optimization_suggestions(self) -> List[Dict]:
        """
        Get current optimization suggestions.
        
        Returns:
            List[Dict]: Optimization suggestions
        """
        if self.integration_enabled and self.integration_system:
            try:
                return self.integration_system.get_optimization_suggestions()
            except Exception as e:
                print(f"Failed to get optimization suggestions: {e}")
        
        return []
    
    def apply_optimization_suggestions(self, suggestion_ids: List[str]) -> bool:
        """
        Apply selected optimization suggestions.
        
        Args:
            suggestion_ids: List of suggestion IDs to apply
            
        Returns:
            bool: True if applied successfully
        """
        if self.integration_enabled and self.integration_system:
            try:
                return self.integration_system.apply_optimization_suggestions(suggestion_ids)
            except Exception as e:
                print(f"Failed to apply optimization suggestions: {e}")
        
        return False
    
    def finalize_session(self) -> Dict:
        """
        Finalize the current performance session and get summary.
        
        Returns:
            Dict: Session summary
        """
        session_summary = {}
        
        if self.performance_session_active and self.integration_system:
            try:
                session_summary = self.integration_system.stop_performance_session()
                self.performance_session_active = False
                
                # Display session summary in status
                if session_summary:
                    violations_per_sec = session_summary.get('violations_per_second', 0)
                    if violations_per_sec > 0:
                        self.status_message_changed.emit(
                            f"Session completed: {violations_per_sec:.0f} violations/sec"
                        )
                
            except Exception as e:
                print(f"Failed to finalize performance session: {e}")
        
        return session_summary
    
    def get_system_health_status(self) -> Dict:
        """
        Get system health status for diagnostics.
        
        Returns:
            Dict: System health information
        """
        health_status = {
            'integration_enabled': self.integration_enabled,
            'use_legacy_parsing': self.use_legacy_parsing,
            'use_legacy_table': self.use_legacy_table,
            'performance_session_active': self.performance_session_active,
            'current_violation_count': len(self.current_violations),
            'current_file_path': self.current_file_path
        }
        
        if self.integration_enabled and self.integration_system:
            try:
                system_status = self.integration_system.get_system_status()
                health_status.update(system_status)
            except Exception as e:
                health_status['integration_system_error'] = str(e)
        
        return health_status
    
    def enable_legacy_mode(self):
        """Enable legacy mode for all operations"""
        self.use_legacy_parsing = True
        self.use_legacy_table = True
        self.integration_enabled = False
        self.status_message_changed.emit("Switched to legacy mode")
        print("⚠ Switched to legacy mode")
    
    def try_enable_integration_mode(self):
        """Try to re-enable integration mode"""
        if self.integration_system and self.integration_system.is_integration_healthy():
            self.use_legacy_parsing = False
            self.use_legacy_table = False
            self.integration_enabled = True
            self.status_message_changed.emit("Integration mode enabled")
            print("✓ Integration mode re-enabled")
            return True
        return False
    
    # Private methods
    
    def _handle_performance_status_change(self, status_data: Dict):
        """Handle performance status changes"""
        violations_per_sec = status_data.get('violations_per_second', 0)
        memory_usage = status_data.get('memory_usage_mb', 0)
        
        if violations_per_sec > 0:
            status_msg = f"Processing: {violations_per_sec:.0f} violations/sec, Memory: {memory_usage:.0f}MB"
            self.status_message_changed.emit(status_msg)
    
    def _handle_optimization_applied(self, optimization_type: str, description: str):
        """Handle optimization application"""
        self.status_message_changed.emit(f"Optimization applied: {description}")
        print(f"✓ Optimization applied: {optimization_type} - {description}")
    
    def _handle_integration_error(self, error_type: str, error_message: str):
        """Handle integration errors"""
        self.performance_alert.emit(error_type, error_message)
        
        # Check if we should switch to legacy mode
        if self.integration_system and not self.integration_system.is_integration_healthy():
            self.enable_legacy_mode()
    
    def _handle_ui_mode_change(self, mode: str, config: Dict):
        """Handle UI mode changes"""
        self.status_message_changed.emit(f"UI mode changed to: {mode}")
        print(f"UI mode changed: {mode}")
    
    def _create_basic_status_widget(self, parent: QWidget = None) -> QWidget:
        """Create basic performance status widget as fallback"""
        widget = QWidget(parent)
        layout = QHBoxLayout(widget)
        
        # Status label
        self.status_label = QLabel("Performance: Ready")
        layout.addWidget(self.status_label)
        
        # Progress bar
        self.performance_progress_bar = QProgressBar()
        self.performance_progress_bar.setVisible(False)
        layout.addWidget(self.performance_progress_bar)
        
        # Connect progress signal
        self.progress_updated.connect(self._update_basic_progress)
        
        return widget
    
    def _update_basic_progress(self, progress: int, message: str):
        """Update basic progress display"""
        if hasattr(self, 'performance_progress_bar') and self.performance_progress_bar:
            self.performance_progress_bar.setVisible(progress < 100)
            self.performance_progress_bar.setValue(progress)
        
        if hasattr(self, 'status_label') and self.status_label:
            if progress < 100:
                self.status_label.setText(f"Performance: {message} ({progress}%)")
            else:
                self.status_label.setText("Performance: Ready")


class BackwardCompatibilityManager:
    """
    Manages backward compatibility for existing TimingViolationWindow methods.
    """
    
    def __init__(self, main_window, integration_adapter: MainWindowIntegrationAdapter):
        self.main_window = main_window
        self.adapter = integration_adapter
        
        # Store original methods
        self.original_methods = {}
        
    def apply_integration_patches(self):
        """Apply integration patches to existing methods"""
        try:
            # Patch file parsing if method exists
            if hasattr(self.main_window, 'parse_violation_file'):
                self.original_methods['parse_violation_file'] = self.main_window.parse_violation_file
                self.main_window.parse_violation_file = self.adapter.integrate_file_parsing(
                    self.original_methods['parse_violation_file']
                )
                print("✓ Patched parse_violation_file method")
            
            # Patch table creation if method exists
            if hasattr(self.main_window, 'create_violation_table'):
                self.original_methods['create_violation_table'] = self.main_window.create_violation_table
                self.main_window.create_violation_table = self.adapter.integrate_table_creation(
                    self.original_methods['create_violation_table']
                )
                print("✓ Patched create_violation_table method")
            
            # Patch batch operations if method exists
            if hasattr(self.main_window, 'batch_confirm_violations'):
                self.original_methods['batch_confirm_violations'] = self.main_window.batch_confirm_violations
                self.main_window.batch_confirm_violations = self.adapter.integrate_batch_operations(
                    self.original_methods['batch_confirm_violations']
                )
                print("✓ Patched batch_confirm_violations method")
            
            # Add performance status widget if main window has a status area
            if hasattr(self.main_window, 'status_layout') or hasattr(self.main_window, 'statusBar'):
                performance_widget = self.adapter.create_performance_status_widget()
                if performance_widget:
                    self._add_performance_widget(performance_widget)
                    print("✓ Added performance status widget")
            
            print("✓ Backward compatibility patches applied successfully")
            
        except Exception as e:
            print(f"✗ Failed to apply integration patches: {e}")
    
    def remove_integration_patches(self):
        """Remove integration patches and restore original methods"""
        try:
            for method_name, original_method in self.original_methods.items():
                setattr(self.main_window, method_name, original_method)
            
            self.original_methods.clear()
            print("✓ Integration patches removed, original methods restored")
            
        except Exception as e:
            print(f"✗ Failed to remove integration patches: {e}")
    
    def _add_performance_widget(self, performance_widget: QWidget):
        """Add performance widget to main window"""
        try:
            # Try to add to status layout first
            if hasattr(self.main_window, 'status_layout'):
                self.main_window.status_layout.addWidget(performance_widget)
            # Try to add to status bar
            elif hasattr(self.main_window, 'statusBar'):
                status_bar = self.main_window.statusBar()
                status_bar.addPermanentWidget(performance_widget)
            # Try to add to main layout
            elif hasattr(self.main_window, 'layout'):
                layout = self.main_window.layout()
                if layout:
                    layout.addWidget(performance_widget)
            
        except Exception as e:
            print(f"Failed to add performance widget: {e}")