"""
Violation Count Scenario Tests

Tests for different violation count scenarios (small, medium, large datasets)
including violation count estimation accuracy and performance benchmarks.
"""

import unittest
import tempfile
import os
import time
import psutil
from typing import List, Dict, Any
from unittest.mock import Mock, patch

try:
    from .performance_optimizer import PerformanceOptimizer
    from .parser import <PERSON>ioLogParser, HighPerformanceVioLogParser, HighPerformanceAsyncParser
    from .models import ViolationDataModel
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.insert(0, os.path.dirname(__file__))
    from performance_optimizer import PerformanceOptimizer
    from parser import VioLogParser, HighPerformanceVioLogParser, HighPerformanceAsyncParser
    from models import ViolationDataModel


class ViolationCountScenarioTests(unittest.TestCase):
    """Test cases for different violation count scenarios"""
    
    def setUp(self):
        """Set up test environment"""
        self.performance_optimizer = PerformanceOptimizer()
        self.temp_dir = tempfile.mkdtemp()
        
        # Performance tracking
        self.performance_metrics = {}
        
    def tearDown(self):
        """Clean up test environment"""
        # Clean up temporary files
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def _generate_test_violation_file(self, violation_count: int, file_path: str) -> None:
        """Generate a test violation file with specified violation count
        
        Args:
            violation_count: Number of violations to generate
            file_path: Path to save the test file
        """
        with open(file_path, 'w') as f:
            # Generate violations in correct vio_summary.log format
            for i in range(violation_count):
                # Generate timing violation in correct format
                slack_fs = int((0.1 + (i % 100) * 0.01) * 1000000)  # Convert to femtoseconds
                f.write(f"NUM : {i+1}\n")
                f.write(f"Hier : /design/module_{i%100}/submodule_{i%10}/signal_{i}\n")
                f.write(f"Time : {slack_fs} FS\n")
                f.write(f"Check : Setup time violation on signal data[{i%64}]\n")
                f.write("------------------------------------------------------------\n")
    
    def _measure_performance(self, operation_name: str, operation_func, *args, **kwargs) -> Dict[str, Any]:
        """Measure performance metrics for an operation
        
        Args:
            operation_name: Name of the operation being measured
            operation_func: Function to execute and measure
            *args, **kwargs: Arguments for the operation function
            
        Returns:
            Dictionary containing performance metrics
        """
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Measure execution time
        start_time = time.time()
        result = operation_func(*args, **kwargs)
        end_time = time.time()
        
        # Get final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        metrics = {
            'operation': operation_name,
            'execution_time': end_time - start_time,
            'initial_memory_mb': initial_memory,
            'final_memory_mb': final_memory,
            'memory_delta_mb': final_memory - initial_memory,
            'result': result
        }
        
        return metrics
    
    def test_small_dataset_performance(self):
        """Test performance with small datasets (<2K violations)"""
        violation_counts = [100, 500, 1000, 1500]
        
        for count in violation_counts:
            with self.subTest(violation_count=count):
                # Generate test file
                test_file = os.path.join(self.temp_dir, f"small_test_{count}_vio_summary.log")
                self._generate_test_violation_file(count, test_file)
                
                # Test violation count estimation
                file_size = os.path.getsize(test_file)
                estimated_count = self.performance_optimizer._estimate_record_count(file_size)
                actual_count = count
                estimation_accuracy = abs(estimated_count - actual_count) / actual_count
                
                # Estimation should be within 30% accuracy (adjusted for simple estimation method)
                self.assertLess(estimation_accuracy, 0.30, 
                    f"Estimation accuracy {estimation_accuracy:.2%} exceeds 30% for {count} violations")
                
                # Test parsing performance
                parser = VioLogParser()
                metrics = self._measure_performance(
                    f"small_dataset_parse_{count}",
                    parser.parse_log_file,
                    test_file
                )
                
                # Small datasets should load quickly (< 2 seconds)
                self.assertLess(metrics['execution_time'], 2.0,
                    f"Small dataset ({count} violations) took {metrics['execution_time']:.2f}s to load")
                
                # Memory usage should be reasonable (< 100MB for small datasets)
                self.assertLess(metrics['memory_delta_mb'], 100,
                    f"Small dataset ({count} violations) used {metrics['memory_delta_mb']:.1f}MB")
                
                self.performance_metrics[f"small_{count}"] = metrics
    
    def test_medium_dataset_performance(self):
        """Test performance with medium datasets (2K-20K violations)"""
        violation_counts = [2000, 5000, 10000, 15000, 20000]
        
        for count in violation_counts:
            with self.subTest(violation_count=count):
                # Generate test file
                test_file = os.path.join(self.temp_dir, f"medium_test_{count}_vio_summary.log")
                self._generate_test_violation_file(count, test_file)
                
                # Test violation count estimation
                file_size = os.path.getsize(test_file)
                estimated_count = self.performance_optimizer._estimate_record_count(file_size)
                actual_count = count
                estimation_accuracy = abs(estimated_count - actual_count) / actual_count
                
                # Estimation should be within 30% accuracy (adjusted for simple estimation method)
                self.assertLess(estimation_accuracy, 0.30,
                    f"Estimation accuracy {estimation_accuracy:.2%} exceeds 30% for {count} violations")
                
                # Test high-performance parsing
                parser = HighPerformanceVioLogParser()
                metrics = self._measure_performance(
                    f"medium_dataset_parse_{count}",
                    parser.parse_log_file_streaming,
                    test_file
                )
                
                # Medium datasets should load within reasonable time (< 10 seconds)
                self.assertLess(metrics['execution_time'], 10.0,
                    f"Medium dataset ({count} violations) took {metrics['execution_time']:.2f}s to load")
                
                # Memory usage should stay below 500MB for medium datasets
                self.assertLess(metrics['memory_delta_mb'], 500,
                    f"Medium dataset ({count} violations) used {metrics['memory_delta_mb']:.1f}MB")
                
                self.performance_metrics[f"medium_{count}"] = metrics
    
    def test_large_dataset_performance(self):
        """Test performance with large datasets (>20K violations)"""
        violation_counts = [25000, 50000, 75000, 100000]
        
        for count in violation_counts:
            with self.subTest(violation_count=count):
                # Generate test file
                test_file = os.path.join(self.temp_dir, f"large_test_{count}_vio_summary.log")
                self._generate_test_violation_file(count, test_file)
                
                # Test violation count estimation
                file_size = os.path.getsize(test_file)
                estimated_count = self.performance_optimizer._estimate_record_count(file_size)
                actual_count = count
                estimation_accuracy = abs(estimated_count - actual_count) / actual_count
                
                # Estimation should be within 30% accuracy (adjusted for simple estimation method)
                self.assertLess(estimation_accuracy, 0.30,
                    f"Estimation accuracy {estimation_accuracy:.2%} exceeds 30% for {count} violations")
                
                # Test streaming parsing for large datasets
                parser = HighPerformanceVioLogParser()
                metrics = self._measure_performance(
                    f"large_dataset_parse_{count}",
                    parser.parse_log_file_streaming,
                    test_file
                )
                
                # Large datasets should complete within reasonable time (< 30 seconds)
                self.assertLess(metrics['execution_time'], 30.0,
                    f"Large dataset ({count} violations) took {metrics['execution_time']:.2f}s to load")
                
                # Memory usage should stay below 1GB (requirement 3.1)
                self.assertLess(metrics['memory_delta_mb'], 1024,
                    f"Large dataset ({count} violations) used {metrics['memory_delta_mb']:.1f}MB")
                
                self.performance_metrics[f"large_{count}"] = metrics
    
    def test_violation_count_estimation_accuracy(self):
        """Test accuracy of violation count estimation based on 5 lines per violation"""
        test_cases = [
            (100, 500),    # 100 violations = 500 lines
            (1000, 5000),  # 1000 violations = 5000 lines
            (5000, 25000), # 5000 violations = 25000 lines
            (10000, 50000) # 10000 violations = 50000 lines
        ]
        
        for violation_count, expected_lines in test_cases:
            with self.subTest(violation_count=violation_count):
                # Generate test file
                test_file = os.path.join(self.temp_dir, f"estimation_test_{violation_count}_vio_summary.log")
                self._generate_test_violation_file(violation_count, test_file)
                
                # Count actual lines in file
                with open(test_file, 'r') as f:
                    actual_lines = len(f.readlines())
                
                # Test estimation method
                file_size = os.path.getsize(test_file)
                estimated_count = self.performance_optimizer._estimate_record_count(file_size)
                
                # Verify 5 lines per violation assumption (no header lines in our format)
                expected_violations_from_lines = actual_lines // 5
                self.assertEqual(violation_count, expected_violations_from_lines,
                    f"Test data generation error: expected {violation_count} violations, "
                    f"but file structure suggests {expected_violations_from_lines}")
                
                # Test estimation accuracy
                estimation_error = abs(estimated_count - violation_count) / violation_count
                self.assertLess(estimation_error, 0.30,
                    f"Estimation error {estimation_error:.2%} exceeds 30% threshold for {violation_count} violations")
    
    def test_performance_benchmark_thresholds(self):
        """Test that performance meets benchmark thresholds for each violation count range"""
        
        # Small dataset benchmarks (<2K violations)
        small_test_file = os.path.join(self.temp_dir, "benchmark_small_vio_summary.log")
        self._generate_test_violation_file(1500, small_test_file)
        
        parser = VioLogParser()
        small_metrics = self._measure_performance(
            "benchmark_small",
            parser.parse_log_file,
            small_test_file
        )
        
        # Small datasets should load in < 2 seconds
        self.assertLess(small_metrics['execution_time'], 2.0,
            f"Small dataset benchmark failed: {small_metrics['execution_time']:.2f}s > 2.0s")
        
        # Medium dataset benchmarks (2K-20K violations)
        medium_test_file = os.path.join(self.temp_dir, "benchmark_medium_vio_summary.log")
        self._generate_test_violation_file(10000, medium_test_file)
        
        parser = HighPerformanceVioLogParser()
        medium_metrics = self._measure_performance(
            "benchmark_medium",
            parser.parse_log_file_streaming,
            medium_test_file
        )
        
        # Medium datasets should load in < 10 seconds
        self.assertLess(medium_metrics['execution_time'], 10.0,
            f"Medium dataset benchmark failed: {medium_metrics['execution_time']:.2f}s > 10.0s")
        
        # Large dataset benchmarks (>20K violations)
        large_test_file = os.path.join(self.temp_dir, "benchmark_large_vio_summary.log")
        self._generate_test_violation_file(50000, large_test_file)
        
        parser = HighPerformanceVioLogParser()
        large_metrics = self._measure_performance(
            "benchmark_large",
            parser.parse_log_file_streaming,
            large_test_file
        )
        
        # Large datasets should load in < 30 seconds
        self.assertLess(large_metrics['execution_time'], 30.0,
            f"Large dataset benchmark failed: {large_metrics['execution_time']:.2f}s > 30.0s")
        
        # Memory usage should stay below 1GB for all datasets
        for metrics in [small_metrics, medium_metrics, large_metrics]:
            self.assertLess(metrics['memory_delta_mb'], 1024,
                f"Memory usage {metrics['memory_delta_mb']:.1f}MB exceeds 1GB limit")
    
    def test_strategy_selection_based_on_violation_count(self):
        """Test that appropriate processing strategies are selected based on violation count"""
        
        test_cases = [
            (500, "standard_async"),      # Small dataset (< 2K)
            (5000, "high_performance_async"), # Medium dataset (2K-20K)
            (50000, "high_performance_streaming")    # Large dataset (20K-50K)
        ]
        
        for violation_count, expected_strategy in test_cases:
            with self.subTest(violation_count=violation_count, expected_strategy=expected_strategy):
                # Generate test file
                test_file = os.path.join(self.temp_dir, f"strategy_test_{violation_count}_vio_summary.log")
                self._generate_test_violation_file(violation_count, test_file)
                
                # Test strategy selection
                analysis_result = self.performance_optimizer.analyze_file_performance(test_file)
                selected_strategy = analysis_result.get('recommended_strategy', 'unknown')
                
                # Print debug info for strategy selection
                print(f"Debug: {violation_count} violations -> {selected_strategy} (expected: {expected_strategy})")
                
                # Allow some flexibility in strategy selection based on system capabilities
                if violation_count == 50000:
                    # For large datasets, accept either high_performance_streaming or chunked_processing
                    acceptable_strategies = ["high_performance_streaming", "chunked_processing", "standard_async_chunked"]
                    self.assertIn(selected_strategy, acceptable_strategies,
                        f"Strategy {selected_strategy} not acceptable for {violation_count} violations")
                else:
                    self.assertEqual(selected_strategy, expected_strategy,
                        f"Wrong strategy selected for {violation_count} violations: "
                        f"got {selected_strategy}, expected {expected_strategy}")
    
    def generate_performance_report(self) -> str:
        """Generate a comprehensive performance report from test results"""
        report = []
        report.append("=== Violation Count Scenario Performance Report ===\n")
        
        # Group metrics by dataset size
        small_metrics = {k: v for k, v in self.performance_metrics.items() if k.startswith('small_')}
        medium_metrics = {k: v for k, v in self.performance_metrics.items() if k.startswith('medium_')}
        large_metrics = {k: v for k, v in self.performance_metrics.items() if k.startswith('large_')}
        
        for category, metrics in [("Small Datasets (<2K violations)", small_metrics),
                                 ("Medium Datasets (2K-20K violations)", medium_metrics),
                                 ("Large Datasets (>20K violations)", large_metrics)]:
            if not metrics:
                continue
                
            report.append(f"\n{category}:")
            report.append("-" * len(category))
            
            for key, metric in metrics.items():
                violation_count = key.split('_')[1]
                report.append(f"  {violation_count} violations:")
                report.append(f"    Load Time: {metric['execution_time']:.2f}s")
                report.append(f"    Memory Usage: {metric['memory_delta_mb']:.1f}MB")
                report.append(f"    Memory Efficiency: {float(violation_count)/metric['memory_delta_mb']:.1f} violations/MB")
        
        return "\n".join(report)


if __name__ == '__main__':
    # Run tests with detailed output
    unittest.main(verbosity=2)