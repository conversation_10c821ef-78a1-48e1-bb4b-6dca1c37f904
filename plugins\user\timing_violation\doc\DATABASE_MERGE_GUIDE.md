# 数据库合并功能使用指南

## 概述

时序违例插件的数据库合并功能允许多个用户共享时序违例确认信息，通过合并不同用户的本地数据库，实现团队协作和知识共享。

## 功能特性

### 🔒 安全备份
- 合并前自动备份当前数据库
- 备份文件命名格式：`timing_violations_backup_YYYYMMDD_HHMMSS.db`
- 确保数据安全，支持回滚操作

### 🔍 智能验证
- 自动验证数据库schema兼容性
- 检查必要表结构和字段
- 防止不兼容数据库的合并

### 🚀 高效合并
- 基于唯一约束的智能去重
- 保留最完整的确认信息
- 合并历史模式和使用统计

### 📊 详细统计
- 实时显示合并进度
- 提供详细的合并结果统计
- 支持合并前后数据对比

## 使用方法

### 1. 打开数据库合并功能
1. 在时序违例插件主界面中
2. 点击工具栏中的"数据库合并"按钮
3. 打开数据库合并对话框

### 2. 查看当前数据库信息
对话框会显示当前数据库的统计信息：
- 数据库路径
- 总违例数
- 已确认/待确认违例数
- 历史模式数
- 涉及用例数

### 3. 选择要合并的数据库
1. 点击"添加数据库文件..."按钮
2. 选择其他用户的时序违例数据库文件（*.db）
3. 系统会自动验证数据库兼容性
4. 可以选择多个数据库文件进行批量合并

### 4. 执行合并操作
1. 确认选择的数据库文件列表
2. 点击"开始合并"按钮
3. 确认合并操作（会显示备份提示）
4. 等待合并完成

### 5. 查看合并结果
合并完成后会显示：
- 处理的数据库数量
- 新增的违例记录数
- 更新的确认记录数
- 合并的历史模式数
- 备份文件位置

## 合并策略

### 违例记录合并
- **唯一性判断**：基于 `(case_name, corner, num, hier, check_info)` 组合
- **重复处理**：保留本地记录，避免重复插入
- **新记录**：自动创建对应的确认记录

### 确认记录合并
- **优先级**：已确认记录 > 待确认记录
- **完整性**：保留更完整的确认信息（确认人、结果、理由）
- **时间戳**：更新确认时间和修改时间

### 历史模式合并
- **模式匹配**：基于 `(hier_pattern, check_pattern)` 组合
- **使用统计**：累加使用次数
- **时间更新**：保留最新的使用时间

## 注意事项

### ⚠️ 重要提醒
1. **备份重要性**：合并前会自动备份，请妥善保管备份文件
2. **数据兼容性**：只能合并相同版本的时序违例数据库
3. **权限要求**：确保对数据库文件有读写权限
4. **磁盘空间**：确保有足够的磁盘空间进行备份和合并

### 🔧 故障排除

#### 数据库文件无效
- **原因**：选择的文件不是有效的时序违例数据库
- **解决**：确认文件是由时序违例插件生成的数据库文件

#### Schema不兼容
- **原因**：数据库版本不匹配或表结构不同
- **解决**：确保所有数据库都是相同版本的插件生成

#### 合并失败
- **原因**：文件权限、磁盘空间或数据冲突
- **解决**：检查错误信息，确保文件可访问且有足够空间

#### 备份失败
- **原因**：目标目录权限不足或磁盘空间不够
- **解决**：检查VIOLATION_CHECK目录权限和磁盘空间

## 最佳实践

### 📋 合并前准备
1. **确认数据源**：确保要合并的数据库来源可靠
2. **检查空间**：确保有足够的磁盘空间
3. **关闭其他程序**：避免数据库文件被占用

### 🔄 定期合并
1. **团队协作**：定期与团队成员交换数据库
2. **增量合并**：避免重复合并相同的数据库
3. **版本管理**：保持数据库版本的一致性

### 📈 合并后验证
1. **数据检查**：验证合并后的数据完整性
2. **功能测试**：确保插件功能正常工作
3. **备份管理**：定期清理旧的备份文件

## 技术细节

### 数据库表结构
- `timing_violations`：时序违例记录表
- `confirmation_records`：确认记录表
- `violation_patterns`：历史匹配模式表

### 合并算法
1. **备份阶段**：创建当前数据库的时间戳备份
2. **验证阶段**：检查源数据库的schema兼容性
3. **合并阶段**：逐个处理源数据库，执行智能合并
4. **统计阶段**：生成详细的合并结果报告

### 性能优化
- 使用事务确保数据一致性
- 批量操作提高合并效率
- 索引优化加速查询性能
- 内存管理避免大数据集问题

## 版本历史

### v1.0.0 (2024-07-30)
- 初始版本发布
- 支持基本的数据库合并功能
- 包含备份、验证、合并、统计等完整流程
- 提供用户友好的图形界面

## 支持与反馈

如有问题或建议，请联系RunSim GUI开发团队。

---

*本文档随时序违例插件更新而更新，请关注最新版本。*
