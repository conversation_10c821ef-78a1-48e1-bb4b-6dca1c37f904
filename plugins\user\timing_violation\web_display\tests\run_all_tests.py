"""
Test runner for all timing violation web display tests.

This script runs all unit tests, integration tests, and frontend tests
with comprehensive reporting.
"""

import unittest
import sys
import os
import time
from pathlib import Path
from io import StringIO

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

# Import all test modules
from tests.test_excel_parser import TestExcelParser
from tests.test_database_reader import TestDatabaseReader
from tests.test_data_exporter import TestDataExporter
from tests.test_file_utils import TestFileUtils
from tests.test_date_utils import TestDateUtils
from tests.test_validation_utils import TestValidationUtils
from tests.test_integration import TestIntegration
from tests.test_frontend import TestFrontend


class TestResult:
    """Custom test result class for detailed reporting."""
    
    def __init__(self):
        self.tests_run = 0
        self.failures = []
        self.errors = []
        self.skipped = []
        self.success_count = 0
        self.start_time = None
        self.end_time = None
    
    def start_test(self):
        """Mark test start time."""
        self.start_time = time.time()
    
    def stop_test(self):
        """Mark test end time."""
        self.end_time = time.time()
    
    def get_duration(self):
        """Get test duration in seconds."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0
    
    def add_success(self):
        """Add successful test."""
        self.success_count += 1
    
    def add_failure(self, test, error):
        """Add failed test."""
        self.failures.append((test, error))
    
    def add_error(self, test, error):
        """Add error test."""
        self.errors.append((test, error))
    
    def add_skip(self, test, reason):
        """Add skipped test."""
        self.skipped.append((test, reason))
    
    def was_successful(self):
        """Check if all tests were successful."""
        return len(self.failures) == 0 and len(self.errors) == 0


def run_test_suite(test_class, suite_name):
    """
    Run a specific test suite and return results.
    
    Args:
        test_class: Test class to run
        suite_name: Name of the test suite
        
    Returns:
        TestResult object with results
    """
    print(f"\n{'='*60}")
    print(f"Running {suite_name}")
    print(f"{'='*60}")
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(test_class)
    
    # Create custom result object
    result = TestResult()
    result.start_test()
    
    # Capture output
    stream = StringIO()
    runner = unittest.TextTestRunner(stream=stream, verbosity=2)
    test_result = runner.run(suite)
    
    result.stop_test()
    
    # Process results
    result.tests_run = test_result.testsRun
    result.failures = test_result.failures
    result.errors = test_result.errors
    result.skipped = test_result.skipped
    result.success_count = result.tests_run - len(result.failures) - len(result.errors) - len(result.skipped)
    
    # Print summary
    print(f"\nTests run: {result.tests_run}")
    print(f"Successes: {result.success_count}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped)}")
    print(f"Duration: {result.get_duration():.2f} seconds")
    
    # Print detailed results if there are failures or errors
    if result.failures:
        print(f"\nFAILURES:")
        for test, error in result.failures:
            print(f"  - {test}: {error}")
    
    if result.errors:
        print(f"\nERRORS:")
        for test, error in result.errors:
            print(f"  - {test}: {error}")
    
    if result.skipped:
        print(f"\nSKIPPED:")
        for test, reason in result.skipped:
            print(f"  - {test}: {reason}")
    
    return result


def run_all_tests():
    """
    Run all test suites and provide comprehensive reporting.
    
    Returns:
        bool: True if all tests passed, False otherwise
    """
    print("Timing Violation Web Display - Comprehensive Test Suite")
    print("=" * 60)
    
    # Define test suites
    test_suites = [
        (TestExcelParser, "Excel Parser Tests"),
        (TestDatabaseReader, "Database Reader Tests"),
        (TestDataExporter, "Data Exporter Tests"),
        (TestFileUtils, "File Utils Tests"),
        (TestDateUtils, "Date Utils Tests"),
        (TestValidationUtils, "Validation Utils Tests"),
        (TestIntegration, "Integration Tests"),
        (TestFrontend, "Frontend Tests"),
    ]
    
    # Track overall results
    overall_start_time = time.time()
    total_tests = 0
    total_successes = 0
    total_failures = 0
    total_errors = 0
    total_skipped = 0
    failed_suites = []
    
    # Run each test suite
    for test_class, suite_name in test_suites:
        try:
            result = run_test_suite(test_class, suite_name)
            
            total_tests += result.tests_run
            total_successes += result.success_count
            total_failures += len(result.failures)
            total_errors += len(result.errors)
            total_skipped += len(result.skipped)
            
            if not result.was_successful():
                failed_suites.append(suite_name)
                
        except Exception as e:
            print(f"ERROR: Failed to run {suite_name}: {e}")
            failed_suites.append(suite_name)
            total_errors += 1
    
    # Calculate overall duration
    overall_duration = time.time() - overall_start_time
    
    # Print overall summary
    print(f"\n{'='*60}")
    print("OVERALL TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Total test suites: {len(test_suites)}")
    print(f"Total tests run: {total_tests}")
    print(f"Total successes: {total_successes}")
    print(f"Total failures: {total_failures}")
    print(f"Total errors: {total_errors}")
    print(f"Total skipped: {total_skipped}")
    print(f"Total duration: {overall_duration:.2f} seconds")
    
    # Calculate success rate
    if total_tests > 0:
        success_rate = (total_successes / total_tests) * 100
        print(f"Success rate: {success_rate:.1f}%")
    
    # Print failed suites
    if failed_suites:
        print(f"\nFAILED SUITES:")
        for suite in failed_suites:
            print(f"  - {suite}")
    
    # Determine overall result
    all_passed = total_failures == 0 and total_errors == 0
    
    if all_passed:
        print(f"\n🎉 ALL TESTS PASSED! 🎉")
    else:
        print(f"\n❌ SOME TESTS FAILED")
    
    return all_passed


def run_specific_test_suite(suite_name):
    """
    Run a specific test suite by name.
    
    Args:
        suite_name: Name of the test suite to run
        
    Returns:
        bool: True if tests passed, False otherwise
    """
    test_suites = {
        'excel': (TestExcelParser, "Excel Parser Tests"),
        'database': (TestDatabaseReader, "Database Reader Tests"),
        'exporter': (TestDataExporter, "Data Exporter Tests"),
        'file': (TestFileUtils, "File Utils Tests"),
        'date': (TestDateUtils, "Date Utils Tests"),
        'validation': (TestValidationUtils, "Validation Utils Tests"),
        'integration': (TestIntegration, "Integration Tests"),
        'frontend': (TestFrontend, "Frontend Tests"),
    }
    
    if suite_name.lower() not in test_suites:
        print(f"Unknown test suite: {suite_name}")
        print(f"Available suites: {', '.join(test_suites.keys())}")
        return False
    
    test_class, full_name = test_suites[suite_name.lower()]
    result = run_test_suite(test_class, full_name)
    
    return result.was_successful()


def main():
    """Main entry point for test runner."""
    if len(sys.argv) > 1:
        # Run specific test suite
        suite_name = sys.argv[1]
        success = run_specific_test_suite(suite_name)
    else:
        # Run all tests
        success = run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()