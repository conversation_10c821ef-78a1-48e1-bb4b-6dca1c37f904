#!/usr/bin/env python3
"""
Production optimization script for Timing Violation Web Display.

This script performs final optimizations and validations before production deployment.
"""

import os
import sys
import json
import gzip
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Any
import subprocess
import time

# Add the web_display directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from data_exporter import DataExporter
from utils.file_utils import FileUtils
from utils.validation_utils import ValidationUtils


class ProductionOptimizer:
    """Handles production optimization and validation."""
    
    def __init__(self, violation_check_dir: str = "VIOLATION_CHECK"):
        """Initialize the production optimizer."""
        self.violation_check_dir = Path(violation_check_dir)
        self.web_display_dir = self.violation_check_dir / "web_display"
        self.data_dir = self.web_display_dir / "data"
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('production_optimization.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.optimization_results = {
            'start_time': time.time(),
            'tests_passed': 0,
            'tests_failed': 0,
            'optimizations_applied': [],
            'warnings': [],
            'errors': []
        }
    
    def run_optimization(self) -> bool:
        """Run complete production optimization process."""
        self.logger.info("Starting production optimization process...")
        
        try:
            # Step 1: Validate environment
            if not self._validate_environment():
                return False
            
            # Step 2: Run comprehensive tests
            if not self._run_comprehensive_tests():
                return False
            
            # Step 3: Optimize data files
            self._optimize_data_files()
            
            # Step 4: Optimize web assets
            self._optimize_web_assets()
            
            # Step 5: Generate production configuration
            self._generate_production_config()
            
            # Step 6: Create deployment package
            self._create_deployment_package()
            
            # Step 7: Final validation
            if not self._final_validation():
                return False
            
            self._generate_optimization_report()
            self.logger.info("Production optimization completed successfully!")
            return True
            
        except Exception as e:
            self.logger.error(f"Production optimization failed: {e}")
            self.optimization_results['errors'].append(str(e))
            return False
    
    def _validate_environment(self) -> bool:
        """Validate the environment for production deployment."""
        self.logger.info("Validating environment...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            self.optimization_results['errors'].append("Python 3.8+ required")
            return False
        
        # Check required directories
        if not self.violation_check_dir.exists():
            self.optimization_results['errors'].append(f"VIOLATION_CHECK directory not found: {self.violation_check_dir}")
            return False
        
        # Check dependencies
        try:
            import openpyxl
            self.logger.info(f"openpyxl version: {openpyxl.__version__}")
        except ImportError:
            self.optimization_results['errors'].append("openpyxl library not installed")
            return False
        
        # Check disk space (require at least 1GB)
        try:
            statvfs = os.statvfs(self.violation_check_dir)
            free_space = statvfs.f_frsize * statvfs.f_bavail
            if free_space < 1024 * 1024 * 1024:  # 1GB
                self.optimization_results['warnings'].append(f"Low disk space: {free_space / (1024**3):.1f}GB available")
        except (OSError, AttributeError):
            # Windows doesn't have statvfs, skip this check
            pass
        
        self.logger.info("Environment validation completed")
        return True
    
    def _run_comprehensive_tests(self) -> bool:
        """Run comprehensive test suite."""
        self.logger.info("Running comprehensive tests...")
        
        # Run the test suite
        try:
            test_script = Path(__file__).parent / "tests" / "run_all_tests.py"
            if test_script.exists():
                result = subprocess.run([sys.executable, str(test_script)], 
                                      capture_output=True, text=True, cwd=Path(__file__).parent)
                
                # Parse test results
                if "Success rate:" in result.stdout:
                    success_rate_line = [line for line in result.stdout.split('\n') if 'Success rate:' in line][0]
                    success_rate = float(success_rate_line.split(':')[1].strip().rstrip('%'))
                    
                    if success_rate >= 90.0:
                        self.logger.info(f"Tests passed with {success_rate}% success rate")
                        self.optimization_results['tests_passed'] = 1
                        return True
                    else:
                        self.logger.error(f"Test success rate too low: {success_rate}%")
                        self.optimization_results['tests_failed'] = 1
                        return False
                else:
                    self.logger.warning("Could not parse test results")
                    self.optimization_results['warnings'].append("Could not parse test results")
            else:
                self.logger.warning("Test script not found, skipping comprehensive tests")
                self.optimization_results['warnings'].append("Test script not found")
        
        except Exception as e:
            self.logger.error(f"Test execution failed: {e}")
            self.optimization_results['errors'].append(f"Test execution failed: {e}")
            return False
        
        return True
    
    def _optimize_data_files(self) -> None:
        """Optimize data files for production."""
        self.logger.info("Optimizing data files...")
        
        if not self.data_dir.exists():
            self.logger.warning("Data directory not found, generating data first...")
            exporter = DataExporter(str(self.violation_check_dir))
            exporter.export_all_data()
        
        # Compress large JSON files
        for json_file in self.data_dir.rglob("*.json"):
            if json_file.stat().st_size > 100 * 1024:  # Files larger than 100KB
                self._compress_json_file(json_file)
        
        # Optimize file structure for large datasets
        violations_dir = self.data_dir / "violations"
        if violations_dir.exists():
            violation_files = list(violations_dir.glob("*.json"))
            if len(violation_files) > 50:  # Many files, optimize structure
                self._optimize_violation_file_structure(violations_dir)
        
        self.optimization_results['optimizations_applied'].append("Data file optimization")
    
    def _compress_json_file(self, json_file: Path) -> None:
        """Compress a JSON file using gzip."""
        try:
            with open(json_file, 'rb') as f_in:
                with gzip.open(f"{json_file}.gz", 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # Keep original for compatibility, but log compression ratio
            original_size = json_file.stat().st_size
            compressed_size = Path(f"{json_file}.gz").stat().st_size
            ratio = (1 - compressed_size / original_size) * 100
            
            self.logger.info(f"Compressed {json_file.name}: {ratio:.1f}% reduction")
            
        except Exception as e:
            self.logger.warning(f"Failed to compress {json_file}: {e}")
    
    def _optimize_violation_file_structure(self, violations_dir: Path) -> None:
        """Optimize violation file structure for better performance."""
        self.logger.info("Optimizing violation file structure...")
        
        # Create index file for faster navigation
        index_data = {}
        
        for json_file in violations_dir.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list) and data:
                    # Extract metadata
                    corner = data[0].get('corner', 'unknown')
                    case = data[0].get('case', 'unknown')
                    
                    if corner not in index_data:
                        index_data[corner] = {}
                    
                    index_data[corner][case] = {
                        'file': json_file.name,
                        'count': len(data),
                        'size': json_file.stat().st_size
                    }
                    
            except Exception as e:
                self.logger.warning(f"Failed to process {json_file}: {e}")
        
        # Write index file
        index_file = violations_dir / "file_index.json"
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, indent=2)
        
        self.logger.info(f"Created violation file index with {len(index_data)} corners")
    
    def _optimize_web_assets(self) -> None:
        """Optimize web assets for production."""
        self.logger.info("Optimizing web assets...")
        
        # Add cache headers to HTML
        html_file = self.web_display_dir / "index.html"
        if html_file.exists():
            self._add_cache_headers_to_html(html_file)
        
        # Create minified versions of custom CSS/JS if they exist
        css_dir = self.web_display_dir / "css"
        js_dir = self.web_display_dir / "js"
        
        if (css_dir / "custom.css").exists():
            self._optimize_css_file(css_dir / "custom.css")
        
        if (js_dir / "app.js").exists():
            self._optimize_js_file(js_dir / "app.js")
        
        self.optimization_results['optimizations_applied'].append("Web asset optimization")
    
    def _add_cache_headers_to_html(self, html_file: Path) -> None:
        """Add cache optimization meta tags to HTML."""
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add cache control meta tags
            cache_meta = '''    <!-- Cache optimization -->
    <meta http-equiv="Cache-Control" content="public, max-age=3600">
    <meta http-equiv="Expires" content="3600">
    
'''
            
            # Insert after charset meta tag
            if '<meta charset="UTF-8">' in content:
                content = content.replace('<meta charset="UTF-8">', 
                                        '<meta charset="UTF-8">\n' + cache_meta)
                
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.logger.info("Added cache headers to HTML")
            
        except Exception as e:
            self.logger.warning(f"Failed to optimize HTML: {e}")
    
    def _optimize_css_file(self, css_file: Path) -> None:
        """Basic CSS optimization."""
        try:
            with open(css_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Basic minification: remove comments and extra whitespace
            lines = []
            for line in content.split('\n'):
                line = line.strip()
                if line and not line.startswith('/*'):
                    lines.append(line)
            
            minified_content = ' '.join(lines)
            
            # Write minified version
            minified_file = css_file.parent / f"{css_file.stem}.min.css"
            with open(minified_file, 'w', encoding='utf-8') as f:
                f.write(minified_content)
            
            self.logger.info(f"Created minified CSS: {minified_file.name}")
            
        except Exception as e:
            self.logger.warning(f"Failed to optimize CSS: {e}")
    
    def _optimize_js_file(self, js_file: Path) -> None:
        """Basic JavaScript optimization."""
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Basic optimization: remove console.log statements in production
            lines = []
            for line in content.split('\n'):
                if 'console.log(' not in line or 'console.error(' in line or 'console.warn(' in line:
                    lines.append(line)
                else:
                    # Comment out console.log statements
                    lines.append('    // ' + line.strip() + ' // Removed for production')
            
            optimized_content = '\n'.join(lines)
            
            # Write optimized version
            optimized_file = js_file.parent / f"{js_file.stem}.prod.js"
            with open(optimized_file, 'w', encoding='utf-8') as f:
                f.write(optimized_content)
            
            self.logger.info(f"Created production JS: {optimized_file.name}")
            
        except Exception as e:
            self.logger.warning(f"Failed to optimize JavaScript: {e}")
    
    def _generate_production_config(self) -> None:
        """Generate production configuration files."""
        self.logger.info("Generating production configuration...")
        
        # Create production config
        config = {
            "environment": "production",
            "optimization_level": "high",
            "cache_timeout": 3600,
            "compression_enabled": True,
            "debug_mode": False,
            "performance_monitoring": True,
            "generated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "version": "1.0.0"
        }
        
        config_file = self.web_display_dir / "production_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
        
        # Create .htaccess for Apache
        htaccess_content = '''# Production optimizations for Timing Violation Web Display

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Set cache headers
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/json "access plus 1 hour"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
'''
        
        htaccess_file = self.web_display_dir / ".htaccess"
        with open(htaccess_file, 'w', encoding='utf-8') as f:
            f.write(htaccess_content)
        
        self.optimization_results['optimizations_applied'].append("Production configuration")
    
    def _create_deployment_package(self) -> None:
        """Create deployment package."""
        self.logger.info("Creating deployment package...")
        
        try:
            import tarfile
            
            package_name = f"timing_violation_web_display_{time.strftime('%Y%m%d_%H%M%S')}.tar.gz"
            package_path = self.violation_check_dir.parent / package_name
            
            with tarfile.open(package_path, "w:gz") as tar:
                tar.add(self.web_display_dir, arcname="web_display")
            
            self.logger.info(f"Created deployment package: {package_name}")
            self.optimization_results['optimizations_applied'].append(f"Deployment package: {package_name}")
            
        except Exception as e:
            self.logger.warning(f"Failed to create deployment package: {e}")
    
    def _final_validation(self) -> bool:
        """Perform final validation checks."""
        self.logger.info("Performing final validation...")
        
        # Check required files exist
        required_files = [
            self.web_display_dir / "index.html",
            self.data_dir / "index.json",
            self.web_display_dir / "css" / "custom.css",
            self.web_display_dir / "js" / "app.js"
        ]
        
        for file_path in required_files:
            if not file_path.exists():
                self.optimization_results['errors'].append(f"Required file missing: {file_path}")
                return False
        
        # Validate JSON files
        validator = ValidationUtils()
        json_files = list(self.data_dir.rglob("*.json"))
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    json.load(f)
            except json.JSONDecodeError as e:
                self.optimization_results['errors'].append(f"Invalid JSON in {json_file}: {e}")
                return False
        
        self.logger.info(f"Validated {len(json_files)} JSON files")
        
        # Check file sizes are reasonable
        total_size = sum(f.stat().st_size for f in self.web_display_dir.rglob("*") if f.is_file())
        if total_size > 500 * 1024 * 1024:  # 500MB
            self.optimization_results['warnings'].append(f"Large deployment size: {total_size / (1024**2):.1f}MB")
        
        return True
    
    def _generate_optimization_report(self) -> None:
        """Generate optimization report."""
        self.optimization_results['end_time'] = time.time()
        self.optimization_results['total_duration'] = self.optimization_results['end_time'] - self.optimization_results['start_time']
        
        report = f"""
# Production Optimization Report

## Summary
- **Duration**: {self.optimization_results['total_duration']:.2f} seconds
- **Tests Passed**: {self.optimization_results['tests_passed']}
- **Tests Failed**: {self.optimization_results['tests_failed']}
- **Optimizations Applied**: {len(self.optimization_results['optimizations_applied'])}
- **Warnings**: {len(self.optimization_results['warnings'])}
- **Errors**: {len(self.optimization_results['errors'])}

## Optimizations Applied
{chr(10).join(f"- {opt}" for opt in self.optimization_results['optimizations_applied'])}

## Warnings
{chr(10).join(f"- {warn}" for warn in self.optimization_results['warnings']) if self.optimization_results['warnings'] else "None"}

## Errors
{chr(10).join(f"- {err}" for err in self.optimization_results['errors']) if self.optimization_results['errors'] else "None"}

## Next Steps
1. Deploy the optimized web_display directory to production server
2. Configure web server with appropriate cache headers
3. Set up monitoring and health checks
4. Schedule regular data updates

Generated at: {time.strftime("%Y-%m-%d %H:%M:%S")}
"""
        
        report_file = self.web_display_dir / "optimization_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        self.logger.info(f"Optimization report saved to: {report_file}")


def main():
    """Main entry point for production optimization."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Optimize Timing Violation Web Display for production")
    parser.add_argument("--violation-check-dir", default="VIOLATION_CHECK",
                       help="Path to VIOLATION_CHECK directory")
    parser.add_argument("--verbose", action="store_true",
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    optimizer = ProductionOptimizer(args.violation_check_dir)
    success = optimizer.run_optimization()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()