"""
Unit tests for Excel parser.
"""

import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch

# Import the module to test
import sys
sys.path.append(str(Path(__file__).parent.parent))

from parsers.excel_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>, ExcelParsingError


class TestExcelParser(unittest.TestCase):
    """Test cases for ExcelParser class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.parser = ExcelParser()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_extract_metadata_from_path(self):
        """Test metadata extraction from file paths."""
        # Test pattern 1: corner_case_violations.xlsx
        corner, case = self.parser.extract_metadata_from_path("ss_test1_violations.xlsx")
        self.assertEqual(corner, "ss")
        self.assertEqual(case, "test1")
        
        # Test pattern 2: violations_corner_case.xlsx
        corner, case = self.parser.extract_metadata_from_path("violations_ff_test2.xlsx")
        self.assertEqual(corner, "ff")
        self.assertEqual(case, "test2")
        
        # Test directory structure
        test_path = "/path/to/corner_ss/case_test3/violations.xlsx"
        corner, case = self.parser.extract_metadata_from_path(test_path)
        # Should extract from directory names
        self.assertIn("ss", corner.lower())
    
    def test_validate_excel_format_missing_file(self):
        """Test validation with missing file."""
        non_existent_file = os.path.join(self.temp_dir, "nonexistent.xlsx")
        
        with self.assertRaises(ExcelParsingError) as context:
            self.parser.parse_excel_file(non_existent_file)
        
        self.assertIn("not found", str(context.exception))
    
    def test_validate_excel_format_invalid_extension(self):
        """Test validation with invalid file extension."""
        invalid_file = os.path.join(self.temp_dir, "test.txt")
        Path(invalid_file).touch()
        
        with self.assertRaises(ExcelParsingError) as context:
            self.parser.parse_excel_file(invalid_file)
        
        self.assertIn("Invalid Excel file extension", str(context.exception))
    
    def test_convert_to_int(self):
        """Test integer conversion utility."""
        self.assertEqual(self.parser._convert_to_int("123"), 123)
        self.assertEqual(self.parser._convert_to_int("123.45"), 123)
        self.assertEqual(self.parser._convert_to_int(None), 0)
        self.assertEqual(self.parser._convert_to_int("invalid"), 0)
    
    def test_convert_to_float(self):
        """Test float conversion utility."""
        self.assertEqual(self.parser._convert_to_float("123.45"), 123.45)
        self.assertEqual(self.parser._convert_to_float("123"), 123.0)
        self.assertEqual(self.parser._convert_to_float(None), 0.0)
        self.assertEqual(self.parser._convert_to_float("invalid"), 0.0)
    
    def test_convert_to_string(self):
        """Test string conversion utility."""
        self.assertEqual(self.parser._convert_to_string("test"), "test")
        self.assertEqual(self.parser._convert_to_string(123), "123")
        self.assertEqual(self.parser._convert_to_string(None), "")
        self.assertEqual(self.parser._convert_to_string("  test  "), "test")
    
    def test_is_empty_row(self):
        """Test empty row detection."""
        self.assertTrue(self.parser._is_empty_row((None, None, None)))
        self.assertTrue(self.parser._is_empty_row(("", "", "")))
        self.assertTrue(self.parser._is_empty_row(("  ", "", None)))
        self.assertFalse(self.parser._is_empty_row(("test", None, None)))
        self.assertFalse(self.parser._is_empty_row((1, None, None)))
    
    @patch('plugins.user.timing_violation.web_display.parsers.excel_parser.OPENPYXL_AVAILABLE', False)
    def test_openpyxl_not_available(self):
        """Test behavior when openpyxl is not available."""
        parser = ExcelParser()
        
        with self.assertRaises(ExcelParsingError) as context:
            parser.parse_excel_file("test.xlsx")
        
        self.assertIn("openpyxl library is not available", str(context.exception))
    
    def test_parse_directory_not_exists(self):
        """Test parsing directory that doesn't exist."""
        non_existent_dir = os.path.join(self.temp_dir, "nonexistent")
        
        with self.assertRaises(ExcelParsingError) as context:
            self.parser.parse_directory(non_existent_dir)
        
        self.assertIn("Directory not found", str(context.exception))
    
    def test_parse_directory_empty(self):
        """Test parsing empty directory."""
        empty_dir = os.path.join(self.temp_dir, "empty")
        os.makedirs(empty_dir)
        
        violations = self.parser.parse_directory(empty_dir)
        self.assertEqual(len(violations), 0)


if __name__ == '__main__':
    unittest.main()