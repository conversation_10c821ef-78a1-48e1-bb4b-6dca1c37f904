#!/usr/bin/env python3
"""
数据库合并功能测试脚本

测试时序违例插件的数据库合并功能。
"""

import os
import sys
import sqlite3
import tempfile
import shutil
from datetime import datetime

# 添加插件路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from models import ViolationDataModel


def create_test_database(db_path: str, case_prefix: str = "test_case") -> bool:
    """创建测试数据库
    
    Args:
        db_path: 数据库文件路径
        case_prefix: 用例名前缀
        
    Returns:
        bool: 是否创建成功
    """
    try:
        # 创建数据库目录
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 创建数据库连接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建表结构（复制自models.py）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS timing_violations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_name TEXT NOT NULL,
                corner TEXT,
                num INTEGER NOT NULL,
                hier TEXT NOT NULL,
                time_fs INTEGER NOT NULL,
                time_display TEXT NOT NULL,
                check_info TEXT NOT NULL,
                file_path TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(case_name, corner, num, hier, check_info)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS confirmation_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                violation_id INTEGER NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                confirmer TEXT,
                result TEXT,
                reason TEXT,
                is_auto_confirmed BOOLEAN DEFAULT 0,
                confirmed_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (violation_id) REFERENCES timing_violations(id)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS violation_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                hier_pattern TEXT NOT NULL,
                check_pattern TEXT NOT NULL,
                default_confirmer TEXT,
                default_result TEXT,
                default_reason TEXT,
                match_count INTEGER DEFAULT 1,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(hier_pattern, check_pattern)
            )
        ''')
        
        # 插入测试数据
        now = datetime.now().isoformat()
        
        # 插入违例记录
        test_violations = [
            (f"{case_prefix}_1", "npg_f1_ssg", 1, "tb_top.cpu.core", 1500000, "1.5 NS", "setup(posedge clk, data)", "/path/to/log1", now),
            (f"{case_prefix}_1", "npg_f1_ssg", 2, "tb_top.cpu.cache", 2000000, "2.0 NS", "hold(posedge clk, addr)", "/path/to/log1", now),
            (f"{case_prefix}_2", "npg_f2_ffg", 1, "tb_top.mem.ctrl", 800000, "0.8 NS", "setup(posedge clk, we)", "/path/to/log2", now),
        ]
        
        for violation in test_violations:
            cursor.execute('''
                INSERT INTO timing_violations
                (case_name, corner, num, hier, time_fs, time_display, check_info, file_path, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', violation)
            
            violation_id = cursor.lastrowid
            
            # 为每个违例创建确认记录
            cursor.execute('''
                INSERT INTO confirmation_records
                (violation_id, status, created_at, updated_at)
                VALUES (?, 'pending', ?, ?)
            ''', (violation_id, now, now))
        
        # 插入一些已确认的记录
        if case_prefix == "user2":
            cursor.execute('''
                UPDATE confirmation_records 
                SET status='confirmed', confirmer='张三', result='通过', 
                    reason='复位期间时序违例，可以忽略', confirmed_at=?
                WHERE violation_id=1
            ''', (now,))
        
        # 插入历史模式
        test_patterns = [
            ("tb_top.cpu.*", "setup*", "李四", "通过", "CPU相关时序违例", 5, now),
            ("tb_top.mem.*", "hold*", "王五", "有问题", "内存时序需要检查", 3, now),
        ]
        
        for pattern in test_patterns:
            cursor.execute('''
                INSERT INTO violation_patterns
                (hier_pattern, check_pattern, default_confirmer, default_result, 
                 default_reason, match_count, last_used)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', pattern)
        
        conn.commit()
        conn.close()
        
        print(f"测试数据库创建成功: {db_path}")
        return True
        
    except Exception as e:
        print(f"创建测试数据库失败: {str(e)}")
        return False


def test_database_merge():
    """测试数据库合并功能"""
    print("开始测试数据库合并功能...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="timing_violation_test_")
    print(f"测试目录: {temp_dir}")
    
    try:
        # 创建主数据库
        main_db_path = os.path.join(temp_dir, "main", "timing_violations.db")
        create_test_database(main_db_path, "main_case")
        
        # 创建用户数据库
        user1_db_path = os.path.join(temp_dir, "user1", "timing_violations.db")
        create_test_database(user1_db_path, "user1")
        
        user2_db_path = os.path.join(temp_dir, "user2", "timing_violations.db")
        create_test_database(user2_db_path, "user2")
        
        # 创建数据模型（使用主数据库）
        original_cwd = os.getcwd()
        os.chdir(os.path.dirname(main_db_path))
        
        data_model = ViolationDataModel()
        
        # 获取合并前的统计信息
        print("\n=== 合并前统计信息 ===")
        main_stats = data_model.get_database_statistics()
        print(f"主数据库: {main_stats}")
        
        user1_stats = data_model.get_database_statistics(user1_db_path)
        print(f"用户1数据库: {user1_stats}")
        
        user2_stats = data_model.get_database_statistics(user2_db_path)
        print(f"用户2数据库: {user2_stats}")
        
        # 执行合并
        print("\n=== 开始合并 ===")
        source_databases = [user1_db_path, user2_db_path]
        
        def progress_callback(current, total, message):
            print(f"进度 [{current}/{total}]: {message}")
        
        merge_result = data_model.merge_databases(source_databases, progress_callback)
        
        # 显示合并结果
        print("\n=== 合并结果 ===")
        print(f"合并成功: {merge_result['success']}")
        print(f"备份文件: {merge_result['backup_path']}")
        print(f"新增违例: {merge_result['total_violations_added']}")
        print(f"更新确认: {merge_result['total_confirmations_updated']}")
        print(f"合并模式: {merge_result['total_patterns_merged']}")
        print(f"处理数据库: {merge_result['processed_databases']}")
        
        if merge_result['errors']:
            print(f"错误信息: {merge_result['errors']}")
        
        # 获取合并后的统计信息
        print("\n=== 合并后统计信息 ===")
        final_stats = data_model.get_database_statistics()
        print(f"最终数据库: {final_stats}")
        
        # 验证备份文件是否存在
        if merge_result['backup_path'] and os.path.exists(merge_result['backup_path']):
            print(f"备份文件验证: 成功 ({os.path.basename(merge_result['backup_path'])})")
        else:
            print("备份文件验证: 失败")
        
        os.chdir(original_cwd)
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
            print(f"\n临时目录已清理: {temp_dir}")
        except Exception as e:
            print(f"清理临时目录失败: {str(e)}")


if __name__ == "__main__":
    test_database_merge()
