#!/usr/bin/env python3
"""
自动启动Web服务器脚本

这个脚本会自动生成数据并启动Web服务器，解决CORS问题。
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path


def find_available_port(start_port=8000):
    """查找可用端口"""
    import socket
    
    for port in range(start_port, start_port + 20):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('localhost', port))
                if result != 0:  # 端口未被占用
                    return port
        except:
            return port
    return start_port + 20  # 如果都被占用，返回一个更高的端口


def auto_start_web_display():
    """自动启动Web展示"""
    print("🚀 自动启动时序违例Web展示")
    print("=" * 50)
    
    try:
        # 1. 生成数据
        print("📊 生成网页数据...")
        data_script = Path("plugins/user/timing_violation/generate_optimized_web_data.py")
        if data_script.exists():
            result = subprocess.run([
                sys.executable, str(data_script)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 数据生成成功")
            else:
                print("⚠️ 数据生成失败，使用现有数据")
        
        # 2. 查找可用端口
        port = find_available_port()
        print(f"🔍 使用端口: {port}")
        
        # 3. 启动增强版Web服务器
        print("🌐 启动增强版Web服务器...")
        enhanced_server_script = Path("plugins/user/timing_violation/enhanced_web_server.py")
        
        if enhanced_server_script.exists():
            # 启动增强版服务器进程
            process = subprocess.Popen([
                sys.executable, str(enhanced_server_script),
                "--port", str(port)
            ])
            
            # 等待服务器启动
            time.sleep(3)
            
            if process.poll() is None:
                print("✅ 增强版Web服务器启动成功")
                print("✨ 支持数据实时刷新功能")
                
                print("\n" + "=" * 50)
                print("🎉 Web展示已启动！")
                print(f"📱 访问地址: http://localhost:{port}/index.html")
                print("🔄 支持数据刷新: 点击网页上的 'Refresh Data' 按钮")
                print("⏹️ 按 Ctrl+C 停止服务器")
                print("=" * 50)
                
                try:
                    # 等待用户中断
                    process.wait()
                except KeyboardInterrupt:
                    print("\n🛑 正在停止服务器...")
                    process.terminate()
                    process.wait()
                    print("✅ 服务器已停止")
                
                return True
            else:
                print("❌ 增强版Web服务器启动失败")
                return False
        else:
            print("⚠️ 增强版服务器不存在，使用标准服务器...")
            
            # 回退到标准服务器
            server_script = Path("plugins/user/timing_violation/start_web_server.py")
            
            if server_script.exists():
                # 启动标准服务器进程
                process = subprocess.Popen([
                    sys.executable, str(server_script),
                    "--port", str(port),
                    "--no-browser"
                ])
                
                # 等待服务器启动
                time.sleep(3)
                
                if process.poll() is None:
                    print("✅ 标准Web服务器启动成功")
                    
                    # 打开浏览器
                    url = f"http://localhost:{port}/index.html"
                    print(f"🌍 打开浏览器: {url}")
                    webbrowser.open(url)
                    
                    print("\n" + "=" * 50)
                    print("🎉 Web展示已启动！")
                    print(f"📱 访问地址: {url}")
                    print("⏹️ 按 Ctrl+C 停止服务器")
                    print("=" * 50)
                    
                    try:
                        # 等待用户中断
                        process.wait()
                    except KeyboardInterrupt:
                        print("\n🛑 正在停止服务器...")
                        process.terminate()
                        process.wait()
                        print("✅ 服务器已停止")
                    
                    return True
                else:
                    print("❌ 标准Web服务器启动失败")
                    return False
            else:
                print("❌ 找不到Web服务器脚本")
                return False
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False


if __name__ == "__main__":
    success = auto_start_web_display()
    sys.exit(0 if success else 1)