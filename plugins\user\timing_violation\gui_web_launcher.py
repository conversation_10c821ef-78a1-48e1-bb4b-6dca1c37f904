#!/usr/bin/env python3
"""
GUI Web启动器

专门为GUI集成设计的简化Web展示启动器。
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path


def generate_web_data_for_gui(violations_data=None):
    """为GUI生成网页数据"""
    try:
        from .web_display.data_exporter import DataExporter
        
        exporter = DataExporter(
            violation_check_dir="VIOLATION_CHECK",
            enable_performance_monitoring=True
        )
        
        success = exporter.export_all_data(violations_data)
        return success
        
    except Exception as e:
        print(f"生成网页数据失败: {e}")
        return False


def start_web_server_for_gui(port=8000):
    """为GUI启动Web服务器"""
    try:
        server_script = Path("plugins/user/timing_violation/start_web_server.py")
        
        if not server_script.exists():
            print(f"Web服务器脚本不存在: {server_script}")
            return None
        
        # 启动服务器进程
        process = subprocess.Popen([
            sys.executable, str(server_script),
            "--port", str(port),
            "--no-browser"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        time.sleep(2)
        
        if process.poll() is None:
            return process
        else:
            print("Web服务器启动失败")
            return None
            
    except Exception as e:
        print(f"启动Web服务器失败: {e}")
        return None


def open_web_browser(port=8000):
    """打开Web浏览器"""
    try:
        url = f"http://localhost:{port}/standalone_test.html"
        webbrowser.open(url)
        return True
    except Exception as e:
        print(f"打开浏览器失败: {e}")
        return False


def launch_web_display_for_gui(violations_data=None, port=8000, auto_open_browser=True):
    """为GUI启动完整的Web展示功能"""
    try:
        print("正在生成网页数据...")
        if not generate_web_data_for_gui(violations_data):
            return None, "数据生成失败"
        
        print("正在启动Web服务器...")
        process = start_web_server_for_gui(port)
        if not process:
            return None, "Web服务器启动失败"
        
        if auto_open_browser:
            print("正在打开浏览器...")
            open_web_browser(port)
        
        return process, f"Web展示已启动: http://localhost:{port}/standalone_test.html"
        
    except Exception as e:
        return None, f"启动失败: {str(e)}"


if __name__ == "__main__":
    # 命令行使用
    import argparse
    
    parser = argparse.ArgumentParser(description="GUI Web启动器")
    parser.add_argument("--port", type=int, default=8000, help="端口号")
    parser.add_argument("--no-browser", action="store_true", help="不打开浏览器")
    
    args = parser.parse_args()
    
    process, message = launch_web_display_for_gui(
        port=args.port,
        auto_open_browser=not args.no_browser
    )
    
    if process:
        print(message)
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n正在停止服务器...")
            process.terminate()
            process.wait()
    else:
        print(f"启动失败: {message}")
        sys.exit(1)