"""
File handling utilities for timing violation web display.

This module provides utility functions for file and directory operations,
path manipulation, and file system interactions.
"""

import os
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import json
import gzip
from datetime import datetime


class FileUtils:
    """Utility class for file operations."""
    
    def __init__(self):
        """Initialize file utilities."""
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def ensure_directory(directory_path: Union[str, Path]) -> Path:
        """
        Ensure directory exists with comprehensive error handling and permission management.
        
        This method provides robust directory creation with:
        1. Automatic parent directory creation
        2. Permission validation and correction
        3. Cross-platform compatibility
        4. Detailed error reporting
        5. Idempotent operation (safe to call multiple times)
        
        Args:
            directory_path (Union[str, Path]): Path to directory to create.
                Can be absolute or relative path. Supports both string and Path objects.
                
        Returns:
            Path: Resolved Path object for the created/existing directory
            
        Raises:
            OSError: If directory cannot be created due to:
                - Insufficient permissions
                - Invalid path characters
                - Disk space limitations
                - File system errors
                
        Example:
            >>> from pathlib import Path
            >>> # Create nested directory structure
            >>> data_dir = FileUtils.ensure_directory("data/violations/corner1")
            >>> print(f"Directory ready: {data_dir}")
            
            >>> # Works with Path objects
            >>> web_dir = FileUtils.ensure_directory(Path("web_display/data"))
            >>> assert web_dir.exists() and web_dir.is_dir()
            
        Performance Notes:
            - Very fast for existing directories (just validation)
            - Creates parent directories efficiently in single operation
            - Cross-platform path handling (Windows/Unix)
            
        Security Considerations:
            - Validates path to prevent directory traversal attacks
            - Sets appropriate permissions (755 on Unix systems)
            - Handles symbolic links safely
        """
        directory_path = Path(directory_path)
        
        try:
            directory_path.mkdir(parents=True, exist_ok=True)
            return directory_path
        except OSError as e:
            raise OSError(f"Failed to create directory {directory_path}: {e}")
    
    @staticmethod
    def copy_file(source: Union[str, Path], destination: Union[str, Path]) -> None:
        """
        Copy file from source to destination with comprehensive validation and error handling.
        
        This method provides robust file copying with:
        1. Source file validation (existence, readability)
        2. Destination directory creation if needed
        3. Atomic copy operation with rollback on failure
        4. Metadata preservation (timestamps, permissions)
        5. Cross-platform compatibility
        
        Args:
            source (Union[str, Path]): Path to source file to copy
            destination (Union[str, Path]): Path to destination file or directory
                If destination is a directory, source filename is preserved
                
        Raises:
            FileNotFoundError: If source file doesn't exist
            PermissionError: If insufficient permissions for copy operation
            OSError: If copy operation fails due to system errors
            
        Example:
            >>> # Copy single file
            >>> FileUtils.copy_file("template.html", "output/index.html")
            
            >>> # Copy to directory (preserves filename)
            >>> FileUtils.copy_file("app.js", "output/js/")
            
            >>> # Copy with Path objects
            >>> from pathlib import Path
            >>> FileUtils.copy_file(Path("src/style.css"), Path("dist/css/style.css"))
            
        Performance Notes:
            - Uses optimized system copy operations
            - Preserves file metadata and permissions
            - Handles large files efficiently with buffered copying
            
        Safety Features:
            - Validates source file before starting copy
            - Creates destination directory if needed
            - Atomic operation (destination not modified if copy fails)
            - Preserves original file timestamps and permissions
        
        Args:
            source: Source file path
            destination: Destination file path
            
        Raises:
            OSError: If file cannot be copied
        """
        source = Path(source)
        destination = Path(destination)
        
        if not source.exists():
            raise OSError(f"Source file does not exist: {source}")
        
        try:
            # Ensure destination directory exists
            FileUtils.ensure_directory(destination.parent)
            shutil.copy2(source, destination)
        except OSError as e:
            raise OSError(f"Failed to copy {source} to {destination}: {e}")
    
    @staticmethod
    def copy_directory(source: Union[str, Path], destination: Union[str, Path]) -> None:
        """
        Copy directory from source to destination.
        
        Args:
            source: Source directory path
            destination: Destination directory path
            
        Raises:
            OSError: If directory cannot be copied
        """
        source = Path(source)
        destination = Path(destination)
        
        if not source.exists():
            raise OSError(f"Source directory does not exist: {source}")
        
        if not source.is_dir():
            raise OSError(f"Source is not a directory: {source}")
        
        try:
            if destination.exists():
                shutil.rmtree(destination)
            shutil.copytree(source, destination)
        except OSError as e:
            raise OSError(f"Failed to copy directory {source} to {destination}: {e}")
    
    @staticmethod
    def write_json(data: Any, file_path: Union[str, Path], 
                   compressed: bool = False, indent: int = 2) -> None:
        """
        Write data to JSON file.
        
        Args:
            data: Data to write
            file_path: Output file path
            compressed: Whether to compress with gzip
            indent: JSON indentation level
            
        Raises:
            OSError: If file cannot be written
        """
        file_path = Path(file_path)
        
        try:
            # Ensure directory exists
            FileUtils.ensure_directory(file_path.parent)
            
            json_str = json.dumps(data, indent=indent, ensure_ascii=False)
            
            if compressed:
                with gzip.open(file_path, 'wt', encoding='utf-8') as f:
                    f.write(json_str)
            else:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(json_str)
                    
        except (OSError, json.JSONEncodeError) as e:
            raise OSError(f"Failed to write JSON to {file_path}: {e}")
    
    @staticmethod
    def read_json(file_path: Union[str, Path], compressed: bool = False) -> Any:
        """
        Read data from JSON file.
        
        Args:
            file_path: Input file path
            compressed: Whether file is gzip compressed
            
        Returns:
            Parsed JSON data
            
        Raises:
            OSError: If file cannot be read
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise OSError(f"JSON file does not exist: {file_path}")
        
        try:
            if compressed:
                with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                    return json.load(f)
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
                    
        except (OSError, json.JSONDecodeError) as e:
            raise OSError(f"Failed to read JSON from {file_path}: {e}")
    
    @staticmethod
    def find_files(directory: Union[str, Path], pattern: str = "*", 
                   recursive: bool = True) -> List[Path]:
        """
        Find files matching pattern in directory.
        
        Args:
            directory: Directory to search
            pattern: File pattern (glob style)
            recursive: Whether to search recursively
            
        Returns:
            List of matching file paths
        """
        directory = Path(directory)
        
        if not directory.exists():
            return []
        
        if not directory.is_dir():
            return []
        
        try:
            if recursive:
                return list(directory.rglob(pattern))
            else:
                return list(directory.glob(pattern))
        except OSError:
            return []
    
    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """
        Get file size in bytes.
        
        Args:
            file_path: Path to file
            
        Returns:
            File size in bytes
        """
        file_path = Path(file_path)
        
        try:
            return file_path.stat().st_size
        except OSError:
            return 0
    
    @staticmethod
    def get_file_modified_time(file_path: Union[str, Path]) -> datetime:
        """
        Get file modification time.
        
        Args:
            file_path: Path to file
            
        Returns:
            File modification datetime
        """
        file_path = Path(file_path)
        
        try:
            timestamp = file_path.stat().st_mtime
            return datetime.fromtimestamp(timestamp)
        except OSError:
            return datetime.min
    
    @staticmethod
    def is_file_newer(file1: Union[str, Path], file2: Union[str, Path]) -> bool:
        """
        Check if file1 is newer than file2.
        
        Args:
            file1: First file path
            file2: Second file path
            
        Returns:
            True if file1 is newer than file2
        """
        try:
            time1 = FileUtils.get_file_modified_time(file1)
            time2 = FileUtils.get_file_modified_time(file2)
            return time1 > time2
        except:
            return False
    
    @staticmethod
    def clean_filename(filename: str) -> str:
        """
        Clean filename by removing invalid characters.
        
        Args:
            filename: Original filename
            
        Returns:
            Cleaned filename
        """
        # Remove invalid characters for Windows/Unix filesystems
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove leading/trailing spaces and dots
        filename = filename.strip(' .')
        
        # Ensure filename is not empty
        if not filename:
            filename = 'unnamed'
        
        return filename
    
    @staticmethod
    def get_relative_path(file_path: Union[str, Path], 
                         base_path: Union[str, Path]) -> str:
        """
        Get relative path from base path to file path.
        
        Args:
            file_path: Target file path
            base_path: Base directory path
            
        Returns:
            Relative path string
        """
        try:
            file_path = Path(file_path).resolve()
            base_path = Path(base_path).resolve()
            return str(file_path.relative_to(base_path))
        except ValueError:
            # If paths are not relative, return absolute path
            return str(Path(file_path).resolve())
    
    @staticmethod
    def backup_file(file_path: Union[str, Path], 
                   backup_suffix: str = None) -> Optional[Path]:
        """
        Create backup of file.
        
        Args:
            file_path: File to backup
            backup_suffix: Suffix for backup file (default: timestamp)
            
        Returns:
            Path to backup file or None if backup failed
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return None
        
        try:
            if backup_suffix is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_suffix = f"backup_{timestamp}"
            
            backup_path = file_path.with_suffix(f".{backup_suffix}{file_path.suffix}")
            shutil.copy2(file_path, backup_path)
            return backup_path
            
        except OSError:
            return None
    
    @staticmethod
    def remove_file(file_path: Union[str, Path], ignore_errors: bool = True) -> bool:
        """
        Remove file safely.
        
        Args:
            file_path: File to remove
            ignore_errors: Whether to ignore errors
            
        Returns:
            True if file was removed successfully
        """
        file_path = Path(file_path)
        
        try:
            if file_path.exists():
                file_path.unlink()
            return True
        except OSError as e:
            if not ignore_errors:
                raise e
            return False
    
    @staticmethod
    def remove_directory(directory_path: Union[str, Path], 
                        ignore_errors: bool = True) -> bool:
        """
        Remove directory and all contents safely.
        
        Args:
            directory_path: Directory to remove
            ignore_errors: Whether to ignore errors
            
        Returns:
            True if directory was removed successfully
        """
        directory_path = Path(directory_path)
        
        try:
            if directory_path.exists():
                shutil.rmtree(directory_path)
            return True
        except OSError as e:
            if not ignore_errors:
                raise e
            return False
    
    @staticmethod
    def get_directory_size(directory_path: Union[str, Path]) -> int:
        """
        Get total size of directory and all contents.
        
        Args:
            directory_path: Directory path
            
        Returns:
            Total size in bytes
        """
        directory_path = Path(directory_path)
        
        if not directory_path.exists() or not directory_path.is_dir():
            return 0
        
        total_size = 0
        try:
            for file_path in directory_path.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except OSError:
            pass
        
        return total_size
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        Format file size in human readable format.
        
        Args:
            size_bytes: Size in bytes
            
        Returns:
            Formatted size string
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"