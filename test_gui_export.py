#!/usr/bin/env python3
"""
测试GUI数据导出功能
"""

import sys
import os
from pathlib import Path

# 添加插件路径
sys.path.insert(0, str(Path(__file__).parent / "plugins" / "user" / "timing_violation"))

def test_gui_export():
    """测试GUI数据导出功能"""
    try:
        from web_display.data_exporter import DataExporter
        
        # 模拟GUI中的违例数据
        gui_violations = [
            {
                "id": 1,
                "num": 1,
                "hier": "test/path/module1",
                "time_ns": 1.234,
                "time_fs": 1234000,
                "check_info": "setup (clk: 2.5ns, data: 1.2ns, slack: -0.1ns)",
                "status": "pending",
                "confirmer": "",
                "result": "",
                "reason": "",
                "confirmed_at": "",
                "corner": "ss_125c_0p81v",
                "case": "test_case_1",
                "source": "gui"
            },
            {
                "id": 2,
                "num": 2,
                "hier": "test/path/module2", 
                "time_ns": 2.567,
                "time_fs": 2567000,
                "check_info": "hold (clk: 2.5ns, data: 2.8ns, slack: -0.3ns)",
                "status": "confirmed",
                "confirmer": "测试用户",
                "result": "pass",
                "reason": "复位期间违例，可忽略",
                "confirmed_at": "2024-01-15 10:30:00",
                "corner": "ss_125c_0p81v",
                "case": "test_case_1",
                "source": "gui"
            },
            {
                "id": 3,
                "num": 3,
                "hier": "test/path/module3",
                "time_ns": 0.789,
                "time_fs": 789000,
                "check_info": "setup (clk: 2.0ns, data: 0.8ns, slack: -0.01ns)",
                "status": "pending",
                "confirmer": "",
                "result": "",
                "reason": "",
                "confirmed_at": "",
                "corner": "ff_m40c_1p32v",
                "case": "test_case_2",
                "source": "gui"
            }
        ]
        
        print("创建数据导出器...")
        violation_check_dir = Path.cwd() / "VIOLATION_CHECK"
        exporter = DataExporter(str(violation_check_dir))
        
        print(f"使用GUI数据导出: {len(gui_violations)} 条记录")
        success = exporter.export_all_data(gui_violations=gui_violations)
        
        if success:
            print("✅ 数据导出成功!")
            
            # 检查生成的文件
            web_dir = violation_check_dir / "web_display"
            files_to_check = [
                "index.html",
                "offline.html", 
                "data/index.json",
                "data/violations.json"
            ]
            
            print("\n检查生成的文件:")
            for file_path in files_to_check:
                full_path = web_dir / file_path
                if full_path.exists():
                    size = full_path.stat().st_size
                    print(f"  ✅ {file_path} ({size} bytes)")
                else:
                    print(f"  ❌ {file_path} (不存在)")
            
            # 检查离线HTML是否包含数据
            offline_html_path = web_dir / "offline.html"
            if offline_html_path.exists():
                with open(offline_html_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "EMBEDDED_VIOLATION_DATA" in content:
                        print("  ✅ 离线HTML包含嵌入数据")
                    else:
                        print("  ❌ 离线HTML不包含嵌入数据")
            
            print(f"\n网页文件位置: {web_dir.absolute()}")
            print("可以在浏览器中打开 offline.html 查看结果")
            
        else:
            print("❌ 数据导出失败!")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gui_export()