<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>时序违例数据加载测试</h1>
        
        <div class="test-section">
            <h3>1. 测试 pagination_manifest.json 加载</h3>
            <button onclick="testManifest()">测试 Manifest</button>
            <div id="manifest-result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 测试违例数据文件加载</h3>
            <button onclick="testViolationFiles()">测试违例文件</button>
            <div id="violation-result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 测试完整数据加载流程</h3>
            <button onclick="testFullDataLoading()">测试完整流程</button>
            <div id="full-result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 控制台日志</h3>
            <div id="console-log"></div>
        </div>
    </div>

    <script>
        // 重定向console.log到页面
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        function logToPage(message, type = 'info') {
            const logDiv = document.getElementById('console-log');
            const logEntry = document.createElement('div');
            logEntry.className = `result ${type}`;
            logEntry.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToPage(args.join(' '), 'info');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToPage(args.join(' '), 'error');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToPage(args.join(' '), 'error');
        };

        async function testManifest() {
            const resultDiv = document.getElementById('manifest-result');
            resultDiv.innerHTML = '<div class="info">正在测试 manifest 加载...</div>';
            
            try {
                const response = await fetch('data/pagination_manifest.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const manifest = await response.json();
                console.log('Manifest 加载成功:', manifest);
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Manifest 加载成功</div>
                    <div class="info">
                        <strong>总违例数:</strong> ${manifest.total_violations}<br>
                        <strong>页面大小:</strong> ${manifest.page_size}<br>
                        <strong>是否压缩:</strong> ${manifest.compressed}<br>
                        <strong>Corner-Case 组合数:</strong> ${Object.keys(manifest.corner_cases).length}
                    </div>
                    <pre>${JSON.stringify(manifest, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Manifest 加载失败:', error);
                resultDiv.innerHTML = `<div class="error">❌ Manifest 加载失败: ${error.message}</div>`;
            }
        }

        async function testViolationFiles() {
            const resultDiv = document.getElementById('violation-result');
            resultDiv.innerHTML = '<div class="info">正在测试违例文件加载...</div>';
            
            try {
                // 首先加载manifest
                const manifestResponse = await fetch('data/pagination_manifest.json');
                const manifest = await manifestResponse.json();
                
                let totalLoaded = 0;
                let results = [];
                
                for (const [cornerCase, info] of Object.entries(manifest.corner_cases)) {
                    for (const filename of info.files) {
                        try {
                            const filePath = `data/violations/${filename}`;
                            const response = await fetch(filePath);
                            
                            if (response.ok) {
                                const data = await response.json();
                                let violationCount = 0;
                                
                                if (data.violations && Array.isArray(data.violations)) {
                                    violationCount = data.violations.length;
                                } else if (Array.isArray(data)) {
                                    violationCount = data.length;
                                }
                                
                                totalLoaded += violationCount;
                                results.push(`✅ ${filename}: ${violationCount} 条违例`);
                                console.log(`加载文件 ${filename}: ${violationCount} 条违例`);
                            } else {
                                results.push(`❌ ${filename}: HTTP ${response.status}`);
                            }
                        } catch (error) {
                            results.push(`❌ ${filename}: ${error.message}`);
                        }
                    }
                }
                
                resultDiv.innerHTML = `
                    <div class="success">总共加载了 ${totalLoaded} 条违例记录</div>
                    <div class="info">
                        ${results.map(r => `<div>${r}</div>`).join('')}
                    </div>
                `;
                
            } catch (error) {
                console.error('违例文件测试失败:', error);
                resultDiv.innerHTML = `<div class="error">❌ 违例文件测试失败: ${error.message}</div>`;
            }
        }

        async function testFullDataLoading() {
            const resultDiv = document.getElementById('full-result');
            resultDiv.innerHTML = '<div class="info">正在测试完整数据加载流程...</div>';
            
            try {
                // 模拟app.js中的loadFromManifest方法
                async function loadFromManifest(cornerCase, manifestInfo, isCompressed) {
                    const violations = [];
                    
                    for (const filename of manifestInfo.files) {
                        try {
                            const filePath = `data/violations/${filename}`;
                            
                            if (isCompressed && filename.endsWith('.gz')) {
                                // 尝试加载未压缩版本
                                const uncompressedPath = filePath.replace('.gz', '');
                                const response = await fetch(uncompressedPath);
                                if (response.ok) {
                                    const data = await response.json();
                                    if (data.violations && Array.isArray(data.violations)) {
                                        violations.push(...data.violations);
                                    } else if (Array.isArray(data)) {
                                        violations.push(...data);
                                    }
                                    continue;
                                }
                                console.error(`无法加载压缩文件 ${filename}，请生成未压缩版本`);
                                continue;
                            }
                            
                            // 加载常规JSON文件
                            const response = await fetch(filePath);
                            if (response.ok) {
                                const data = await response.json();
                                if (data.violations && Array.isArray(data.violations)) {
                                    violations.push(...data.violations);
                                } else if (Array.isArray(data)) {
                                    violations.push(...data);
                                }
                            }
                        } catch (error) {
                            console.warn(`加载文件 ${filename} 失败:`, error);
                        }
                    }
                    
                    return violations;
                }

                // 加载manifest
                const manifestResponse = await fetch('data/pagination_manifest.json');
                const manifest = await manifestResponse.json();
                console.log(`找到 pagination manifest，包含 ${manifest.total_violations} 条总违例`);
                
                const allViolations = [];
                
                // 从manifest加载数据
                for (const [cornerCase, info] of Object.entries(manifest.corner_cases)) {
                    try {
                        const cornerCaseViolations = await loadFromManifest(cornerCase, info, manifest.compressed);
                        if (cornerCaseViolations && cornerCaseViolations.length > 0) {
                            allViolations.push(...cornerCaseViolations);
                            console.log(`从 ${cornerCase} 加载了 ${cornerCaseViolations.length} 条违例`);
                        }
                    } catch (error) {
                        console.warn(`加载 ${cornerCase} 数据失败:`, error);
                    }
                }
                
                if (allViolations.length > 0) {
                    console.log(`从 manifest 总共加载了 ${allViolations.length} 条违例`);
                    
                    // 显示一些样本数据
                    const sampleViolations = allViolations.slice(0, 3);
                    
                    resultDiv.innerHTML = `
                        <div class="success">✅ 完整数据加载成功</div>
                        <div class="info">
                            <strong>总违例数:</strong> ${allViolations.length}<br>
                            <strong>预期违例数:</strong> ${manifest.total_violations}<br>
                            <strong>匹配状态:</strong> ${allViolations.length === manifest.total_violations ? '✅ 完全匹配' : '⚠️ 数量不匹配'}
                        </div>
                        <div class="info">
                            <strong>样本数据 (前3条):</strong>
                            <pre>${JSON.stringify(sampleViolations, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 没有加载到任何违例数据</div>`;
                }
                
            } catch (error) {
                console.error('完整数据加载测试失败:', error);
                resultDiv.innerHTML = `<div class="error">❌ 完整数据加载测试失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
