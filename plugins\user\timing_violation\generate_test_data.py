#!/usr/bin/env python3
"""
生成大型vio_summary.log文件用于性能测试
默认生成6万条违例记录（约30万行）
"""

import os
import time
import random
import argparse

def generate_vio_summary(output_file, violation_count=60000):
    """
    生成vio_summary.log测试文件
    
    Args:
        output_file: 输出文件路径
        violation_count: 违例记录数量
    """
    start_time = time.time()
    print(f"开始生成 {violation_count:,} 条违例记录...")
    
    # 模拟的模块名称
    modules = [
        "cpu_top", "memory_ctrl", "pcie_if", "ddr_ctrl", "cache_unit", 
        "pipeline", "fetch_unit", "decode_unit", "execute_unit", "writeback_unit",
        "branch_pred", "tlb", "mmu", "interrupt_ctrl", "debug_unit"
    ]
    
    # 模拟的信号名称
    signals = [
        "clk", "rst_n", "enable", "valid", "ready", "data", "addr", "wr_en", 
        "rd_en", "cs", "req", "ack", "done", "error", "status", "mode", 
        "intr", "grant", "busy", "idle"
    ]
    
    # 模拟的检查类型
    check_types = [
        "Setup time violation", 
        "Hold time violation",
        "Recovery time violation",
        "Removal time violation",
        "Pulse width violation",
        "Period violation",
        "Skew violation"
    ]
    
    # 时间单位
    time_units = ["FS", "PS", "NS"]
    time_unit_weights = [70, 25, 5]  # 70% FS, 25% PS, 5% NS
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for i in range(1, violation_count + 1):
            # 生成随机层级路径
            depth = random.randint(3, 6)
            path_parts = []
            path_parts.append("tb")
            
            for _ in range(depth):
                module = random.choice(modules)
                if random.random() < 0.3:  # 30%概率添加索引
                    index = random.randint(0, 31)
                    module = f"{module}[{index}]"
                path_parts.append(module)
            
            hier_path = ".".join(path_parts)
            
            # 生成随机信号名
            signal = random.choice(signals)
            if random.random() < 0.5:  # 50%概率添加位宽
                width = random.randint(0, 63)
                signal = f"{signal}[{width}]"
            
            # 生成随机时间值
            time_unit = random.choices(time_units, weights=time_unit_weights)[0]
            if time_unit == "FS":
                time_value = random.randint(100000, 9999999)
            elif time_unit == "PS":
                time_value = random.randint(100, 9999)
            else:  # NS
                time_value = random.uniform(0.1, 9.9)
                time_value = round(time_value, 3)
            
            # 生成随机检查信息
            check_type = random.choice(check_types)
            check_info = f"{check_type} on signal {signal}"
            
            # 写入违例记录
            f.write(f"NUM : {i}\n")
            f.write(f"Hier : {hier_path}\n")
            f.write(f"Time : {time_value} {time_unit}\n")
            f.write(f"Check : {check_info}\n")
            f.write("------------------------------------------------------------\n")
            
            # 显示进度
            if i % 10000 == 0 or i == violation_count:
                elapsed = time.time() - start_time
                print(f"已生成 {i:,}/{violation_count:,} 条记录 ({i/violation_count*100:.1f}%)，耗时: {elapsed:.1f}秒")
    
    # 计算文件大小
    file_size = os.path.getsize(output_file)
    file_size_mb = file_size / (1024 * 1024)
    
    # 统计行数
    with open(output_file, 'r', encoding='utf-8') as f:
        line_count = sum(1 for _ in f)
    
    elapsed = time.time() - start_time
    print(f"\n生成完成!")
    print(f"文件路径: {os.path.abspath(output_file)}")
    print(f"文件大小: {file_size_mb:.2f} MB ({file_size:,} 字节)")
    print(f"总行数: {line_count:,} 行")
    print(f"违例记录: {violation_count:,} 条")
    print(f"总耗时: {elapsed:.2f} 秒")

def main():
    parser = argparse.ArgumentParser(description="生成vio_summary.log测试文件")
    parser.add_argument("-o", "--output", default="vio_summary.log", 
                        help="输出文件路径 (默认: vio_summary.log)")
    parser.add_argument("-c", "--count", type=int, default=60000, 
                        help="违例记录数量 (默认: 60000)")
    args = parser.parse_args()
    
    generate_vio_summary(args.output, args.count)

if __name__ == "__main__":
    main()
