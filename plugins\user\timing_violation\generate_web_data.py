#!/usr/bin/env python3
"""
时序违例网页数据生成脚本

这个脚本用于生成时序违例的网页显示数据，可以独立运行或从GUI调用。
"""

import os
import sys
import argparse
import logging
from pathlib import Path

def setup_logging(verbose=False):
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('web_data_generation.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成时序违例网页显示数据")
    parser.add_argument(
        "--violation-check-dir", 
        default="VIOLATION_CHECK",
        help="VIOLATION_CHECK目录路径 (默认: VIOLATION_CHECK)"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="启用详细日志输出"
    )
    parser.add_argument(
        "--validate", 
        action="store_true",
        help="验证生成的数据"
    )
    parser.add_argument(
        "--info", 
        action="store_true",
        help="显示导出信息"
    )
    parser.add_argument(
        "--force", 
        action="store_true",
        help="强制重新生成所有数据"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    try:
        # 导入数据导出器
        from web_display.data_exporter import DataExporter
        
        logger.info(f"开始生成网页数据...")
        logger.info(f"VIOLATION_CHECK目录: {args.violation_check_dir}")
        
        # 检查目录是否存在
        violation_check_path = Path(args.violation_check_dir)
        if not violation_check_path.exists():
            logger.error(f"VIOLATION_CHECK目录不存在: {violation_check_path}")
            return 1
        
        # 创建数据导出器
        exporter = DataExporter(
            str(violation_check_path),
            enable_performance_monitoring=args.verbose
        )
        
        # 导出数据
        success = exporter.export_all_data()
        
        if success:
            web_display_path = violation_check_path / "web_display"
            index_path = web_display_path / "index.html"
            
            logger.info("✅ 网页数据生成成功！")
            logger.info(f"📁 网页目录: {web_display_path}")
            logger.info(f"🌐 主页面: {index_path}")
            
            if args.info:
                # 显示详细信息
                data_dir = web_display_path / "data"
                if data_dir.exists():
                    json_files = list(data_dir.rglob("*.json"))
                    logger.info(f"📊 生成的数据文件数量: {len(json_files)}")
                    
                    # 计算总大小
                    total_size = sum(f.stat().st_size for f in json_files)
                    logger.info(f"💾 数据总大小: {total_size / 1024:.1f} KB")
            
            if args.validate:
                # 验证生成的数据
                logger.info("🔍 验证生成的数据...")
                validation_errors = validate_generated_data(web_display_path)
                if validation_errors:
                    logger.warning("⚠️ 数据验证发现问题:")
                    for error in validation_errors:
                        logger.warning(f"  - {error}")
                else:
                    logger.info("✅ 数据验证通过")
            
            # 提供使用说明
            logger.info("\n📖 使用说明:")
            logger.info(f"1. 在浏览器中打开: {index_path}")
            logger.info("2. 或者使用本地服务器:")
            logger.info(f"   cd {web_display_path}")
            logger.info("   python -m http.server 8000")
            logger.info("   然后访问: http://localhost:8000")
            
            return 0
        else:
            logger.error("❌ 网页数据生成失败")
            return 1
            
    except ImportError as e:
        logger.error(f"❌ 导入错误: {e}")
        logger.error("请确保 web_display 模块在正确的位置")
        return 1
    except Exception as e:
        logger.error(f"❌ 生成失败: {e}")
        return 1

def validate_generated_data(web_display_path):
    """验证生成的数据"""
    errors = []
    
    try:
        import json
        
        # 检查必需的文件
        required_files = [
            "index.html",
            "data/index.json",
            "css/custom.css",
            "js/app.js"
        ]
        
        for file_path in required_files:
            full_path = web_display_path / file_path
            if not full_path.exists():
                errors.append(f"缺少必需文件: {file_path}")
        
        # 验证JSON文件格式
        data_dir = web_display_path / "data"
        if data_dir.exists():
            for json_file in data_dir.rglob("*.json"):
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        json.load(f)
                except json.JSONDecodeError as e:
                    errors.append(f"JSON格式错误 {json_file.name}: {e}")
        
    except Exception as e:
        errors.append(f"验证过程出错: {e}")
    
    return errors

if __name__ == "__main__":
    sys.exit(main())