"""
Unit tests for Validation utilities.
"""

import unittest
from datetime import datetime

# Import the module to test
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from utils.validation_utils import ValidationUtils


class TestValidationUtils(unittest.TestCase):
    """Test cases for ValidationUtils class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.valid_record = {
            'num': 1,
            'hier': 'cpu/core/reg1',
            'time_ns': 1.5,
            'check_info': 'setup check',
            'corner': 'ss',
            'case': 'test1',
            'status': 'confirmed',
            'confirmer': 'john_doe',
            'result': 'false_positive',
            'reason': 'Clock skew issue',
            'confirmed_at': '2024-01-15 10:30:00'
        }
        
        self.invalid_record = {
            'num': None,  # Missing required field
            'hier': '',   # Empty required field
            'time_ns': 'invalid',  # Invalid type
            'check_info': 'setup check',
            'corner': 'ss',
            'case': 'test1',
            'status': 'invalid_status',  # Invalid status
            'confirmed_at': 'invalid_date'  # Invalid date
        }
    
    def test_validate_violation_record_valid(self):
        """Test validation of valid violation record."""
        is_valid, errors = ValidationUtils.validate_violation_record(self.valid_record)
        
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_validate_violation_record_invalid(self):
        """Test validation of invalid violation record."""
        is_valid, errors = ValidationUtils.validate_violation_record(self.invalid_record)
        
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
        
        # Check specific error messages
        error_text = ' '.join(errors)
        self.assertIn("cannot be None", error_text)
        self.assertIn("cannot be empty", error_text)
        self.assertIn("valid number", error_text)
        self.assertIn("invalid status", error_text)
        self.assertIn("invalid date", error_text)
    
    def test_validate_violation_record_missing_fields(self):
        """Test validation with missing required fields."""
        incomplete_record = {'num': 1}
        
        is_valid, errors = ValidationUtils.validate_violation_record(incomplete_record)
        
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
        
        # Should have errors for missing required fields
        required_fields = ['hier', 'time_ns', 'check_info', 'corner', 'case']
        for field in required_fields:
            self.assertTrue(any(field in error for error in errors))
    
    def test_validate_violation_dataset_valid(self):
        """Test validation of valid dataset."""
        violations = [self.valid_record.copy(), self.valid_record.copy()]
        violations[1]['num'] = 2  # Make unique
        
        report = ValidationUtils.validate_violation_dataset(violations)
        
        self.assertTrue(report['valid'])
        self.assertEqual(report['total_records'], 2)
        self.assertEqual(report['valid_records'], 2)
        self.assertEqual(report['invalid_records'], 0)
    
    def test_validate_violation_dataset_invalid(self):
        """Test validation of invalid dataset."""
        violations = [self.valid_record, self.invalid_record]
        
        report = ValidationUtils.validate_violation_dataset(violations)
        
        self.assertFalse(report['valid'])
        self.assertEqual(report['total_records'], 2)
        self.assertEqual(report['valid_records'], 1)
        self.assertEqual(report['invalid_records'], 1)
        self.assertGreater(len(report['errors']), 0)
    
    def test_validate_violation_dataset_empty(self):
        """Test validation of empty dataset."""
        violations = []
        
        report = ValidationUtils.validate_violation_dataset(violations)
        
        self.assertTrue(report['valid'])  # Empty is technically valid
        self.assertEqual(report['total_records'], 0)
        self.assertIn("Dataset is empty", report['warnings'])
    
    def test_validate_violation_dataset_duplicates(self):
        """Test detection of duplicate violation numbers."""
        violations = [self.valid_record.copy(), self.valid_record.copy()]
        # Both have same num (1)
        
        report = ValidationUtils.validate_violation_dataset(violations)
        
        self.assertEqual(report['statistics']['duplicate_nums'], 1)
        self.assertTrue(any("Duplicate violation number" in warning for warning in report['warnings']))
    
    def test_is_valid_integer(self):
        """Test integer validation."""
        self.assertTrue(ValidationUtils.is_valid_integer(123))
        self.assertTrue(ValidationUtils.is_valid_integer("123"))
        self.assertTrue(ValidationUtils.is_valid_integer(123.0))
        self.assertFalse(ValidationUtils.is_valid_integer(123.5))
        self.assertFalse(ValidationUtils.is_valid_integer("abc"))
        self.assertFalse(ValidationUtils.is_valid_integer(None))
    
    def test_is_valid_number(self):
        """Test number validation."""
        self.assertTrue(ValidationUtils.is_valid_number(123))
        self.assertTrue(ValidationUtils.is_valid_number(123.45))
        self.assertTrue(ValidationUtils.is_valid_number("123.45"))
        self.assertTrue(ValidationUtils.is_valid_number("123"))
        self.assertFalse(ValidationUtils.is_valid_number("abc"))
        self.assertFalse(ValidationUtils.is_valid_number(None))
        self.assertFalse(ValidationUtils.is_valid_number(float('nan')))
    
    def test_is_valid_hierarchy_path(self):
        """Test hierarchy path validation."""
        self.assertTrue(ValidationUtils.is_valid_hierarchy_path("cpu/core/reg1"))
        self.assertTrue(ValidationUtils.is_valid_hierarchy_path("top.cpu.core[0].reg"))
        self.assertTrue(ValidationUtils.is_valid_hierarchy_path("module_name/sub_module"))
        self.assertFalse(ValidationUtils.is_valid_hierarchy_path(""))
        self.assertFalse(ValidationUtils.is_valid_hierarchy_path("   "))
        self.assertFalse(ValidationUtils.is_valid_hierarchy_path("path with spaces"))
        self.assertFalse(ValidationUtils.is_valid_hierarchy_path("path@with#special"))
        self.assertFalse(ValidationUtils.is_valid_hierarchy_path(None))
    
    def test_is_valid_date_string(self):
        """Test date string validation."""
        self.assertTrue(ValidationUtils.is_valid_date_string("2024-01-15 10:30:00"))
        self.assertTrue(ValidationUtils.is_valid_date_string("2024-01-15"))
        self.assertTrue(ValidationUtils.is_valid_date_string("2024/01/15 10:30:00"))
        self.assertTrue(ValidationUtils.is_valid_date_string("15-01-2024 10:30:00"))
        self.assertFalse(ValidationUtils.is_valid_date_string("invalid_date"))
        self.assertFalse(ValidationUtils.is_valid_date_string(""))
        self.assertFalse(ValidationUtils.is_valid_date_string("   "))
        self.assertFalse(ValidationUtils.is_valid_date_string(None))
    
    def test_is_valid_status(self):
        """Test status validation."""
        self.assertTrue(ValidationUtils.is_valid_status("confirmed"))
        self.assertTrue(ValidationUtils.is_valid_status("pending"))
        self.assertTrue(ValidationUtils.is_valid_status("rejected"))
        self.assertTrue(ValidationUtils.is_valid_status("CONFIRMED"))  # Case insensitive
        self.assertTrue(ValidationUtils.is_valid_status("  confirmed  "))  # Whitespace
        self.assertFalse(ValidationUtils.is_valid_status("invalid_status"))
        self.assertFalse(ValidationUtils.is_valid_status(""))
        self.assertFalse(ValidationUtils.is_valid_status(None))
        self.assertFalse(ValidationUtils.is_valid_status(123))
    
    def test_sanitize_string(self):
        """Test string sanitization."""
        self.assertEqual(ValidationUtils.sanitize_string("  test  "), "test")
        self.assertEqual(ValidationUtils.sanitize_string("test\x00control"), "testcontrol")
        self.assertEqual(ValidationUtils.sanitize_string("a" * 1500, max_length=10), "a" * 10 + "...")
        self.assertEqual(ValidationUtils.sanitize_string(None), "")
        self.assertEqual(ValidationUtils.sanitize_string(123), "123")
    
    def test_normalize_corner_name(self):
        """Test corner name normalization."""
        self.assertEqual(ValidationUtils.normalize_corner_name("SS"), "ss")
        self.assertEqual(ValidationUtils.normalize_corner_name("  FF  "), "ff")
        self.assertEqual(ValidationUtils.normalize_corner_name("slow_slow"), "ss")
        self.assertEqual(ValidationUtils.normalize_corner_name("fast_fast"), "ff")
        self.assertEqual(ValidationUtils.normalize_corner_name("typical"), "tt")
        self.assertEqual(ValidationUtils.normalize_corner_name("custom"), "custom")
        self.assertEqual(ValidationUtils.normalize_corner_name(None), "")
        self.assertEqual(ValidationUtils.normalize_corner_name(123), "123")
    
    def test_normalize_case_name(self):
        """Test case name normalization."""
        self.assertEqual(ValidationUtils.normalize_case_name("test_case1"), "case1")
        self.assertEqual(ValidationUtils.normalize_case_name("case_test2"), "test2")
        self.assertEqual(ValidationUtils.normalize_case_name("tc_test3"), "test3")
        self.assertEqual(ValidationUtils.normalize_case_name("  normal_case  "), "normal_case")
        self.assertEqual(ValidationUtils.normalize_case_name("no_prefix"), "no_prefix")
        self.assertEqual(ValidationUtils.normalize_case_name(None), "")
        self.assertEqual(ValidationUtils.normalize_case_name(123), "123")
    
    def test_validate_json_structure(self):
        """Test JSON structure validation."""
        schema = {
            'name': {'required': True, 'type': str},
            'count': {'required': True, 'type': int},
            'items': {'required': False, 'type': list, 'item_type': str}
        }
        
        # Valid data
        valid_data = {
            'name': 'test',
            'count': 5,
            'items': ['item1', 'item2']
        }
        
        is_valid, errors = ValidationUtils.validate_json_structure(valid_data, schema)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
        
        # Invalid data - missing required field
        invalid_data = {
            'name': 'test'
            # missing 'count'
        }
        
        is_valid, errors = ValidationUtils.validate_json_structure(invalid_data, schema)
        self.assertFalse(is_valid)
        self.assertTrue(any("Missing required key: count" in error for error in errors))
        
        # Invalid data - wrong type
        invalid_data2 = {
            'name': 'test',
            'count': 'not_an_int'
        }
        
        is_valid, errors = ValidationUtils.validate_json_structure(invalid_data2, schema)
        self.assertFalse(is_valid)
        self.assertTrue(any("should be of type int" in error for error in errors))
    
    def test_check_data_consistency(self):
        """Test data consistency checking."""
        violations = [
            {
                'num': 1,
                'corner': 'ss',
                'case': 'test1',
                'confirmer': 'john',
                'time_ns': 1.5,
                'status': 'confirmed'
            },
            {
                'num': 2,
                'corner': 'ff',
                'case': 'test1',
                'confirmer': 'john',
                'time_ns': 2.3,
                'status': 'confirmed'
            },
            {
                'num': 3,
                'corner': 'ss',
                'case': 'test2',
                'confirmer': 'jane',
                'time_ns': 0.8,
                'status': 'pending'
            }
        ]
        
        report = ValidationUtils.check_data_consistency(violations)
        
        self.assertTrue(report['consistent'])
        
        # Check statistics
        self.assertIn('ss_test1', report['statistics']['corner_case_combinations'])
        self.assertIn('ff_test1', report['statistics']['corner_case_combinations'])
        self.assertIn('ss_test2', report['statistics']['corner_case_combinations'])
        
        self.assertIn('john', report['statistics']['confirmer_patterns'])
        self.assertIn('jane', report['statistics']['confirmer_patterns'])
        
        self.assertEqual(report['statistics']['confirmer_patterns']['john']['total'], 2)
        self.assertEqual(report['statistics']['confirmer_patterns']['jane']['total'], 1)
        
        self.assertIn('confirmed', report['statistics']['status_distribution'])
        self.assertIn('pending', report['statistics']['status_distribution'])
        self.assertEqual(report['statistics']['status_distribution']['confirmed'], 2)
        self.assertEqual(report['statistics']['status_distribution']['pending'], 1)
        
        # Check time ranges
        self.assertEqual(report['statistics']['time_ranges']['min'], 0.8)
        self.assertEqual(report['statistics']['time_ranges']['max'], 2.3)
        self.assertAlmostEqual(report['statistics']['time_ranges']['avg'], (1.5 + 2.3 + 0.8) / 3)
    
    def test_check_data_consistency_empty(self):
        """Test data consistency with empty dataset."""
        violations = []
        
        report = ValidationUtils.check_data_consistency(violations)
        
        self.assertTrue(report['consistent'])
        self.assertEqual(len(report['issues']), 0)


if __name__ == '__main__':
    unittest.main()