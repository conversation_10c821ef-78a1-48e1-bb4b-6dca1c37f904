#!/usr/bin/env python3
"""
GUI Web启动器 - 修复版

专门为GUI集成设计的简化Web展示启动器，修复了导入路径和错误处理问题。
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path


def generate_web_data_for_gui(violations_data=None):
    """为GUI生成网页数据"""
    try:
        # 尝试多种导入方式
        data_exporter = None
        
        # 方式1：相对导入
        try:
            from .web_display.data_exporter import DataExporter
            data_exporter = DataExporter
        except ImportError:
            pass
        
        # 方式2：绝对导入
        if data_exporter is None:
            try:
                import sys
                current_dir = Path(__file__).parent
                sys.path.insert(0, str(current_dir))
                from web_display.data_exporter import DataExporter
                data_exporter = DataExporter
            except ImportError:
                pass
        
        # 方式3：直接执行脚本
        if data_exporter is None:
            print("使用脚本方式生成数据...")
            return generate_web_data_by_script(violations_data)
        
        # 使用DataExporter生成数据
        # 确定正确的VIOLATION_CHECK路径
        violation_check_dir = "VIOLATION_CHECK"
        if not Path(violation_check_dir).exists():
            # 尝试相对于项目根目录的路径
            violation_check_dir = "../../../VIOLATION_CHECK"
            if not Path(violation_check_dir).exists():
                violation_check_dir = "VIOLATION_CHECK"  # 回退到默认路径
        
        exporter = data_exporter(
            violation_check_dir=violation_check_dir,
            enable_performance_monitoring=True
        )
        
        success = exporter.export_all_data(violations_data)
        return success
        
    except Exception as e:
        print(f"生成网页数据失败: {e}")
        return generate_web_data_by_script(violations_data)


def generate_web_data_by_script(violations_data=None):
    """通过脚本方式生成网页数据"""
    try:
        # 如果有violations_data，先保存到临时文件
        temp_data_file = None
        if violations_data:
            import json
            temp_data_file = Path("temp_violations_data.json")
            with open(temp_data_file, 'w', encoding='utf-8') as f:
                json.dump(violations_data, f, ensure_ascii=False, indent=2)
        
        # 查找数据生成脚本
        scripts_to_try = [
            Path("plugins/user/timing_violation/generate_optimized_web_data.py"),
            Path("generate_optimized_web_data.py"),
            Path("quick_start.py")
        ]
        
        for script_path in scripts_to_try:
            if script_path.exists():
                print(f"使用脚本生成数据: {script_path}")
                
                # 执行脚本
                result = subprocess.run([
                    sys.executable, str(script_path)
                ], capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    # 清理临时文件
                    if temp_data_file and temp_data_file.exists():
                        temp_data_file.unlink()
                    return True
                else:
                    print(f"脚本执行失败: {result.stderr}")
        
        # 清理临时文件
        if temp_data_file and temp_data_file.exists():
            temp_data_file.unlink()
        
        return False
        
    except Exception as e:
        print(f"脚本方式生成数据失败: {e}")
        return False


def start_web_server_for_gui(port=8000):
    """为GUI启动Web服务器"""
    try:
        # 查找Web服务器脚本
        server_scripts = [
            Path("plugins/user/timing_violation/start_web_server.py"),
            Path("start_web_server.py")
        ]
        
        server_script = None
        for script in server_scripts:
            if script.exists():
                server_script = script
                break
        
        if not server_script:
            print("找不到Web服务器脚本")
            return None
        
        # 启动服务器进程
        process = subprocess.Popen([
            sys.executable, str(server_script),
            "--port", str(port),
            "--no-browser"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        time.sleep(2)
        
        if process.poll() is None:
            return process
        else:
            print("Web服务器启动失败")
            return None
            
    except Exception as e:
        print(f"启动Web服务器失败: {e}")
        return None


def open_web_browser(port=8000):
    """打开Web浏览器"""
    try:
        # 尝试多个URL
        urls_to_try = [
            f"http://localhost:{port}/index.html",
            f"http://localhost:{port}",
            f"http://localhost:{port}/offline.html"
        ]
        
        for url in urls_to_try:
            try:
                webbrowser.open(url)
                print(f"已打开浏览器: {url}")
                return True
            except Exception as e:
                print(f"打开 {url} 失败: {e}")
                continue
        
        return False
    except Exception as e:
        print(f"打开浏览器失败: {e}")
        return False


def check_web_display_files():
    """检查网页展示文件是否存在"""
    try:
        # 使用正确的路径
        violation_check_dir = "VIOLATION_CHECK"
        if not Path(violation_check_dir).exists():
            violation_check_dir = "../../../VIOLATION_CHECK"
        
        web_dir = Path(violation_check_dir) / "web_display"
        if not web_dir.exists():
            return False, "web_display目录不存在"
        
        required_files = [
            "data/violations.json",
            "data/index.json"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (web_dir / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            return False, f"缺少文件: {', '.join(missing_files)}"
        
        return True, "文件检查通过"
        
    except Exception as e:
        return False, f"文件检查失败: {e}"


def launch_web_display_for_gui(violations_data=None, port=8000, auto_open_browser=True):
    """为GUI启动完整的Web展示功能"""
    try:
        print("正在生成网页数据...")
        if not generate_web_data_for_gui(violations_data):
            return None, "数据生成失败"
        
        # 检查生成的文件
        files_ok, file_message = check_web_display_files()
        if not files_ok:
            return None, f"文件检查失败: {file_message}"
        
        print("正在启动Web服务器...")
        process = start_web_server_for_gui(port)
        if not process:
            # 如果服务器启动失败，尝试使用不同端口重新启动
            print("Web服务器启动失败，尝试使用其他端口...")
            
            # 尝试其他端口
            alternative_ports = [8001, 8002, 8003, 8004, 8005, 8007, 8008, 8009]
            for alt_port in alternative_ports:
                print(f"尝试端口 {alt_port}...")
                alt_process = start_web_server_for_gui(alt_port)
                if alt_process:
                    print(f"成功在端口 {alt_port} 启动服务器")
                    if auto_open_browser:
                        open_web_browser(alt_port)
                    return alt_process, f"Web展示已启动: http://localhost:{alt_port}/index.html"
            
            # 如果所有端口都失败，提供手动启动指导
            return None, ("所有端口都被占用，无法启动Web服务器。\n\n"
                         "请手动执行以下命令启动服务器：\n"
                         "python plugins/user/timing_violation/start_web_server.py --port 8010\n\n"
                         "然后访问: http://localhost:8010/index.html")
        
        if auto_open_browser:
            print("正在打开浏览器...")
            open_web_browser(port)
        
        return process, f"Web展示已启动: http://localhost:{port}/index.html"
        
    except Exception as e:
        return None, f"启动失败: {str(e)}"


def simple_data_export_for_gui(violations_data):
    """简单的数据导出方法，直接生成JSON文件"""
    try:
        import json
        from datetime import datetime
        
        # 确保目录存在 - 使用正确的路径
        violation_check_dir = "VIOLATION_CHECK"
        if not Path(violation_check_dir).exists():
            violation_check_dir = "../../../VIOLATION_CHECK"
        
        web_dir = Path(violation_check_dir) / "web_display"
        data_dir = web_dir / "data"
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # 处理违例数据
        if not violations_data:
            violations_data = []
        
        # 提取corners和cases
        corners = set()
        cases = set()
        for violation in violations_data:
            if violation.get('corner'):
                corners.add(violation['corner'])
            if violation.get('case') or violation.get('case_name'):
                case_name = violation.get('case') or violation.get('case_name')
                cases.add(case_name)
        
        corners = sorted(list(corners))
        cases = sorted(list(cases))
        
        # 生成violations.json
        violations_data_export = {
            'metadata': {
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_violations': len(violations_data),
                'total_corners': len(corners),
                'total_cases': len(cases),
                'data_sources': ['gui']
            },
            'corners': corners,
            'cases': cases,
            'violations': violations_data
        }
        
        with open(data_dir / 'violations.json', 'w', encoding='utf-8') as f:
            json.dump(violations_data_export, f, ensure_ascii=False, indent=2)
        
        # 生成index.json
        index_data = {
            'metadata': violations_data_export['metadata'],
            'corners': corners,
            'cases': cases,
            'summary': {
                'total_violations': len(violations_data),
                'confirmed_violations': len([v for v in violations_data if v.get('status', '').lower() == 'confirmed']),
                'pending_violations': len([v for v in violations_data if v.get('status', '').lower() != 'confirmed']),
                'confirmation_rate': 0.0
            }
        }
        
        if len(violations_data) > 0:
            confirmed_count = len([v for v in violations_data if v.get('status', '').lower() == 'confirmed'])
            index_data['summary']['confirmation_rate'] = (confirmed_count / len(violations_data)) * 100
        
        with open(data_dir / 'index.json', 'w', encoding='utf-8') as f:
            json.dump(index_data, f, ensure_ascii=False, indent=2)
        
        print(f"简单数据导出完成: {len(violations_data)} 条违例")
        return True
        
    except Exception as e:
        print(f"简单数据导出失败: {e}")
        return False


if __name__ == "__main__":
    # 命令行使用
    import argparse
    
    parser = argparse.ArgumentParser(description="GUI Web启动器 - 修复版")
    parser.add_argument("--port", type=int, default=8000, help="端口号")
    parser.add_argument("--no-browser", action="store_true", help="不打开浏览器")
    parser.add_argument("--simple", action="store_true", help="使用简单数据导出")
    
    args = parser.parse_args()
    
    if args.simple:
        # 测试简单数据导出
        test_data = [
            {
                'id': 1,
                'num': 1,
                'hier': 'test.signal',
                'time_fs': 1000000,
                'check_info': 'Test violation',
                'status': 'confirmed',
                'confirmer': 'test_user',
                'result': 'pass',
                'reason': 'test',
                'confirmed_at': '2025-08-07',
                'corner': 'test_corner',
                'case': 'test_case',
                'source': 'gui_test'
            }
        ]
        
        if simple_data_export_for_gui(test_data):
            print("简单数据导出测试成功")
        else:
            print("简单数据导出测试失败")
        sys.exit(0)
    
    process, message = launch_web_display_for_gui(
        port=args.port,
        auto_open_browser=not args.no_browser
    )
    
    if process:
        print(message)
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n正在停止服务器...")
            process.terminate()
            process.wait()
    else:
        print(f"启动结果: {message}")
        if "已直接打开文件" in message:
            sys.exit(0)  # 直接打开文件也算成功
        else:
            sys.exit(1)