"""
关于对话框
"""
import sys
import platform
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QTabWidget, QWidget, QScrollArea, QFrame
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont, QPixmap, QIcon


class AboutDialog(QDialog):
    """关于对话框类"""

    def __init__(self, parent=None):
        """初始化关于对话框"""
        super().__init__(parent)
        self.setWindowTitle("关于 RunSim 控制台")
        self.setFixedSize(600, 500)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # 设置对话框样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: white;
            }
            QTabBar::tab {
                font-family: "Microsoft YaHei";
                padding: 8px 15px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                border: 1px solid #d0d0d0;
                background-color: #f5f5f5;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
            }
            QLabel {
                font-family: "Microsoft YaHei";
            }
            QTextEdit {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: white;
                font-family: "Microsoft YaHei";
            }
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 20px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #3d8ced;
            }
            QPushButton:pressed {
                background-color: #3274bf;
            }
        """)
        
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)

        # 创建标签页控件
        tab_widget = QTabWidget()
        
        # 添加各个标签页
        tab_widget.addTab(self.create_general_tab(), "基本信息")
        tab_widget.addTab(self.create_tools_tab(), "工具集成")
        tab_widget.addTab(self.create_changelog_tab(), "更新日志")
        tab_widget.addTab(self.create_system_tab(), "系统信息")
        tab_widget.addTab(self.create_dependencies_tab(), "依赖信息")
        tab_widget.addTab(self.create_license_tab(), "许可证")
        
        layout.addWidget(tab_widget)
        
        # 添加关闭按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)

    def create_general_tab(self):
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 应用程序标题和图标
        title_layout = QHBoxLayout()
        title_layout.setAlignment(Qt.AlignCenter)

        # 应用程序图标（使用文本图标）
        icon_label = QLabel("🖥️")
        icon_label.setFont(QFont("Segoe UI Emoji", 24))
        icon_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(icon_label)

        title_label = QLabel("RunSim 控制台")
        title_font = QFont("Microsoft YaHei", 18, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 10px 0 10px 10px;")
        title_layout.addWidget(title_label)

        layout.addLayout(title_layout)

        # 版本信息
        version_label = QLabel("版本: 2.4.0 (Build 20250805)")
        version_font = QFont("Microsoft YaHei", 12)
        version_label.setFont(version_font)
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        layout.addWidget(version_label)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("color: #bdc3c7;")
        layout.addWidget(line)

        # 开发者信息
        info_layout = QVBoxLayout()
        info_layout.setSpacing(10)

        # 开发者
        dev_label = QLabel("开发者：何佳东")
        dev_label.setFont(QFont("Microsoft YaHei", 11))
        dev_label.setStyleSheet("color: #34495e; font-weight: bold;")
        info_layout.addWidget(dev_label)

        # 邮箱
        email_label = QLabel("📧 邮箱：<EMAIL>")
        email_label.setFont(QFont("Microsoft YaHei", 10))
        email_label.setStyleSheet("color: #7f8c8d;")
        info_layout.addWidget(email_label)

        # 公司
        company_label = QLabel("🏢 公司：紫光展锐 (UNISOC)")
        company_label.setFont(QFont("Microsoft YaHei", 10))
        company_label.setStyleSheet("color: #7f8c8d;")
        info_layout.addWidget(company_label)

        # 更新日期
        update_label = QLabel("📅 最后更新：2024年8月5日")
        update_label.setFont(QFont("Microsoft YaHei", 10))
        update_label.setStyleSheet("color: #7f8c8d;")
        info_layout.addWidget(update_label)

        # 描述
        desc_text = """RunSim 控制台是一个专业的EDA仿真运行管理工具，为IC设计验证提供统一的图形化操作界面。

主要特性：
• 支持多种主流EDA仿真器 (Cadence irun, Synopsys VCS等)
• 智能参数配置和命令生成
• 多任务并行执行和队列管理
• 实时日志监控和性能分析
• 插件化架构支持功能扩展
• 数据看板提供统计分析
• 跨平台支持和现代化UI设计"""

        desc_label = QLabel(desc_text)
        desc_label.setFont(QFont("Microsoft YaHei", 10))
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #2c3e50; margin-top: 15px; line-height: 1.6;")
        info_layout.addWidget(desc_label)

        layout.addLayout(info_layout)
        layout.addStretch()

        return widget

    def create_tools_tab(self):
        """创建工具集成标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # 标题
        title_label = QLabel("集成的EDA工具和流程软件")
        title_font = QFont("Microsoft YaHei", 14, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        scroll_layout.addWidget(title_label)

        # 工具信息
        tools_info = [
            ("仿真器", [
                "Cadence Incisive (irun) - 高性能混合信号仿真器",
                "Synopsys VCS - 业界领先的SystemVerilog仿真器",
                "Mentor ModelSim/QuestaSim - 多语言仿真平台",
                "支持多种仿真器的统一接口管理"
            ]),
            ("综合工具", [
                "Synopsys Design Compiler - 逻辑综合工具",
                "Cadence Genus - 下一代综合平台",
                "支持综合脚本的自动化执行"
            ]),
            ("时序分析", [
                "Synopsys PrimeTime - 静态时序分析工具",
                "Cadence Tempus - 时序签核平台",
                "时序违例检查和分析功能"
            ]),
            ("布局布线", [
                "Cadence Innovus - 数字后端设计平台",
                "Synopsys IC Compiler II - 布局布线工具",
                "支持物理设计流程的集成管理"
            ]),
            ("验证工具", [
                "Cadence JasperGold - 形式化验证平台",
                "Synopsys VC Formal - 形式化验证工具",
                "UVM/OVM 验证方法学支持"
            ]),
            ("调试工具", [
                "Cadence SimVision - 波形查看器",
                "Synopsys Verdi - 调试和分析平台",
                "支持FSDB、VCD等多种波形格式"
            ]),
            ("覆盖率分析", [
                "Cadence IMC - 覆盖率管理平台",
                "Synopsys VCS Coverage - 覆盖率分析工具",
                "支持功能覆盖率和代码覆盖率统计"
            ]),
            ("回归测试", [
                "LSF/SGE - 集群作业管理系统",
                "支持大规模并行回归测试",
                "智能任务调度和资源管理"
            ]),
            ("版本控制", [
                "Git - 分布式版本控制系统",
                "Perforce - 企业级版本控制",
                "支持设计数据的版本管理"
            ]),
            ("项目管理", [
                "支持多项目并行开发",
                "配置模板和环境管理",
                "自动化流程脚本生成"
            ])
        ]

        for category, tools in tools_info:
            # 分类标题
            category_label = QLabel(f"📁 {category}")
            category_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
            category_label.setStyleSheet("color: #34495e; margin: 15px 0 8px 0;")
            scroll_layout.addWidget(category_label)

            # 工具列表
            for tool in tools:
                tool_label = QLabel(f"  • {tool}")
                tool_label.setFont(QFont("Microsoft YaHei", 10))
                tool_label.setStyleSheet("color: #7f8c8d; margin-left: 20px; margin-bottom: 3px;")
                tool_label.setWordWrap(True)
                scroll_layout.addWidget(tool_label)

        # 特色功能
        features_label = QLabel("🚀 特色功能")
        features_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        features_label.setStyleSheet("color: #34495e; margin: 20px 0 8px 0;")
        scroll_layout.addWidget(features_label)

        features = [
            "统一的图形化界面管理多种EDA工具",
            "智能命令生成和参数配置",
            "实时日志监控和错误分析",
            "多任务并行执行和队列管理",
            "插件化架构支持工具扩展",
            "数据看板提供执行统计分析",
            "配置模板简化重复操作",
            "跨平台支持(Windows/Linux)"
        ]

        for feature in features:
            feature_label = QLabel(f"  ✓ {feature}")
            feature_label.setFont(QFont("Microsoft YaHei", 10))
            feature_label.setStyleSheet("color: #27ae60; margin-left: 20px; margin-bottom: 3px;")
            feature_label.setWordWrap(True)
            scroll_layout.addWidget(feature_label)

        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        return widget

    def create_changelog_tab(self):
        """创建更新日志标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        changelog_text = QTextEdit()
        changelog_text.setReadOnly(True)
        changelog_text.setFont(QFont("Consolas", 9))

        changelog_content = """RunSim 控制台 - 版本更新日志

═══════════════════════════════════════════════════════════════

📦 版本 2.0.0 (Build 20240805) - 当前版本
───────────────────────────────────────────────────────────────
🎉 重大更新：
• 全新的关于对话框，提供详细的软件信息
• 集成多种主流EDA工具支持信息
• 优化用户界面和交互体验
• 增强系统信息显示

🔧 功能改进：
• 添加工具集成信息展示
• 完善版本信息和开发者信息
• 优化对话框样式和布局
• 增加更新日志功能

───────────────────────────────────────────────────────────────

📦 版本 1.9.x 系列
───────────────────────────────────────────────────────────────
🚀 主要特性：
• 任务队列系统 - 支持多任务并行执行
• 数据看板 - 提供执行统计和性能分析
• 插件系统 - 支持功能扩展和自定义工具
• 智能自动补全 - 提高配置效率
• 配置模板管理 - 简化重复操作

🔧 技术改进：
• 性能监控和资源管理
• 内存优化和缓存机制
• 异步日志处理
• 跨平台兼容性增强

───────────────────────────────────────────────────────────────

📦 版本 1.8.x 系列
───────────────────────────────────────────────────────────────
🎯 核心功能：
• 多标签页执行管理
• 实时日志监控
• 命令历史记录
• 参数配置管理

🔧 界面优化：
• 现代化UI设计
• 响应式布局
• 主题支持
• 字体优化

───────────────────────────────────────────────────────────────

📦 版本 1.0.x 系列 - 初始版本
───────────────────────────────────────────────────────────────
🎯 基础功能：
• 基本的仿真执行管理
• 简单的参数配置
• 日志输出显示
• 文件管理功能

═══════════════════════════════════════════════════════════════

🔮 未来规划：
• AI辅助调试功能
• 云端协作支持
• 更多EDA工具集成
• 移动端支持
• 国际化多语言支持

═══════════════════════════════════════════════════════════════

💡 反馈建议：
如有任何问题或建议，请联系开发者：
📧 <EMAIL>

感谢您使用 RunSim 控制台！
"""

        changelog_text.setPlainText(changelog_content)
        layout.addWidget(changelog_text)

        return widget

    def create_system_tab(self):
        """创建系统信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # 系统信息
        system_info = [
            ("操作系统", f"{platform.system()} {platform.release()}"),
            ("架构", platform.machine()),
            ("处理器", platform.processor() or "未知"),
            ("Python 版本", f"{sys.version.split()[0]}"),
            ("Python 路径", sys.executable),
            ("PyQt5 版本", self.get_pyqt_version()),
        ]

        for label, value in system_info:
            info_widget = self.create_info_item(label, value)
            scroll_layout.addWidget(info_widget)

        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        return widget

    def create_dependencies_tab(self):
        """创建依赖信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        # 依赖信息文本
        dependencies_text = QTextEdit()
        dependencies_text.setReadOnly(True)
        dependencies_text.setFont(QFont("Consolas", 9))
        
        deps_content = """主要依赖库：

• PyQt5 - GUI框架
  版本: """ + self.get_pyqt_version() + """
  用途: 提供图形用户界面

• Python 标准库
  - os, sys - 系统操作
  - json - 配置文件处理
  - subprocess - 进程管理
  - threading - 多线程支持
  - sqlite3 - 数据库操作

• 可选依赖库
  - psutil - 系统资源监控
  - openpyxl - Excel文件处理
  - matplotlib - 图表绘制

插件系统：
• 支持动态加载插件
• 内置多种实用工具插件
• 支持用户自定义插件开发

特性：
• 现代化的GUI设计
• 多标签页执行管理
• 实时日志监控
• 智能自动补全
• 配置模板管理
• 任务队列系统
• 数据看板分析
• 性能监控
"""
        
        dependencies_text.setPlainText(deps_content)
        layout.addWidget(dependencies_text)

        return widget

    def create_license_tab(self):
        """创建许可证标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        license_text = QTextEdit()
        license_text.setReadOnly(True)
        license_text.setFont(QFont("Consolas", 9))
        
        license_content = """MIT License

Copyright (c) 2024 何佳东 (Jiadong He)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

第三方库许可证：

PyQt5: GPL v3 / Commercial License
Python: PSF License Agreement
其他依赖库请参考各自的许可证条款。

---

免责声明：
本软件仅供学习和研究使用。使用本软件所产生的任何后果，
开发者不承担任何责任。用户应当遵守相关法律法规。
"""
        
        license_text.setPlainText(license_content)
        layout.addWidget(license_text)

        return widget

    def create_info_item(self, label, value):
        """创建信息项控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 5, 0, 5)

        # 标签
        label_widget = QLabel(f"{label}:")
        label_widget.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        label_widget.setStyleSheet("color: #2c3e50; min-width: 100px;")
        label_widget.setAlignment(Qt.AlignTop)
        layout.addWidget(label_widget)

        # 值
        value_widget = QLabel(str(value))
        value_widget.setFont(QFont("Microsoft YaHei", 10))
        value_widget.setStyleSheet("color: #7f8c8d;")
        value_widget.setWordWrap(True)
        value_widget.setAlignment(Qt.AlignTop)
        layout.addWidget(value_widget, 1)

        return widget

    def get_pyqt_version(self):
        """获取PyQt5版本"""
        try:
            from PyQt5.QtCore import QT_VERSION_STR
            return QT_VERSION_STR
        except ImportError:
            return "未知"
