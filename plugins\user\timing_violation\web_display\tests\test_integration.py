"""
Integration tests for timing violation web display.

These tests verify the complete workflow from data loading
to web interface generation.
"""

import unittest
import tempfile
import os
import json
import sqlite3
from pathlib import Path
from unittest.mock import patch, Mock

# Import modules to test
import sys
sys.path.append(str(Path(__file__).parent.parent))

from data_exporter import DataExporter
from parsers.excel_parser import ExcelParser
from parsers.database_reader import DatabaseReader
from utils.file_utils import FileUtils
from utils.date_utils import DateUtils
from utils.validation_utils import ValidationUtils


class TestIntegration(unittest.TestCase):
    """Integration test cases for complete workflow."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.violation_check_dir = os.path.join(self.temp_dir, "VIOLATION_CHECK")
        os.makedirs(self.violation_check_dir)
        
        # Create test database
        self.test_db_path = os.path.join(self.violation_check_dir, "timing_violations.db")
        self.create_test_database()
        
        # Create test Excel files
        self.create_test_excel_files()
        
        self.exporter = DataExporter(self.violation_check_dir)
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_database(self):
        """Create test database with sample data."""
        conn = sqlite3.connect(self.test_db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE timing_violations (
                id INTEGER PRIMARY KEY,
                num INTEGER,
                hier TEXT,
                time_ns REAL,
                check_info TEXT,
                corner TEXT,
                case_name TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE confirmation_records (
                id INTEGER PRIMARY KEY,
                violation_id INTEGER,
                status TEXT,
                confirmer TEXT,
                result TEXT,
                reason TEXT,
                confirmed_at TEXT,
                FOREIGN KEY (violation_id) REFERENCES timing_violations (id)
            )
        ''')
        
        # Insert test data
        test_violations = [
            (1, 'cpu/core/reg1', 1.5, 'setup check', 'ss', 'test1'),
            (2, 'cpu/core/reg2', 2.3, 'hold check', 'ff', 'test2'),
            (3, 'cpu/mem/reg3', 0.8, 'setup check', 'ss', 'test2'),
        ]
        
        for i, (num, hier, time_ns, check_info, corner, case) in enumerate(test_violations, 1):
            cursor.execute('''
                INSERT INTO timing_violations (id, num, hier, time_ns, check_info, corner, case_name)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (i, num, hier, time_ns, check_info, corner, case))
        
        test_confirmations = [
            (1, 'confirmed', 'john_doe', 'false_positive', 'Clock skew issue', '2024-01-15 10:30:00'),
            (2, 'confirmed', 'jane_smith', 'real_violation', 'Timing constraint violation', '2024-01-16 14:20:00'),
            (3, 'confirmed', 'bob_wilson', 'waived', 'Design exception approved', '2024-01-17 09:15:00'),
        ]
        
        for violation_id, status, confirmer, result, reason, confirmed_at in test_confirmations:
            cursor.execute('''
                INSERT INTO confirmation_records (violation_id, status, confirmer, result, reason, confirmed_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (violation_id, status, confirmer, result, reason, confirmed_at))
        
        conn.commit()
        conn.close()
    
    def create_test_excel_files(self):
        """Create test Excel files (mock structure)."""
        # Create directory structure for Excel files
        excel_dirs = [
            os.path.join(self.violation_check_dir, "corner_ss", "case_test1"),
            os.path.join(self.violation_check_dir, "corner_ff", "case_test2"),
        ]
        
        for excel_dir in excel_dirs:
            os.makedirs(excel_dir, exist_ok=True)
            
            # Create mock Excel file (we'll mock the actual parsing)
            excel_file = os.path.join(excel_dir, "violations.xlsx")
            Path(excel_file).touch()
    
    def test_complete_workflow_with_database(self):
        """Test complete workflow using database as data source."""
        # Mock Excel parser to return empty results (force database fallback)
        with patch.object(ExcelParser, 'parse_directory', return_value=[]):
            result = self.exporter.export_all_data()
        
        self.assertTrue(result)
        
        # Verify data was loaded
        self.assertGreater(len(self.exporter.violations_data), 0)
        self.assertGreater(len(self.exporter.corners), 0)
        self.assertGreater(len(self.exporter.cases), 0)
        
        # Verify output files were created
        web_display_dir = Path(self.violation_check_dir) / "web_display"
        self.assertTrue(web_display_dir.exists())
        
        data_dir = web_display_dir / "data"
        self.assertTrue(data_dir.exists())
        
        # Check index file
        index_file = data_dir / "index.json"
        self.assertTrue(index_file.exists())
        
        with open(index_file, 'r') as f:
            index_data = json.load(f)
        
        self.assertIn('metadata', index_data)
        self.assertIn('corners', index_data)
        self.assertIn('cases', index_data)
        self.assertIn('statistics', index_data)
        
        # Verify corners and cases directories
        corners_dir = data_dir / "corners"
        violations_dir = data_dir / "violations"
        self.assertTrue(corners_dir.exists())
        self.assertTrue(violations_dir.exists())
    
    @patch('plugins.user.timing_violation.web_display.parsers.excel_parser.OPENPYXL_AVAILABLE', True)
    def test_complete_workflow_with_excel(self):
        """Test complete workflow using Excel files as data source."""
        # Mock Excel parser to return test data
        mock_violations = [
            {
                'num': 1,
                'hier': 'cpu/core/reg1',
                'time_ns': 1.5,
                'check_info': 'setup check',
                'status': 'confirmed',
                'confirmer': 'john_doe',
                'result': 'false_positive',
                'reason': 'Clock skew issue',
                'confirmed_at': '2024-01-15 10:30:00',
                'corner': 'ss',
                'case': 'test1',
                'source': 'excel'
            },
            {
                'num': 2,
                'hier': 'cpu/core/reg2',
                'time_ns': 2.3,
                'check_info': 'hold check',
                'status': 'confirmed',
                'confirmer': 'jane_smith',
                'result': 'real_violation',
                'reason': 'Timing constraint violation',
                'confirmed_at': '2024-01-16 14:20:00',
                'corner': 'ff',
                'case': 'test2',
                'source': 'excel'
            }
        ]
        
        with patch.object(ExcelParser, 'parse_directory', return_value=mock_violations):
            result = self.exporter.export_all_data()
        
        self.assertTrue(result)
        
        # Verify data was loaded from Excel
        self.assertEqual(len(self.exporter.violations_data), 2)
        self.assertTrue(all(v['source'] == 'excel' for v in self.exporter.violations_data))
        
        # Verify web files were created
        web_display_dir = Path(self.violation_check_dir) / "web_display"
        self.assertTrue(web_display_dir.exists())
        
        # Check that HTML file was created
        html_file = web_display_dir / "index.html"
        self.assertTrue(html_file.exists())
    
    def test_data_validation_integration(self):
        """Test data validation integration in workflow."""
        # Load data first
        with patch.object(ExcelParser, 'parse_directory', return_value=[]):
            self.exporter.load_violation_data()
        
        # Validate the loaded data
        validation_report = ValidationUtils.validate_violation_dataset(self.exporter.violations_data)
        
        self.assertIsInstance(validation_report, dict)
        self.assertIn('valid', validation_report)
        self.assertIn('total_records', validation_report)
        self.assertIn('statistics', validation_report)
        
        # Check that all loaded records are valid
        if self.exporter.violations_data:
            self.assertTrue(validation_report['valid'])
            self.assertEqual(validation_report['total_records'], len(self.exporter.violations_data))
    
    def test_file_utilities_integration(self):
        """Test file utilities integration in workflow."""
        # Test directory creation
        test_dir = os.path.join(self.temp_dir, "test_integration")
        FileUtils.ensure_directory(test_dir)
        self.assertTrue(Path(test_dir).exists())
        
        # Test JSON writing and reading
        test_data = {
            'violations': [
                {
                    'num': 1,
                    'corner': 'ss',
                    'case': 'test1',
                    'confirmed_at': DateUtils.get_current_timestamp()
                }
            ]
        }
        
        json_file = os.path.join(test_dir, "test_data.json")
        FileUtils.write_json(test_data, json_file)
        self.assertTrue(Path(json_file).exists())
        
        loaded_data = FileUtils.read_json(json_file)
        self.assertEqual(loaded_data, test_data)
        
        # Test compressed JSON
        compressed_file = os.path.join(test_dir, "test_data_compressed.json.gz")
        FileUtils.write_json(test_data, compressed_file, compressed=True)
        self.assertTrue(Path(compressed_file).exists())
        
        loaded_compressed = FileUtils.read_json(compressed_file, compressed=True)
        self.assertEqual(loaded_compressed, test_data)
    
    def test_date_utilities_integration(self):
        """Test date utilities integration in workflow."""
        # Test date parsing and formatting in context
        test_date_string = "2024-01-15 10:30:45"
        
        parsed_date = DateUtils.parse_date(test_date_string)
        self.assertIsNotNone(parsed_date)
        
        # Test formatting for web display
        web_formatted = DateUtils.format_date_for_web(parsed_date)
        self.assertEqual(web_formatted, test_date_string)
        
        # Test formatting for filename
        filename_formatted = DateUtils.format_date_for_filename(parsed_date)
        self.assertEqual(filename_formatted, "20240115_103045")
        
        # Test relative time
        relative_time = DateUtils.get_relative_time_string(parsed_date)
        self.assertIsInstance(relative_time, str)
        self.assertGreater(len(relative_time), 0)
    
    def test_error_handling_integration(self):
        """Test error handling throughout the workflow."""
        # Test with invalid violation check directory
        invalid_exporter = DataExporter("/nonexistent/path")
        
        result = invalid_exporter.export_all_data()
        self.assertFalse(result)
        
        # Test with corrupted database
        corrupted_db = os.path.join(self.temp_dir, "corrupted.db")
        with open(corrupted_db, 'w') as f:
            f.write("not a database")
        
        reader = DatabaseReader(corrupted_db)
        with self.assertRaises(Exception):
            with reader:
                reader.get_confirmed_violations()
    
    def test_performance_with_large_dataset(self):
        """Test performance with larger dataset."""
        # Create larger test dataset
        large_violations = []
        for i in range(1000):
            violation = {
                'num': i + 1,
                'hier': f'cpu/core/reg{i}',
                'time_ns': 1.0 + (i * 0.1),
                'check_info': 'setup check',
                'status': 'confirmed',
                'confirmer': f'user_{i % 10}',
                'result': 'false_positive' if i % 2 == 0 else 'real_violation',
                'reason': f'Test reason {i}',
                'confirmed_at': '2024-01-15 10:30:00',
                'corner': 'ss' if i % 2 == 0 else 'ff',
                'case': f'test{i % 5}',
                'source': 'test'
            }
            large_violations.append(violation)
        
        # Test validation performance
        import time
        start_time = time.time()
        
        validation_report = ValidationUtils.validate_violation_dataset(large_violations)
        
        validation_time = time.time() - start_time
        
        # Should complete validation in reasonable time (< 5 seconds for 1000 records)
        self.assertLess(validation_time, 5.0)
        self.assertTrue(validation_report['valid'])
        self.assertEqual(validation_report['total_records'], 1000)
    
    def test_web_template_integration(self):
        """Test web template integration."""
        # Mock successful data export
        with patch.object(ExcelParser, 'parse_directory', return_value=[]):
            result = self.exporter.export_all_data()
        
        self.assertTrue(result)
        
        # Check that web template files were copied
        web_display_dir = Path(self.violation_check_dir) / "web_display"
        
        expected_files = [
            "index.html",
            "css/bootstrap.min.css",
            "css/custom.css",
            "js/app.js",
            "js/bootstrap.min.js",
            "js/jquery.min.js",
            "js/datatables.min.js"
        ]
        
        for expected_file in expected_files:
            file_path = web_display_dir / expected_file
            self.assertTrue(file_path.exists(), f"Missing file: {expected_file}")
    
    def test_data_consistency_integration(self):
        """Test data consistency checking integration."""
        # Load data
        with patch.object(ExcelParser, 'parse_directory', return_value=[]):
            self.exporter.load_violation_data()
        
        if self.exporter.violations_data:
            # Check data consistency
            consistency_report = ValidationUtils.check_data_consistency(self.exporter.violations_data)
            
            self.assertIsInstance(consistency_report, dict)
            self.assertIn('consistent', consistency_report)
            self.assertIn('statistics', consistency_report)
            
            # Verify statistics are populated
            stats = consistency_report['statistics']
            self.assertIn('corner_case_combinations', stats)
            self.assertIn('confirmer_patterns', stats)
            self.assertIn('status_distribution', stats)
    
    def test_export_info_integration(self):
        """Test export information integration."""
        export_info = self.exporter.get_export_info()
        
        self.assertIsInstance(export_info, dict)
        self.assertIn('violation_check_dir', export_info)
        self.assertIn('web_display_dir', export_info)
        self.assertIn('data_dir', export_info)
        self.assertIn('violations_loaded', export_info)
        self.assertIn('corners_found', export_info)
        self.assertIn('cases_found', export_info)
        
        # After loading data, counts should be updated
        with patch.object(ExcelParser, 'parse_directory', return_value=[]):
            self.exporter.load_violation_data()
        
        updated_info = self.exporter.get_export_info()
        if self.exporter.violations_data:
            self.assertGreater(updated_info['violations_loaded'], 0)
            self.assertGreater(updated_info['corners_found'], 0)
            self.assertGreater(updated_info['cases_found'], 0)


if __name__ == '__main__':
    unittest.main()