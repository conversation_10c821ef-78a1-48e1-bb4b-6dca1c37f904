#!/usr/bin/env python3
"""
集成测试

测试完整的GUI工作流程，从文件加载到字段编辑
"""

import unittest
import sys
import os
import tempfile
import time
from unittest.mock import Mock, patch, MagicMock

# 添加路径以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtTest import QTest

from main_window import RegisterParserMainWindow
from models import HeaderInfo, FieldInfo, RegisterInfo, RegisterTableData, NumberFormat
from widgets import RegisterListWidget, FieldEditorWidget


class TestIntegrationWorkflows(unittest.TestCase):
    """测试完整工作流程集成"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        # 创建QApplication实例
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置每个测试"""
        # 创建主窗口
        self.main_window = RegisterParserMainWindow()
        
        # 创建测试数据
        self.test_header = HeaderInfo(
            project_name="测试项目",
            sub_system="测试子系统",
            module_name="测试模块",
            base_addr="0x1000"
        )
        
        self.test_fields = [
            FieldInfo("FIELD_A", "31:24", "RW", "0x12", "字段A"),
            FieldInfo("Reserved", "23:16", "RO", "0x00", "保留字段"),
            FieldInfo("FIELD_B", "15:8", "RO", "0x34", "字段B"),
            FieldInfo("FIELD_C", "7:0", "RW", "0x56", "字段C"),
        ]
        
        self.test_register = RegisterInfo(
            offset="0x0000",
            name="TEST_REG",
            description="测试寄存器",
            width=32,
            fields=self.test_fields
        )
        
        self.test_table_data = RegisterTableData(
            header=self.test_header,
            registers=[self.test_register]
        )
    
    def tearDown(self):
        """清理每个测试"""
        if self.main_window:
            self.main_window.close()
            self.main_window = None
    
    def test_complete_file_loading_workflow(self):
        """测试完整的文件加载工作流程"""
        # 1. 初始状态验证
        self.assertFalse(self.main_window.content_splitter.isEnabled())
        self.assertEqual(self.main_window.project_label.text(), "-")
        
        # 2. 模拟文件加载成功
        self.main_window.table_data = self.test_table_data
        self.main_window.update_ui_with_data()
        
        # 3. 验证UI更新
        self.assertTrue(self.main_window.content_splitter.isEnabled())
        self.assertEqual(self.main_window.project_label.text(), "测试项目")
        self.assertEqual(self.main_window.subsystem_label.text(), "测试子系统")
        self.assertEqual(self.main_window.module_label.text(), "测试模块")
        self.assertEqual(self.main_window.baseaddr_label.text(), "0x1000")
        
        # 4. 验证寄存器列表更新
        register_list = self.main_window.register_list_widget
        self.assertIsNotNone(register_list.table_data)
        self.assertEqual(len(register_list.table_data.registers), 1)
    
    def test_register_selection_workflow(self):
        """测试寄存器选择工作流程"""
        # 1. 设置测试数据
        self.main_window.table_data = self.test_table_data
        self.main_window.update_ui_with_data()
        
        # 2. 模拟寄存器选择
        register_list = self.main_window.register_list_widget
        field_editor = self.main_window.field_editor
        
        # 触发寄存器选择
        register_list.register_selected.emit(self.test_register)
        
        # 3. 验证字段编辑器更新
        self.assertEqual(field_editor.current_register, self.test_register)
        
        # 4. 验证非保留字段显示
        non_reserved_fields = self.test_register.non_reserved_fields
        self.assertEqual(len(non_reserved_fields), 3)  # 排除Reserved字段
        
        # 5. 验证可编辑字段
        writable_fields = self.test_register.writable_fields
        self.assertEqual(len(writable_fields), 2)  # FIELD_A和FIELD_C
    
    def test_field_editing_workflow(self):
        """测试字段编辑工作流程"""
        # 1. 设置初始状态
        self.main_window.table_data = self.test_table_data
        self.main_window.update_ui_with_data()
        
        field_editor = self.main_window.field_editor
        field_editor.set_register(self.test_register)
        
        # 2. 模拟字段值修改
        new_value = 0xAB
        field_editor.set_field_value("FIELD_A", new_value)
        
        # 3. 验证字段值更新
        stored_value = field_editor.get_field_value("FIELD_A")
        self.assertEqual(stored_value, new_value)
        
        # 4. 验证寄存器值重新计算
        expected_register_value = (new_value << 24) | (0x34 << 8) | 0x56
        calculated_value = field_editor.calculate_register_value()
        self.assertEqual(calculated_value, expected_register_value)
    
    def test_number_format_conversion_workflow(self):
        """测试数字格式转换工作流程"""
        # 1. 设置初始状态
        self.main_window.table_data = self.test_table_data
        self.main_window.update_ui_with_data()
        
        field_editor = self.main_window.field_editor
        field_editor.set_register(self.test_register)
        
        # 2. 验证默认格式（十六进制）
        self.assertEqual(field_editor.current_format, NumberFormat.HEXADECIMAL)
        
        # 3. 测试格式切换
        formats_to_test = [
            (NumberFormat.DECIMAL, "18"),      # 0x12 = 18
            (NumberFormat.BINARY, "0b00010010"),  # 0x12 = 0b00010010
            (NumberFormat.HEXADECIMAL, "0x12")
        ]
        
        for format_type, expected_display in formats_to_test:
            # 切换格式
            field_editor.current_format = format_type
            field_editor.update_all_field_displays()
            
            # 验证显示更新（如果有输入控件的话）
            if hasattr(field_editor, 'field_inputs') and "FIELD_A" in field_editor.field_inputs:
                input_widget = field_editor.field_inputs["FIELD_A"]
                # 这里可以验证显示格式，但需要具体的实现
    
    def test_search_functionality_workflow(self):
        """测试搜索功能工作流程"""
        # 1. 创建多个寄存器用于搜索测试
        registers = [
            RegisterInfo("0x0000", "CTRL_REG", "控制寄存器", 32, []),
            RegisterInfo("0x0004", "STATUS_REG", "状态寄存器", 32, []),
            RegisterInfo("0x0008", "DATA_REG", "数据寄存器", 32, []),
        ]
        
        table_data = RegisterTableData(self.test_header, registers)
        
        # 2. 设置测试数据
        self.main_window.table_data = table_data
        self.main_window.update_ui_with_data()
        
        register_list = self.main_window.register_list_widget
        
        # 3. 测试按名称搜索
        if hasattr(register_list, 'search_input'):
            # 模拟搜索输入
            register_list.search_input.setText("CTRL")
            register_list.filter_registers()
            
            # 验证搜索结果（需要具体实现来验证）
        
        # 4. 测试按地址搜索
        if hasattr(register_list, 'search_input'):
            register_list.search_input.setText("0x0004")
            register_list.filter_registers()
            
            # 验证搜索结果
    
    def test_error_handling_workflow(self):
        """测试错误处理工作流程"""
        # 1. 测试无效文件加载
        with patch.object(self.main_window, 'parse_excel_file') as mock_parse:
            mock_parse.side_effect = Exception("文件解析失败")
            
            # 模拟文件加载失败
            self.main_window.current_file_path = "invalid_file.xlsx"
            
            # 验证错误处理（不应该崩溃）
            try:
                self.main_window.refresh_data()
            except Exception:
                self.fail("错误处理失败，应用程序崩溃")
        
        # 2. 测试无效字段值输入
        field_editor = self.main_window.field_editor
        field_editor.set_register(self.test_register)
        
        # 尝试设置无效值
        try:
            field_editor.set_field_value("FIELD_A", -1)  # 负值
        except ValueError:
            pass  # 期望的行为
        
        try:
            field_editor.set_field_value("FIELD_A", 0x100)  # 超出8位范围
        except ValueError:
            pass  # 期望的行为
    
    def test_performance_with_large_dataset(self):
        """测试大数据集性能"""
        # 1. 创建大量寄存器数据
        large_registers = []
        for i in range(100):  # 100个寄存器
            fields = []
            for j in range(4):  # 每个寄存器4个字段
                field = FieldInfo(
                    name=f"FIELD_{i}_{j}",
                    bit_range=f"{j*8+7}:{j*8}",
                    rw_attribute="RW",
                    reset_value="0x00"
                )
                fields.append(field)
            
            register = RegisterInfo(
                offset=f"0x{i*4:04X}",
                name=f"REG_{i}",
                description=f"寄存器{i}",
                width=32,
                fields=fields
            )
            large_registers.append(register)
        
        large_table_data = RegisterTableData(self.test_header, large_registers)
        
        # 2. 测试加载性能
        start_time = time.time()
        self.main_window.table_data = large_table_data
        self.main_window.update_ui_with_data()
        load_time = time.time() - start_time
        
        # 3. 验证性能要求
        self.assertLess(load_time, 2.0, f"大数据集加载时间过长: {load_time:.2f}秒")
        
        # 4. 测试寄存器选择性能
        start_time = time.time()
        for i in range(10):  # 选择10个不同的寄存器
            register = large_registers[i]
            self.main_window.field_editor.set_register(register)
        selection_time = time.time() - start_time
        
        self.assertLess(selection_time, 1.0, f"寄存器选择时间过长: {selection_time:.2f}秒")
    
    def test_memory_usage_optimization(self):
        """测试内存使用优化"""
        import psutil
        import os
        
        # 1. 获取初始内存使用
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 2. 加载大量数据
        large_registers = []
        for i in range(500):  # 500个寄存器
            fields = [
                FieldInfo(f"FIELD_{i}_{j}", f"{j*8+7}:{j*8}", "RW", "0x00")
                for j in range(4)
            ]
            register = RegisterInfo(f"0x{i*4:04X}", f"REG_{i}", f"寄存器{i}", 32, fields)
            large_registers.append(register)
        
        large_table_data = RegisterTableData(self.test_header, large_registers)
        self.main_window.table_data = large_table_data
        self.main_window.update_ui_with_data()
        
        # 3. 获取加载后内存使用
        loaded_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = loaded_memory - initial_memory
        
        # 4. 验证内存使用合理
        self.assertLess(memory_increase, 100, f"内存使用过多: {memory_increase:.2f}MB")
        
        # 5. 清理数据
        self.main_window.table_data = None
        
        # 强制垃圾回收
        import gc
        gc.collect()
    
    def test_ui_responsiveness(self):
        """测试UI响应性"""
        # 1. 设置测试数据
        self.main_window.table_data = self.test_table_data
        self.main_window.update_ui_with_data()
        
        # 2. 测试UI操作响应时间
        operations = [
            lambda: self.main_window.register_list_widget.register_selected.emit(self.test_register),
            lambda: self.main_window.field_editor.set_field_value("FIELD_A", 0xFF),
            lambda: self.main_window.field_editor.update_register_value(),
        ]
        
        for operation in operations:
            start_time = time.time()
            operation()
            # 处理事件循环
            QApplication.processEvents()
            operation_time = time.time() - start_time
            
            # UI操作应该在100ms内完成
            self.assertLess(operation_time, 0.1, f"UI操作响应时间过长: {operation_time:.3f}秒")
    
    def test_window_lifecycle(self):
        """测试窗口生命周期"""
        # 1. 测试窗口显示
        self.main_window.show()
        self.assertTrue(self.main_window.isVisible())
        
        # 2. 测试窗口隐藏
        self.main_window.hide()
        self.assertFalse(self.main_window.isVisible())
        
        # 3. 测试窗口关闭
        self.main_window.close()
        
        # 4. 验证资源清理
        # 这里可以添加更多的资源清理验证


class TestPluginIntegration(unittest.TestCase):
    """测试插件集成"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def test_plugin_initialization(self):
        """测试插件初始化"""
        from register_table_parser_plugin import RegisterTableParserPlugin
        
        # 1. 创建插件实例
        plugin = RegisterTableParserPlugin()
        
        # 2. 验证插件属性
        self.assertEqual(plugin.name, "寄存器表格解析器")
        self.assertEqual(plugin.version, "1.0.0")
        self.assertIsNotNone(plugin.description)
        
        # 3. 模拟主窗口
        mock_main_window = Mock()
        mock_main_window.tools_menu = Mock()
        
        # 4. 测试插件初始化
        plugin.initialize(mock_main_window)
        
        # 5. 验证菜单项添加
        mock_main_window.tools_menu.addAction.assert_called_once()
        
        # 6. 测试插件清理
        plugin.cleanup()
    
    def test_plugin_menu_integration(self):
        """测试插件菜单集成"""
        from register_table_parser_plugin import RegisterTableParserPlugin
        
        plugin = RegisterTableParserPlugin()
        
        # 模拟主窗口和菜单
        mock_main_window = Mock()
        mock_tools_menu = Mock()
        mock_main_window.tools_menu = mock_tools_menu
        
        # 初始化插件
        plugin.initialize(mock_main_window)
        
        # 验证菜单动作创建
        self.assertIsNotNone(plugin.menu_action)
        
        # 模拟菜单点击
        plugin._on_menu_triggered()
        
        # 验证窗口创建
        self.assertIsNotNone(plugin.parser_window)
    
    def test_plugin_window_management(self):
        """测试插件窗口管理"""
        from register_table_parser_plugin import RegisterTableParserPlugin
        
        plugin = RegisterTableParserPlugin()
        
        # 模拟主窗口
        mock_main_window = Mock()
        mock_main_window.tools_menu = Mock()
        mock_main_window.active_plugin_windows = []
        
        plugin.initialize(mock_main_window)
        
        # 测试窗口创建和显示
        plugin._on_menu_triggered()
        self.assertIsNotNone(plugin.parser_window)
        
        # 测试窗口关闭
        plugin._on_window_closed()
        self.assertIsNone(plugin.parser_window)
        
        # 测试插件清理
        plugin.cleanup()


if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTest(unittest.makeSuite(TestIntegrationWorkflows))
    suite.addTest(unittest.makeSuite(TestPluginIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print(f"\n✅ 所有集成测试通过！运行了 {result.testsRun} 个测试")
    else:
        print(f"\n❌ 集成测试失败！{len(result.failures)} 个失败，{len(result.errors)} 个错误")
        sys.exit(1)