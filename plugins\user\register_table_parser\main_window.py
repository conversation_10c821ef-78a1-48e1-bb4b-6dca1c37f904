"""
寄存器表格解析器主窗口

提供用户界面，包括：
- 文件加载功能
- 表头信息显示
- 寄存器列表显示
- 字段编辑器
- 数字格式转换
"""

import os
import sys
from typing import Optional, Dict, Any
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFileDialog, QMessageBox, QSplitter, QGroupBox, QTextEdit,
    QProgressBar, QFrame, QGridLayout
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon

from plugins.base import NonModalDialog
from .models import RegisterTableData, ParseError, ValidationError
from .parser import ExcelTableParser
from .widgets import RegisterListWidget


class ParseWorker(QThread):
    """解析工作线程"""
    
    # 信号定义
    parse_finished = pyqtSignal(object)  # RegisterTableData
    parse_error = pyqtSignal(str)  # 错误消息
    parse_progress = pyqtSignal(str)  # 进度消息
    parse_progress_value = pyqtSignal(int)  # 进度值 (0-100)
    
    def __init__(self, file_path: str):
        super().__init__()
        self.file_path = file_path
        self.parser = ExcelTableParser()
    
    def run(self):
        """运行解析任务"""
        try:
            self.parse_progress.emit("正在打开Excel文件...")
            self.parse_progress_value.emit(10)
            
            self.parse_progress.emit("正在验证文件格式...")
            self.parse_progress_value.emit(30)
            
            self.parse_progress.emit("正在解析表头信息...")
            self.parse_progress_value.emit(50)
            
            self.parse_progress.emit("正在解析寄存器数据...")
            self.parse_progress_value.emit(70)
            
            table_data = self.parser.parse_register_table(self.file_path)
            
            self.parse_progress.emit("解析完成")
            self.parse_progress_value.emit(100)
            
            self.parse_finished.emit(table_data)
        except (ParseError, ValidationError) as e:
            self.parse_error.emit(str(e))
        except Exception as e:
            self.parse_error.emit(f"解析失败: {str(e)}")


class RegisterParserMainWindow(NonModalDialog):
    """寄存器表格解析器主窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent, "寄存器表格解析器")
        
        # 数据
        self.table_data: Optional[RegisterTableData] = None
        self.current_file_path: Optional[str] = None
        self.last_directory: Optional[str] = None
        
        # 工作线程
        self.parse_worker: Optional[ParseWorker] = None
        
        # 初始化UI
        self.init_ui()
        self.setup_connections()
        
        # 设置窗口属性
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # 启用窗口最大化功能
        self.setWindowFlags(self.windowFlags() | Qt.WindowMaximizeButtonHint)
    
    def init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(6)
        
        # 工具栏
        toolbar_layout = self.create_toolbar()
        main_layout.addLayout(toolbar_layout)
        
        # 进度条（初始隐藏）
        self.progress_widget = self.create_progress_widget()
        main_layout.addWidget(self.progress_widget)
        self.progress_widget.setVisible(False)
        
        # 表头信息面板
        self.header_panel = self.create_header_panel()
        main_layout.addWidget(self.header_panel)
        
        # 设置表头面板在布局中不拉伸
        main_layout.setStretchFactor(self.header_panel, 0)
        
        # 内容区域（分割器）
        self.content_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(self.content_splitter)
        
        # 设置内容区域在布局中获得最大拉伸空间
        main_layout.setStretchFactor(self.content_splitter, 1)
        
        # 左侧面板（寄存器列表）
        self.left_panel = self.create_left_panel()
        self.content_splitter.addWidget(self.left_panel)
        
        # 右侧面板（字段编辑器）
        self.right_panel = self.create_right_panel()
        self.content_splitter.addWidget(self.right_panel)
        
        # 设置分割器比例
        self.content_splitter.setSizes([400, 600])
        
        # 状态栏
        self.status_label = QLabel("请选择Excel文件开始解析")
        self.status_label.setStyleSheet("""
            QLabel { 
                padding: 2px 6px; 
                background-color: #f0f0f0; 
                border: 1px solid #d0d0d0; 
                font-size: 10px;
                max-height: 16px;
            }
        """)
        main_layout.addWidget(self.status_label)
        
        # 设置状态栏在布局中不拉伸
        main_layout.setStretchFactor(self.status_label, 0)
        
        # 初始状态
        self.set_ui_enabled(False)
    
    def create_toolbar(self) -> QHBoxLayout:
        """创建工具栏"""
        toolbar_layout = QHBoxLayout()
        
        # 加载文件按钮
        self.load_button = QPushButton("📁 加载Excel文件")
        self.load_button.setMinimumHeight(35)
        self.load_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        toolbar_layout.addWidget(self.load_button)
        
        # 刷新按钮
        self.refresh_button = QPushButton("🔄 刷新")
        self.refresh_button.setMinimumHeight(35)
        self.refresh_button.setEnabled(False)
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        toolbar_layout.addWidget(self.refresh_button)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        toolbar_layout.addWidget(separator)
        
        # 文件信息标签
        self.file_info_label = QLabel("未加载文件")
        self.file_info_label.setStyleSheet("QLabel { color: #666666; font-style: italic; }")
        toolbar_layout.addWidget(self.file_info_label)
        
        # 弹性空间
        toolbar_layout.addStretch()
        
        # 帮助按钮
        self.help_button = QPushButton("❓ 帮助")
        self.help_button.setMinimumHeight(35)
        self.help_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)
        toolbar_layout.addWidget(self.help_button)
        
        return toolbar_layout
    
    def create_progress_widget(self) -> QWidget:
        """创建进度条控件"""
        progress_widget = QWidget()
        progress_layout = QVBoxLayout(progress_widget)
        progress_layout.setContentsMargins(0, 5, 0, 5)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #d0d0d0;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)
        
        # 进度文本
        self.progress_text = QLabel("准备解析...")
        self.progress_text.setAlignment(Qt.AlignCenter)
        self.progress_text.setStyleSheet("QLabel { color: #666666; font-size: 12px; }")
        progress_layout.addWidget(self.progress_text)
        
        return progress_widget
    
    def create_header_panel(self) -> QGroupBox:
        """创建表头信息面板"""
        header_group = QGroupBox("📋 表头信息")
        # 设置固定高度以限制垂直空间占用
        header_group.setMaximumHeight(100)
        header_group.setMinimumHeight(90)
        header_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 1px solid #d0d0d0;
                border-radius: 3px;
                margin-top: 1px;
                padding-top: 1px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 6px;
                padding: 0 2px 0 2px;
            }
        """)
        
        # 使用网格布局以获得更好的对齐，极度缩小间距
        header_layout = QGridLayout(header_group)
        header_layout.setSpacing(2)  # 从4进一步减少到2
        header_layout.setContentsMargins(6, 4, 6, 2)  # 极度缩小边距
        
        # 创建标签样式，极度缩小padding和高度
        label_style = """
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #333333;
                padding: 1px 3px;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 2px;
                min-height: 12px;
                max-height: 16px;
            }
        """
        
        # 创建标题标签样式，极度紧凑
        title_style = """
            QLabel { 
                font-weight: bold; 
                color: #666666; 
                font-size: 12px;
                padding: 0px 1px;
                max-height: 14px;
            }
        """
        
        # 项目名称
        project_title = QLabel("项目名称:")
        project_title.setStyleSheet(title_style)
        self.project_label = QLabel("-")
        self.project_label.setStyleSheet(label_style)
        header_layout.addWidget(project_title, 0, 0)
        header_layout.addWidget(self.project_label, 0, 1)
        
        # 子系统
        subsystem_title = QLabel("子系统:")
        subsystem_title.setStyleSheet(title_style)
        self.subsystem_label = QLabel("-")
        self.subsystem_label.setStyleSheet(label_style)
        header_layout.addWidget(subsystem_title, 0, 2)
        header_layout.addWidget(self.subsystem_label, 0, 3)
        
        # 模块名称
        module_title = QLabel("模块名称:")
        module_title.setStyleSheet(title_style)
        self.module_label = QLabel("-")
        self.module_label.setStyleSheet(label_style)
        header_layout.addWidget(module_title, 1, 0)
        header_layout.addWidget(self.module_label, 1, 1)
        
        # 基地址
        baseaddr_title = QLabel("基地址:")
        baseaddr_title.setStyleSheet(title_style)
        self.baseaddr_label = QLabel("-")
        self.baseaddr_label.setStyleSheet(label_style)
        header_layout.addWidget(baseaddr_title, 1, 2)
        header_layout.addWidget(self.baseaddr_label, 1, 3)
        
        # 设置列拉伸
        header_layout.setColumnStretch(1, 1)
        header_layout.setColumnStretch(3, 1)
        
        return header_group
    
    def create_left_panel(self) -> QGroupBox:
        """创建左侧面板（寄存器列表）"""
        left_group = QGroupBox("📋 寄存器列表")
        left_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                margin-top: 3px;
                padding-top: 3px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
            }
        """)
        left_layout = QVBoxLayout(left_group)
        left_layout.setContentsMargins(6, 8, 6, 6)
        
        # 寄存器列表控件
        self.register_list_widget = RegisterListWidget()
        left_layout.addWidget(self.register_list_widget)
        
        return left_group
    
    def create_right_panel(self) -> QGroupBox:
        """创建右侧面板（字段编辑器）"""
        right_group = QGroupBox("⚙️ 字段编辑器")
        right_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                margin-top: 3px;
                padding-top: 3px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
            }
        """)
        right_layout = QVBoxLayout(right_group)
        right_layout.setContentsMargins(4, 8, 4, 6)
        
        # 字段编辑器控件
        from .widgets import FieldEditorWidget
        self.field_editor = FieldEditorWidget()
        right_layout.addWidget(self.field_editor)
        
        return right_group
    
    def setup_connections(self):
        """设置信号连接"""
        self.load_button.clicked.connect(self.load_excel_file)
        self.refresh_button.clicked.connect(self.refresh_data)
        self.help_button.clicked.connect(self.show_help)
        
        # 寄存器选择信号
        self.register_list_widget.register_selected.connect(self.on_register_selected)
        
        # 字段编辑器信号
        self.field_editor.register_value_changed.connect(self.on_register_value_changed)
        self.field_editor.field_value_changed.connect(self.on_field_value_changed)
    
    def set_ui_enabled(self, enabled: bool):
        """设置UI启用状态"""
        self.refresh_button.setEnabled(enabled and self.current_file_path is not None)
        self.content_splitter.setEnabled(enabled)
    
    @pyqtSlot()
    def load_excel_file(self):
        """加载Excel文件"""
        file_dialog = QFileDialog(self)
        file_dialog.setWindowTitle("选择寄存器规格表Excel文件")
        file_dialog.setNameFilter("Excel文件 (*.xlsx *.xls);;所有文件 (*)")
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        file_dialog.setViewMode(QFileDialog.Detail)
        
        # 设置默认目录（如果有的话）
        if hasattr(self, 'last_directory') and self.last_directory:
            file_dialog.setDirectory(self.last_directory)
        
        if file_dialog.exec_() == QFileDialog.Accepted:
            file_paths = file_dialog.selectedFiles()
            if file_paths:
                file_path = file_paths[0]
                self.current_file_path = file_path
                
                # 保存目录以便下次使用
                self.last_directory = os.path.dirname(file_path)
                
                # 更新文件信息显示
                file_name = os.path.basename(file_path)
                self.file_info_label.setText(f"文件: {file_name}")
                
                self.parse_excel_file(file_path)
    
    def parse_excel_file(self, file_path: str):
        """解析Excel文件"""
        # 禁用UI
        self.load_button.setEnabled(False)
        self.refresh_button.setEnabled(False)
        self.set_ui_enabled(False)
        
        # 显示进度条
        self.progress_widget.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_text.setText("准备解析...")
        self.status_label.setText("正在解析文件...")
        
        # 创建并启动解析工作线程
        self.parse_worker = ParseWorker(file_path)
        self.parse_worker.parse_finished.connect(self.on_parse_finished)
        self.parse_worker.parse_error.connect(self.on_parse_error)
        self.parse_worker.parse_progress.connect(self.on_parse_progress)
        self.parse_worker.parse_progress_value.connect(self.on_parse_progress_value)
        self.parse_worker.finished.connect(self.on_parse_thread_finished)
        self.parse_worker.start()
    
    @pyqtSlot(object)
    def on_parse_finished(self, table_data: RegisterTableData):
        """解析完成处理"""
        self.table_data = table_data
        self.update_ui_with_data()
        
        # 隐藏进度条
        self.progress_widget.setVisible(False)
        
        # 更新状态
        register_count = table_data.register_count
        field_count = table_data.non_reserved_field_count
        self.status_label.setText(f"✅ 解析完成: {register_count} 个寄存器, {field_count} 个字段")
    
    @pyqtSlot(str)
    def on_parse_error(self, error_message: str):
        """解析错误处理"""
        # 隐藏进度条
        self.progress_widget.setVisible(False)
        
        # 显示错误对话框
        error_dialog = QMessageBox(self)
        error_dialog.setIcon(QMessageBox.Critical)
        error_dialog.setWindowTitle("解析错误")
        error_dialog.setText("Excel文件解析失败")
        error_dialog.setDetailedText(error_message)
        error_dialog.setStandardButtons(QMessageBox.Ok)
        error_dialog.exec_()
        
        self.status_label.setText("❌ 解析失败")
        
        # 重置文件信息
        self.file_info_label.setText("解析失败")
    
    @pyqtSlot(str)
    def on_parse_progress(self, message: str):
        """解析进度更新"""
        self.progress_text.setText(message)
        self.status_label.setText(message)
    
    @pyqtSlot(int)
    def on_parse_progress_value(self, value: int):
        """解析进度值更新"""
        self.progress_bar.setValue(value)
    
    @pyqtSlot()
    def on_parse_thread_finished(self):
        """解析线程结束处理"""
        self.load_button.setEnabled(True)
        self.refresh_button.setEnabled(self.current_file_path is not None)
        self.parse_worker = None
    
    def update_ui_with_data(self):
        """使用解析的数据更新UI"""
        if not self.table_data:
            return
        
        # 更新表头信息（根据需求2.1, 2.2, 2.3, 2.4）
        header = self.table_data.header
        self.project_label.setText(header.project_name)
        self.subsystem_label.setText(header.sub_system)
        self.module_label.setText(header.module_name)
        self.baseaddr_label.setText(header.base_addr)
        
        # 更新寄存器列表控件
        self.register_list_widget.set_table_data(self.table_data)
        
        # 启用UI
        self.set_ui_enabled(True)
    
    @pyqtSlot()
    def refresh_data(self):
        """刷新数据"""
        if self.current_file_path:
            self.parse_excel_file(self.current_file_path)
    
    @pyqtSlot(object)
    def on_register_selected(self, register):
        """寄存器选择处理"""
        # 设置字段编辑器的寄存器
        self.field_editor.set_register(register)
        
        if register:
            # 更新状态栏
            field_count = len(register.non_reserved_fields)
            writable_count = len(register.writable_fields)
            self.status_label.setText(f"✅ 已选择寄存器: {register.name} ({field_count} 个字段, {writable_count} 个可编辑)")
        else:
            # 清空选择
            if self.table_data:
                register_count = self.table_data.register_count
                field_count = self.table_data.non_reserved_field_count
                self.status_label.setText(f"✅ 解析完成: {register_count} 个寄存器, {field_count} 个字段")
    
    @pyqtSlot(int)
    def on_register_value_changed(self, value: int):
        """寄存器值变化处理"""
        # 可以在这里添加额外的处理逻辑，比如记录历史、验证等
        pass
    
    @pyqtSlot(str, int)
    def on_field_value_changed(self, field_name: str, value: int):
        """字段值变化处理"""
        # 可以在这里添加额外的处理逻辑，比如记录历史、验证等
        pass
    
    @pyqtSlot()
    def show_help(self):
        """显示帮助信息"""
        help_dialog = QMessageBox(self)
        help_dialog.setIcon(QMessageBox.Information)
        help_dialog.setWindowTitle("寄存器表格解析器 - 使用帮助")
        help_dialog.setText("寄存器表格解析器使用说明")
        
        help_text = """
<h3>📋 功能概述</h3>
<p>寄存器表格解析器用于解析Excel格式的寄存器规格表，提供可视化显示和交互式字段编辑功能。</p>

<h3>📁 文件加载</h3>
<ul>
<li>点击"📁 加载Excel文件"按钮选择寄存器规格表文件</li>
<li>支持 .xlsx 和 .xls 格式的Excel文件</li>
<li>文件加载后会显示解析进度</li>
</ul>

<h3>📊 文件格式要求</h3>
<ul>
<li><b>表头信息</b>：前4行包含项目名称、子系统、模块名称、基地址</li>
<li><b>列标题</b>：第5行或之后为列标题行</li>
<li><b>必需列</b>：Offset、RegName、FieldName、Bit Range、RW</li>
<li><b>可选列</b>：Reset Value、Description</li>
</ul>

<h3>🔍 数据处理</h3>
<ul>
<li>保留字段（Reserved）将被自动跳过</li>
<li>支持十六进制（0x前缀）和十进制地址格式</li>
<li>字段位范围支持 "31:24" 或 "15" 格式</li>
<li>自动验证数据完整性和格式正确性</li>
</ul>

<h3>🔍 寄存器搜索</h3>
<ul>
<li>在寄存器列表上方的搜索框中输入关键词</li>
<li>支持按寄存器名称模糊搜索</li>
<li>支持按偏移地址精确搜索（十六进制或十进制）</li>
<li>搜索结果实时更新，支持300ms防抖</li>
<li>清空搜索框可显示所有寄存器</li>
</ul>

<h3>📋 寄存器列表</h3>
<ul>
<li>显示偏移地址、寄存器名称和可编辑字段数</li>
<li>点击寄存器可查看详细字段信息</li>
<li>支持按列排序（点击列标题）</li>
<li>鼠标悬停显示详细工具提示</li>
</ul>

<h3>🚀 后续功能</h3>
<ul>
<li>字段值编辑和实时计算</li>
<li>多种数字格式转换（二进制、十进制、十六进制）</li>
</ul>

<h3>❓ 常见问题</h3>
<ul>
<li><b>解析失败</b>：检查Excel文件格式是否符合要求</li>
<li><b>缺少列</b>：确保包含所有必需的列标题</li>
<li><b>数据错误</b>：检查寄存器名称和偏移地址是否重复</li>
</ul>
        """
        
        help_dialog.setDetailedText(help_text)
        help_dialog.setStandardButtons(QMessageBox.Ok)
        help_dialog.exec_()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止解析线程
        if self.parse_worker and self.parse_worker.isRunning():
            self.parse_worker.terminate()
            self.parse_worker.wait()
        
        super().closeEvent(event)