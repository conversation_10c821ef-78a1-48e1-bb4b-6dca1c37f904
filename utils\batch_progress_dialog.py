"""
批量操作进度对话框
提供详细的进度显示、取消功能和操作统计
"""
import time
from typing import Optional
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                           QProgressBar, QPushButton, QTextEdit, QGroupBox,
                           QGridLayout, QFrame, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPalette

try:
    from .batch_operations_manager import BatchOperationProgress
except ImportError:
    from batch_operations_manager import BatchOperationProgress


class BatchProgressDialog(QDialog):
    """批量操作进度对话框"""
    
    cancel_requested = pyqtSignal()
    
    def __init__(self, parent=None, operation_name: str = "批量操作"):
        super().__init__(parent)
        self.operation_name = operation_name
        self.start_time = time.time()
        self.last_progress = None
        
        self.setWindowTitle(f"{operation_name} - 进度")
        self.setModal(True)
        self.resize(500, 400)
        
        # 设置窗口标志，禁用关闭按钮
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.CustomizeWindowHint)
        
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        
        # 操作信息组
        info_group = QGroupBox("操作信息")
        info_layout = QGridLayout()
        
        self.operation_label = QLabel(self.operation_name)
        self.operation_label.setFont(QFont("", 10, QFont.Bold))
        info_layout.addWidget(QLabel("操作类型:"), 0, 0)
        info_layout.addWidget(self.operation_label, 0, 1)
        
        self.status_label = QLabel("正在启动...")
        info_layout.addWidget(QLabel("当前状态:"), 1, 0)
        info_layout.addWidget(self.status_label, 1, 1)
        
        info_group.setLayout(info_layout)
        main_layout.addWidget(info_group)
        
        # 进度组
        progress_group = QGroupBox("进度信息")
        progress_layout = QVBoxLayout()
        
        # 总体进度
        self.overall_progress = QProgressBar()
        self.overall_progress.setRange(0, 100)
        self.overall_progress.setValue(0)
        self.overall_progress.setTextVisible(True)
        progress_layout.addWidget(QLabel("总体进度:"))
        progress_layout.addWidget(self.overall_progress)
        
        # 进度详情
        details_layout = QGridLayout()
        
        self.processed_label = QLabel("0 / 0")
        details_layout.addWidget(QLabel("已处理:"), 0, 0)
        details_layout.addWidget(self.processed_label, 0, 1)
        
        self.success_label = QLabel("0")
        details_layout.addWidget(QLabel("成功:"), 0, 2)
        details_layout.addWidget(self.success_label, 0, 3)
        
        self.error_label = QLabel("0")
        details_layout.addWidget(QLabel("失败:"), 1, 0)
        details_layout.addWidget(self.error_label, 1, 1)
        
        self.chunk_label = QLabel("0 / 0")
        details_layout.addWidget(QLabel("分块:"), 1, 2)
        details_layout.addWidget(self.chunk_label, 1, 3)
        
        progress_layout.addLayout(details_layout)
        progress_group.setLayout(progress_layout)
        main_layout.addWidget(progress_group)
        
        # 时间信息组
        time_group = QGroupBox("时间信息")
        time_layout = QGridLayout()
        
        self.elapsed_label = QLabel("00:00:00")
        time_layout.addWidget(QLabel("已用时间:"), 0, 0)
        time_layout.addWidget(self.elapsed_label, 0, 1)
        
        self.remaining_label = QLabel("等待开始...")
        time_layout.addWidget(QLabel("预计剩余:"), 0, 2)
        time_layout.addWidget(self.remaining_label, 0, 3)
        
        self.speed_label = QLabel("0 项/秒")
        time_layout.addWidget(QLabel("处理速度:"), 1, 0)
        time_layout.addWidget(self.speed_label, 1, 1)
        
        self.success_rate_label = QLabel("0%")
        time_layout.addWidget(QLabel("成功率:"), 1, 2)
        time_layout.addWidget(self.success_rate_label, 1, 3)
        
        time_group.setLayout(time_layout)
        main_layout.addWidget(time_group)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("取消操作")
        self.cancel_button.clicked.connect(self.on_cancel_clicked)
        button_layout.addWidget(self.cancel_button)
        
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.accept)
        self.close_button.setEnabled(False)
        button_layout.addWidget(self.close_button)
        
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)
    
    def setup_timer(self):
        """设置定时器用于更新显示"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_time_display)
        self.update_timer.start(1000)  # 每秒更新一次
    
    def showEvent(self, event):
        """对话框显示时的事件处理"""
        super().showEvent(event)
        # 显示时立即更新状态
        self.status_label.setText("正在初始化...")
        QTimer.singleShot(100, self._delayed_start)  # 延迟100ms开始，确保UI已显示
    
    def _delayed_start(self):
        """延迟启动，确保UI已完全显示"""
        self.status_label.setText("正在处理...")
        # 如果5秒后还没有收到进度更新，显示警告
        QTimer.singleShot(5000, self._check_progress_timeout)
    
    def _check_progress_timeout(self):
        """检查进度更新超时"""
        if self.last_progress is None:
            self.status_label.setText("等待操作响应...")
            print("警告：5秒内未收到进度更新")
    
    def update_progress(self, progress: BatchOperationProgress):
        """更新进度显示"""
        # 更新进度条
        self.overall_progress.setValue(int(progress.progress_percentage))
        
        # 更新状态
        if progress.is_cancelling:
            self.status_label.setText("正在取消操作...")
            self.cancel_button.setEnabled(False)
            self.cancel_button.setText("正在取消...")
        else:
            self.status_label.setText(progress.current_operation or "处理中...")
        
        # 更新数量信息
        self.processed_label.setText(f"{progress.processed_items:,} / {progress.total_items:,}")
        self.success_label.setText(f"{progress.success_count:,}")
        self.error_label.setText(f"{progress.error_count:,}")
        self.chunk_label.setText(f"{progress.current_chunk} / {progress.total_chunks}")
        
        # 更新时间信息
        self.update_time_display()
        
        # 计算处理速度
        speed = progress.items_per_second
        speed_text = f"{speed:.1f} 项/秒"
        
        # 添加速度评估
        if speed < 2.0:
            speed_text += " (较慢)"
        elif speed < 5.0:
            speed_text += " (正常)"
        elif speed < 10.0:
            speed_text += " (较快)"
        else:
            speed_text += " (很快)"
            
        self.speed_label.setText(speed_text)
        
        # 更新成功率
        self.success_rate_label.setText(f"{progress.success_rate:.1f}%")
        
        # 更新预计剩余时间
        if progress.estimated_remaining_time > 0:
            remaining_str = self.format_time(progress.estimated_remaining_time)
            self.remaining_label.setText(remaining_str)
        else:
            self.remaining_label.setText("计算中...")
        
        # 更新取消按钮状态
        if hasattr(progress, 'can_be_cancelled'):
            self.cancel_button.setEnabled(progress.can_be_cancelled and not progress.is_cancelling)
    
    def update_time_display(self):
        """更新时间显示"""
        elapsed = time.time() - self.start_time
        self.elapsed_label.setText(self.format_time(elapsed))
    
    def format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    
    def on_cancel_clicked(self):
        """取消按钮点击处理"""
        # 确认取消操作
        reply = QMessageBox.question(
            self, "确认取消",
            "确定要取消当前操作吗？已处理的数据将会保留。",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.cancel_requested.emit()
            self.cancel_button.setEnabled(False)
            self.cancel_button.setText("正在取消...")
    
    def on_operation_completed(self, success: bool, message: str):
        """操作完成处理"""
        self.update_timer.stop()
        
        if success:
            self.status_label.setText("操作完成")
            self.overall_progress.setValue(100)
        else:
            self.status_label.setText(f"操作失败: {message}")
        
        self.cancel_button.setEnabled(False)
        self.close_button.setEnabled(True)
        self.close_button.setText("完成")
        
        # 自动关闭对话框（可选）
        # QTimer.singleShot(2000, self.accept)


class SimpleBatchProgressDialog(QDialog):
    """简化版批量操作进度对话框"""
    
    cancel_requested = pyqtSignal()
    
    def __init__(self, parent=None, title: str = "批量操作"):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(400, 200)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 状态标签
        self.status_label = QLabel("准备中...")
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        layout.addWidget(self.progress_bar)
        
        # 详细信息
        self.detail_label = QLabel("")
        layout.addWidget(self.detail_label)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.cancel_requested.emit)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def update_progress(self, percentage: float, message: str = None):
        """更新进度"""
        self.progress_bar.setValue(int(percentage))
        if message:
            self.status_label.setText(message)
    
    def set_detail(self, detail: str):
        """设置详细信息"""
        self.detail_label.setText(detail)
    
    def on_completed(self, success: bool, message: str = None):
        """操作完成"""
        if success:
            self.status_label.setText("操作完成")
            self.progress_bar.setValue(100)
        else:
            self.status_label.setText(f"操作失败: {message or '未知错误'}")
        
        self.cancel_button.setText("关闭")
        self.cancel_button.clicked.disconnect()
        self.cancel_button.clicked.connect(self.accept)