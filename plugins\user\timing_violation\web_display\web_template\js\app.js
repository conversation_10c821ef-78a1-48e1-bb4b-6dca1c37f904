/**
 * Timing Violation Display - Main Application
 * 
 * This is the core JavaScript application that manages the timing violation web interface.
 * It provides comprehensive data management, filtering, and visualization capabilities
 * with performance optimizations for large datasets.
 * 
 * Key Features:
 * - Intelligent data loading with multiple source support
 * - Advanced filtering by corner, case, and status
 * - Virtual scrolling for large datasets (10,000+ records)
 * - Real-time statistics and performance monitoring
 * - Responsive design with mobile support
 * - Memory management and caching optimization
 * 
 * Architecture:
 * - Event-driven design with async/await patterns
 * - Modular methods for maintainability
 * - Comprehensive error handling and recovery
 * - Performance monitoring and optimization
 * 
 * Browser Compatibility:
 * - Modern browsers (Chrome 90+, Firefox 88+, Safari 14+)
 * - ES6+ features with graceful degradation
 * - Mobile browser support with responsive design
 * 
 * @class ViolationDataManager
 * @version 1.0.0
 * <AUTHOR> Violation Web Display System
 */

class ViolationDataManager {
    /**
     * Initialize the ViolationDataManager with default settings and performance optimizations.
     * 
     * Sets up the core data structures, performance monitoring, and application state.
     * Automatically begins the initialization process upon instantiation.
     * 
     * @constructor
     * @example
     * // Basic initialization
     * const app = new ViolationDataManager();
     * 
     * // Access after initialization
     * document.addEventListener('DOMContentLoaded', () => {
     *     window.violationApp = new ViolationDataManager();
     * });
     */
    constructor() {
        this.dataCache = new Map();
        this.currentData = [];
        this.filteredData = [];
        this.allCorners = [];
        this.allCases = [];
        this.statistics = {};
        this.dataTable = null;
        this.isLoading = false;

        // Performance optimization settings
        this.maxRecordsPerPage = 1000;
        this.virtualScrollBuffer = 50;
        this.cacheTimeout = 300000; // 5 minutes
        this.lastCacheTime = new Map();

        // Filter state management
        this.currentFilters = {
            corner: 'all',
            case: 'all',
            status: 'all'
        };

        // Initialize the application
        this.init();
    }

    /**
     * Initialize the application with comprehensive setup and error handling.
     * 
     * This is the main initialization method that coordinates the entire application startup:
     * 1. Loads initial data from JSON files
     * 2. Sets up the DataTables interface with virtual scrolling
     * 3. Configures event listeners for user interactions
     * 4. Updates statistics and UI components
     * 5. Handles initialization errors gracefully
     * 
     * Initialization Sequence:
     * - Data loading (index.json, violations data)
     * - UI component initialization (DataTables, filters)
     * - Event listener setup (filters, buttons, keyboard)
     * - Statistics calculation and display
     * - Performance monitoring activation
     * 
     * @async
     * @method init
     * @returns {Promise<void>} Resolves when initialization is complete
     * 
     * @example
     * // Manual initialization (usually automatic)
     * const app = new ViolationDataManager();
     * await app.init();
     * 
     * @throws {Error} If critical initialization steps fail
     * 
     * Performance Notes:
     * - Typical initialization time: 500ms - 2s depending on data size
     * - Memory allocation: ~10MB for 10,000 violations
     * - Automatic performance monitoring and logging
     */
    async init() {
        try {
            this.showLoading(true);
            await this.loadInitialData();
            this.initializeDataTable();
            this.setupEventListeners();
            this.updateStatistics();
            this.updateLastUpdated();
            this.showLoading(false);
        } catch (error) {
            console.error('Failed to initialize application:', error);
            this.showError('Failed to initialize application: ' + error.message);
            this.showLoading(false);
        }
    }

    /**
     * Load initial data from JSON files
     */
    async loadInitialData() {
        try {
            // Try to load index data first to get metadata
            const indexUrls = ['data/index.json', 'test_data.json']; // Include test data for development
            let indexData = null;

            for (const url of indexUrls) {
                try {
                    const response = await fetch(url);
                    if (response.ok) {
                        indexData = await response.json();
                        break;
                    }
                } catch (error) {
                    console.warn(`Failed to load index from ${url}:`, error);
                    continue;
                }
            }

            if (indexData) {
                this.statistics = indexData.statistics || {};
                this.allCorners = indexData.corners || [];
                this.allCases = indexData.cases || [];

                // Populate filter dropdowns
                this.populateFilters();

                // Load initial violation data
                await this.loadViolationData();
            } else {
                // Fallback: try to load from a single violations.json file
                const fallbackData = await this.loadFallbackData();
                this.currentData = Array.isArray(fallbackData) ? fallbackData : [];
                this.filteredData = [...this.currentData];
            }

        } catch (error) {
            console.error('Error loading initial data:', error);
            // Final fallback: try to load from a single violations.json file
            const fallbackData = await this.loadFallbackData();
            this.currentData = Array.isArray(fallbackData) ? fallbackData : [];
            this.filteredData = [...this.currentData];
        }
    }

    /**
     * Load violation data based on current filters
     */
    async loadViolationData(corner = 'all', case_name = 'all') {
        try {
            let violations = [];

            if (corner === 'all' && case_name === 'all') {
                // Load all data - try paginated approach first
                violations = await this.loadAllViolations();
            } else {
                // Load specific corner/case data
                violations = await this.loadCornerCaseData(corner, case_name);
            }

            this.currentData = violations;
            this.filteredData = [...violations];

        } catch (error) {
            console.error('Error loading violation data:', error);
            throw error;
        }
    }

    /**
     * Load all violations using database-organized paginated approach
     */
    async loadAllViolations() {
        const violations = [];

        // First try to load from unified violations.json file
        try {
            const unifiedResponse = await fetch('data/violations.json');
            if (unifiedResponse.ok) {
                const unifiedData = await unifiedResponse.json();
                if (unifiedData && unifiedData.violations && Array.isArray(unifiedData.violations)) {
                    console.log(`Loaded ${unifiedData.violations.length} violations from unified file`);
                    return unifiedData.violations;
                }
            }
        } catch (error) {
            console.warn('Failed to load unified violations file:', error);
        }

        // Fallback: Try to load from multiple corner/case files
        if (this.allCorners && this.allCorners.length > 0 && this.allCases && this.allCases.length > 0) {
            for (const corner of this.allCorners) {
                for (const case_name of this.allCases) {
                    try {
                        const cornerCaseData = await this.loadCornerCaseData(corner, case_name);
                        if (cornerCaseData && cornerCaseData.length > 0) {
                            violations.push(...cornerCaseData);
                            console.log(`Loaded ${cornerCaseData.length} violations from ${corner}/${case_name}`);
                        }
                    } catch (error) {
                        // Continue loading other combinations
                        console.warn(`Failed to load data for ${corner}/${case_name}:`, error);
                    }
                }
            }
        }

        // Final fallback: try single violations.json file
        if (violations.length === 0) {
            console.log('No data loaded from corner/case files, trying fallback...');
            return await this.loadFallbackData();
        }

        console.log(`Total violations loaded: ${violations.length}`);
        return violations;
    }

    /**
     * Load data for specific corner and case with intelligent caching
     */
    async loadCornerCaseData(corner, case_name) {
        const cacheKey = `${corner}_${case_name}`;

        // Check if data is cached and not expired
        if (this.dataCache.has(cacheKey)) {
            const cacheTime = this.lastCacheTime.get(cacheKey);
            if (cacheTime && (Date.now() - cacheTime) < this.cacheTimeout) {
                return this.dataCache.get(cacheKey);
            } else {
                // Cache expired, remove old data
                this.dataCache.delete(cacheKey);
                this.lastCacheTime.delete(cacheKey);
            }
        }

        const violations = [];
        let page = 1;

        // Try to load paginated files
        while (true) {
            try {
                const filename = `data/violations/${corner}_${case_name}_page${page}.json`;
                const response = await fetch(filename);

                if (!response.ok) {
                    if (page === 1) {
                        // Try single file format
                        const singleFileResponse = await fetch(`data/violations/${corner}_${case_name}.json`);
                        if (singleFileResponse.ok) {
                            const data = await singleFileResponse.json();
                            violations.push(...data);
                        }
                    }
                    break;
                }

                const pageData = await response.json();
                violations.push(...pageData);
                page++;

            } catch (error) {
                console.warn(`Failed to load page ${page} for ${corner}/${case_name}:`, error);
                break;
            }
        }

        // Cache the data with timestamp
        this.dataCache.set(cacheKey, violations);
        this.lastCacheTime.set(cacheKey, Date.now());
        return violations;
    }

    /**
     * Fallback data loading from single violations.json file
     */
    async loadFallbackData() {
        try {
            // Try multiple fallback locations
            const fallbackUrls = [
                'data/violations.json',
                'test_violations.json', // For testing
                'violations.json'
            ];

            for (const url of fallbackUrls) {
                try {
                    const response = await fetch(url);
                    if (response.ok) {
                        const rawData = await response.json();
                        
                        // Handle different data formats
                        let data = [];
                        if (Array.isArray(rawData)) {
                            // Data is already an array
                            data = rawData;
                        } else if (rawData && rawData.violations && Array.isArray(rawData.violations)) {
                            // Data is an object with violations array
                            data = rawData.violations;
                            
                            // Also extract corners and cases from metadata if available
                            if (rawData.corners && Array.isArray(rawData.corners)) {
                                this.allCorners = rawData.corners;
                            }
                            if (rawData.cases && Array.isArray(rawData.cases)) {
                                this.allCases = rawData.cases;
                            }
                        } else if (rawData && typeof rawData === 'object') {
                            // Try to extract violations from object properties
                            const possibleArrays = Object.values(rawData).filter(Array.isArray);
                            if (possibleArrays.length > 0) {
                                data = possibleArrays[0];
                            }
                        }

                        // Ensure data is always an array
                        if (!Array.isArray(data)) {
                            console.warn(`Data from ${url} is not an array, using empty array`);
                            data = [];
                        }

                        // Extract corners and cases from data if not already set
                        if (data.length > 0) {
                            if (this.allCorners.length === 0) {
                                this.allCorners = [...new Set(data.map(v => v.corner).filter(Boolean))];
                            }
                            if (this.allCases.length === 0) {
                                this.allCases = [...new Set(data.map(v => v.case).filter(Boolean))];
                            }
                            this.populateFilters();
                        }

                        return data;
                    }
                } catch (error) {
                    console.warn(`Failed to load from ${url}:`, error);
                    continue;
                }
            }

            throw new Error('No violation data available from any source');

        } catch (error) {
            console.error('Fallback data loading failed:', error);
            return [];
        }
    }

    /**
     * Populate filter dropdowns
     */
    populateFilters() {
        const cornerSelect = document.getElementById('corner-filter');
        const caseSelect = document.getElementById('case-filter');

        // Clear existing options (except "All")
        cornerSelect.innerHTML = '<option value="all">All Corners</option>';
        caseSelect.innerHTML = '<option value="all">All Cases</option>';

        // Add corner options
        this.allCorners.forEach(corner => {
            const option = document.createElement('option');
            option.value = corner;
            option.textContent = corner;
            cornerSelect.appendChild(option);
        });

        // Add case options
        this.allCases.forEach(case_name => {
            const option = document.createElement('option');
            option.value = case_name;
            option.textContent = case_name;
            caseSelect.appendChild(option);
        });
    }

    /**
     * Initialize DataTables with virtual scrolling
     */
    initializeDataTable() {
        const tableElement = document.getElementById('violations-table');

        this.dataTable = $(tableElement).DataTable({
            data: this.filteredData,
            destroy: true, // Allow reinitialization
            columns: [
                {
                    data: 'num',
                    title: 'NUM',
                    width: '60px',
                    className: 'text-center'
                },
                {
                    data: 'hier',
                    title: 'Hier',
                    width: '300px',
                    className: 'hier-column',
                    render: function (data, type, row) {
                        if (type === 'display' && data && data.length > 50) {
                            return `<span title="${data}">${data.substring(0, 50)}...</span>`;
                        }
                        return data || '';
                    }
                },
                {
                    data: 'time_ns',
                    title: 'Time (ns)',
                    width: '100px',
                    className: 'time-column text-end',
                    render: function (data, type, row) {
                        if (type === 'display' && data !== null && data !== undefined) {
                            return parseFloat(data).toFixed(3);
                        }
                        return data || '';
                    }
                },
                {
                    data: 'check_info',
                    title: 'Check Info',
                    width: '250px',
                    className: 'check-info',
                    render: function (data, type, row) {
                        if (type === 'display' && data && data.length > 40) {
                            return `<span title="${data}">${data.substring(0, 40)}...</span>`;
                        }
                        return data || '';
                    }
                },
                {
                    data: 'status',
                    title: 'Status',
                    width: '100px',
                    className: 'text-center',
                    render: function (data, type, row) {
                        if (type === 'display') {
                            const status = (data || '').toLowerCase();
                            let className = 'status-pending';
                            if (status === 'confirmed') {
                                className = 'status-confirmed';
                            } else if (status === 'rejected') {
                                className = 'status-rejected';
                            }
                            return `<span class="${className}">${data || 'Pending'}</span>`;
                        }
                        return data || '';
                    }
                },
                {
                    data: 'confirmer',
                    title: 'Confirmer',
                    width: '120px'
                },
                {
                    data: 'result',
                    title: 'Result',
                    width: '100px'
                },
                {
                    data: 'reason',
                    title: 'Reason',
                    width: '200px',
                    className: 'reason-column',
                    render: function (data, type, row) {
                        if (type === 'display' && data && data.length > 30) {
                            return `<span title="${data}">${data.substring(0, 30)}...</span>`;
                        }
                        return data || '';
                    }
                },
                {
                    data: 'confirmed_at',
                    title: 'Confirmed At',
                    width: '150px',
                    render: function (data, type, row) {
                        if (type === 'display' && data) {
                            const date = new Date(data);
                            if (!isNaN(date.getTime())) {
                                return date.toLocaleString();
                            }
                        }
                        return data || '';
                    }
                },
                {
                    data: 'corner',
                    title: 'Corner',
                    width: '120px',
                    render: function (data, type, row) {
                        if (type === 'display' && data) {
                            return `<span class="corner-badge">${data}</span>`;
                        }
                        return data || '';
                    }
                },
                {
                    data: 'case',
                    title: 'Case',
                    width: '120px',
                    render: function (data, type, row) {
                        if (type === 'display' && data) {
                            return `<span class="case-badge">${data}</span>`;
                        }
                        return data || '';
                    }
                }
            ],
            scrollY: '600px',
            scrollX: true,
            scroller: {
                displayBuffer: this.virtualScrollBuffer,
                boundaryScale: 0.5,
                loadingIndicator: true
            },
            deferRender: true,
            processing: true,
            pageLength: 100,
            lengthMenu: [[50, 100, 250, 500, 1000, -1], [50, 100, 250, 500, 1000, "All"]],
            stateSave: true, // Save table state
            stateDuration: 3600, // 1 hour
            order: [[0, 'asc']], // Sort by NUM column
            responsive: true,
            language: {
                processing: "Loading violation data...",
                emptyTable: "No violation records found",
                info: "Showing _START_ to _END_ of _TOTAL_ violations",
                infoEmpty: "No violations to display",
                infoFiltered: "(filtered from _MAX_ total violations)",
                search: "Search violations:",
                lengthMenu: "Show _MENU_ violations per page"
            },
            drawCallback: () => {
                this.updateFilteredCount();
            }
        });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Filter change handlers
        document.getElementById('corner-filter').addEventListener('change', (e) => {
            this.handleCornerFilterChange(e.target.value);
        });

        document.getElementById('case-filter').addEventListener('change', (e) => {
            this.handleCaseFilterChange(e.target.value);
        });

        document.getElementById('status-filter').addEventListener('change', (e) => {
            this.handleStatusFilterChange(e.target.value);
        });

        // Button handlers
        document.getElementById('clear-filters').addEventListener('click', () => {
            this.clearAllFilters();
        });

        document.getElementById('refresh-data').addEventListener('click', () => {
            this.refreshData();
        });

        document.getElementById('retry-button').addEventListener('click', () => {
            this.init();
        });
    }

    /**
     * Handle corner filter change with performance tracking
     */
    async handleCornerFilterChange(selectedCorner) {
        const startTime = performance.now();

        try {
            this.showLoading(true);
            this.currentFilters.corner = selectedCorner;

            // Update case filter based on selected corner
            await this.updateCaseFilter(selectedCorner);

            // Apply filters
            await this.applyFilters();

            const endTime = performance.now();
            console.log(`Corner filter change took ${(endTime - startTime).toFixed(2)}ms`);

            this.showLoading(false);
        } catch (error) {
            console.error('Error handling corner filter change:', error);
            this.showError('Failed to apply corner filter: ' + error.message);
            this.showLoading(false);
        }
    }

    /**
     * Handle case filter change with performance tracking
     */
    async handleCaseFilterChange(selectedCase) {
        const startTime = performance.now();

        try {
            this.showLoading(true);
            this.currentFilters.case = selectedCase;
            await this.applyFilters();

            const endTime = performance.now();
            console.log(`Case filter change took ${(endTime - startTime).toFixed(2)}ms`);

            this.showLoading(false);
        } catch (error) {
            console.error('Error handling case filter change:', error);
            this.showError('Failed to apply case filter: ' + error.message);
            this.showLoading(false);
        }
    }

    /**
     * Handle status filter change with performance tracking
     */
    async handleStatusFilterChange(selectedStatus) {
        const startTime = performance.now();

        try {
            this.showLoading(true);
            this.currentFilters.status = selectedStatus;
            await this.applyFilters();

            const endTime = performance.now();
            console.log(`Status filter change took ${(endTime - startTime).toFixed(2)}ms`);

            this.showLoading(false);
        } catch (error) {
            console.error('Error handling status filter change:', error);
            this.showError('Failed to apply status filter: ' + error.message);
            this.showLoading(false);
        }
    }

    /**
     * Update case filter based on selected corner
     */
    async updateCaseFilter(selectedCorner) {
        const caseSelect = document.getElementById('case-filter');

        // Clear existing options
        caseSelect.innerHTML = '<option value="all">All Cases</option>';

        let availableCases = [];

        if (selectedCorner === 'all') {
            availableCases = this.allCases;
        } else {
            // Get cases for the selected corner
            try {
                const cornerCasesResponse = await fetch(`data/corners/${selectedCorner}_cases.json`);
                if (cornerCasesResponse.ok) {
                    availableCases = await cornerCasesResponse.json();
                } else {
                    // Fallback: extract from current data
                    availableCases = [...new Set(
                        this.currentData
                            .filter(v => v.corner === selectedCorner)
                            .map(v => v.case)
                            .filter(Boolean)
                    )];
                }
            } catch (error) {
                console.warn('Failed to load corner-specific cases:', error);
                availableCases = this.allCases;
            }
        }

        // Ensure availableCases is an array
        if (!Array.isArray(availableCases)) {
            console.warn('availableCases is not an array, using fallback:', availableCases);
            availableCases = this.allCases || [];
        }

        // Add case options
        availableCases.forEach(case_name => {
            const option = document.createElement('option');
            option.value = case_name;
            option.textContent = case_name;
            caseSelect.appendChild(option);
        });
    }

    /**
     * Apply all active filters
     */
    async applyFilters() {
        const selectedCorner = document.getElementById('corner-filter').value;
        const selectedCase = document.getElementById('case-filter').value;
        const selectedStatus = document.getElementById('status-filter').value;

        // Always load all data first, then filter in memory for better reliability
        if (this.currentData.length === 0) {
            await this.loadViolationData('all', 'all');
        }

        // Apply filters in memory
        this.filteredData = this.currentData.filter(violation => {
            // Corner filter
            if (selectedCorner !== 'all' && violation.corner !== selectedCorner) {
                return false;
            }

            // Case filter
            if (selectedCase !== 'all' && violation.case !== selectedCase) {
                return false;
            }

            // Status filter
            if (selectedStatus !== 'all') {
                const status = (violation.status || '').toLowerCase();
                if (selectedStatus === 'confirmed' && status !== 'confirmed') {
                    return false;
                }
                if (selectedStatus === 'pending' && status === 'confirmed') {
                    return false;
                }
            }

            return true;
        });

        console.log(`Applied filters: corner=${selectedCorner}, case=${selectedCase}, status=${selectedStatus}`);
        console.log(`Filtered data: ${this.filteredData.length} records from ${this.currentData.length} total`);

        // Update DataTable
        this.updateDataTable();
        this.updateStatistics();
    }

    /**
     * Update DataTable with filtered data
     */
    updateDataTable() {
        if (this.dataTable) {
            this.dataTable.clear();
            this.dataTable.rows.add(this.filteredData);
            this.dataTable.draw();
        }
    }

    /**
     * Update statistics display
     */
    updateStatistics() {
        const totalViolations = this.filteredData.length;
        const confirmedViolations = this.filteredData.filter(v =>
            (v.status || '').toLowerCase() === 'confirmed'
        ).length;
        const pendingViolations = totalViolations - confirmedViolations;
        const confirmationRate = totalViolations > 0 ?
            ((confirmedViolations / totalViolations) * 100).toFixed(1) : 0;

        document.getElementById('total-violations').textContent = totalViolations.toLocaleString();
        document.getElementById('confirmed-violations').textContent = confirmedViolations.toLocaleString();
        document.getElementById('pending-violations').textContent = pendingViolations.toLocaleString();
        document.getElementById('confirmation-rate').textContent = confirmationRate + '%';
    }

    /**
     * Update filtered count display
     */
    updateFilteredCount() {
        const count = this.filteredData.length;
        document.getElementById('filtered-count').textContent =
            `${count.toLocaleString()} record${count !== 1 ? 's' : ''}`;
    }

    /**
     * Clear all filters
     */
    async clearAllFilters() {
        document.getElementById('corner-filter').value = 'all';
        document.getElementById('case-filter').value = 'all';
        document.getElementById('status-filter').value = 'all';

        await this.applyFilters();
    }

    /**
     * Refresh all data with memory cleanup
     */
    async refreshData() {
        try {
            this.showLoading(true);

            // Clear cache and memory
            this.clearCache();

            // Try to trigger data regeneration by calling a refresh endpoint
            try {
                const refreshResponse = await fetch('/refresh_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (refreshResponse.ok) {
                    console.log('Data regeneration triggered successfully');
                    // Wait a moment for data to be regenerated
                    await new Promise(resolve => setTimeout(resolve, 2000));
                } else {
                    console.warn('Data regeneration endpoint not available, using cached data');
                }
            } catch (error) {
                console.warn('Could not trigger data regeneration:', error.message);
            }

            // Reload data
            await this.loadInitialData();
            await this.applyFilters();

            this.updateLastUpdated();
            this.showLoading(false);

        } catch (error) {
            console.error('Error refreshing data:', error);
            this.showError('Failed to refresh data: ' + error.message);
            this.showLoading(false);
        }
    }

    /**
     * Update last updated timestamp
     */
    updateLastUpdated() {
        const now = new Date();
        document.getElementById('last-updated').textContent = now.toLocaleString();
    }

    /**
     * Show/hide loading overlay
     */
    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        overlay.style.display = show ? 'flex' : 'none';
        this.isLoading = show;
    }

    /**
     * Show error modal
     */
    showError(message) {
        document.getElementById('error-message').textContent = message;
        const errorModal = new bootstrap.Modal(document.getElementById('error-modal'));
        errorModal.show();
    }

    /**
     * Clear cache and perform memory cleanup
     */
    clearCache() {
        this.dataCache.clear();
        this.lastCacheTime.clear();
        this.currentData = [];
        this.filteredData = [];
        
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
    }

    /**
     * Get memory usage statistics
     */
    getMemoryStats() {
        const stats = {
            cacheSize: this.dataCache.size,
            currentDataSize: this.currentData.length,
            filteredDataSize: this.filteredData.length
        };
        
        if (performance.memory) {
            stats.jsHeapSizeLimit = performance.memory.jsHeapSizeLimit;
            stats.totalJSHeapSize = performance.memory.totalJSHeapSize;
            stats.usedJSHeapSize = performance.memory.usedJSHeapSize;
        }
        
        return stats;
    }

    /**
     * Optimize memory usage by cleaning old cache entries
     */
    optimizeMemory() {
        const now = Date.now();
        const keysToDelete = [];
        
        for (const [key, timestamp] of this.lastCacheTime.entries()) {
            if (now - timestamp > this.cacheTimeout) {
                keysToDelete.push(key);
            }
        }
        
        keysToDelete.forEach(key => {
            this.dataCache.delete(key);
            this.lastCacheTime.delete(key);
        });
        
        console.log(`Cleaned ${keysToDelete.length} expired cache entries`);
    }

    /**
     * Debounced filter application for better performance
     */
    debounceFilter(func, delay = 300) {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    /**
     * Batch update statistics for better performance
     */
    batchUpdateStatistics() {
        // Use requestAnimationFrame for smooth UI updates
        requestAnimationFrame(() => {
            this.updateStatistics();
            this.updateFilteredCount();
        });
    }
}

// Performance monitoring and memory optimization
setInterval(() => {
    if (window.violationApp) {
        window.violationApp.optimizeMemory();
        
        // Log memory stats in development
        if (console.debug) {
            const stats = window.violationApp.getMemoryStats();
            console.debug('Memory stats:', stats);
        }
    }
}, 60000); // Every minute

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.violationApp = new ViolationDataManager();
});

// Handle window resize for responsive table
window.addEventListener('resize', () => {
    if (window.violationApp && window.violationApp.dataTable) {
        window.violationApp.dataTable.columns.adjust().draw();
    }
});

// Handle page visibility changes for performance optimization
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // Page is hidden, pause expensive operations
        if (window.violationApp) {
            window.violationApp.optimizeMemory();
        }
    } else {
        // Page is visible, resume operations
        if (window.violationApp && window.violationApp.dataTable) {
            window.violationApp.dataTable.columns.adjust().draw();
        }
    }
});