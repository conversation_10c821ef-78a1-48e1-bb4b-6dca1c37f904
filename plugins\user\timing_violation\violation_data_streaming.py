"""
Violation Data Streaming System

Implements streaming data structures, lazy loading, and LRU caching
specifically designed for large violation datasets.
"""

import threading
import time
import weakref
from typing import Dict, List, Optional, Any, Iterator, Callable, Union
from collections import OrderedDict, deque
from dataclasses import dataclass
from abc import ABC, abstractmethod


@dataclass
class StreamingMetrics:
    """Metrics for streaming operations"""
    total_violations: int
    loaded_chunks: int
    cache_hits: int
    cache_misses: int
    memory_usage_mb: float
    load_time_ms: float
    timestamp: float


class ViolationDataSource(ABC):
    """Abstract base class for violation data sources"""
    
    @abstractmethod
    def get_total_count(self) -> int:
        """Get total number of violations"""
        pass
    
    @abstractmethod
    def load_range(self, start: int, count: int) -> List[Dict[str, Any]]:
        """Load a range of violations"""
        pass
    
    @abstractmethod
    def get_violation(self, index: int) -> Optional[Dict[str, Any]]:
        """Get a single violation by index"""
        pass


class ListDataSource(ViolationDataSource):
    """Data source backed by a Python list"""
    
    def __init__(self, data: List[Dict[str, Any]]):
        self.data = data
    
    def get_total_count(self) -> int:
        return len(self.data)
    
    def load_range(self, start: int, count: int) -> List[Dict[str, Any]]:
        end = min(start + count, len(self.data))
        return self.data[start:end]
    
    def get_violation(self, index: int) -> Optional[Dict[str, Any]]:
        if 0 <= index < len(self.data):
            return self.data[index]
        return None


class FileDataSource(ViolationDataSource):
    """Data source backed by a file with lazy loading"""
    
    def __init__(self, file_path: str, parser_func: Callable[[str], List[Dict[str, Any]]]):
        self.file_path = file_path
        self.parser_func = parser_func
        self._total_count = None
        self._line_offsets = None
        self._lock = threading.RLock()
    
    def get_total_count(self) -> int:
        if self._total_count is None:
            self._build_index()
        return self._total_count or 0
    
    def load_range(self, start: int, count: int) -> List[Dict[str, Any]]:
        with self._lock:
            if self._line_offsets is None:
                self._build_index()
            
            if not self._line_offsets:
                return []
            
            # Calculate line range (assuming 5 lines per violation)
            start_line = start * 5
            end_line = min((start + count) * 5, len(self._line_offsets))
            
            if start_line >= len(self._line_offsets):
                return []
            
            # Read the required lines
            lines = []
            try:
                with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    for line_idx in range(start_line, end_line):
                        if line_idx < len(self._line_offsets):
                            f.seek(self._line_offsets[line_idx])
                            lines.append(f.readline().strip())
            except Exception as e:
                print(f"Error reading file range: {e}")
                return []
            
            # Parse the lines into violations
            if lines:
                return self.parser_func('\n'.join(lines))
            return []
    
    def get_violation(self, index: int) -> Optional[Dict[str, Any]]:
        violations = self.load_range(index, 1)
        return violations[0] if violations else None
    
    def _build_index(self):
        """Build line offset index for efficient seeking"""
        try:
            self._line_offsets = []
            with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                offset = 0
                for line in f:
                    self._line_offsets.append(offset)
                    offset = f.tell()
            
            # Estimate violation count (5 lines per violation)
            self._total_count = len(self._line_offsets) // 5
            
        except Exception as e:
            print(f"Error building file index: {e}")
            self._line_offsets = []
            self._total_count = 0


class ViolationChunk:
    """A chunk of violation data with metadata"""
    
    def __init__(self, chunk_id: int, data: List[Dict[str, Any]], 
                 load_time: float = None):
        self.chunk_id = chunk_id
        self.data = data
        self.load_time = load_time or time.time()
        self.access_count = 0
        self.last_access = time.time()
        self.size_bytes = self._calculate_size()
    
    def _calculate_size(self) -> int:
        """Estimate memory size of chunk data"""
        try:
            import sys
            total_size = sys.getsizeof(self.data)
            for item in self.data:
                total_size += sys.getsizeof(item)
                if isinstance(item, dict):
                    for key, value in item.items():
                        total_size += sys.getsizeof(key) + sys.getsizeof(value)
            return total_size
        except Exception:
            return len(self.data) * 1024  # Rough estimate: 1KB per violation
    
    def access(self) -> List[Dict[str, Any]]:
        """Access chunk data and update statistics"""
        self.access_count += 1
        self.last_access = time.time()
        return self.data
    
    def get_stats(self) -> Dict[str, Any]:
        """Get chunk statistics"""
        return {
            'chunk_id': self.chunk_id,
            'violation_count': len(self.data),
            'size_bytes': self.size_bytes,
            'access_count': self.access_count,
            'last_access': self.last_access,
            'age_seconds': time.time() - self.load_time
        }


class ViolationStreamingCache:
    """
    LRU cache with memory pressure awareness for violation chunks
    """
    
    def __init__(self, max_chunks: int = 10, max_memory_mb: float = 100.0):
        self.max_chunks = max_chunks
        self.max_memory_mb = max_memory_mb
        self._cache = OrderedDict()
        self._memory_usage = 0
        self._lock = threading.RLock()
        
        # Statistics
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_loads': 0,
            'memory_pressure_evictions': 0
        }
    
    def get(self, chunk_id: int) -> Optional[ViolationChunk]:
        """Get chunk from cache"""
        with self._lock:
            if chunk_id in self._cache:
                # Move to end (most recently used)
                chunk = self._cache.pop(chunk_id)
                self._cache[chunk_id] = chunk
                self._stats['hits'] += 1
                return chunk
            
            self._stats['misses'] += 1
            return None
    
    def put(self, chunk: ViolationChunk):
        """Put chunk in cache with eviction if needed"""
        with self._lock:
            chunk_id = chunk.chunk_id
            
            # Remove existing chunk if present
            if chunk_id in self._cache:
                old_chunk = self._cache.pop(chunk_id)
                self._memory_usage -= old_chunk.size_bytes
            
            # Check if we need to evict chunks
            self._evict_if_needed(chunk.size_bytes)
            
            # Add new chunk
            self._cache[chunk_id] = chunk
            self._memory_usage += chunk.size_bytes
            self._stats['total_loads'] += 1
    
    def _evict_if_needed(self, new_chunk_size: int):
        """Evict chunks if needed based on limits"""
        # Calculate memory usage after adding new chunk
        projected_memory = (self._memory_usage + new_chunk_size) / (1024 * 1024)
        
        # Evict based on memory limit
        while (projected_memory > self.max_memory_mb or 
               len(self._cache) >= self.max_chunks):
            
            if not self._cache:
                break
            
            # Remove least recently used chunk
            chunk_id, chunk = self._cache.popitem(last=False)
            self._memory_usage -= chunk.size_bytes
            projected_memory = self._memory_usage / (1024 * 1024)
            
            self._stats['evictions'] += 1
            
            if projected_memory > self.max_memory_mb:
                self._stats['memory_pressure_evictions'] += 1
    
    def clear(self):
        """Clear all cached chunks"""
        with self._lock:
            self._cache.clear()
            self._memory_usage = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_ratio = self._stats['hits'] / max(total_requests, 1)
            
            return {
                'cached_chunks': len(self._cache),
                'max_chunks': self.max_chunks,
                'memory_usage_mb': self._memory_usage / (1024 * 1024),
                'max_memory_mb': self.max_memory_mb,
                'hit_ratio': hit_ratio,
                'total_requests': total_requests,
                **self._stats
            }
    
    def optimize_for_memory_pressure(self):
        """Optimize cache for memory pressure"""
        with self._lock:
            # Reduce cache size by half
            target_size = max(self.max_chunks // 2, 2)
            
            while len(self._cache) > target_size:
                if not self._cache:
                    break
                
                chunk_id, chunk = self._cache.popitem(last=False)
                self._memory_usage -= chunk.size_bytes
                self._stats['memory_pressure_evictions'] += 1


class ViolationDataStream:
    """
    Main streaming data structure for large violation datasets
    """
    
    def __init__(self, data_source: ViolationDataSource, 
                 chunk_size: int = 1000, cache_size: int = 10):
        self.data_source = data_source
        self.chunk_size = chunk_size
        self.cache = ViolationStreamingCache(max_chunks=cache_size)
        self._lock = threading.RLock()
        
        # Lazy loading state
        self._total_size = None
        self._loading_chunks = set()  # Track chunks being loaded
        
        # Background loading
        self._prefetch_enabled = True
        self._prefetch_distance = 2  # Prefetch 2 chunks ahead
        
        # Statistics
        self._stats = {
            'total_accesses': 0,
            'chunk_loads': 0,
            'background_loads': 0,
            'load_time_total': 0.0
        }
    
    def __len__(self) -> int:
        """Get total number of violations"""
        if self._total_size is None:
            self._total_size = self.data_source.get_total_count()
        return self._total_size
    
    def __getitem__(self, index: Union[int, slice]) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """Get violation(s) by index with lazy loading"""
        if isinstance(index, slice):
            return self._get_slice(index)
        
        return self._get_single(index)
    
    def _get_single(self, index: int) -> Dict[str, Any]:
        """Get single violation by index"""
        if not (0 <= index < len(self)):
            raise IndexError(f"Violation index {index} out of range")
        
        chunk_id = index // self.chunk_size
        item_index = index % self.chunk_size
        
        chunk = self._get_chunk(chunk_id)
        if chunk and item_index < len(chunk.data):
            self._stats['total_accesses'] += 1
            
            # Trigger prefetching if enabled
            if self._prefetch_enabled:
                self._prefetch_adjacent_chunks(chunk_id)
            
            return chunk.access()[item_index]
        
        raise IndexError(f"Violation index {index} not available")
    
    def _get_slice(self, slice_obj: slice) -> List[Dict[str, Any]]:
        """Get slice of violations"""
        start, stop, step = slice_obj.indices(len(self))
        
        if step != 1:
            # Handle non-contiguous slices
            return [self._get_single(i) for i in range(start, stop, step)]
        
        # Handle contiguous slices efficiently
        result = []
        current_index = start
        
        while current_index < stop:
            chunk_id = current_index // self.chunk_size
            chunk_start = current_index % self.chunk_size
            chunk_end = min(self.chunk_size, chunk_start + (stop - current_index))
            
            chunk = self._get_chunk(chunk_id)
            if chunk:
                chunk_data = chunk.access()
                result.extend(chunk_data[chunk_start:chunk_end])
                current_index += len(chunk_data[chunk_start:chunk_end])
            else:
                break
        
        return result
    
    def _get_chunk(self, chunk_id: int) -> Optional[ViolationChunk]:
        """Get chunk with caching and lazy loading"""
        with self._lock:
            # Check cache first
            chunk = self.cache.get(chunk_id)
            if chunk:
                return chunk
            
            # Prevent concurrent loading of same chunk
            if chunk_id in self._loading_chunks:
                # Wait for other thread to finish loading
                while chunk_id in self._loading_chunks:
                    time.sleep(0.001)  # Small delay
                
                # Try cache again
                return self.cache.get(chunk_id)
            
            # Load chunk
            self._loading_chunks.add(chunk_id)
        
        try:
            chunk = self._load_chunk(chunk_id)
            if chunk:
                self.cache.put(chunk)
            return chunk
        finally:
            with self._lock:
                self._loading_chunks.discard(chunk_id)
    
    def _load_chunk(self, chunk_id: int) -> Optional[ViolationChunk]:
        """Load chunk from data source"""
        start_time = time.perf_counter()
        
        try:
            start_index = chunk_id * self.chunk_size
            data = self.data_source.load_range(start_index, self.chunk_size)
            
            if data:
                load_time = time.perf_counter() - start_time
                chunk = ViolationChunk(chunk_id, data, time.time())
                
                # Update statistics
                self._stats['chunk_loads'] += 1
                self._stats['load_time_total'] += load_time
                
                return chunk
            
        except Exception as e:
            print(f"Error loading chunk {chunk_id}: {e}")
        
        return None
    
    def _prefetch_adjacent_chunks(self, current_chunk_id: int):
        """Prefetch adjacent chunks in background"""
        if not self._prefetch_enabled:
            return
        
        # Prefetch next chunks
        max_chunk_id = (len(self) - 1) // self.chunk_size
        
        for offset in range(1, self._prefetch_distance + 1):
            next_chunk_id = current_chunk_id + offset
            if next_chunk_id <= max_chunk_id:
                # Check if chunk is already cached or being loaded
                if (self.cache.get(next_chunk_id) is None and 
                    next_chunk_id not in self._loading_chunks):
                    
                    # Load in background (simplified - could use thread pool)
                    try:
                        chunk = self._load_chunk(next_chunk_id)
                        if chunk:
                            self.cache.put(chunk)
                            self._stats['background_loads'] += 1
                    except Exception as e:
                        print(f"Background prefetch error: {e}")
    
    def get_chunk_range(self, start_chunk: int, chunk_count: int) -> List[ViolationChunk]:
        """Get multiple chunks efficiently"""
        chunks = []
        for chunk_id in range(start_chunk, start_chunk + chunk_count):
            chunk = self._get_chunk(chunk_id)
            if chunk:
                chunks.append(chunk)
        return chunks
    
    def prefetch_range(self, start_index: int, count: int):
        """Prefetch a range of violations"""
        start_chunk = start_index // self.chunk_size
        end_chunk = (start_index + count - 1) // self.chunk_size
        
        for chunk_id in range(start_chunk, end_chunk + 1):
            if self.cache.get(chunk_id) is None:
                self._get_chunk(chunk_id)
    
    def clear_cache(self):
        """Clear the chunk cache"""
        self.cache.clear()
    
    def optimize_for_memory_pressure(self):
        """Optimize stream for memory pressure"""
        # Disable prefetching
        self._prefetch_enabled = False
        
        # Optimize cache
        self.cache.optimize_for_memory_pressure()
        
        # Reduce chunk size for future loads
        self.chunk_size = max(self.chunk_size // 2, 100)
    
    def get_streaming_stats(self) -> StreamingMetrics:
        """Get comprehensive streaming statistics"""
        cache_stats = self.cache.get_stats()
        
        return StreamingMetrics(
            total_violations=len(self),
            loaded_chunks=cache_stats['cached_chunks'],
            cache_hits=cache_stats['hits'],
            cache_misses=cache_stats['misses'],
            memory_usage_mb=cache_stats['memory_usage_mb'],
            load_time_ms=self._stats['load_time_total'] * 1000,
            timestamp=time.time()
        )
    
    def get_detailed_stats(self) -> Dict[str, Any]:
        """Get detailed statistics"""
        cache_stats = self.cache.get_stats()
        
        return {
            'stream': {
                'total_violations': len(self),
                'chunk_size': self.chunk_size,
                'total_chunks': (len(self) + self.chunk_size - 1) // self.chunk_size,
                'prefetch_enabled': self._prefetch_enabled,
                'prefetch_distance': self._prefetch_distance,
                **self._stats
            },
            'cache': cache_stats,
            'timestamp': time.time()
        }


class LazyViolationLoader:
    """
    Lazy loader for violation details that loads full data only when needed
    """
    
    def __init__(self, violation_id: str, basic_data: Dict[str, Any], 
                 detail_loader: Callable[[str], Dict[str, Any]]):
        self.violation_id = violation_id
        self._basic_data = basic_data
        self._detail_loader = detail_loader
        self._full_data = None
        self._loaded = False
        self._lock = threading.RLock()
    
    def get_basic_data(self) -> Dict[str, Any]:
        """Get basic violation data (always available)"""
        return self._basic_data.copy()
    
    def get_full_data(self) -> Dict[str, Any]:
        """Get full violation data (loads details if needed)"""
        if not self._loaded:
            with self._lock:
                if not self._loaded:
                    try:
                        details = self._detail_loader(self.violation_id)
                        self._full_data = {**self._basic_data, **details}
                        self._loaded = True
                    except Exception as e:
                        print(f"Error loading violation details: {e}")
                        self._full_data = self._basic_data.copy()
                        self._loaded = True
        
        return self._full_data.copy() if self._full_data else self._basic_data.copy()
    
    def is_loaded(self) -> bool:
        """Check if full data is loaded"""
        return self._loaded
    
    def __getitem__(self, key: str) -> Any:
        """Get item from violation data"""
        # Try basic data first
        if key in self._basic_data:
            return self._basic_data[key]
        
        # Load full data if needed
        full_data = self.get_full_data()
        return full_data.get(key)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get item with default value"""
        try:
            return self[key]
        except KeyError:
            return default


class ViolationStreamManager:
    """
    Manager for multiple violation streams with memory coordination
    """
    
    def __init__(self, max_total_memory_mb: float = 500.0):
        self.max_total_memory_mb = max_total_memory_mb
        self._streams = {}
        self._stream_priorities = {}
        self._lock = threading.RLock()
        
        # Global statistics
        self._global_stats = {
            'total_streams': 0,
            'total_violations': 0,
            'total_memory_mb': 0.0,
            'memory_optimizations': 0
        }
    
    def create_stream(self, name: str, data_source: ViolationDataSource,
                     chunk_size: int = 1000, priority: int = 1) -> ViolationDataStream:
        """Create a new violation stream"""
        with self._lock:
            stream = ViolationDataStream(data_source, chunk_size)
            self._streams[name] = stream
            self._stream_priorities[name] = priority
            
            self._global_stats['total_streams'] += 1
            self._global_stats['total_violations'] += len(stream)
            
            # Check if we need to optimize memory usage
            self._check_memory_usage()
            
            return stream
    
    def get_stream(self, name: str) -> Optional[ViolationDataStream]:
        """Get existing stream"""
        with self._lock:
            return self._streams.get(name)
    
    def remove_stream(self, name: str):
        """Remove stream and free resources"""
        with self._lock:
            if name in self._streams:
                stream = self._streams.pop(name)
                stream.clear_cache()
                
                if name in self._stream_priorities:
                    del self._stream_priorities[name]
                
                self._global_stats['total_streams'] -= 1
    
    def _check_memory_usage(self):
        """Check total memory usage and optimize if needed"""
        total_memory = 0.0
        
        for stream in self._streams.values():
            stats = stream.get_streaming_stats()
            total_memory += stats.memory_usage_mb
        
        self._global_stats['total_memory_mb'] = total_memory
        
        if total_memory > self.max_total_memory_mb:
            self._optimize_memory_usage()
    
    def _optimize_memory_usage(self):
        """Optimize memory usage across all streams"""
        # Sort streams by priority (lower priority optimized first)
        sorted_streams = sorted(
            self._streams.items(),
            key=lambda x: self._stream_priorities.get(x[0], 1)
        )
        
        for name, stream in sorted_streams:
            stream.optimize_for_memory_pressure()
            
            # Check if we're under the limit now
            total_memory = sum(
                s.get_streaming_stats().memory_usage_mb 
                for s in self._streams.values()
            )
            
            if total_memory <= self.max_total_memory_mb:
                break
        
        self._global_stats['memory_optimizations'] += 1
    
    def get_global_stats(self) -> Dict[str, Any]:
        """Get global streaming statistics"""
        with self._lock:
            stream_stats = {}
            total_memory = 0.0
            
            for name, stream in self._streams.items():
                stats = stream.get_streaming_stats()
                stream_stats[name] = {
                    'violations': stats.total_violations,
                    'memory_mb': stats.memory_usage_mb,
                    'cache_hit_ratio': stats.cache_hits / max(stats.cache_hits + stats.cache_misses, 1),
                    'priority': self._stream_priorities.get(name, 1)
                }
                total_memory += stats.memory_usage_mb
            
            return {
                'global': {
                    **self._global_stats,
                    'current_memory_mb': total_memory,
                    'memory_utilization': total_memory / self.max_total_memory_mb
                },
                'streams': stream_stats,
                'timestamp': time.time()
            }
    
    def cleanup(self):
        """Clean up all streams"""
        with self._lock:
            for stream in self._streams.values():
                stream.clear_cache()
            self._streams.clear()
            self._stream_priorities.clear()