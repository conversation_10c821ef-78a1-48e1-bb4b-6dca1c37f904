# 寄存器表格解析器 - 用户文档

## 📋 目录

1. [概述](#概述)
2. [安装与启动](#安装与启动)
3. [界面介绍](#界面介绍)
4. [功能详解](#功能详解)
5. [文件格式要求](#文件格式要求)
6. [使用教程](#使用教程)
7. [高级功能](#高级功能)
8. [故障排除](#故障排除)
9. [性能优化](#性能优化)
10. [常见问题](#常见问题)

---

## 概述

寄存器表格解析器是一个专业的硬件寄存器规格表解析和编辑工具，支持：

- 📁 **Excel文件解析** - 解析标准格式的寄存器规格表
- 🔍 **智能搜索** - 按名称或地址快速查找寄存器
- ⚙️ **交互式编辑** - 实时修改字段值并计算寄存器值
- 🔢 **多格式支持** - 二进制、十进制、十六进制格式转换
- 📊 **数据验证** - 自动验证数据完整性和格式正确性
- 🚀 **性能优化** - 支持大型寄存器表的高效处理

---

## 安装与启动

### 系统要求

- **操作系统**: Windows 10/11, Linux, macOS
- **Python版本**: 3.7+
- **依赖库**: PyQt5, openpyxl, psutil (可选)

### 启动方式

1. **通过主程序菜单**
   - 启动主程序
   - 点击菜单栏 `工具` → `寄存器表格解析器`

2. **直接运行插件**
   ```bash
   cd plugins/user/register_table_parser
   python main_window.py
   ```

---

## 界面介绍

### 主界面布局

```
┌─────────────────────────────────────────────────────────────┐
│ 📁 加载Excel文件  🔄 刷新  │  文件: example.xlsx  │ ❓ 帮助 │
├─────────────────────────────────────────────────────────────┤
│ 📋 表头信息                                                  │
│ 项目名称: [项目名]  子系统: [子系统名]                        │
│ 模块名称: [模块名]  基地址: [0x1000]                         │
├─────────────────┬───────────────────────────────────────────┤
│ 📋 寄存器列表    │ ⚙️ 字段编辑器                              │
│ ┌─────────────┐ │ ┌───────────────────────────────────────┐ │
│ │ 🔍 搜索框   │ │ │ 寄存器值: 0x12345678                  │ │
│ ├─────────────┤ │ ├───────────────────────────────────────┤ │
│ │ 偏移 │ 名称  │ │ │ 字段名    │ 位范围 │ 属性 │ 值      │ │
│ ├─────────────┤ │ ├───────────────────────────────────────┤ │
│ │0x000│REG1   │ │ │ FIELD_A   │ 31:24  │ RW   │ [0x12] │ │
│ │0x004│REG2   │ │ │ FIELD_B   │ 23:16  │ RO   │  0x34  │ │
│ │0x008│REG3   │ │ │ FIELD_C   │ 15:8   │ RW   │ [0x56] │ │
│ └─────────────┘ │ └───────────────────────────────────────┘ │
├─────────────────┴───────────────────────────────────────────┤
│ 状态: ✅ 解析完成: 128 个寄存器, 456 个字段                   │
└─────────────────────────────────────────────────────────────┘
```

### 界面组件说明

1. **工具栏**
   - 📁 加载Excel文件：选择并解析寄存器规格表
   - 🔄 刷新：重新加载当前文件
   - 文件信息：显示当前加载的文件名
   - ❓ 帮助：显示使用帮助

2. **表头信息面板**
   - 显示从Excel文件前4行提取的项目信息
   - 包括项目名称、子系统、模块名称、基地址

3. **寄存器列表**
   - 🔍 搜索框：支持按名称或地址搜索
   - 寄存器列表：显示偏移地址和寄存器名称
   - 点击选择寄存器查看详细信息

4. **字段编辑器**
   - 寄存器值显示：实时显示计算的寄存器值
   - 字段列表：显示选中寄存器的所有字段
   - 值编辑：可编辑字段支持实时修改

5. **状态栏**
   - 显示当前操作状态和统计信息

---

## 功能详解

### 1. 文件加载功能

#### 支持的文件格式
- `.xlsx` (Excel 2007+)
- `.xls` (Excel 97-2003)

#### 加载流程
1. 点击 `📁 加载Excel文件` 按钮
2. 选择寄存器规格表文件
3. 系统自动验证文件格式
4. 解析表头信息和寄存器数据
5. 更新界面显示

#### 进度指示
- 显示解析进度条
- 实时更新解析状态
- 完成后显示统计信息

### 2. 寄存器搜索功能

#### 搜索方式
- **按名称搜索**: 输入寄存器名称的部分或全部
- **按地址搜索**: 支持十六进制(0x1000)和十进制(4096)格式
- **实时过滤**: 输入时实时更新搜索结果

#### 搜索示例
```
搜索词          匹配结果
CTRL           CTRL_REG, SYS_CTRL, CTRL_STATUS
0x1000         偏移地址为0x1000的寄存器
4096           偏移地址为4096(0x1000)的寄存器
```

### 3. 字段编辑功能

#### 字段类型
- **RW (读写)**: 可编辑字段，支持值修改
- **RO (只读)**: 只读字段，显示但不可编辑
- **Reserved**: 保留字段，自动跳过不显示

#### 编辑操作
1. 选择寄存器
2. 在字段编辑器中找到可编辑字段
3. 点击输入框修改值
4. 系统自动重新计算寄存器值

#### 实时计算
- 修改任何字段值后立即重新计算寄存器总值
- 支持位域合并和掩码操作
- 自动处理字段重叠检测

### 4. 数字格式转换

#### 支持的格式
- **二进制**: `0b11111111` (前缀0b)
- **十进制**: `255` (无前缀)
- **十六进制**: `0xFF` (前缀0x)

#### 格式切换
1. 在字段编辑器中选择数字格式
2. 所有字段值自动转换为选定格式
3. 寄存器值同步更新显示格式

#### 输入支持
- 自动识别输入格式
- 支持大小写不敏感
- 自动去除前后空格

---

## 文件格式要求

### Excel文件结构

#### 表头信息 (前4行)
```
行号  A列标题        B列内容
1     Project Name   [具体项目名称，如QogirS6]
2     Sub System     [子系统名称，如apcpu_top_pwr_wrap]
3     Module Name    [模块名称，如apcpu_aon_apb_rf]
4     BASE ADDR      [基地址，如0x6495_1000]
```

#### 其他表头信息 (第5-9行)
第5-9行为其他表头信息，解析器会自动忽略这些行。

#### 寄存器表头 (第10行)
第10行为固定的寄存器表头，包含以下列：
```
A列: Offset | B列: RegName | C列: Short Description | D列: Register Description | 
E列: Width | F列: Other Reset | G列: Protect Register | H列: Bit | 
I列: FieldName | J列: RW | K列: Reset Value | L列: Set/Clear | M列: Misc | N列: Description
```

#### Register Group行 (第11行)
第11行通常为"Register group"分组标识，解析器会自动跳过。

#### 数据行格式 (第12行开始)
```
列位置  列名         说明                    示例
A列     Offset       寄存器偏移地址          0x0000, 0x6495_1000
B列     RegName      寄存器名称              CTRL_REG, STATUS_REG
C列     忽略         短描述（忽略）          -
D列     忽略         寄存器描述（忽略）      -
E列     Width        寄存器位宽              32, 16
F列     忽略         其他复位（忽略）        -
G列     忽略         保护寄存器（忽略）      -
H列     Bit          位域范围                [31:24], [15:8], [0]
I列     FieldName    位域名称                ENABLE, MODE, STATUS
J列     RW           读写属性                RW, RO, WO
K列     ResetValue   位域复位值              0x0, 0xFF, 0b1010
L列     Set/Clear    是否支持set/clear操作   Yes, No, -
M列     忽略         其他信息（忽略）        -
N列     忽略         描述信息（忽略）        -
```

### 数据验证规则

1. **表头验证**
   - 前4行必须包含完整的项目信息
   - 基地址支持带下划线的十六进制格式（如0x6495_1000）

2. **表格结构验证**
   - 第10行必须为寄存器表头
   - 第12行开始为寄存器数据
   - 至少需要12行数据

3. **数据完整性**
   - 偏移地址不能重复
   - 位范围不能超出寄存器宽度
   - 字段名称在同一寄存器内不能重复

4. **格式验证**
   - 偏移地址支持0x前缀的十六进制（可带下划线）或纯十进制
   - 位范围支持带中括号的格式："[31:24]"、"[15]"等
   - RW属性支持"RW"、"RO"、"WO"
   - 自动跳过"Reserved"、"RSVD"等保留字段

5. **新格式特性**
   - 支持Set/Clear操作标识
   - 支持寄存器位宽定义
   - 固定列位置映射，提高解析准确性

---

## 使用教程

### 快速入门

#### 第一步：准备Excel文件
1. 按照格式要求准备寄存器规格表
2. 确保包含必需的表头信息和列标题
3. 验证数据格式正确性

#### 第二步：加载文件
1. 启动寄存器表格解析器
2. 点击 `📁 加载Excel文件`
3. 选择准备好的Excel文件
4. 等待解析完成

#### 第三步：浏览寄存器
1. 在寄存器列表中浏览所有寄存器
2. 使用搜索框快速定位特定寄存器
3. 点击寄存器查看详细字段信息

#### 第四步：编辑字段值
1. 选择要编辑的寄存器
2. 在字段编辑器中找到可编辑字段
3. 修改字段值
4. 观察寄存器值的实时更新

### 高级使用技巧

#### 1. 批量搜索
- 使用通配符搜索相关寄存器
- 利用地址范围快速定位寄存器组

#### 2. 格式转换
- 根据需要切换数字显示格式
- 利用不同格式便于理解和调试

#### 3. 数据验证
- 注意观察状态栏的错误提示
- 及时修正数据格式问题

---

## 高级功能

### 1. 性能优化

#### 大数据集处理
- 自动检测大型寄存器表(>500个寄存器)
- 启用虚拟化显示提高响应速度
- 使用延迟加载减少内存占用

#### 内存管理
- 智能缓存常用数据
- 自动清理未使用的资源
- 监控内存使用情况

#### 搜索优化
- 构建搜索索引加速查找
- 缓存搜索结果避免重复计算
- 支持模糊匹配和精确匹配

### 2. 数据导出

#### 支持的导出格式
- CSV格式：便于Excel处理
- JSON格式：便于程序处理
- 文本格式：便于查看和打印

#### 导出选项
- 导出全部寄存器
- 导出搜索结果
- 导出当前编辑状态

### 3. 批量操作

#### 批量编辑
- 选择多个寄存器进行批量操作
- 批量设置字段值
- 批量格式转换

#### 批量验证
- 验证所有寄存器数据完整性
- 检查字段值范围
- 生成验证报告

---

## 故障排除

### 常见错误及解决方案

#### 1. 文件解析失败

**错误信息**: "Excel文件解析失败"

**可能原因**:
- 文件格式不正确
- 文件损坏或被占用
- 缺少必需的列标题

**解决方案**:
1. 检查文件格式是否为.xlsx或.xls
2. 确保文件未被其他程序占用
3. 验证表头信息和列标题完整性
4. 尝试用Excel重新保存文件

#### 2. 数据验证错误

**错误信息**: "数据验证失败: 缺少必需列"

**解决方案**:
1. 检查Excel文件是否包含所有必需列
2. 确认列标题拼写正确
3. 验证数据行格式符合要求

#### 3. 内存不足

**错误信息**: "内存不足，无法加载大型文件"

**解决方案**:
1. 关闭其他不必要的程序
2. 分割大型Excel文件为多个小文件
3. 启用性能优化模式

#### 4. 界面响应缓慢

**解决方案**:
1. 检查是否处理大型数据集
2. 启用虚拟化显示模式
3. 清理缓存和临时数据
4. 重启应用程序

### 调试模式

#### 启用调试模式
```python
# 在启动时设置环境变量
export DEBUG_MODE=1
python main_window.py
```

#### 调试信息
- 详细的错误堆栈信息
- 性能监控数据
- 内存使用统计
- 操作日志记录

---

## 性能优化

### 系统要求优化

#### 推荐配置
- **内存**: 8GB+ (处理大型文件时)
- **CPU**: 多核处理器
- **存储**: SSD硬盘

#### 性能设置
1. **虚拟化显示**: 自动启用(>500个寄存器)
2. **缓存大小**: 默认1000项，可调整
3. **批处理大小**: 默认100项，可调整

### 大数据集优化策略

#### 1. 分页加载
- 只加载当前可见的寄存器
- 按需加载详细信息
- 智能预加载相邻数据

#### 2. 索引优化
- 构建名称和地址索引
- 使用哈希表加速查找
- 缓存常用搜索结果

#### 3. 内存管理
- 及时释放未使用的数据
- 使用弱引用避免内存泄漏
- 定期执行垃圾回收

### 性能监控

#### 监控指标
- 文件加载时间
- 搜索响应时间
- 内存使用量
- UI响应延迟

#### 性能报告
```
性能监控报告:
--------------------------------------------------
data_optimization:
  调用次数: 1
  平均时间: 0.1234秒
  总时间: 0.1234秒

search_operation:
  调用次数: 15
  平均时间: 0.0045秒
  总时间: 0.0675秒

当前内存使用: 156.78 MB
```

---

## 常见问题

### Q1: 支持哪些Excel文件版本？
**A**: 支持Excel 2007+的.xlsx格式和Excel 97-2003的.xls格式。

### Q2: 如何处理包含中文的寄存器名称？
**A**: 完全支持中文字符，确保Excel文件使用UTF-8编码保存。

### Q3: 可以同时打开多个寄存器表吗？
**A**: 当前版本每次只能打开一个文件，但可以通过菜单打开多个插件窗口。

### Q4: 如何备份编辑的数据？
**A**: 建议定期导出数据或保存Excel文件的副本。

### Q5: 程序崩溃后如何恢复数据？
**A**: 程序会自动保存最近的操作状态，重启后可以恢复到最后的编辑状态。

### Q6: 如何提高大文件的处理速度？
**A**: 
1. 启用性能优化模式
2. 增加系统内存
3. 使用SSD硬盘
4. 关闭不必要的后台程序

### Q7: 支持自定义数字格式吗？
**A**: 当前支持二进制、十进制、十六进制三种标准格式。

### Q8: 如何报告Bug或提出功能建议？
**A**: 请通过以下方式联系：
- 邮件: <EMAIL>
- 问题跟踪: GitHub Issues
- 内部反馈系统

---

## 版本历史

### v1.0.0 (当前版本)
- ✅ 基础Excel解析功能
- ✅ 寄存器列表显示和搜索
- ✅ 字段编辑和实时计算
- ✅ 多种数字格式支持
- ✅ 性能优化和大数据集支持

### 计划功能
- 📋 数据导出功能
- 🔄 批量操作支持
- 📊 数据统计和报告
- 🎨 界面主题定制
- 🔌 插件扩展支持

---

## 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **用户手册**: 本文档
- **在线帮助**: 程序内置帮助系统

### 支持时间
- **工作日**: 9:00-18:00
- **响应时间**: 24小时内
- **紧急问题**: 4小时内

---

*最后更新: 2024年1月*
*版本: v1.0.0*