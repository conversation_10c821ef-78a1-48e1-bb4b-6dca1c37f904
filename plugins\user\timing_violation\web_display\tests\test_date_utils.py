"""
Unit tests for Date utilities.
"""

import unittest
from datetime import datetime, timedelta

# Import the module to test
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from utils.date_utils import DateUtils


class TestDateUtils(unittest.TestCase):
    """Test cases for DateUtils class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_datetime = datetime(2024, 1, 15, 10, 30, 45)
        self.test_date_strings = [
            "2024-01-15 10:30:45",
            "2024-01-15 10:30:45.123456",
            "2024/01/15 10:30:45",
            "2024-01-15",
            "2024/01/15",
            "15-01-2024 10:30:45",
            "15/01/2024 10:30:45",
            "15-01-2024",
            "15/01/2024",
            "01-15-2024 10:30:45",
            "01/15/2024 10:30:45",
            "01-15-2024",
            "01/15/2024",
            "20240115_103045",
            "20240115103045",
            "20240115"
        ]
    
    def test_parse_date_valid_formats(self):
        """Test parsing various valid date formats."""
        for date_string in self.test_date_strings:
            with self.subTest(date_string=date_string):
                result = DateUtils.parse_date(date_string)
                self.assertIsNotNone(result, f"Failed to parse: {date_string}")
                self.assertIsInstance(result, datetime)
    
    def test_parse_date_invalid_formats(self):
        """Test parsing invalid date formats."""
        invalid_dates = [
            "invalid_date",
            "2024-13-01",  # Invalid month
            "2024-01-32",  # Invalid day
            "2024-01-01 25:00:00",  # Invalid hour
            "",
            "   ",
            None
        ]
        
        for date_string in invalid_dates:
            with self.subTest(date_string=date_string):
                result = DateUtils.parse_date(date_string)
                self.assertIsNone(result, f"Should not parse: {date_string}")
    
    def test_parse_date_iso_format(self):
        """Test parsing ISO format dates."""
        iso_dates = [
            "2024-01-15T10:30:45",
            "2024-01-15T10:30:45Z",
            "2024-01-15T10:30:45+00:00"
        ]
        
        for date_string in iso_dates:
            with self.subTest(date_string=date_string):
                result = DateUtils.parse_date(date_string)
                self.assertIsNotNone(result, f"Failed to parse ISO date: {date_string}")
    
    def test_parse_date_timestamp_extraction(self):
        """Test extracting timestamp from text."""
        text_with_timestamp = "Log entry at 2024-01-15 10:30:45 shows error"
        
        result = DateUtils.parse_date(text_with_timestamp)
        
        self.assertIsNotNone(result)
        self.assertEqual(result.year, 2024)
        self.assertEqual(result.month, 1)
        self.assertEqual(result.day, 15)
    
    def test_format_date_default(self):
        """Test default date formatting."""
        result = DateUtils.format_date(self.test_datetime)
        
        self.assertEqual(result, "2024-01-15 10:30:45")
    
    def test_format_date_custom_format(self):
        """Test custom date formatting."""
        result = DateUtils.format_date(self.test_datetime, "%Y/%m/%d")
        
        self.assertEqual(result, "2024/01/15")
    
    def test_format_date_invalid_input(self):
        """Test formatting invalid input."""
        result = DateUtils.format_date("not_a_date")
        
        self.assertEqual(result, "")
    
    def test_format_date_for_web(self):
        """Test web-friendly date formatting."""
        result = DateUtils.format_date_for_web(self.test_datetime)
        
        self.assertEqual(result, "2024-01-15 10:30:45")
    
    def test_format_date_for_filename(self):
        """Test filename-safe date formatting."""
        result = DateUtils.format_date_for_filename(self.test_datetime)
        
        self.assertEqual(result, "20240115_103045")
        # Ensure no special characters that could cause issues in filenames
        self.assertNotIn(":", result)
        self.assertNotIn("/", result)
        self.assertNotIn("\\", result)
    
    def test_get_current_timestamp(self):
        """Test getting current timestamp."""
        result = DateUtils.get_current_timestamp()
        
        self.assertIsInstance(result, str)
        self.assertGreater(len(result), 0)
        
        # Should be parseable
        parsed = DateUtils.parse_date(result)
        self.assertIsNotNone(parsed)
    
    def test_get_current_timestamp_for_filename(self):
        """Test getting current timestamp for filename."""
        result = DateUtils.get_current_timestamp_for_filename()
        
        self.assertIsInstance(result, str)
        self.assertGreater(len(result), 0)
        
        # Should not contain special characters
        self.assertNotIn(":", result)
        self.assertNotIn("/", result)
        self.assertNotIn(" ", result)
    
    def test_is_valid_date_string(self):
        """Test date string validation."""
        for date_string in self.test_date_strings:
            with self.subTest(date_string=date_string):
                result = DateUtils.is_valid_date_string(date_string)
                self.assertTrue(result, f"Should be valid: {date_string}")
        
        invalid_dates = ["invalid", "", None, "2024-13-01"]
        for date_string in invalid_dates:
            with self.subTest(date_string=date_string):
                result = DateUtils.is_valid_date_string(date_string)
                self.assertFalse(result, f"Should be invalid: {date_string}")
    
    def test_get_date_range_string(self):
        """Test date range string formatting."""
        start_date = datetime(2024, 1, 15, 10, 0, 0)
        end_date = datetime(2024, 1, 16, 15, 30, 0)
        
        result = DateUtils.get_date_range_string(start_date, end_date)
        
        expected = "2024-01-15 10:00:00 - 2024-01-16 15:30:00"
        self.assertEqual(result, expected)
    
    def test_get_date_range_string_invalid(self):
        """Test date range string with invalid input."""
        result = DateUtils.get_date_range_string("not_a_date", self.test_datetime)
        
        self.assertEqual(result, "")
    
    def test_get_relative_time_string(self):
        """Test relative time string generation."""
        now = datetime.now()
        
        # Test various time differences
        test_cases = [
            (now - timedelta(seconds=30), "30 seconds ago"),
            (now - timedelta(minutes=5), "5 minutes ago"),
            (now - timedelta(minutes=1), "1 minute ago"),
            (now - timedelta(hours=2), "2 hours ago"),
            (now - timedelta(hours=1), "1 hour ago"),
            (now - timedelta(days=3), "3 days ago"),
            (now - timedelta(days=1), "1 day ago"),
            (now - timedelta(weeks=2), "2 weeks ago"),
            (now - timedelta(weeks=1), "1 week ago"),
            (now + timedelta(hours=1), "in the future")
        ]
        
        for test_date, expected_pattern in test_cases:
            with self.subTest(test_date=test_date):
                result = DateUtils.get_relative_time_string(test_date)
                if "ago" in expected_pattern:
                    self.assertIn("ago", result)
                elif "future" in expected_pattern:
                    self.assertIn("future", result)
    
    def test_get_relative_time_string_invalid(self):
        """Test relative time string with invalid input."""
        result = DateUtils.get_relative_time_string("not_a_date")
        
        self.assertEqual(result, "")
    
    def test_add_time_delta(self):
        """Test adding time delta to datetime."""
        result = DateUtils.add_time_delta(self.test_datetime, days=1, hours=2)
        
        expected = self.test_datetime + timedelta(days=1, hours=2)
        self.assertEqual(result, expected)
    
    def test_add_time_delta_invalid_input(self):
        """Test adding time delta to invalid input."""
        result = DateUtils.add_time_delta("not_a_date", days=1)
        
        self.assertEqual(result, "not_a_date")  # Should return original value
    
    def test_get_start_of_day(self):
        """Test getting start of day."""
        result = DateUtils.get_start_of_day(self.test_datetime)
        
        expected = datetime(2024, 1, 15, 0, 0, 0, 0)
        self.assertEqual(result, expected)
    
    def test_get_end_of_day(self):
        """Test getting end of day."""
        result = DateUtils.get_end_of_day(self.test_datetime)
        
        expected = datetime(2024, 1, 15, 23, 59, 59, 999999)
        self.assertEqual(result, expected)
    
    def test_is_same_day(self):
        """Test checking if dates are on same day."""
        date1 = datetime(2024, 1, 15, 10, 0, 0)
        date2 = datetime(2024, 1, 15, 20, 30, 0)
        date3 = datetime(2024, 1, 16, 10, 0, 0)
        
        self.assertTrue(DateUtils.is_same_day(date1, date2))
        self.assertFalse(DateUtils.is_same_day(date1, date3))
    
    def test_is_same_day_invalid(self):
        """Test same day check with invalid input."""
        result = DateUtils.is_same_day("not_a_date", self.test_datetime)
        
        self.assertFalse(result)
    
    def test_get_duration_string(self):
        """Test duration string generation."""
        start = datetime(2024, 1, 15, 10, 0, 0)
        
        test_cases = [
            (start + timedelta(seconds=30), "30s"),
            (start + timedelta(minutes=5), "5m"),
            (start + timedelta(minutes=5, seconds=30), "5m 30s"),
            (start + timedelta(hours=2), "2h"),
            (start + timedelta(hours=2, minutes=30), "2h 30m"),
            (start + timedelta(days=1), "1d"),
            (start + timedelta(days=1, hours=5), "1d 5h")
        ]
        
        for end_date, expected in test_cases:
            with self.subTest(end_date=end_date):
                result = DateUtils.get_duration_string(start, end_date)
                self.assertEqual(result, expected)
    
    def test_get_duration_string_reversed(self):
        """Test duration string with reversed dates."""
        start = datetime(2024, 1, 15, 12, 0, 0)
        end = datetime(2024, 1, 15, 10, 0, 0)  # Earlier than start
        
        result = DateUtils.get_duration_string(start, end)
        
        self.assertEqual(result, "2h")  # Should handle reversed dates
    
    def test_get_duration_string_invalid(self):
        """Test duration string with invalid input."""
        result = DateUtils.get_duration_string("not_a_date", self.test_datetime)
        
        self.assertEqual(result, "")
    
    def test_normalize_date_string(self):
        """Test date string normalization."""
        test_cases = [
            ("2024-01-15 10:30:45", "2024-01-15 10:30:45"),
            ("2024/01/15", "2024-01-15 00:00:00"),
            ("invalid_date", "invalid_date")  # Should return original if unparseable
        ]
        
        for input_date, expected in test_cases:
            with self.subTest(input_date=input_date):
                result = DateUtils.normalize_date_string(input_date)
                if expected == input_date and input_date == "invalid_date":
                    self.assertEqual(result, expected)
                else:
                    # For valid dates, just check that it's a valid format
                    self.assertIsNotNone(DateUtils.parse_date(result))
    
    def test_get_date_boundaries_full_date(self):
        """Test getting date boundaries for full date."""
        start, end = DateUtils.get_date_boundaries("2024-01-15 10:30:45")
        
        self.assertEqual(start, datetime(2024, 1, 15, 0, 0, 0, 0))
        self.assertEqual(end, datetime(2024, 1, 15, 23, 59, 59, 999999))
    
    def test_get_date_boundaries_year_only(self):
        """Test getting date boundaries for year only."""
        start, end = DateUtils.get_date_boundaries("2024")
        
        self.assertEqual(start, datetime(2024, 1, 1, 0, 0, 0, 0))
        self.assertEqual(end, datetime(2024, 12, 31, 23, 59, 59, 999999))
    
    def test_get_date_boundaries_year_month(self):
        """Test getting date boundaries for year-month."""
        start, end = DateUtils.get_date_boundaries("2024-01")
        
        self.assertEqual(start, datetime(2024, 1, 1, 0, 0, 0, 0))
        self.assertEqual(end, datetime(2024, 1, 31, 23, 59, 59, 999999))
    
    def test_get_date_boundaries_december(self):
        """Test getting date boundaries for December (year boundary)."""
        start, end = DateUtils.get_date_boundaries("2024-12")
        
        self.assertEqual(start, datetime(2024, 12, 1, 0, 0, 0, 0))
        self.assertEqual(end, datetime(2024, 12, 31, 23, 59, 59, 999999))
    
    def test_get_date_boundaries_invalid(self):
        """Test getting date boundaries for invalid input."""
        start, end = DateUtils.get_date_boundaries("invalid")
        
        self.assertIsNone(start)
        self.assertIsNone(end)
    
    def test_get_date_boundaries_empty(self):
        """Test getting date boundaries for empty input."""
        start, end = DateUtils.get_date_boundaries("")
        
        self.assertIsNone(start)
        self.assertIsNone(end)


if __name__ == '__main__':
    unittest.main()