#!/usr/bin/env python3
"""
时序违例网页展示功能集成测试

这个脚本测试整个系统的各个组件，确保数据库优先策略、
数据组织、Web界面等功能正常工作。
"""

import os
import sys
import json
import time
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Any


class IntegrationTester:
    """集成测试器"""
    
    def __init__(self):
        self.test_results = []
        self.web_server_process = None
        self.test_port = 8003
        
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'status': status
        }
        self.test_results.append(result)
        print(f"{status} {test_name}: {message}")
        
    def test_data_generation(self) -> bool:
        """测试数据生成功能"""
        print("\n🔄 测试数据生成...")
        
        try:
            # 运行数据生成脚本
            script_path = Path("plugins/user/timing_violation/generate_optimized_web_data.py")
            result = subprocess.run([
                sys.executable, str(script_path)
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.log_test("数据生成", True, "数据生成脚本执行成功")
                return True
            else:
                self.log_test("数据生成", False, f"脚本执行失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.log_test("数据生成", False, "脚本执行超时")
            return False
        except Exception as e:
            self.log_test("数据生成", False, f"执行异常: {e}")
            return False
    
    def test_data_files(self) -> bool:
        """测试生成的数据文件"""
        print("\n🔄 测试数据文件...")
        
        data_dir = Path("VIOLATION_CHECK/web_display/data")
        required_files = [
            "violations.json",
            "index.json", 
            "statistics.json",
            "pagination_manifest.json"
        ]
        
        all_files_exist = True
        
        for file_name in required_files:
            file_path = data_dir / file_name
            if file_path.exists():
                try:
                    # 验证JSON格式
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 特殊验证violations.json
                    if file_name == "violations.json":
                        if 'violations' in data and isinstance(data['violations'], list):
                            violation_count = len(data['violations'])
                            self.log_test(f"文件验证 - {file_name}", True, 
                                        f"包含 {violation_count} 条违例数据")
                        else:
                            self.log_test(f"文件验证 - {file_name}", False, "违例数据格式不正确")
                            all_files_exist = False
                    else:
                        self.log_test(f"文件验证 - {file_name}", True, "文件格式正确")
                        
                except json.JSONDecodeError as e:
                    self.log_test(f"文件验证 - {file_name}", False, f"JSON格式错误: {e}")
                    all_files_exist = False
                except Exception as e:
                    self.log_test(f"文件验证 - {file_name}", False, f"文件读取错误: {e}")
                    all_files_exist = False
            else:
                self.log_test(f"文件验证 - {file_name}", False, "文件不存在")
                all_files_exist = False
        
        return all_files_exist
    
    def test_data_organization(self) -> bool:
        """测试数据组织功能"""
        print("\n🔄 测试数据组织...")
        
        try:
            violations_file = Path("VIOLATION_CHECK/web_display/data/violations.json")
            with open(violations_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查数据结构
            required_keys = ['metadata', 'corners', 'cases', 'violations']
            missing_keys = [key for key in required_keys if key not in data]
            
            if missing_keys:
                self.log_test("数据结构", False, f"缺少必要字段: {missing_keys}")
                return False
            
            # 检查corner和case数据
            corners = data.get('corners', [])
            cases = data.get('cases', [])
            violations = data.get('violations', [])
            
            if not corners:
                self.log_test("Corner数据", False, "没有找到Corner数据")
                return False
            else:
                self.log_test("Corner数据", True, f"找到 {len(corners)} 个Corner: {corners}")
            
            if not cases:
                self.log_test("Case数据", False, "没有找到Case数据")
                return False
            else:
                self.log_test("Case数据", True, f"找到 {len(cases)} 个Case: {cases}")
            
            # 验证违例数据的corner和case字段
            if violations:
                sample_violation = violations[0]
                if 'corner' in sample_violation and 'case' in sample_violation:
                    self.log_test("违例数据组织", True, 
                                f"违例数据包含正确的corner和case字段")
                else:
                    self.log_test("违例数据组织", False, 
                                "违例数据缺少corner或case字段")
                    return False
            
            return True
            
        except Exception as e:
            self.log_test("数据组织", False, f"测试异常: {e}")
            return False
    
    def start_test_web_server(self) -> bool:
        """启动测试Web服务器"""
        print(f"\n🔄 启动测试Web服务器 (端口: {self.test_port})...")
        
        try:
            server_script = Path("plugins/user/timing_violation/start_web_server.py")
            self.web_server_process = subprocess.Popen([
                sys.executable, str(server_script),
                "--port", str(self.test_port),
                "--no-browser"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待服务器启动
            time.sleep(3)
            
            if self.web_server_process.poll() is None:
                self.log_test("Web服务器启动", True, f"服务器在端口 {self.test_port} 启动成功")
                return True
            else:
                self.log_test("Web服务器启动", False, "服务器启动失败")
                return False
                
        except Exception as e:
            self.log_test("Web服务器启动", False, f"启动异常: {e}")
            return False
    
    def test_web_endpoints(self) -> bool:
        """测试Web端点"""
        print("\n🔄 测试Web端点...")
        
        base_url = f"http://localhost:{self.test_port}"
        endpoints = [
            ("主页面", "/"),
            ("独立测试页面", "/standalone_test.html"),
            ("违例数据", "/data/violations.json"),
            ("索引数据", "/data/index.json"),
            ("统计数据", "/data/statistics.json")
        ]
        
        all_endpoints_ok = True
        
        for name, endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    if endpoint.endswith('.json'):
                        # 验证JSON格式
                        try:
                            json.loads(response.text)
                            self.log_test(f"端点测试 - {name}", True, 
                                        f"HTTP {response.status_code}, JSON格式正确")
                        except json.JSONDecodeError:
                            self.log_test(f"端点测试 - {name}", False, 
                                        f"HTTP {response.status_code}, JSON格式错误")
                            all_endpoints_ok = False
                    else:
                        self.log_test(f"端点测试 - {name}", True, 
                                    f"HTTP {response.status_code}")
                else:
                    self.log_test(f"端点测试 - {name}", False, 
                                f"HTTP {response.status_code}")
                    all_endpoints_ok = False
                    
            except requests.RequestException as e:
                self.log_test(f"端点测试 - {name}", False, f"请求失败: {e}")
                all_endpoints_ok = False
        
        return all_endpoints_ok
    
    def test_data_loading_functionality(self) -> bool:
        """测试数据加载功能"""
        print("\n🔄 测试数据加载功能...")
        
        try:
            # 获取violations.json数据
            response = requests.get(f"http://localhost:{self.test_port}/data/violations.json", 
                                  timeout=10)
            
            if response.status_code != 200:
                self.log_test("数据加载功能", False, f"无法获取违例数据: HTTP {response.status_code}")
                return False
            
            data = response.json()
            
            # 验证数据完整性
            violations = data.get('violations', [])
            corners = data.get('corners', [])
            cases = data.get('cases', [])
            
            if not violations:
                self.log_test("数据加载功能", False, "违例数据为空")
                return False
            
            # 验证数据字段完整性
            required_fields = ['num', 'hier', 'check_info', 'status', 'corner', 'case']
            sample_violation = violations[0]
            missing_fields = [field for field in required_fields 
                            if field not in sample_violation]
            
            if missing_fields:
                self.log_test("数据加载功能", False, f"违例数据缺少字段: {missing_fields}")
                return False
            
            # 验证corner和case的一致性
            violation_corners = set(v.get('corner') for v in violations if v.get('corner'))
            violation_cases = set(v.get('case') for v in violations if v.get('case'))
            
            if not violation_corners.issubset(set(corners)):
                self.log_test("数据一致性", False, "违例数据中的corner与corner列表不一致")
                return False
            
            if not violation_cases.issubset(set(cases)):
                self.log_test("数据一致性", False, "违例数据中的case与case列表不一致")
                return False
            
            self.log_test("数据加载功能", True, 
                        f"数据完整且一致: {len(violations)} 条违例, "
                        f"{len(corners)} 个corner, {len(cases)} 个case")
            return True
            
        except Exception as e:
            self.log_test("数据加载功能", False, f"测试异常: {e}")
            return False
    
    def stop_test_web_server(self):
        """停止测试Web服务器"""
        if self.web_server_process:
            print(f"\n🔄 停止测试Web服务器...")
            self.web_server_process.terminate()
            self.web_server_process.wait()
            self.log_test("Web服务器停止", True, "服务器已停止")
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("🧪 开始集成测试...")
        print("=" * 60)
        
        try:
            # 测试数据生成
            if not self.test_data_generation():
                return False
            
            # 测试数据文件
            if not self.test_data_files():
                return False
            
            # 测试数据组织
            if not self.test_data_organization():
                return False
            
            # 启动Web服务器
            if not self.start_test_web_server():
                return False
            
            # 测试Web端点
            if not self.test_web_endpoints():
                return False
            
            # 测试数据加载功能
            if not self.test_data_loading_functionality():
                return False
            
            return True
            
        finally:
            # 确保停止Web服务器
            self.stop_test_web_server()
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "=" * 60)
        print("🧪 集成测试摘要")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        print("\n详细结果:")
        for result in self.test_results:
            print(f"  {result['status']} {result['test']}: {result['message']}")
        
        if passed == total:
            print(f"\n🎉 所有测试通过！系统运行正常。")
            print(f"🌐 访问地址: http://localhost:8000/standalone_test.html")
            return True
        else:
            print(f"\n❌ 有 {total - passed} 个测试失败，请检查相关功能。")
            return False


def main():
    """主函数"""
    tester = IntegrationTester()
    
    try:
        success = tester.run_all_tests()
        final_success = tester.print_summary()
        
        if success and final_success:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        tester.stop_test_web_server()
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        tester.stop_test_web_server()
        sys.exit(1)


if __name__ == "__main__":
    main()