#!/usr/bin/env python3
"""
测试网页数据导出功能
"""

import sys
import os
from pathlib import Path

# 添加插件路径
sys.path.insert(0, str(Path(__file__).parent / "plugins" / "user" / "timing_violation"))

def test_data_export():
    """测试数据导出功能"""
    try:
        from web_display.data_exporter import DataExporter
        
        # 创建测试数据
        test_data = [
            {
                "num": 1,
                "hier": "test/path/module1",
                "time_ns": 1.234,
                "check_info": "setup (clk: 2.5ns, data: 1.2ns, slack: -0.1ns)",
                "status": "pending",
                "confirmer": "",
                "result": "",
                "reason": "",
                "confirmed_at": "",
                "corner": "ss_125c_0p81v",
                "case": "test_case_1"
            },
            {
                "num": 2,
                "hier": "test/path/module2", 
                "time_ns": 2.567,
                "check_info": "hold (clk: 2.5ns, data: 2.8ns, slack: -0.3ns)",
                "status": "confirmed",
                "confirmer": "测试用户",
                "result": "pass",
                "reason": "复位期间违例，可忽略",
                "confirmed_at": "2024-01-15 10:30:00",
                "corner": "ss_125c_0p81v",
                "case": "test_case_1"
            },
            {
                "num": 3,
                "hier": "test/path/module3",
                "time_ns": 0.789,
                "check_info": "setup (clk: 2.0ns, data: 0.8ns, slack: -0.01ns)",
                "status": "pending",
                "confirmer": "",
                "result": "",
                "reason": "",
                "confirmed_at": "",
                "corner": "ff_m40c_1p32v",
                "case": "test_case_2"
            }
        ]
        
        print("创建数据导出器...")
        exporter = DataExporter("VIOLATION_CHECK")
        
        print(f"使用测试数据导出: {len(test_data)} 条记录")
        success = exporter.export_all_data(gui_data=test_data)
        
        if success:
            print("✅ 数据导出成功!")
            
            # 检查生成的文件
            web_dir = Path("VIOLATION_CHECK/web_display")
            files_to_check = [
                "index.html",
                "offline.html", 
                "data/index.json",
                "data/violations.json"
            ]
            
            print("\n检查生成的文件:")
            for file_path in files_to_check:
                full_path = web_dir / file_path
                if full_path.exists():
                    size = full_path.stat().st_size
                    print(f"  ✅ {file_path} ({size} bytes)")
                else:
                    print(f"  ❌ {file_path} (不存在)")
            
            print(f"\n网页文件位置: {web_dir.absolute()}")
            print("可以在浏览器中打开 offline.html 查看结果")
            
        else:
            print("❌ 数据导出失败!")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_export()