# Web Template for Timing Violation Display

This directory contains the web interface template files for the timing violation display system.

## Structure

```
web_template/
├── index.html              # Main HTML page template
├── css/
│   ├── bootstrap.min.css   # Bootstrap CSS (placeholder - uses CDN)
│   └── custom.css          # Custom styles for timing violations
├── js/
│   ├── bootstrap.min.js    # Bootstrap JS (placeholder - uses CDN)
│   ├── jquery.min.js       # jQuery (placeholder - uses CDN)
│   └── app.js              # Main application JavaScript
├── test_data.json          # Sample index data for testing
├── test_violations.json    # Sample violation data for testing
└── README.md               # This file
```

## Features

### Responsive Design
- Bootstrap 5 framework for responsive layout
- Mobile-friendly interface that works on desktop, tablet, and mobile
- Adaptive table layout with horizontal scrolling on smaller screens

### Filter Controls
- Corner name dropdown filter
- Case name dropdown filter (updates based on selected corner)
- Status filter (All, Confirmed, Pending)
- Clear filters and refresh data buttons

### Data Table
- DataTables integration with virtual scrolling for performance
- Sortable columns with proper data types
- Search functionality
- Pagination controls
- Responsive column sizing

### Statistics Dashboard
- Real-time statistics cards showing:
  - Total violations count
  - Confirmed violations count
  - Pending violations count
  - Confirmation rate percentage

### Performance Optimizations
- Virtual scrolling for large datasets (10,000+ records)
- Data caching to minimize API calls
- Lazy loading of corner/case specific data
- Efficient DOM manipulation and rendering

### Error Handling
- Loading overlay during data operations
- Error modal for user-friendly error messages
- Graceful fallback when data files are missing
- Retry functionality for failed operations

## Data Format

The web interface expects JSON data files in the following structure:

### Index Data (`data/index.json`)
```json
{
    "statistics": {
        "total_violations": 150,
        "confirmed_violations": 120,
        "pending_violations": 30,
        "confirmation_rate": 80.0
    },
    "corners": ["corner1", "corner2", "corner3"],
    "cases": ["case1", "case2", "case3"]
}
```

### Violation Data (`data/violations/*.json`)
```json
[
    {
        "num": 1,
        "hier": "top.cpu.core0.alu",
        "time_ns": 2.456,
        "check_info": "Setup time violation",
        "status": "confirmed",
        "confirmer": "John Doe",
        "result": "Valid",
        "reason": "Design constraint verified",
        "confirmed_at": "2024-01-15T10:30:00",
        "corner": "corner1",
        "case": "case1"
    }
]
```

## Usage

1. The `data_exporter.py` script copies these template files to `VIOLATION_CHECK/web_display/`
2. JSON data files are generated in the `data/` subdirectory
3. The web interface can be opened directly in a browser (no server required)
4. All dependencies are loaded from CDN for reliability

## Browser Compatibility

- Modern browsers (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)
- Graceful degradation for older browsers
- Mobile browser support (iOS Safari, Chrome Mobile, Samsung Internet)

## Accessibility

- ARIA labels and roles for screen readers
- Keyboard navigation support
- High contrast mode support
- Focus indicators for interactive elements
- Semantic HTML structure

## Testing

Use the included test data files to verify the interface:
1. Copy `test_data.json` to `data/index.json`
2. Copy `test_violations.json` to `data/violations.json`
3. Open `index.html` in a browser to test functionality