#!/usr/bin/env python3
"""
最终的GUI Web集成测试

测试修复后的GUI Web展示功能。
"""

import sys
import os
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))

def test_sql_fix():
    """测试SQL修复"""
    print("🧪 测试SQL修复...")
    
    try:
        # 导入models模块
        from plugins.user.timing_violation.models import ViolationDataModel
        
        # 创建模型实例
        model = ViolationDataModel()
        
        # 测试get_all_violations方法（这会触发SQL查询）
        violations = model.get_all_violations()
        
        print(f"✅ SQL查询成功，获取到 {len(violations)} 条违例记录")
        
        # 检查数据结构
        if violations:
            sample = violations[0]
            required_fields = ['id', 'case_name', 'corner', 'num', 'hier']
            missing_fields = [field for field in required_fields if field not in sample]
            
            if missing_fields:
                print(f"❌ 数据结构不完整，缺少字段: {missing_fields}")
                return False
            else:
                print(f"✅ 数据结构完整，包含所有必要字段")
        
        return True
        
    except Exception as e:
        print(f"❌ SQL测试失败: {e}")
        return False


def test_gui_web_launcher():
    """测试GUI Web启动器"""
    print("\n🧪 测试GUI Web启动器...")
    
    try:
        # 导入修复版启动器
        from plugins.user.timing_violation.gui_web_launcher_fixed import (
            generate_web_data_for_gui, 
            simple_data_export_for_gui,
            check_web_display_files
        )
        
        # 创建测试数据
        test_violations = [
            {
                'id': 1,
                'num': 1,
                'hier': 'test.signal.path',
                'time_fs': 1000000,
                'time_display': '1000000 FS',
                'check_info': 'Test timing violation',
                'status': 'confirmed',
                'confirmer': 'test_user',
                'result': 'pass',
                'reason': 'test reason',
                'confirmed_at': '2025-08-07',
                'corner': 'test_corner',
                'case_name': 'test_case',
                'case': 'test_case',
                'source': 'gui_test'
            },
            {
                'id': 2,
                'num': 2,
                'hier': 'test.another.signal',
                'time_fs': 2000000,
                'time_display': '2000000 FS',
                'check_info': 'Another test violation',
                'status': 'pending',
                'confirmer': '',
                'result': '',
                'reason': '',
                'confirmed_at': '',
                'corner': 'test_corner',
                'case_name': 'test_case',
                'case': 'test_case',
                'source': 'gui_test'
            }
        ]
        
        # 测试简单数据导出
        print("  测试简单数据导出...")
        if simple_data_export_for_gui(test_violations):
            print("  ✅ 简单数据导出成功")
        else:
            print("  ❌ 简单数据导出失败")
            return False
        
        # 检查生成的文件
        print("  检查生成的文件...")
        files_ok, message = check_web_display_files()
        if files_ok:
            print(f"  ✅ 文件检查通过: {message}")
        else:
            print(f"  ❌ 文件检查失败: {message}")
            return False
        
        # 验证生成的数据
        print("  验证生成的数据...")
        import json
        
        violation_check_dir = "VIOLATION_CHECK"
        if not Path(violation_check_dir).exists():
            violation_check_dir = "../../../VIOLATION_CHECK"
        
        violations_file = Path(violation_check_dir) / "web_display/data/violations.json"
        
        if violations_file.exists():
            with open(violations_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'violations' in data and len(data['violations']) == 2:
                print(f"  ✅ 数据验证成功: {len(data['violations'])} 条违例")
            else:
                print(f"  ❌ 数据验证失败: 期望2条违例，实际{len(data.get('violations', []))}")
                return False
        else:
            print(f"  ❌ 违例数据文件不存在: {violations_file}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ GUI Web启动器测试失败: {e}")
        return False


def test_main_window_integration():
    """测试主窗口集成"""
    print("\n🧪 测试主窗口集成...")
    
    try:
        # 模拟主窗口的get_all_violations方法
        class MockMainWindow:
            def __init__(self):
                self.data_model = MockDataModel()
            
            def get_all_violations(self):
                """模拟获取所有违例数据"""
                try:
                    if hasattr(self, 'data_model') and self.data_model:
                        all_violations = self.data_model.get_all_violations()
                        
                        violations = []
                        for violation in all_violations:
                            web_violation = {
                                'id': violation.get('id', 0),
                                'num': violation.get('num', 0),
                                'hier': violation.get('hier', ''),
                                'time_fs': violation.get('time_fs', 0),
                                'time_display': violation.get('time_display', ''),
                                'check_info': violation.get('check_info', ''),
                                'status': violation.get('status', ''),
                                'confirmer': violation.get('confirmer', ''),
                                'result': violation.get('result', ''),
                                'reason': violation.get('reason', ''),
                                'confirmed_at': violation.get('confirmed_at', ''),
                                'corner': violation.get('corner', ''),
                                'case': violation.get('case_name', ''),
                                'case_name': violation.get('case_name', ''),
                                'source': 'gui'
                            }
                            violations.append(web_violation)
                        
                        return violations
                    
                    return []
                    
                except Exception as e:
                    print(f"获取违例数据失败: {e}")
                    return []
        
        class MockDataModel:
            def get_all_violations(self):
                return [
                    {
                        'id': 1,
                        'num': 1,
                        'hier': 'mock.integration.test',
                        'time_fs': 3000000,
                        'time_display': '3000000 FS',
                        'check_info': 'Integration test violation',
                        'status': 'confirmed',
                        'confirmer': 'integration_test',
                        'result': 'pass',
                        'reason': 'integration test',
                        'confirmed_at': '2025-08-07',
                        'corner': 'integration_corner',
                        'case_name': 'integration_case'
                    }
                ]
        
        # 测试模拟主窗口
        mock_window = MockMainWindow()
        violations = mock_window.get_all_violations()
        
        if violations and len(violations) > 0:
            print(f"  ✅ 主窗口数据获取成功: {len(violations)} 条违例")
            
            # 检查数据格式
            sample = violations[0]
            required_fields = ['id', 'num', 'hier', 'status', 'corner', 'case']
            missing_fields = [field for field in required_fields if field not in sample]
            
            if missing_fields:
                print(f"  ❌ 数据格式不完整，缺少字段: {missing_fields}")
                return False
            else:
                print(f"  ✅ 数据格式完整")
        else:
            print("  ❌ 主窗口数据获取失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 主窗口集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 GUI Web展示功能最终测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("SQL修复", test_sql_fix),
        ("GUI Web启动器", test_gui_web_launcher),
        ("主窗口集成", test_main_window_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！GUI Web展示功能已修复并可正常使用。")
        print("\n💡 使用方法:")
        print("   1. 打开时序违例确认工具GUI")
        print("   2. 点击工具栏中的'网页显示'按钮")
        print("   3. 等待系统自动处理并打开浏览器")
        print("   4. 如果浏览器没有自动打开，会直接打开HTML文件")
        return True
    else:
        print(f"\n❌ 有 {total - passed} 个测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)