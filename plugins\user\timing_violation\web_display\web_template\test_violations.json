[{"num": 1, "hier": "top.cpu.core0.alu", "time_ns": 2.456, "check_info": "Setup time violation", "status": "confirmed", "confirmer": "<PERSON>", "result": "<PERSON><PERSON>", "reason": "Design constraint verified", "confirmed_at": "2024-01-15T10:30:00", "corner": "corner1", "case": "case1"}, {"num": 2, "hier": "top.cpu.core1.fpu", "time_ns": 1.234, "check_info": "Hold time violation", "status": "pending", "confirmer": "", "result": "", "reason": "", "confirmed_at": "", "corner": "corner1", "case": "case2"}, {"num": 3, "hier": "top.memory.controller", "time_ns": 3.789, "check_info": "Clock skew violation", "status": "confirmed", "confirmer": "<PERSON>", "result": "False positive", "reason": "Clock domain crossing handled by synchronizer", "confirmed_at": "2024-01-16T14:20:00", "corner": "corner2", "case": "case1"}]