#!/usr/bin/env python3
"""
Test script to debug data export issues
"""

import sys
import os
import sqlite3
import json
from pathlib import Path

# Add the web_display directory to path
sys.path.insert(0, 'plugins/user/timing_violation/web_display')

def test_database_connection():
    """Test direct database connection"""
    db_path = "VIOLATION_CHECK/timing_violations.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test basic connection
        cursor.execute("SELECT 1")
        
        # Check tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ Database connection OK. Tables: {[t[0] for t in tables]}")
        
        # Check violation count
        cursor.execute("SELECT COUNT(*) FROM timing_violations")
        count = cursor.fetchone()[0]
        print(f"📊 Total violations in database: {count}")
        
        # Check some sample data
        cursor.execute("SELECT corner, case_name, COUNT(*) FROM timing_violations GROUP BY corner, case_name LIMIT 5")
        samples = cursor.fetchall()
        print(f"📋 Sample data (corner, case, count): {samples}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_data_export():
    """Test data export functionality"""
    try:
        # Import with proper path handling
        from data_exporter import DataExporter
        
        print("🔧 Creating DataExporter...")
        exporter = DataExporter()
        
        print("🔍 Testing database connection...")
        if exporter.database_reader.test_connection():
            print("✅ Database connection OK")
            
            print("📥 Loading violation data...")
            exporter.load_violation_data()
            
            print(f"📊 Results:")
            print(f"  - Violations loaded: {len(exporter.violations_data)}")
            print(f"  - Corners found: {len(exporter.corners)}")
            print(f"  - Cases found: {len(exporter.cases)}")
            
            if exporter.violations_data:
                print(f"📋 Sample violation: {exporter.violations_data[0]}")
            
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Data export test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_generated_data():
    """Check the generated JSON data files"""
    data_dir = Path("VIOLATION_CHECK/web_display/data")
    
    if not data_dir.exists():
        print(f"❌ Data directory not found: {data_dir}")
        return False
    
    print(f"📁 Checking data directory: {data_dir}")
    
    # Check key files
    key_files = ["index.json", "violations.json", "statistics.json"]
    
    for filename in key_files:
        filepath = data_dir / filename
        if filepath.exists():
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"✅ {filename}: {filepath.stat().st_size} bytes")
                
                if filename == "violations.json":
                    violations = data.get('violations', [])
                    print(f"   - Violations count: {len(violations)}")
                    
                elif filename == "index.json":
                    metadata = data.get('metadata', {})
                    print(f"   - Total violations: {metadata.get('total_violations', 0)}")
                    print(f"   - Corners: {len(data.get('corners', []))}")
                    print(f"   - Cases: {len(data.get('cases', []))}")
                    
            except Exception as e:
                print(f"❌ Error reading {filename}: {e}")
        else:
            print(f"❌ Missing file: {filename}")

if __name__ == "__main__":
    print("🧪 Testing Timing Violation Data Export")
    print("=" * 50)
    
    print("\n1. Testing direct database connection...")
    db_ok = test_database_connection()
    
    print("\n2. Testing data export functionality...")
    export_ok = test_data_export()
    
    print("\n3. Checking generated data files...")
    check_generated_data()
    
    print("\n" + "=" * 50)
    if db_ok and export_ok:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed - check output above")