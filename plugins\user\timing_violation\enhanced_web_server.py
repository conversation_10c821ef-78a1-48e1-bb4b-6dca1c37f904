#!/usr/bin/env python3
"""
增强版Web服务器，支持数据刷新功能
"""

import os
import sys
import json
import subprocess
import threading
import time
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import webbrowser


class DataRefreshHandler(SimpleHTTPRequestHandler):
    """支持数据刷新的HTTP请求处理器"""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def do_POST(self):
        """处理POST请求，支持数据刷新"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/refresh_data':
            self.handle_refresh_data()
        else:
            self.send_response(404)
            self.end_headers()
    
    def handle_refresh_data(self):
        """处理数据刷新请求"""
        try:
            print("🔄 收到数据刷新请求...")
            
            # 在后台线程中重新生成数据
            def regenerate_data():
                try:
                    # 运行数据生成脚本
                    script_path = Path("plugins/user/timing_violation/generate_optimized_web_data.py")
                    if script_path.exists():
                        result = subprocess.run([
                            sys.executable, str(script_path)
                        ], capture_output=True, text=True, cwd=Path.cwd())
                        
                        if result.returncode == 0:
                            print("✅ 数据重新生成成功")
                        else:
                            print(f"❌ 数据重新生成失败: {result.stderr}")
                    else:
                        print(f"❌ 数据生成脚本不存在: {script_path}")
                        
                except Exception as e:
                    print(f"❌ 数据重新生成异常: {e}")
            
            # 启动后台线程
            thread = threading.Thread(target=regenerate_data)
            thread.daemon = True
            thread.start()
            
            # 立即返回成功响应
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            
            response = {
                'status': 'success',
                'message': 'Data regeneration started',
                'timestamp': time.time()
            }
            
            self.wfile.write(json.dumps(response).encode('utf-8'))
            
        except Exception as e:
            print(f"❌ 处理刷新请求失败: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            
            error_response = {
                'status': 'error',
                'message': str(e),
                'timestamp': time.time()
            }
            
            self.wfile.write(json.dumps(error_response).encode('utf-8'))


def find_available_port(start_port=8000, max_attempts=20):
    """查找可用端口"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"无法找到可用端口 (尝试范围: {start_port}-{start_port + max_attempts - 1})")


def start_enhanced_web_server(directory=None, port=None, open_browser=True):
    """
    启动增强版Web服务器
    
    Args:
        directory: 服务目录
        port: 端口号，如果为None则自动查找
        open_browser: 是否自动打开浏览器
    """
    if directory is None:
        directory = Path("VIOLATION_CHECK/web_display").resolve()
    else:
        directory = Path(directory).resolve()
    
    if not directory.exists():
        print(f"❌ 目录不存在: {directory}")
        print("请先运行数据生成脚本: python plugins/user/timing_violation/generate_optimized_web_data.py")
        return False
    
    # 切换到服务目录
    original_cwd = Path.cwd()
    os.chdir(directory)
    
    try:
        # 查找可用端口
        if port is None:
            port = find_available_port(8000)
        
        print("=" * 60)
        print("🚀 增强版时序违例网页展示服务器")
        print("=" * 60)
        print(f"📁 服务目录: {directory}")
        print(f"🌐 访问地址: http://localhost:{port}")
        print(f"🔄 支持数据刷新: http://localhost:{port}/refresh_data")
        print("⏹️ 按 Ctrl+C 停止服务器")
        print("=" * 60)
        
        # 创建服务器
        httpd = HTTPServer(('localhost', port), DataRefreshHandler)
        
        # 自动打开浏览器
        if open_browser:
            url = f"http://localhost:{port}/index.html"
            print(f"🌍 打开浏览器: {url}")
            webbrowser.open(url)
        
        print("✅ 服务器启动成功")
        print("=" * 60)
        
        # 启动服务器
        httpd.serve_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 停止服务器...")
        httpd.shutdown()
        httpd.server_close()
        print("✅ 服务器已停止")
        
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return False
        
    finally:
        # 恢复原始工作目录
        os.chdir(original_cwd)
    
    return True


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="启动增强版时序违例网页展示服务器")
    parser.add_argument("--port", type=int, help="指定端口号")
    parser.add_argument("--directory", help="指定服务目录")
    parser.add_argument("--no-browser", action="store_true", help="不自动打开浏览器")
    
    args = parser.parse_args()
    
    try:
        start_enhanced_web_server(
            directory=args.directory,
            port=args.port,
            open_browser=not args.no_browser
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)