#!/usr/bin/env python3
"""
验证数据库合并功能的核心方法
"""

import sqlite3
import os
import tempfile
import shutil
from datetime import datetime


def test_backup_function():
    """测试备份功能"""
    print("测试备份功能...")
    
    # 创建临时数据库
    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "test.db")
    
    # 创建数据库
    conn = sqlite3.connect(db_path)
    conn.execute("CREATE TABLE test (id INTEGER, name TEXT)")
    conn.execute("INSERT INTO test VALUES (1, 'test')")
    conn.commit()
    conn.close()
    
    # 测试备份
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"test_backup_{timestamp}.db"
    backup_path = os.path.join(temp_dir, backup_filename)
    
    try:
        shutil.copy2(db_path, backup_path)
        print(f"✓ 备份成功: {backup_path}")
        
        # 验证备份文件
        if os.path.exists(backup_path):
            backup_conn = sqlite3.connect(backup_path)
            cursor = backup_conn.cursor()
            cursor.execute("SELECT * FROM test")
            data = cursor.fetchall()
            backup_conn.close()
            
            if data == [(1, 'test')]:
                print("✓ 备份文件内容验证成功")
            else:
                print("✗ 备份文件内容验证失败")
        else:
            print("✗ 备份文件不存在")
            
    except Exception as e:
        print(f"✗ 备份失败: {str(e)}")
    
    finally:
        shutil.rmtree(temp_dir)


def test_schema_validation():
    """测试schema验证功能"""
    print("\n测试schema验证功能...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建正确的数据库
        valid_db = os.path.join(temp_dir, "valid.db")
        conn = sqlite3.connect(valid_db)
        cursor = conn.cursor()
        
        # 创建正确的表结构
        cursor.execute('''
            CREATE TABLE timing_violations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_name TEXT NOT NULL,
                corner TEXT,
                num INTEGER NOT NULL,
                hier TEXT NOT NULL,
                time_fs INTEGER NOT NULL,
                time_display TEXT NOT NULL,
                check_info TEXT NOT NULL,
                file_path TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE confirmation_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                violation_id INTEGER NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending'
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE violation_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                hier_pattern TEXT NOT NULL,
                check_pattern TEXT NOT NULL
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # 验证正确的数据库
        conn = sqlite3.connect(valid_db)
        cursor = conn.cursor()
        
        required_tables = ['timing_violations', 'confirmation_records', 'violation_patterns']
        all_tables_exist = True
        
        for table in required_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if not cursor.fetchone():
                all_tables_exist = False
                break
        
        conn.close()
        
        if all_tables_exist:
            print("✓ 正确数据库schema验证成功")
        else:
            print("✗ 正确数据库schema验证失败")
        
        # 创建错误的数据库
        invalid_db = os.path.join(temp_dir, "invalid.db")
        conn = sqlite3.connect(invalid_db)
        conn.execute("CREATE TABLE wrong_table (id INTEGER)")
        conn.commit()
        conn.close()
        
        # 验证错误的数据库
        conn = sqlite3.connect(invalid_db)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", ('timing_violations',))
        if not cursor.fetchone():
            print("✓ 错误数据库schema验证成功（正确识别为无效）")
        else:
            print("✗ 错误数据库schema验证失败")
        conn.close()
        
    except Exception as e:
        print(f"✗ Schema验证测试失败: {str(e)}")
    
    finally:
        shutil.rmtree(temp_dir)


def test_database_statistics():
    """测试数据库统计功能"""
    print("\n测试数据库统计功能...")
    
    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "stats_test.db")
    
    try:
        # 创建数据库和表
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE timing_violations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_name TEXT NOT NULL
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE confirmation_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                violation_id INTEGER NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending'
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE violation_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                hier_pattern TEXT NOT NULL
            )
        ''')
        
        # 插入测试数据
        cursor.execute("INSERT INTO timing_violations (case_name) VALUES ('case1')")
        cursor.execute("INSERT INTO timing_violations (case_name) VALUES ('case2')")
        cursor.execute("INSERT INTO timing_violations (case_name) VALUES ('case1')")
        
        cursor.execute("INSERT INTO confirmation_records (violation_id, status) VALUES (1, 'confirmed')")
        cursor.execute("INSERT INTO confirmation_records (violation_id, status) VALUES (2, 'pending')")
        cursor.execute("INSERT INTO confirmation_records (violation_id, status) VALUES (3, 'pending')")
        
        cursor.execute("INSERT INTO violation_patterns (hier_pattern) VALUES ('pattern1')")
        cursor.execute("INSERT INTO violation_patterns (hier_pattern) VALUES ('pattern2')")
        
        conn.commit()
        
        # 获取统计信息
        cursor.execute("SELECT COUNT(*) FROM timing_violations")
        total_violations = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM confirmation_records WHERE status = 'confirmed'")
        confirmed_violations = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM confirmation_records WHERE status = 'pending'")
        pending_violations = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM violation_patterns")
        total_patterns = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT case_name) FROM timing_violations")
        unique_cases = cursor.fetchone()[0]
        
        conn.close()
        
        # 验证结果
        expected = {
            'total_violations': 3,
            'confirmed_violations': 1,
            'pending_violations': 2,
            'total_patterns': 2,
            'unique_cases': 2
        }
        
        actual = {
            'total_violations': total_violations,
            'confirmed_violations': confirmed_violations,
            'pending_violations': pending_violations,
            'total_patterns': total_patterns,
            'unique_cases': unique_cases
        }
        
        if actual == expected:
            print("✓ 数据库统计功能测试成功")
            print(f"  统计结果: {actual}")
        else:
            print("✗ 数据库统计功能测试失败")
            print(f"  期望: {expected}")
            print(f"  实际: {actual}")
        
    except Exception as e:
        print(f"✗ 数据库统计测试失败: {str(e)}")
    
    finally:
        shutil.rmtree(temp_dir)


def test_basic_merge():
    """测试基本合并功能"""
    print("\n测试基本合并功能...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建主数据库
        main_db = os.path.join(temp_dir, "main.db")
        conn = sqlite3.connect(main_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE timing_violations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_name TEXT NOT NULL,
                corner TEXT,
                num INTEGER NOT NULL,
                hier TEXT NOT NULL,
                time_fs INTEGER NOT NULL,
                time_display TEXT NOT NULL,
                check_info TEXT NOT NULL,
                file_path TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(case_name, corner, num, hier, check_info)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE confirmation_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                violation_id INTEGER NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending'
            )
        ''')
        
        # 插入主数据库数据
        now = datetime.now().isoformat()
        cursor.execute('''
            INSERT INTO timing_violations 
            (case_name, corner, num, hier, time_fs, time_display, check_info, file_path, created_at)
            VALUES ('main_case', 'corner1', 1, 'hier1', 1000, '1ns', 'check1', '/path1', ?)
        ''', (now,))
        
        violation_id = cursor.lastrowid
        cursor.execute('INSERT INTO confirmation_records (violation_id, status) VALUES (?, "pending")', (violation_id,))
        
        conn.commit()
        conn.close()
        
        # 创建源数据库
        source_db = os.path.join(temp_dir, "source.db")
        conn = sqlite3.connect(source_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE timing_violations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_name TEXT NOT NULL,
                corner TEXT,
                num INTEGER NOT NULL,
                hier TEXT NOT NULL,
                time_fs INTEGER NOT NULL,
                time_display TEXT NOT NULL,
                check_info TEXT NOT NULL,
                file_path TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(case_name, corner, num, hier, check_info)
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE confirmation_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                violation_id INTEGER NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending'
            )
        ''')
        
        # 插入源数据库数据（不同的违例）
        cursor.execute('''
            INSERT INTO timing_violations 
            (case_name, corner, num, hier, time_fs, time_display, check_info, file_path, created_at)
            VALUES ('source_case', 'corner2', 2, 'hier2', 2000, '2ns', 'check2', '/path2', ?)
        ''', (now,))
        
        violation_id = cursor.lastrowid
        cursor.execute('INSERT INTO confirmation_records (violation_id, status) VALUES (?, "pending")', (violation_id,))
        
        conn.commit()
        conn.close()
        
        # 执行简单合并
        target_conn = sqlite3.connect(main_db)
        source_conn = sqlite3.connect(source_db)
        
        target_cursor = target_conn.cursor()
        source_cursor = source_conn.cursor()
        
        # 获取源数据库的违例
        source_cursor.execute('''
            SELECT case_name, corner, num, hier, time_fs, time_display, 
                   check_info, file_path, created_at
            FROM timing_violations
        ''')
        
        violations_data = source_cursor.fetchall()
        violations_added = 0
        
        for violation in violations_data:
            # 检查是否已存在
            target_cursor.execute('''
                SELECT id FROM timing_violations
                WHERE case_name=? AND corner=? AND num=? AND hier=? AND check_info=?
            ''', (violation[0], violation[1], violation[2], violation[3], violation[6]))
            
            existing = target_cursor.fetchone()
            if not existing:
                # 插入新违例
                target_cursor.execute('''
                    INSERT INTO timing_violations
                    (case_name, corner, num, hier, time_fs, time_display, 
                     check_info, file_path, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', violation)
                
                new_violation_id = target_cursor.lastrowid
                violations_added += 1
                
                # 创建确认记录
                target_cursor.execute('''
                    INSERT INTO confirmation_records
                    (violation_id, status)
                    VALUES (?, 'pending')
                ''', (new_violation_id,))
        
        target_conn.commit()
        
        # 验证合并结果
        target_cursor.execute("SELECT COUNT(*) FROM timing_violations")
        total_violations = target_cursor.fetchone()[0]
        
        target_conn.close()
        source_conn.close()
        
        if total_violations == 2 and violations_added == 1:
            print("✓ 基本合并功能测试成功")
            print(f"  总违例数: {total_violations}, 新增违例: {violations_added}")
        else:
            print("✗ 基本合并功能测试失败")
            print(f"  总违例数: {total_violations}, 新增违例: {violations_added}")
        
    except Exception as e:
        print(f"✗ 基本合并测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        shutil.rmtree(temp_dir)


def main():
    """运行所有测试"""
    print("开始验证数据库合并功能的核心方法...")
    print("=" * 50)
    
    test_backup_function()
    test_schema_validation()
    test_database_statistics()
    test_basic_merge()
    
    print("\n" + "=" * 50)
    print("验证完成！")


if __name__ == "__main__":
    main()
