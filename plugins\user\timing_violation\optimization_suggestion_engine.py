"""
优化建议引擎

基于违例数量和系统性能智能生成优化建议，支持自动优化应用。
"""

import time
import psutil
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QCheckBox, QTextEdit

try:
    from .violation_performance_monitor import ViolationProcessingMetrics, PerformanceBaseline
    from .performance_reporting_system import PerformanceReport
except ImportError:
    # Fallback for direct execution
    from violation_performance_monitor import ViolationProcessingMetrics, PerformanceBaseline
    from performance_reporting_system import PerformanceReport


@dataclass
class OptimizationSuggestion:
    """优化建议数据结构"""
    suggestion_id: str
    category: str  # 'parsing', 'rendering', 'memory', 'ui', 'configuration'
    priority: str  # 'high', 'medium', 'low'
    title: str
    description: str
    expected_improvement: str
    implementation_complexity: str  # 'easy', 'medium', 'complex'
    
    # 适用条件
    violation_count_range: Tuple[int, int]
    system_requirements: Dict[str, Any]
    current_performance_threshold: float
    
    # 实施信息
    auto_applicable: bool = False
    requires_user_confirmation: bool = True
    implementation_function: Optional[Callable] = None
    rollback_function: Optional[Callable] = None
    
    # 效果预测
    expected_performance_gain: float = 0.0  # 0-100%
    expected_memory_reduction: float = 0.0  # MB
    expected_ui_improvement: float = 0.0    # ms
    
    # 风险评估
    risk_level: str = 'low'  # 'low', 'medium', 'high'
    potential_side_effects: List[str] = field(default_factory=list)
    
    # 历史数据
    success_rate: float = 0.0  # 0-100%
    average_improvement: float = 0.0
    user_feedback_score: float = 0.0


@dataclass
class OptimizationPlan:
    """优化计划"""
    plan_id: str
    timestamp: float
    violation_count: int
    current_performance_score: float
    target_performance_score: float
    
    suggestions: List[OptimizationSuggestion]
    execution_order: List[str]  # suggestion_ids in execution order
    estimated_total_improvement: float
    estimated_execution_time: float
    
    # 执行状态
    status: str = 'pending'  # 'pending', 'executing', 'completed', 'failed', 'cancelled'
    executed_suggestions: List[str] = field(default_factory=list)
    failed_suggestions: List[str] = field(default_factory=list)
    
    # 结果
    actual_improvement: float = 0.0
    execution_log: List[str] = field(default_factory=list)


class OptimizationConfirmationDialog(QDialog):
    """优化确认对话框"""
    
    def __init__(self, optimization_plan: OptimizationPlan, parent=None):
        super().__init__(parent)
        self.optimization_plan = optimization_plan
        self.selected_suggestions = []
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("优化建议确认")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel(f"检测到 {self.optimization_plan.violation_count} 个违例，建议进行以下优化：")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 当前性能信息
        perf_info = QLabel(f"当前性能评分: {self.optimization_plan.current_performance_score:.1f}/100")
        perf_info.setStyleSheet("color: #666; margin-bottom: 15px;")
        layout.addWidget(perf_info)
        
        # 建议列表
        self.suggestion_checkboxes = []
        for suggestion in self.optimization_plan.suggestions:
            checkbox = QCheckBox()
            checkbox.setText(f"[{suggestion.priority.upper()}] {suggestion.title}")
            checkbox.setChecked(suggestion.priority in ['high', 'medium'])
            checkbox.setToolTip(f"{suggestion.description}\n\n预期改进: {suggestion.expected_improvement}")
            
            # 添加详细信息
            detail_text = QTextEdit()
            detail_text.setMaximumHeight(80)
            detail_text.setPlainText(
                f"描述: {suggestion.description}\n"
                f"预期改进: {suggestion.expected_improvement}\n"
                f"实施复杂度: {suggestion.implementation_complexity}\n"
                f"风险等级: {suggestion.risk_level}"
            )
            detail_text.setReadOnly(True)
            
            layout.addWidget(checkbox)
            layout.addWidget(detail_text)
            
            self.suggestion_checkboxes.append((checkbox, suggestion))
        
        # 预期总体改进
        expected_label = QLabel(f"预期总体性能提升: {self.optimization_plan.estimated_total_improvement:.1f}%")
        expected_label.setStyleSheet("font-weight: bold; color: #2e7d32; margin-top: 15px;")
        layout.addWidget(expected_label)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.apply_button = QPushButton("应用选中的优化")
        self.apply_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.apply_button.clicked.connect(self.accept)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.apply_button)
        
        layout.addLayout(button_layout)
        
    def get_selected_suggestions(self) -> List[OptimizationSuggestion]:
        """获取用户选中的建议"""
        selected = []
        for checkbox, suggestion in self.suggestion_checkboxes:
            if checkbox.isChecked():
                selected.append(suggestion)
        return selected


class OptimizationSuggestionEngine(QObject):
    """优化建议引擎"""
    
    # 信号定义
    suggestions_generated = pyqtSignal(dict)  # 建议生成完成
    optimization_applied = pyqtSignal(str, bool)  # 优化应用结果 (suggestion_id, success)
    optimization_plan_completed = pyqtSignal(dict)  # 优化计划完成
    user_confirmation_required = pyqtSignal(dict)  # 需要用户确认
    
    def __init__(self):
        super().__init__()
        
        # 建议模板库
        self.suggestion_templates = self._initialize_suggestion_templates()
        
        # 历史优化数据
        self.optimization_history = deque(maxlen=500)
        self.suggestion_effectiveness = defaultdict(list)  # suggestion_id -> [success_rates]
        
        # 系统性能基线
        self.performance_baselines = {}
        
        # 当前优化计划
        self.current_optimization_plan = None
        self.optimization_in_progress = False
        
        # 自动优化配置
        self.auto_optimization_config = {
            'enabled': True,
            'max_auto_suggestions': 3,
            'min_performance_threshold': 60.0,
            'auto_apply_low_risk_only': True,
            'require_confirmation_for_high_impact': True
        }
        
        # 性能监控集成
        self.performance_monitor = None
        self.reporting_system = None
        
    def analyze_and_suggest(self, violation_count: int, current_performance: Dict,
                          system_capabilities: Dict = None) -> OptimizationPlan:
        """分析当前状态并生成优化建议
        
        Args:
            violation_count: 违例数量
            current_performance: 当前性能指标
            system_capabilities: 系统能力信息
            
        Returns:
            OptimizationPlan: 优化计划
        """
        plan_id = f"plan_{int(time.time() * 1000)}"
        
        # 获取系统能力信息
        if system_capabilities is None:
            system_capabilities = self._assess_system_capabilities()
        
        # 分析性能问题
        performance_issues = self._analyze_performance_issues(current_performance, violation_count)
        
        # 生成建议
        suggestions = self._generate_suggestions(
            violation_count, current_performance, system_capabilities, performance_issues
        )
        
        # 排序和优先级调整
        prioritized_suggestions = self._prioritize_suggestions(suggestions, current_performance)
        
        # 创建执行顺序
        execution_order = self._create_execution_order(prioritized_suggestions)
        
        # 预测总体改进效果
        estimated_improvement = self._estimate_total_improvement(prioritized_suggestions)
        
        # 创建优化计划
        optimization_plan = OptimizationPlan(
            plan_id=plan_id,
            timestamp=time.time(),
            violation_count=violation_count,
            current_performance_score=current_performance.get('overall_score', 0),
            target_performance_score=min(100, current_performance.get('overall_score', 0) + estimated_improvement),
            suggestions=prioritized_suggestions,
            execution_order=execution_order,
            estimated_total_improvement=estimated_improvement,
            estimated_execution_time=self._estimate_execution_time(prioritized_suggestions)
        )
        
        self.current_optimization_plan = optimization_plan
        
        # 发出信号
        self.suggestions_generated.emit({
            'plan_id': plan_id,
            'violation_count': violation_count,
            'suggestions_count': len(prioritized_suggestions),
            'estimated_improvement': estimated_improvement,
            'high_priority_count': len([s for s in prioritized_suggestions if s.priority == 'high']),
            'auto_applicable_count': len([s for s in prioritized_suggestions if s.auto_applicable])
        })
        
        return optimization_plan
        
    def apply_optimization_plan(self, plan: OptimizationPlan, 
                              selected_suggestions: List[str] = None,
                              require_confirmation: bool = True) -> bool:
        """应用优化计划
        
        Args:
            plan: 优化计划
            selected_suggestions: 选中的建议ID列表，None表示全部
            require_confirmation: 是否需要用户确认
            
        Returns:
            bool: 是否成功开始执行
        """
        if self.optimization_in_progress:
            print("Another optimization is already in progress")
            return False
        
        # 过滤建议
        if selected_suggestions:
            suggestions_to_apply = [s for s in plan.suggestions if s.suggestion_id in selected_suggestions]
        else:
            suggestions_to_apply = plan.suggestions
        
        # 检查是否需要用户确认
        if require_confirmation and self._requires_user_confirmation(suggestions_to_apply):
            # 显示确认对话框
            dialog = OptimizationConfirmationDialog(plan)
            if dialog.exec_() == QDialog.Accepted:
                suggestions_to_apply = dialog.get_selected_suggestions()
            else:
                return False
        
        # 开始执行优化
        self.optimization_in_progress = True
        plan.status = 'executing'
        
        success_count = 0
        total_count = len(suggestions_to_apply)
        
        for suggestion in suggestions_to_apply:
            try:
                success = self._apply_single_suggestion(suggestion)
                if success:
                    plan.executed_suggestions.append(suggestion.suggestion_id)
                    success_count += 1
                    plan.execution_log.append(f"Successfully applied: {suggestion.title}")
                else:
                    plan.failed_suggestions.append(suggestion.suggestion_id)
                    plan.execution_log.append(f"Failed to apply: {suggestion.title}")
                
                # 发出单个优化结果信号
                self.optimization_applied.emit(suggestion.suggestion_id, success)
                
            except Exception as e:
                plan.failed_suggestions.append(suggestion.suggestion_id)
                plan.execution_log.append(f"Error applying {suggestion.title}: {str(e)}")
                print(f"Error applying optimization {suggestion.suggestion_id}: {e}")
        
        # 完成执行
        self.optimization_in_progress = False
        plan.status = 'completed' if success_count > 0 else 'failed'
        plan.actual_improvement = self._measure_actual_improvement(plan)
        
        # 更新历史记录
        self._update_optimization_history(plan)
        
        # 发出完成信号
        self.optimization_plan_completed.emit({
            'plan_id': plan.plan_id,
            'success_count': success_count,
            'total_count': total_count,
            'actual_improvement': plan.actual_improvement,
            'execution_log': plan.execution_log
        })
        
        return success_count > 0
        
    def get_suggestion_for_violation_count(self, violation_count: int) -> List[OptimizationSuggestion]:
        """根据违例数量获取特定建议
        
        Args:
            violation_count: 违例数量
            
        Returns:
            List[OptimizationSuggestion]: 建议列表
        """
        suggestions = []
        
        # 基于违例数量范围的建议
        if violation_count < 2000:
            # 小数据集建议
            suggestions.extend(self._get_small_dataset_suggestions())
        elif violation_count < 20000:
            # 中等数据集建议
            suggestions.extend(self._get_medium_dataset_suggestions())
        else:
            # 大数据集建议
            suggestions.extend(self._get_large_dataset_suggestions())
        
        return suggestions
        
    def update_suggestion_effectiveness(self, suggestion_id: str, success: bool, 
                                     improvement: float):
        """更新建议效果统计
        
        Args:
            suggestion_id: 建议ID
            success: 是否成功
            improvement: 实际改进幅度
        """
        self.suggestion_effectiveness[suggestion_id].append({
            'success': success,
            'improvement': improvement,
            'timestamp': time.time()
        })
        
        # 限制历史记录数量
        if len(self.suggestion_effectiveness[suggestion_id]) > 50:
            self.suggestion_effectiveness[suggestion_id] = \
                self.suggestion_effectiveness[suggestion_id][-50:]
                
    def get_optimization_history(self, limit: int = 20) -> List[Dict]:
        """获取优化历史记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List[Dict]: 优化历史记录
        """
        return list(self.optimization_history)[-limit:]
        
    def _initialize_suggestion_templates(self) -> Dict[str, OptimizationSuggestion]:
        """初始化建议模板库"""
        templates = {}
        
        # 解析策略优化建议
        templates['switch_to_streaming'] = OptimizationSuggestion(
            suggestion_id='switch_to_streaming',
            category='parsing',
            priority='high',
            title='切换到流式解析器',
            description='对于大型数据集(>20K违例)，使用流式解析器可以显著降低内存使用并提高处理速度',
            expected_improvement='内存使用降低60-80%，加载时间减少30-50%',
            implementation_complexity='easy',
            violation_count_range=(20000, float('inf')),
            system_requirements={'min_memory_gb': 2},
            current_performance_threshold=70.0,
            auto_applicable=True,
            requires_user_confirmation=False,
            expected_performance_gain=25.0,
            expected_memory_reduction=400.0,
            expected_ui_improvement=200.0,
            risk_level='low',
            success_rate=85.0
        )
        
        templates['enable_high_performance_parser'] = OptimizationSuggestion(
            suggestion_id='enable_high_performance_parser',
            category='parsing',
            priority='medium',
            title='启用高性能解析器',
            description='对于中等数据集(2K-20K违例)，高性能解析器提供更好的处理速度',
            expected_improvement='处理速度提升40-60%',
            implementation_complexity='easy',
            violation_count_range=(2000, 20000),
            system_requirements={'min_cpu_cores': 2},
            current_performance_threshold=60.0,
            auto_applicable=True,
            requires_user_confirmation=False,
            expected_performance_gain=20.0,
            expected_ui_improvement=100.0,
            risk_level='low',
            success_rate=90.0
        )
        
        # UI渲染优化建议
        templates['enable_virtualization'] = OptimizationSuggestion(
            suggestion_id='enable_virtualization',
            category='rendering',
            priority='high',
            title='启用表格虚拟化',
            description='对于大量违例显示，虚拟化可以显著提升UI响应性',
            expected_improvement='UI响应时间减少70-90%',
            implementation_complexity='medium',
            violation_count_range=(5000, float('inf')),
            system_requirements={},
            current_performance_threshold=50.0,
            auto_applicable=True,
            requires_user_confirmation=False,
            expected_performance_gain=30.0,
            expected_ui_improvement=300.0,
            risk_level='low',
            success_rate=95.0
        )
        
        templates['optimize_page_size'] = OptimizationSuggestion(
            suggestion_id='optimize_page_size',
            category='rendering',
            priority='medium',
            title='优化分页大小',
            description='根据违例数量和系统性能调整最优分页大小',
            expected_improvement='页面加载时间减少20-40%',
            implementation_complexity='easy',
            violation_count_range=(1000, float('inf')),
            system_requirements={},
            current_performance_threshold=70.0,
            auto_applicable=True,
            requires_user_confirmation=False,
            expected_performance_gain=15.0,
            expected_ui_improvement=150.0,
            risk_level='low',
            success_rate=80.0
        )
        
        # 内存管理优化建议
        templates['enable_lazy_loading'] = OptimizationSuggestion(
            suggestion_id='enable_lazy_loading',
            category='memory',
            priority='high',
            title='启用延迟加载',
            description='只加载当前需要的数据，减少内存占用',
            expected_improvement='内存使用降低50-70%',
            implementation_complexity='medium',
            violation_count_range=(5000, float('inf')),
            system_requirements={},
            current_performance_threshold=60.0,
            auto_applicable=True,
            requires_user_confirmation=False,
            expected_performance_gain=20.0,
            expected_memory_reduction=300.0,
            risk_level='low',
            success_rate=88.0
        )
        
        templates['enable_memory_cleanup'] = OptimizationSuggestion(
            suggestion_id='enable_memory_cleanup',
            category='memory',
            priority='medium',
            title='启用自动内存清理',
            description='定期清理不再使用的数据，保持内存使用在合理范围',
            expected_improvement='防止内存泄漏，保持稳定性能',
            implementation_complexity='easy',
            violation_count_range=(1000, float('inf')),
            system_requirements={},
            current_performance_threshold=80.0,
            auto_applicable=True,
            requires_user_confirmation=False,
            expected_performance_gain=10.0,
            expected_memory_reduction=100.0,
            risk_level='low',
            success_rate=92.0
        )
        
        # 配置优化建议
        templates['adjust_batch_size'] = OptimizationSuggestion(
            suggestion_id='adjust_batch_size',
            category='configuration',
            priority='medium',
            title='调整批处理大小',
            description='根据系统性能和数据量优化批处理参数',
            expected_improvement='处理效率提升15-30%',
            implementation_complexity='easy',
            violation_count_range=(2000, float('inf')),
            system_requirements={},
            current_performance_threshold=65.0,
            auto_applicable=True,
            requires_user_confirmation=False,
            expected_performance_gain=12.0,
            risk_level='low',
            success_rate=75.0
        )
        
        return templates 
       
    def _assess_system_capabilities(self) -> Dict:
        """评估系统能力"""
        try:
            memory_info = psutil.virtual_memory()
            cpu_count = psutil.cpu_count()
            
            return {
                'total_memory_gb': memory_info.total / (1024**3),
                'available_memory_gb': memory_info.available / (1024**3),
                'cpu_cores': cpu_count,
                'memory_usage_percent': memory_info.percent
            }
        except Exception as e:
            print(f"Error assessing system capabilities: {e}")
            return {
                'total_memory_gb': 8.0,  # 默认值
                'available_memory_gb': 4.0,
                'cpu_cores': 4,
                'memory_usage_percent': 50.0
            }
            
    def _analyze_performance_issues(self, current_performance: Dict, 
                                  violation_count: int) -> List[str]:
        """分析性能问题"""
        issues = []
        
        # 检查UI响应时间
        ui_response = current_performance.get('ui_response_time_ms', 0)
        if ui_response > 200:
            issues.append('ui_response_critical')
        elif ui_response > 100:
            issues.append('ui_response_slow')
        
        # 检查内存使用
        memory_usage = current_performance.get('memory_usage_mb', 0)
        if memory_usage > 1000:
            issues.append('memory_usage_high')
        elif memory_usage > 800:
            issues.append('memory_usage_warning')
        
        # 检查加载时间
        load_time = current_performance.get('load_time_seconds', 0)
        if load_time > 2.0:
            issues.append('load_time_critical')
        elif load_time > 1.0:
            issues.append('load_time_slow')
        
        # 检查处理速度
        violations_per_second = current_performance.get('violations_per_second', 0)
        if violation_count > 0 and violations_per_second < 500:
            issues.append('processing_speed_low')
        
        return issues
        
    def _generate_suggestions(self, violation_count: int, current_performance: Dict,
                            system_capabilities: Dict, performance_issues: List[str]) -> List[OptimizationSuggestion]:
        """生成优化建议"""
        suggestions = []
        
        # 基于违例数量的建议
        for template_id, template in self.suggestion_templates.items():
            min_count, max_count = template.violation_count_range
            
            # 检查违例数量范围
            if min_count <= violation_count < max_count:
                # 检查性能阈值
                current_score = current_performance.get('overall_score', 100)
                if current_score < template.current_performance_threshold:
                    # 检查系统要求
                    if self._check_system_requirements(template.system_requirements, system_capabilities):
                        # 更新建议的历史效果数据
                        template = self._update_suggestion_with_history(template)
                        suggestions.append(template)
        
        # 基于具体性能问题的建议
        issue_specific_suggestions = self._get_issue_specific_suggestions(performance_issues, violation_count)
        suggestions.extend(issue_specific_suggestions)
        
        return suggestions
        
    def _prioritize_suggestions(self, suggestions: List[OptimizationSuggestion],
                              current_performance: Dict) -> List[OptimizationSuggestion]:
        """对建议进行优先级排序"""
        def priority_score(suggestion):
            score = 0
            
            # 基础优先级分数
            priority_scores = {'high': 100, 'medium': 50, 'low': 25}
            score += priority_scores.get(suggestion.priority, 0)
            
            # 预期改进分数
            score += suggestion.expected_performance_gain
            
            # 成功率分数
            score += suggestion.success_rate * 0.5
            
            # 实施复杂度调整（简单的优先）
            complexity_adjustment = {'easy': 20, 'medium': 0, 'complex': -20}
            score += complexity_adjustment.get(suggestion.implementation_complexity, 0)
            
            # 风险等级调整（低风险优先）
            risk_adjustment = {'low': 10, 'medium': 0, 'high': -15}
            score += risk_adjustment.get(suggestion.risk_level, 0)
            
            return score
        
        return sorted(suggestions, key=priority_score, reverse=True)
        
    def _create_execution_order(self, suggestions: List[OptimizationSuggestion]) -> List[str]:
        """创建执行顺序"""
        # 按类别和依赖关系排序
        category_order = ['parsing', 'memory', 'rendering', 'ui', 'configuration']
        
        ordered_suggestions = []
        for category in category_order:
            category_suggestions = [s for s in suggestions if s.category == category]
            # 在同类别内按优先级排序
            category_suggestions.sort(key=lambda x: {'high': 3, 'medium': 2, 'low': 1}[x.priority], reverse=True)
            ordered_suggestions.extend(category_suggestions)
        
        return [s.suggestion_id for s in ordered_suggestions]
        
    def _estimate_total_improvement(self, suggestions: List[OptimizationSuggestion]) -> float:
        """估算总体改进效果"""
        if not suggestions:
            return 0.0
        
        # 使用非线性组合避免过度乐观
        total_gain = 0.0
        diminishing_factor = 1.0
        
        for suggestion in sorted(suggestions, key=lambda x: x.expected_performance_gain, reverse=True):
            weighted_gain = suggestion.expected_performance_gain * diminishing_factor
            total_gain += weighted_gain
            diminishing_factor *= 0.8  # 递减因子
        
        return min(total_gain, 80.0)  # 限制最大改进幅度
        
    def _estimate_execution_time(self, suggestions: List[OptimizationSuggestion]) -> float:
        """估算执行时间"""
        complexity_time = {'easy': 1.0, 'medium': 3.0, 'complex': 10.0}
        total_time = sum(complexity_time.get(s.implementation_complexity, 1.0) for s in suggestions)
        return total_time
        
    def _requires_user_confirmation(self, suggestions: List[OptimizationSuggestion]) -> bool:
        """检查是否需要用户确认"""
        if not self.auto_optimization_config['enabled']:
            return True
        
        # 检查高风险建议
        high_risk_suggestions = [s for s in suggestions if s.risk_level == 'high']
        if high_risk_suggestions and self.auto_optimization_config['require_confirmation_for_high_impact']:
            return True
        
        # 检查非自动应用的建议
        manual_suggestions = [s for s in suggestions if not s.auto_applicable]
        if manual_suggestions:
            return True
        
        # 检查建议数量限制
        if len(suggestions) > self.auto_optimization_config['max_auto_suggestions']:
            return True
        
        return False
        
    def _apply_single_suggestion(self, suggestion: OptimizationSuggestion) -> bool:
        """应用单个优化建议"""
        try:
            if suggestion.implementation_function:
                return suggestion.implementation_function()
            else:
                # 使用内置实现
                return self._apply_builtin_suggestion(suggestion)
        except Exception as e:
            print(f"Error applying suggestion {suggestion.suggestion_id}: {e}")
            return False
            
    def _apply_builtin_suggestion(self, suggestion: OptimizationSuggestion) -> bool:
        """应用内置建议实现"""
        suggestion_id = suggestion.suggestion_id
        
        try:
            if suggestion_id == 'switch_to_streaming':
                return self._apply_streaming_parser()
            elif suggestion_id == 'enable_high_performance_parser':
                return self._apply_high_performance_parser()
            elif suggestion_id == 'enable_virtualization':
                return self._apply_virtualization()
            elif suggestion_id == 'optimize_page_size':
                return self._apply_page_size_optimization()
            elif suggestion_id == 'enable_lazy_loading':
                return self._apply_lazy_loading()
            elif suggestion_id == 'enable_memory_cleanup':
                return self._apply_memory_cleanup()
            elif suggestion_id == 'adjust_batch_size':
                return self._apply_batch_size_adjustment()
            else:
                print(f"Unknown suggestion ID: {suggestion_id}")
                return False
        except Exception as e:
            print(f"Error in builtin suggestion {suggestion_id}: {e}")
            return False
            
    def _apply_streaming_parser(self) -> bool:
        """应用流式解析器"""
        # 这里应该与实际的解析器系统集成
        print("Applied streaming parser optimization")
        return True
        
    def _apply_high_performance_parser(self) -> bool:
        """应用高性能解析器"""
        print("Applied high-performance parser optimization")
        return True
        
    def _apply_virtualization(self) -> bool:
        """应用表格虚拟化"""
        print("Applied table virtualization optimization")
        return True
        
    def _apply_page_size_optimization(self) -> bool:
        """应用分页大小优化"""
        print("Applied page size optimization")
        return True
        
    def _apply_lazy_loading(self) -> bool:
        """应用延迟加载"""
        print("Applied lazy loading optimization")
        return True
        
    def _apply_memory_cleanup(self) -> bool:
        """应用内存清理"""
        print("Applied memory cleanup optimization")
        return True
        
    def _apply_batch_size_adjustment(self) -> bool:
        """应用批处理大小调整"""
        print("Applied batch size adjustment optimization")
        return True
        
    def _measure_actual_improvement(self, plan: OptimizationPlan) -> float:
        """测量实际改进效果"""
        # 这里应该与性能监控系统集成，获取实际的性能改进数据
        # 暂时返回估算值的80%作为实际改进
        return plan.estimated_total_improvement * 0.8
        
    def _update_optimization_history(self, plan: OptimizationPlan):
        """更新优化历史记录"""
        history_entry = {
            'plan_id': plan.plan_id,
            'timestamp': plan.timestamp,
            'violation_count': plan.violation_count,
            'suggestions_applied': len(plan.executed_suggestions),
            'suggestions_failed': len(plan.failed_suggestions),
            'estimated_improvement': plan.estimated_total_improvement,
            'actual_improvement': plan.actual_improvement,
            'success_rate': len(plan.executed_suggestions) / len(plan.suggestions) if plan.suggestions else 0
        }
        
        self.optimization_history.append(history_entry)
        
        # 更新各建议的效果统计
        for suggestion_id in plan.executed_suggestions:
            self.update_suggestion_effectiveness(suggestion_id, True, plan.actual_improvement)
        
        for suggestion_id in plan.failed_suggestions:
            self.update_suggestion_effectiveness(suggestion_id, False, 0.0)
            
    def _check_system_requirements(self, requirements: Dict, capabilities: Dict) -> bool:
        """检查系统要求"""
        for req_key, req_value in requirements.items():
            if req_key == 'min_memory_gb':
                if capabilities.get('available_memory_gb', 0) < req_value:
                    return False
            elif req_key == 'min_cpu_cores':
                if capabilities.get('cpu_cores', 0) < req_value:
                    return False
        
        return True
        
    def _update_suggestion_with_history(self, suggestion: OptimizationSuggestion) -> OptimizationSuggestion:
        """使用历史数据更新建议"""
        history = self.suggestion_effectiveness.get(suggestion.suggestion_id, [])
        
        if history:
            # 计算成功率
            successes = [h for h in history if h['success']]
            suggestion.success_rate = (len(successes) / len(history)) * 100
            
            # 计算平均改进
            if successes:
                suggestion.average_improvement = sum(h['improvement'] for h in successes) / len(successes)
        
        return suggestion
        
    def _get_issue_specific_suggestions(self, issues: List[str], 
                                      violation_count: int) -> List[OptimizationSuggestion]:
        """获取针对特定问题的建议"""
        suggestions = []
        
        for issue in issues:
            if issue == 'ui_response_critical' and violation_count > 5000:
                suggestions.append(self.suggestion_templates['enable_virtualization'])
            elif issue == 'memory_usage_high':
                suggestions.append(self.suggestion_templates['enable_lazy_loading'])
            elif issue == 'load_time_critical' and violation_count > 20000:
                suggestions.append(self.suggestion_templates['switch_to_streaming'])
            elif issue == 'processing_speed_low' and 2000 <= violation_count < 20000:
                suggestions.append(self.suggestion_templates['enable_high_performance_parser'])
        
        return suggestions
        
    def _get_small_dataset_suggestions(self) -> List[OptimizationSuggestion]:
        """获取小数据集建议"""
        return [
            self.suggestion_templates['enable_memory_cleanup'],
            self.suggestion_templates['optimize_page_size']
        ]
        
    def _get_medium_dataset_suggestions(self) -> List[OptimizationSuggestion]:
        """获取中等数据集建议"""
        return [
            self.suggestion_templates['enable_high_performance_parser'],
            self.suggestion_templates['enable_virtualization'],
            self.suggestion_templates['adjust_batch_size']
        ]
        
    def _get_large_dataset_suggestions(self) -> List[OptimizationSuggestion]:
        """获取大数据集建议"""
        return [
            self.suggestion_templates['switch_to_streaming'],
            self.suggestion_templates['enable_virtualization'],
            self.suggestion_templates['enable_lazy_loading'],
            self.suggestion_templates['optimize_page_size']
        ]
        
    def set_performance_monitor(self, monitor):
        """设置性能监控器"""
        self.performance_monitor = monitor
        
    def set_reporting_system(self, reporting_system):
        """设置报告系统"""
        self.reporting_system = reporting_system
        
    def get_suggestion_statistics(self) -> Dict:
        """获取建议统计信息"""
        stats = {}
        
        for suggestion_id, history in self.suggestion_effectiveness.items():
            if history:
                successes = [h for h in history if h['success']]
                stats[suggestion_id] = {
                    'total_applications': len(history),
                    'success_count': len(successes),
                    'success_rate': (len(successes) / len(history)) * 100,
                    'average_improvement': sum(h['improvement'] for h in successes) / len(successes) if successes else 0,
                    'last_applied': max(h['timestamp'] for h in history)
                }
        
        return stats
        
    def reset_optimization_history(self):
        """重置优化历史记录"""
        self.optimization_history.clear()
        self.suggestion_effectiveness.clear()
        print("Optimization history has been reset")
        
    def export_optimization_data(self) -> Dict:
        """导出优化数据"""
        return {
            'optimization_history': list(self.optimization_history),
            'suggestion_effectiveness': dict(self.suggestion_effectiveness),
            'auto_optimization_config': self.auto_optimization_config,
            'export_timestamp': time.time()
        }