# Design Document

## Overview

The timing violation web display feature provides a web-based interface for reviewing confirmed timing violation records. The system will support two data sources: Excel files exported from the timing violation plugin and direct database access to the SQLite database. The web interface will be built as a static HTML page with JavaScript that reads JSON data files generated by a Python data export script, eliminating the need for a web server framework.

## Architecture

### Directory Structure

```
plugins/user/timing_violation/
├── web_display/                    # Web display feature directory
│   ├── __init__.py
│   ├── data_exporter.py            # Main data export script
│   ├── parsers/                    # Data parsing modules
│   │   ├── __init__.py
│   │   ├── excel_parser.py         # Excel file parser
│   │   └── database_reader.py      # Database reader
│   ├── web_template/               # Web interface template files
│   │   ├── index.html              # Main HTML page template
│   │   ├── css/
│   │   │   ├── bootstrap.min.css   # Bootstrap CSS
│   │   │   └── custom.css          # Custom styles
│   │   └── js/
│   │       ├── bootstrap.min.js    # Bootstrap JS
│   │       ├── jquery.min.js       # jQuery
│   │       ├── datatables.min.js   # DataTables
│   │       └── app.js              # Main application JS
│   ├── utils/                      # Utility modules
│   │   ├── __init__.py
│   │   ├── file_utils.py           # File handling utilities
│   │   └── date_utils.py           # Date formatting utilities
│   └── tests/                      # Test files
│       ├── __init__.py
│       ├── test_data_exporter.py
│       ├── test_excel_parser.py
│       └── test_database_reader.py
├── generate_web_data.py            # Script to generate web data
└── requirements_web.txt            # Web dependencies

VIOLATION_CHECK/                    # Generated web files (accessible to all users)
├── web_display/                    # Generated web interface
│   ├── index.html                  # Generated main HTML page
│   ├── css/                        # Copied CSS files
│   │   ├── bootstrap.min.css
│   │   └── custom.css
│   ├── js/                         # Copied JS files
│   │   ├── bootstrap.min.js
│   │   ├── jquery.min.js
│   │   ├── datatables.min.js
│   │   └── app.js
│   └── data/                       # Generated JSON data files
│       ├── index.json              # Metadata and summary
│       ├── corners/                # Corner-specific data
│       │   ├── corner1_cases.json
│       │   └── corner2_cases.json
│       └── violations/             # Violation data files
│           ├── corner1_case1_page1.json
│           ├── corner1_case1_page2.json
│           └── ...
├── timing_violations.db            # Existing database
└── [existing Excel files]          # Existing Excel exports
```

### System Components

```mermaid
graph TB
    A[Python Data Export Script] --> B[Excel File Parser]
    A --> C[Database Reader]
    B --> D[VIOLATION_CHECK Directory]
    C --> E[timing_violations.db]
    A --> F[JSON Data Files]
    G[Web Browser] --> H[Static HTML Page]
    H --> I[JavaScript App]
    I --> F
    H --> J[CSS/JS Assets]
```

### Technology Stack

- **Data Export**: Python script with openpyxl and sqlite3 libraries
- **Frontend**: Static HTML5, CSS3, JavaScript (ES6+)
- **Database**: SQLite (existing timing_violations.db)
- **Excel Processing**: openpyxl library
- **Data Format**: JSON files for data exchange
- **Styling**: Bootstrap 5 for responsive design
- **Data Tables**: DataTables.js for advanced table functionality

## Components and Interfaces

### 1. Data Exporter Component (`data_exporter.py`)

**Purpose**: Python script that exports violation data to JSON files for web consumption.

**Key Methods**:
- `export_all_data()`: Main method that exports all data to JSON files
- `load_violation_data()`: Loads data from Excel files and database
- `export_violations_json()`: Exports violation data to violations.json
- `export_corners_json()`: Exports available corners to corners.json
- `export_cases_json()`: Exports available cases to cases.json
- `export_statistics_json()`: Exports summary statistics to statistics.json

**Generated Files**:
- `VIOLATION_CHECK/web_display/data/index.json`: Metadata and summary statistics
- `VIOLATION_CHECK/web_display/data/corners/`: Corner-specific case lists
- `VIOLATION_CHECK/web_display/data/violations/`: Paginated violation data files
- `VIOLATION_CHECK/web_display/index.html`: Complete web interface

### 2. JavaScript Application (`app.js`)

**Purpose**: Frontend JavaScript application that loads JSON data and manages the web interface.

**Key Methods**:
- `loadData()`: Loads all JSON data files on page initialization
- `populateFilters()`: Populates corner and case dropdown filters
- `filterViolations()`: Filters violation data based on selected filters
- `updateTable()`: Updates the DataTable with filtered data
- `updateStatistics()`: Updates summary statistics display
- `handleFilterChange()`: Handles filter dropdown change events

**Data Structure**:
```python
{
    'corners': ['corner1', 'corner2', ...],
    'cases': ['case1', 'case2', ...],
    'violations': [
        {
            'num': int,
            'hier': str,
            'time_ns': float,
            'check_info': str,
            'status': str,
            'confirmer': str,
            'result': str,
            'reason': str,
            'confirmed_at': str,
            'corner': str,
            'case': str
        },
        ...
    ]
}
```

### 3. Excel File Parser (`parsers/excel_parser.py`)

**Purpose**: Parses Excel files exported by the timing violation plugin.

**Key Methods**:
- `parse_excel_file(file_path)`: Parses a single Excel file
- `extract_metadata_from_path(file_path)`: Extracts corner and case from file path
- `validate_excel_format(worksheet)`: Validates Excel file format
- `convert_row_to_violation(row)`: Converts Excel row to violation dictionary

**Expected Excel Format**:
- Column 1: 序号 (NUM)
- Column 2: 层级路径 (Hier)  
- Column 3: 时间(ns) (Time in nanoseconds)
- Column 4: 检查信息 (Check Info)
- Column 5: 状态 (Status)
- Column 6: 确认人 (Confirmer)
- Column 7: 确认结果 (Result)
- Column 8: 确认理由 (Reason)
- Column 9: 确认时间 (Confirmed At)

### 4. Database Reader (`parsers/database_reader.py`)

**Purpose**: Reads violation data directly from the SQLite database when Excel files are not available.

**Key Methods**:
- `get_confirmed_violations()`: Queries database for confirmed violations
- `get_violation_with_confirmation(violation_id)`: Gets violation with confirmation details
- `get_corners_from_db()`: Gets unique corners from database
- `get_cases_from_db(corner)`: Gets cases for specific corner from database

**SQL Queries**:
```sql
-- Get confirmed violations with details
SELECT v.*, c.status, c.confirmer, c.result, c.reason, c.confirmed_at
FROM timing_violations v
LEFT JOIN confirmation_records c ON v.id = c.violation_id
WHERE c.status = 'confirmed'
ORDER BY v.case_name, v.corner, v.num;

-- Get unique corners
SELECT DISTINCT corner FROM timing_violations ORDER BY corner;

-- Get cases for corner
SELECT DISTINCT case_name FROM timing_violations 
WHERE corner = ? ORDER BY case_name;
```

## Data Models

### Violation Data Model

```python
class ViolationRecord:
    def __init__(self):
        self.num: int = 0
        self.hier: str = ""
        self.time_ns: float = 0.0
        self.check_info: str = ""
        self.status: str = ""
        self.confirmer: str = ""
        self.result: str = ""
        self.reason: str = ""
        self.confirmed_at: str = ""
        self.corner: str = ""
        self.case: str = ""
        self.source: str = ""  # 'excel' or 'database'
```

### Filter Model

```python
class FilterOptions:
    def __init__(self):
        self.selected_corner: str = "all"
        self.selected_case: str = "all"
        self.available_corners: List[str] = []
        self.available_cases: List[str] = []
```

### Statistics Model

```python
class ViolationStatistics:
    def __init__(self):
        self.total_violations: int = 0
        self.confirmed_violations: int = 0
        self.pending_violations: int = 0
        self.confirmation_rate: float = 0.0
        self.corners_count: int = 0
        self.cases_count: int = 0
        self.last_confirmation_date: str = ""
        self.by_corner: Dict[str, int] = {}
        self.by_case: Dict[str, int] = {}
```

## Error Handling

### Excel Parsing Errors
- **File not found**: Log warning and continue with other files
- **Invalid format**: Log error with file path and skip file
- **Corrupted file**: Log error and attempt to continue parsing other files
- **Permission denied**: Log error and suggest checking file permissions

### Database Errors
- **Database not found**: Create empty database or show appropriate message
- **Connection failed**: Retry with exponential backoff
- **Query failed**: Log error and return empty results with error message
- **Schema mismatch**: Attempt to migrate or show compatibility error

### JavaScript Errors
- **JSON Loading Errors**: Display user-friendly error message when data files cannot be loaded
- **Data Format Errors**: Handle malformed JSON gracefully with fallback empty data
- **Browser Compatibility**: Provide fallback for older browsers that don't support ES6+

## Testing Strategy

### Unit Tests
- **Excel Parser Tests**: Test parsing various Excel file formats and edge cases
- **Database Reader Tests**: Test database queries and connection handling
- **Data Exporter Tests**: Test JSON data generation and file output
- **JavaScript Tests**: Test frontend filtering and data manipulation logic

### Integration Tests
- **End-to-End Tests**: Test complete workflow from data export to web display
- **Cross-browser Tests**: Ensure compatibility across major browsers
- **Performance Tests**: Test with large datasets (10,000+ violations)
- **Error Handling Tests**: Test graceful handling of various error conditions

### Test Data
- **Sample Excel Files**: Create test Excel files with various formats and edge cases
- **Test Database**: Create SQLite database with sample violation data
- **Mock Data**: Generate large datasets for performance testing

### Testing Framework
- **Backend**: pytest for Python unit and integration tests
- **Frontend**: Jest for JavaScript unit tests (or simple HTML test pages)
- **E2E**: Manual testing or simple browser automation
- **Performance**: Browser developer tools for frontend performance analysis

## Performance Considerations

### Large Dataset Handling (10,000+ Records)

**Data Partitioning Strategy**:
- **Split by Corner/Case**: Generate separate JSON files for each corner-case combination
- **Pagination Files**: Split large datasets into paginated JSON files (e.g., 1000 records per file)
- **Index Files**: Create lightweight index files containing only corner/case metadata
- **On-demand Loading**: Load only the required data subset based on user selection

**File Structure for Large Datasets**:
```
VIOLATION_CHECK/web_display/data/
├── index.json                    # Metadata: corners, cases, counts
├── corners/
│   ├── corner1_cases.json       # Cases for corner1
│   ├── corner2_cases.json       # Cases for corner2
│   └── ...
└── violations/
    ├── corner1_case1_page1.json # First 1000 records
    ├── corner1_case1_page2.json # Next 1000 records
    ├── corner2_case1_page1.json
    └── ...
```

### Frontend Performance Optimization

**Virtual Scrolling Implementation**:
- Use DataTables with server-side processing simulation
- Render only visible rows (typically 50-100 rows)
- Implement efficient row recycling for smooth scrolling
- Pre-calculate row heights for consistent scrolling

**Memory Management**:
- **Lazy Loading**: Load data only when corner/case is selected
- **Data Cleanup**: Clear previous data when switching filters
- **Efficient Filtering**: Use indexed arrays for fast filtering
- **DOM Optimization**: Minimize DOM manipulations and reflows

**JavaScript Performance**:
```javascript
// Example efficient data structure
class ViolationDataManager {
    constructor() {
        this.dataCache = new Map();
        this.currentData = [];
        this.filteredIndices = [];
    }
    
    async loadCornerCaseData(corner, case) {
        const cacheKey = `${corner}_${case}`;
        if (!this.dataCache.has(cacheKey)) {
            const data = await this.fetchPaginatedData(corner, case);
            this.dataCache.set(cacheKey, data);
        }
        return this.dataCache.get(cacheKey);
    }
    
    async fetchPaginatedData(corner, case) {
        // Load data in chunks to avoid blocking UI
        const chunks = [];
        let page = 1;
        while (true) {
            const response = await fetch(`data/violations/${corner}_${case}_page${page}.json`);
            if (!response.ok) break;
            const chunk = await response.json();
            chunks.push(...chunk);
            page++;
        }
        return chunks;
    }
}
```

### Data Export Optimization

**Intelligent Data Splitting**:
- Analyze data size and automatically determine optimal splitting strategy
- Generate compressed JSON files (gzip) to reduce file sizes by 70-80%
- Create data manifests for efficient loading coordination

**Python Export Performance**:
```python
class OptimizedDataExporter:
    def __init__(self, max_records_per_file=1000):
        self.max_records_per_file = max_records_per_file
        
    def export_large_dataset(self, violations):
        # Group by corner and case
        grouped_data = self.group_violations(violations)
        
        for (corner, case), records in grouped_data.items():
            if len(records) > self.max_records_per_file:
                # Split into multiple files
                self.export_paginated_files(corner, case, records)
            else:
                # Single file
                self.export_single_file(corner, case, records)
    
    def export_paginated_files(self, corner, case, records):
        for i in range(0, len(records), self.max_records_per_file):
            page_num = (i // self.max_records_per_file) + 1
            chunk = records[i:i + self.max_records_per_file]
            filename = f"VIOLATION_CHECK/web_display/data/violations/{corner}_{case}_page{page_num}.json"
            self.write_compressed_json(filename, chunk)
```

### Performance Benchmarks

**Expected Performance for 10,000+ Records**:
- **Initial Load**: < 2 seconds (loading index and first page)
- **Filter Change**: < 500ms (switching between corner/case combinations)
- **Scrolling**: 60fps with virtual scrolling
- **Memory Usage**: < 100MB browser memory for 10,000 records

**Scalability Limits**:
- **Recommended**: Up to 50,000 records with pagination
- **Maximum**: Up to 100,000 records with aggressive optimization
- **Beyond 100k**: Consider server-side solution or database-backed approach

### Fallback Strategies

**Progressive Enhancement**:
1. **Basic Mode**: Simple table with client-side pagination (< 5,000 records)
2. **Optimized Mode**: Virtual scrolling with data chunking (5,000-50,000 records)
3. **Advanced Mode**: Lazy loading with compression (50,000+ records)

**Performance Monitoring**:
- Implement client-side performance monitoring
- Automatically switch to simpler rendering modes if performance degrades
- Provide user feedback during data loading operations

### Browser Compatibility

**Modern Browser Features**:
- Use Intersection Observer API for efficient virtual scrolling
- Leverage Web Workers for data processing if needed
- Implement Service Worker for data caching

**Fallback for Older Browsers**:
- Graceful degradation to simple pagination
- Polyfills for essential modern JavaScript features
- Alternative rendering strategies for IE11+ support