"""
智能策略管理器

提供基于违例数量的智能策略选择、回退机制和动态切换功能。
"""

import time
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
from PyQt5.QtCore import QObject, pyqtSignal, QTimer


class StrategyType(Enum):
    """策略类型枚举"""
    STANDARD = "standard_processing"
    HIGH_PERFORMANCE = "high_performance_processing"
    STREAMING = "streaming_processing"
    ULTRA_LARGE = "ultra_large_dataset_processing"
    MEMORY_EFFICIENT = "memory_efficient_processing"
    SPEED_OPTIMIZED = "speed_optimized_processing"
    UI_RESPONSIVE = "ui_responsive_processing"
    STABLE = "stable_processing"
    CONSERVATIVE = "conservative_processing"


class TriggerCondition(Enum):
    """触发条件枚举"""
    MEMORY_PRESSURE = "memory_pressure"
    PROCESSING_TIMEOUT = "processing_timeout"
    UI_FREEZE = "ui_freeze"
    HIGH_ERROR_RATE = "high_error_rate"
    PERFORMANCE_DEGRADATION = "performance_degradation"
    SYSTEM_OVERLOAD = "system_overload"


@dataclass
class StrategyConfig:
    """策略配置数据类"""
    strategy_name: str
    parser_type: str
    display_mode: str
    batch_size: int
    use_streaming: bool
    use_pagination: bool
    page_size: int = 100
    memory_limit_mb: float = 200
    progress_interval: int = 1000
    gc_interval: int = 5000
    priority: str = "balanced"
    
    # 高级配置
    lazy_loading: bool = False
    background_processing: bool = False
    aggressive_gc: bool = False
    defer_ui_updates: bool = False
    chunk_size: int = 1000
    ui_update_interval: int = 500

    # 安全模式配置
    minimal_features: bool = False
    safe_mode: bool = False

    # 元数据
    confidence_score: float = 0.8
    selection_reason: str = ""
    alternative_strategies: List[str] = None
    
    def __post_init__(self):
        if self.alternative_strategies is None:
            self.alternative_strategies = []


class IntelligentStrategyManager(QObject):
    """智能策略管理器"""
    
    # 信号定义
    strategy_selected = pyqtSignal(dict)  # 策略选择完成
    strategy_switched = pyqtSignal(str, str)  # 策略切换 (旧策略, 新策略)
    fallback_triggered = pyqtSignal(str, str)  # 回退触发 (触发条件, 回退策略)
    performance_warning = pyqtSignal(str, dict)  # 性能警告
    
    def __init__(self, performance_optimizer=None):
        super().__init__()
        self.performance_optimizer = performance_optimizer
        
        # 违例数量阈值
        self.violation_thresholds = {
            'small_dataset': 2000,      # < 2K violations
            'medium_dataset': 20000,    # 2K-20K violations
            'large_dataset': 50000,     # 20K-50K violations
            'very_large_dataset': 100000 # > 50K violations
        }
        
        # 当前策略状态
        self.current_strategy = None
        self.strategy_history = []
        self.switch_count = 0
        self.last_switch_time = 0
        self.cooldown_period = 30.0  # 30秒冷却期
        
        # 性能监控
        self.monitoring_enabled = True
        self.monitoring_timer = QTimer()
        self.monitoring_timer.timeout.connect(self._monitor_current_strategy)
        self.monitoring_interval = 5000  # 5秒监控间隔
        
        # 策略切换限制
        self.max_switches_per_session = 5
        self.emergency_fallback_enabled = True
        
    def select_strategy_by_violation_count(self, violation_count: int, 
                                         system_capabilities: Dict,
                                         performance_level: str = "good") -> StrategyConfig:
        """基于违例数量智能选择策略
        
        Args:
            violation_count: 违例数量
            system_capabilities: 系统能力信息
            performance_level: 性能等级
            
        Returns:
            StrategyConfig: 选择的策略配置
        """
        print(f"开始策略选择: 违例数量={violation_count:,}, 性能等级={performance_level}")
        
        # 基于违例数量确定基础策略
        base_strategy = self._determine_base_strategy(violation_count)
        
        # 根据系统能力调整策略
        adjusted_strategy = self._adjust_strategy_for_system(
            base_strategy, system_capabilities, violation_count
        )
        
        # 添加回退机制
        fallback_strategies = self._define_fallback_strategies(
            adjusted_strategy, violation_count, system_capabilities
        )
        
        # 创建完整的策略配置
        strategy_config = self._create_strategy_config(
            adjusted_strategy, violation_count, system_capabilities, 
            performance_level, fallback_strategies
        )
        
        # 记录策略选择
        self._record_strategy_selection(strategy_config, violation_count, system_capabilities)
        
        # 发送策略选择信号
        self.strategy_selected.emit(strategy_config.__dict__)
        
        return strategy_config
    
    def _determine_base_strategy(self, violation_count: int) -> StrategyType:
        """确定基础策略类型
        
        Args:
            violation_count: 违例数量
            
        Returns:
            StrategyType: 基础策略类型
        """
        if violation_count < self.violation_thresholds['small_dataset']:
            return StrategyType.STANDARD
        elif violation_count < self.violation_thresholds['medium_dataset']:
            return StrategyType.HIGH_PERFORMANCE
        elif violation_count < self.violation_thresholds['large_dataset']:
            return StrategyType.STREAMING
        else:
            return StrategyType.ULTRA_LARGE
    
    def _adjust_strategy_for_system(self, base_strategy: StrategyType, 
                                  system_caps: Dict, violation_count: int) -> StrategyType:
        """根据系统能力调整策略
        
        Args:
            base_strategy: 基础策略
            system_caps: 系统能力
            violation_count: 违例数量
            
        Returns:
            StrategyType: 调整后的策略
        """
        can_handle_large = system_caps.get('can_handle_large_datasets', False)
        available_memory_gb = system_caps.get('available_memory_gb', 4)
        cpu_cores = system_caps.get('cpu_cores', 4)
        
        # 如果系统资源不足，降级策略
        if not can_handle_large:
            if base_strategy == StrategyType.ULTRA_LARGE:
                return StrategyType.MEMORY_EFFICIENT
            elif base_strategy == StrategyType.STREAMING and violation_count > 30000:
                return StrategyType.MEMORY_EFFICIENT
            elif base_strategy == StrategyType.HIGH_PERFORMANCE and available_memory_gb < 4:
                return StrategyType.STANDARD
        
        # 如果系统性能很好，可以升级策略
        elif available_memory_gb > 8 and cpu_cores >= 8:
            if base_strategy == StrategyType.STANDARD and violation_count > 1000:
                return StrategyType.HIGH_PERFORMANCE
            elif base_strategy == StrategyType.STREAMING and violation_count < 40000:
                return StrategyType.HIGH_PERFORMANCE
        
        return base_strategy
    
    def _create_strategy_config(self, strategy_type: StrategyType, violation_count: int,
                              system_caps: Dict, performance_level: str,
                              fallback_strategies: List[Dict]) -> StrategyConfig:
        """创建策略配置
        
        Args:
            strategy_type: 策略类型
            violation_count: 违例数量
            system_caps: 系统能力
            performance_level: 性能等级
            fallback_strategies: 回退策略列表
            
        Returns:
            StrategyConfig: 策略配置
        """
        # 基础配置映射
        config_templates = {
            StrategyType.STANDARD: {
                'parser_type': 'standard_async',
                'display_mode': 'standard_table',
                'batch_size': 500,
                'use_streaming': False,
                'use_pagination': False,
                'page_size': 100,
                'memory_limit_mb': 100,
                'priority': 'user_experience'
            },
            StrategyType.HIGH_PERFORMANCE: {
                'parser_type': 'high_performance_async',
                'display_mode': 'high_performance_table',
                'batch_size': system_caps.get('optimal_batch_size', 2000),
                'use_streaming': False,
                'use_pagination': violation_count > 5000,
                'page_size': 200,
                'memory_limit_mb': 300,
                'priority': 'balanced'
            },
            StrategyType.STREAMING: {
                'parser_type': 'high_performance_streaming',
                'display_mode': 'virtual_table_with_lazy_loading',
                'batch_size': system_caps.get('optimal_batch_size', 5000),
                'use_streaming': True,
                'use_pagination': True,
                'page_size': 100,
                'memory_limit_mb': 500,
                'lazy_loading': True,
                'chunk_size': 10000,
                'priority': 'memory_efficiency'
            },
            StrategyType.ULTRA_LARGE: {
                'parser_type': 'streaming_with_chunking',
                'display_mode': 'paginated_virtual_table',
                'batch_size': min(system_caps.get('optimal_batch_size', 10000), 10000),
                'use_streaming': True,
                'use_pagination': True,
                'page_size': 50,
                'memory_limit_mb': 800,
                'lazy_loading': True,
                'background_processing': True,
                'chunk_size': 25000,
                'priority': 'stability'
            },
            StrategyType.MEMORY_EFFICIENT: {
                'parser_type': 'memory_efficient_streaming',
                'display_mode': 'paginated_virtual_table',
                'batch_size': 500,
                'use_streaming': True,
                'use_pagination': True,
                'page_size': 25,
                'memory_limit_mb': 100,
                'aggressive_gc': True,
                'lazy_loading': True,
                'priority': 'memory_conservation'
            }
        }
        
        # 获取基础配置
        base_config = config_templates.get(strategy_type, config_templates[StrategyType.STANDARD])
        
        # 根据系统能力调整配置
        if not system_caps.get('can_handle_large_datasets', False):
            base_config['memory_limit_mb'] = min(base_config['memory_limit_mb'], 200)
            base_config['batch_size'] = min(base_config['batch_size'], 1000)
            base_config['page_size'] = min(base_config.get('page_size', 100), 50)
        
        # 创建策略配置对象
        strategy_config = StrategyConfig(
            strategy_name=strategy_type.value,
            **base_config,
            confidence_score=self._calculate_confidence_score(strategy_type, violation_count, system_caps),
            selection_reason=self._get_selection_reason(strategy_type, violation_count, system_caps),
            alternative_strategies=[s['name'] for s in self._get_alternative_strategies(strategy_type, violation_count)]
        )
        
        # 添加回退策略信息
        strategy_config.fallback_strategies = fallback_strategies
        
        return strategy_config
    
    def _define_fallback_strategies(self, strategy_type: StrategyType, 
                                  violation_count: int, system_caps: Dict) -> List[Dict]:
        """定义回退策略
        
        Args:
            strategy_type: 当前策略类型
            violation_count: 违例数量
            system_caps: 系统能力
            
        Returns:
            List[Dict]: 回退策略列表
        """
        fallback_strategies = []
        
        # 内存压力回退
        if strategy_type in [StrategyType.HIGH_PERFORMANCE, StrategyType.STREAMING, StrategyType.ULTRA_LARGE]:
            fallback_strategies.append({
                'trigger': TriggerCondition.MEMORY_PRESSURE.value,
                'target_strategy': StrategyType.MEMORY_EFFICIENT.value,
                'reason': '内存使用过高，切换到内存优化模式',
                'config_adjustments': {
                    'batch_size': 500,
                    'page_size': 25,
                    'memory_limit_mb': 100,
                    'aggressive_gc': True
                }
            })
        
        # 处理超时回退
        if strategy_type != StrategyType.SPEED_OPTIMIZED:
            fallback_strategies.append({
                'trigger': TriggerCondition.PROCESSING_TIMEOUT.value,
                'target_strategy': StrategyType.SPEED_OPTIMIZED.value,
                'reason': '处理速度过慢，切换到速度优化模式',
                'config_adjustments': {
                    'batch_size': min(10000, system_caps.get('optimal_batch_size', 5000)),
                    'defer_ui_updates': True,
                    'background_processing': True
                }
            })
        
        # UI冻结回退
        if strategy_type not in [StrategyType.UI_RESPONSIVE, StrategyType.CONSERVATIVE]:
            fallback_strategies.append({
                'trigger': TriggerCondition.UI_FREEZE.value,
                'target_strategy': StrategyType.UI_RESPONSIVE.value,
                'reason': 'UI响应缓慢，切换到UI优化模式',
                'config_adjustments': {
                    'batch_size': 200,
                    'page_size': 50,
                    'ui_update_interval': 100,
                    'progressive_rendering': True
                }
            })

        # 性能降级回退 - 专门处理performance_degradation
        if strategy_type in [StrategyType.STANDARD, StrategyType.HIGH_PERFORMANCE]:
            fallback_strategies.append({
                'trigger': TriggerCondition.PERFORMANCE_DEGRADATION.value,
                'target_strategy': StrategyType.HIGH_PERFORMANCE.value,
                'reason': '检测到性能降级，强制切换到高性能表格模式',
                'config_adjustments': {
                    'display_mode': 'high_performance_table',
                    'use_pagination': True,
                    'page_size': 100,
                    'batch_size': 1000,
                    'memory_limit_mb': 300,
                    'force_high_performance': True
                }
            })
        elif strategy_type in [StrategyType.STREAMING, StrategyType.ULTRA_LARGE]:
            fallback_strategies.append({
                'trigger': TriggerCondition.PERFORMANCE_DEGRADATION.value,
                'target_strategy': StrategyType.MEMORY_EFFICIENT.value,
                'reason': '检测到性能降级，切换到内存优化模式',
                'config_adjustments': {
                    'display_mode': 'high_performance_table',
                    'use_pagination': True,
                    'page_size': 50,
                    'batch_size': 500,
                    'memory_limit_mb': 150,
                    'aggressive_gc': True
                }
            })

        # 高错误率回退
        fallback_strategies.append({
            'trigger': TriggerCondition.HIGH_ERROR_RATE.value,
            'target_strategy': StrategyType.STABLE.value,
            'reason': '错误率过高，切换到稳定模式',
            'config_adjustments': {
                'parser_type': 'basic_sync',
                'batch_size': 100,
                'error_recovery': True,
                'conservative_mode': True
            }
        })
        
        # 紧急回退（最后手段）
        fallback_strategies.append({
            'trigger': 'emergency',
            'target_strategy': StrategyType.CONSERVATIVE.value,
            'reason': '系统出现严重问题，切换到保守模式',
            'config_adjustments': {
                'parser_type': 'basic_sync',
                'display_mode': 'minimal_table',
                'batch_size': 50,
                'minimal_features': True,
                'safe_mode': True
            }
        })
        
        return fallback_strategies
    
    def _calculate_confidence_score(self, strategy_type: StrategyType, 
                                  violation_count: int, system_caps: Dict) -> float:
        """计算策略选择的置信度分数"""
        base_confidence = 0.8
        
        # 基于策略类型调整
        strategy_confidence = {
            StrategyType.STANDARD: 0.95,
            StrategyType.HIGH_PERFORMANCE: 0.85,
            StrategyType.STREAMING: 0.75,
            StrategyType.ULTRA_LARGE: 0.65,
            StrategyType.MEMORY_EFFICIENT: 0.80
        }
        
        confidence = strategy_confidence.get(strategy_type, base_confidence)
        
        # 基于系统能力调整
        if system_caps.get('can_handle_large_datasets', False):
            confidence += 0.1
        else:
            confidence -= 0.1
        
        # 基于违例数量复杂度调整
        if violation_count > self.violation_thresholds['very_large_dataset']:
            confidence -= 0.15
        elif violation_count > self.violation_thresholds['large_dataset']:
            confidence -= 0.05
        
        return max(0.1, min(1.0, confidence))
    
    def _get_selection_reason(self, strategy_type: StrategyType, 
                            violation_count: int, system_caps: Dict) -> str:
        """获取策略选择原因"""
        reasons = {
            StrategyType.STANDARD: f"小数据集({violation_count:,}个违例)，使用标准处理获得最佳用户体验",
            StrategyType.HIGH_PERFORMANCE: f"中等数据集({violation_count:,}个违例)，使用高性能处理平衡速度和资源使用",
            StrategyType.STREAMING: f"大数据集({violation_count:,}个违例)，使用流式处理优化内存使用",
            StrategyType.ULTRA_LARGE: f"超大数据集({violation_count:,}个违例)，使用分块流式处理确保系统稳定",
            StrategyType.MEMORY_EFFICIENT: f"系统内存有限，使用内存优化策略处理{violation_count:,}个违例"
        }
        
        base_reason = reasons.get(strategy_type, f"为{violation_count:,}个违例选择合适的处理策略")
        
        # 添加系统能力相关信息
        if not system_caps.get('can_handle_large_datasets', False):
            base_reason += "，系统资源有限，采用保守配置"
        
        return base_reason
    
    def _get_alternative_strategies(self, strategy_type: StrategyType, violation_count: int) -> List[Dict]:
        """获取备选策略"""
        alternatives = []
        
        if strategy_type == StrategyType.STANDARD:
            alternatives.append({
                'name': StrategyType.HIGH_PERFORMANCE.value,
                'reason': '如果需要更快的处理速度',
                'trade_offs': '可能消耗更多内存'
            })
        
        elif strategy_type == StrategyType.HIGH_PERFORMANCE:
            alternatives.extend([
                {
                    'name': StrategyType.STANDARD.value,
                    'reason': '如果系统资源紧张',
                    'trade_offs': '处理速度可能较慢'
                },
                {
                    'name': StrategyType.STREAMING.value,
                    'reason': '如果内存使用是主要关注点',
                    'trade_offs': 'UI响应可能稍慢'
                }
            ])
        
        elif strategy_type == StrategyType.STREAMING:
            alternatives.extend([
                {
                    'name': StrategyType.HIGH_PERFORMANCE.value,
                    'reason': '如果系统性能足够强',
                    'trade_offs': '内存使用量会显著增加'
                },
                {
                    'name': StrategyType.MEMORY_EFFICIENT.value,
                    'reason': '如果内存压力很大',
                    'trade_offs': '处理速度会明显降低'
                }
            ])
        
        elif strategy_type == StrategyType.ULTRA_LARGE:
            alternatives.extend([
                {
                    'name': StrategyType.STREAMING.value,
                    'reason': '如果系统性能较好',
                    'trade_offs': '可能遇到内存压力'
                },
                {
                    'name': StrategyType.MEMORY_EFFICIENT.value,
                    'reason': '如果稳定性是首要考虑',
                    'trade_offs': '用户体验可能受影响'
                }
            ])
        
        return alternatives
    
    def _record_strategy_selection(self, strategy_config: StrategyConfig, 
                                 violation_count: int, system_caps: Dict):
        """记录策略选择历史"""
        selection_record = {
            'timestamp': time.time(),
            'strategy_name': strategy_config.strategy_name,
            'violation_count': violation_count,
            'system_capabilities': system_caps,
            'confidence_score': strategy_config.confidence_score,
            'selection_reason': strategy_config.selection_reason
        }
        
        self.strategy_history.append(selection_record)
        self.current_strategy = strategy_config
        
        # 限制历史记录数量
        if len(self.strategy_history) > 10:
            self.strategy_history.pop(0)
    
    def trigger_fallback(self, trigger_condition: str, current_metrics: Dict) -> Optional[StrategyConfig]:
        """触发回退机制
        
        Args:
            trigger_condition: 触发条件
            current_metrics: 当前性能指标
            
        Returns:
            Optional[StrategyConfig]: 回退策略配置，如果没有合适的回退策略则返回None
        """
        if not self.current_strategy or not hasattr(self.current_strategy, 'fallback_strategies'):
            return None
        
        print(f"触发回退机制: {trigger_condition}")
        
        # 查找匹配的回退策略
        fallback_strategy = None
        for strategy in self.current_strategy.fallback_strategies:
            if strategy['trigger'] == trigger_condition:
                fallback_strategy = strategy
                break
        
        # 如果没有找到特定的回退策略，使用紧急回退
        if not fallback_strategy and self.emergency_fallback_enabled:
            for strategy in self.current_strategy.fallback_strategies:
                if strategy['trigger'] == 'emergency':
                    fallback_strategy = strategy
                    break
        
        if not fallback_strategy:
            print(f"未找到适合的回退策略: {trigger_condition}")
            return None
        
        # 创建回退策略配置
        fallback_config = self._create_fallback_config(fallback_strategy, current_metrics)
        
        # 发送回退信号
        self.fallback_triggered.emit(trigger_condition, fallback_config.strategy_name)
        
        # 更新当前策略
        self.current_strategy = fallback_config
        
        return fallback_config
    
    def _create_fallback_config(self, fallback_strategy: Dict, current_metrics: Dict) -> StrategyConfig:
        """创建回退策略配置"""
        target_strategy = fallback_strategy['target_strategy']
        config_adjustments = fallback_strategy.get('config_adjustments', {})

        # 基础配置
        base_config = {
            'strategy_name': target_strategy,
            'parser_type': config_adjustments.get('parser_type', 'standard_async'),
            'display_mode': config_adjustments.get('display_mode', 'high_performance_table'),  # 默认使用高性能表格
            'batch_size': config_adjustments.get('batch_size', 500),
            'use_streaming': config_adjustments.get('use_streaming', False),
            'use_pagination': config_adjustments.get('use_pagination', True),
            'page_size': config_adjustments.get('page_size', 50),
            'memory_limit_mb': config_adjustments.get('memory_limit_mb', 150),
            'priority': 'fallback_recovery'
        }

        # 应用所有调整，包括特殊标志
        for key, value in config_adjustments.items():
            base_config[key] = value

        # 创建配置对象
        fallback_config = StrategyConfig(
            **base_config,
            confidence_score=0.7,  # 回退策略的置信度较低
            selection_reason=fallback_strategy['reason']
        )

        return fallback_config
    
    def enable_dynamic_switching(self, enable: bool = True):
        """启用/禁用动态策略切换"""
        if enable and not self.monitoring_timer.isActive():
            self.monitoring_timer.start(self.monitoring_interval)
            self.monitoring_enabled = True
            print("动态策略切换已启用")
        elif not enable and self.monitoring_timer.isActive():
            self.monitoring_timer.stop()
            self.monitoring_enabled = False
            print("动态策略切换已禁用")
    
    def _monitor_current_strategy(self):
        """监控当前策略性能"""
        if not self.current_strategy or not self.monitoring_enabled:
            return
        
        # 这里应该从性能优化器获取当前性能指标
        # 为了演示，我们使用模拟数据
        current_metrics = self._get_current_performance_metrics()
        
        # 检查是否需要切换策略
        switch_trigger = self._check_switch_triggers(current_metrics)
        
        if switch_trigger and self._can_switch_strategy():
            self._perform_dynamic_switch(switch_trigger, current_metrics)
    
    def _get_current_performance_metrics(self) -> Dict:
        """获取当前性能指标（模拟实现）"""
        # 实际实现中应该从性能监控器获取真实数据
        return {
            'memory_usage_mb': 200,
            'memory_usage_percent': 60,
            'processing_speed': 1.0,
            'ui_response_time': 0.1,
            'error_rate': 0.01,
            'load_time': 3.0
        }
    
    def _check_switch_triggers(self, current_metrics: Dict) -> Optional[str]:
        """检查是否需要触发策略切换"""
        memory_usage_percent = current_metrics.get('memory_usage_percent', 0)
        processing_speed = current_metrics.get('processing_speed', 1.0)
        ui_response_time = current_metrics.get('ui_response_time', 0)
        error_rate = current_metrics.get('error_rate', 0)
        
        # 检查各种触发条件
        if memory_usage_percent > 85:
            return TriggerCondition.MEMORY_PRESSURE.value
        elif processing_speed < 0.3:
            return TriggerCondition.PROCESSING_TIMEOUT.value
        elif ui_response_time > 0.5:
            return TriggerCondition.UI_FREEZE.value
        elif error_rate > 0.1:
            return TriggerCondition.HIGH_ERROR_RATE.value
        elif memory_usage_percent > 90 or processing_speed < 0.1:
            return TriggerCondition.SYSTEM_OVERLOAD.value
        
        return None
    
    def _can_switch_strategy(self) -> bool:
        """检查是否可以切换策略"""
        current_time = time.time()
        
        # 检查冷却期
        if current_time - self.last_switch_time < self.cooldown_period:
            return False
        
        # 检查切换次数限制
        if self.switch_count >= self.max_switches_per_session:
            return False
        
        return True
    
    def _perform_dynamic_switch(self, trigger_condition: str, current_metrics: Dict):
        """执行动态策略切换"""
        old_strategy_name = self.current_strategy.strategy_name if self.current_strategy else "unknown"
        
        # 触发回退机制
        new_strategy = self.trigger_fallback(trigger_condition, current_metrics)
        
        if new_strategy:
            self.switch_count += 1
            self.last_switch_time = time.time()
            
            print(f"动态策略切换: {old_strategy_name} -> {new_strategy.strategy_name}")
            
            # 发送切换信号
            self.strategy_switched.emit(old_strategy_name, new_strategy.strategy_name)
        else:
            print(f"动态切换失败，无法找到合适的策略: {trigger_condition}")
    
    def get_strategy_statistics(self) -> Dict:
        """获取策略使用统计"""
        return {
            'current_strategy': self.current_strategy.strategy_name if self.current_strategy else None,
            'total_selections': len(self.strategy_history),
            'switch_count': self.switch_count,
            'last_switch_time': self.last_switch_time,
            'monitoring_enabled': self.monitoring_enabled,
            'strategy_history': self.strategy_history[-5:],  # 最近5次选择
            'cooldown_remaining': max(0, self.cooldown_period - (time.time() - self.last_switch_time))
        }
    
    def switch_strategy_dynamically(self, current_strategy: Dict, trigger_condition: str, current_metrics: Dict) -> Dict:
        """动态切换策略
        
        Args:
            current_strategy: 当前策略
            trigger_condition: 触发条件
            current_metrics: 当前性能指标
            
        Returns:
            Dict: 新策略配置
        """
        print(f"触发动态策略切换: {trigger_condition}")
        
        # 获取当前违例数量
        violation_count = current_strategy.get('selection_metadata', {}).get('violation_count', 1000)
        
        # 重新评估系统能力
        if self.performance_optimizer:
            system_caps = self.performance_optimizer.assess_system_capabilities()
        else:
            system_caps = {
                'cpu_cores': 4,
                'total_memory_gb': 8,
                'available_memory_gb': 4,
                'can_handle_large_datasets': True,
                'optimal_batch_size': 1000
            }
        
        # 根据触发条件选择新策略
        if trigger_condition == 'memory_pressure':
            new_strategy = self._select_memory_efficient_strategy(violation_count, system_caps)
        elif trigger_condition == 'processing_timeout':
            new_strategy = self._select_speed_optimized_strategy(violation_count, system_caps)
        elif trigger_condition == 'ui_freeze':
            new_strategy = self._select_ui_responsive_strategy(violation_count, system_caps)
        elif trigger_condition == 'high_error_rate':
            new_strategy = self._select_stable_strategy(violation_count, system_caps)
        else:
            # 默认回退到保守策略
            new_strategy = self._select_conservative_strategy(violation_count, system_caps)
        
        # 添加切换元数据
        new_strategy['switch_metadata'] = {
            'previous_strategy': current_strategy.get('strategy_name', 'unknown'),
            'switch_trigger': trigger_condition,
            'switch_time': time.time(),
            'switch_reason': f"由于{trigger_condition}触发策略切换",
            'expected_improvement': self._predict_switch_improvement(current_strategy, new_strategy, trigger_condition)
        }
        
        return new_strategy
    
    def _select_memory_efficient_strategy(self, violation_count: int, system_caps: Dict) -> Dict:
        """选择内存高效策略"""
        return {
            'strategy_name': 'memory_efficient_processing',
            'parser_type': 'memory_efficient_streaming',
            'display_mode': 'paginated_virtual_table',
            'batch_size': 500,
            'use_streaming': True,
            'use_pagination': True,
            'page_size': 25,
            'memory_limit_mb': 100,
            'aggressive_gc': True,
            'lazy_loading': True,
            'priority': 'memory_conservation'
        }
    
    def _select_speed_optimized_strategy(self, violation_count: int, system_caps: Dict) -> Dict:
        """选择速度优化策略"""
        return {
            'strategy_name': 'speed_optimized_processing',
            'parser_type': 'high_performance_async',
            'display_mode': 'background_processing',
            'batch_size': min(10000, system_caps.get('optimal_batch_size', 5000)),
            'use_streaming': False,
            'use_pagination': True,
            'page_size': 200,
            'memory_limit_mb': 600,
            'defer_ui_updates': True,
            'background_processing': True,
            'priority': 'processing_speed'
        }
    
    def _select_ui_responsive_strategy(self, violation_count: int, system_caps: Dict) -> Dict:
        """选择UI响应优化策略"""
        return {
            'strategy_name': 'ui_responsive_processing',
            'parser_type': 'chunked_async',
            'display_mode': 'progressive_loading',
            'batch_size': 200,
            'use_streaming': True,
            'use_pagination': True,
            'page_size': 50,
            'memory_limit_mb': 200,
            'ui_update_interval': 100,
            'progressive_rendering': True,
            'priority': 'user_experience'
        }
    
    def _select_stable_strategy(self, violation_count: int, system_caps: Dict) -> Dict:
        """选择稳定性优化策略"""
        return {
            'strategy_name': 'stable_processing',
            'parser_type': 'basic_sync',
            'display_mode': 'simple_table',
            'batch_size': 100,
            'use_streaming': False,
            'use_pagination': True,
            'page_size': 100,
            'memory_limit_mb': 150,
            'error_recovery': True,
            'conservative_mode': True,
            'priority': 'stability'
        }
    
    def _select_conservative_strategy(self, violation_count: int, system_caps: Dict) -> Dict:
        """选择保守策略"""
        return {
            'strategy_name': 'conservative_processing',
            'parser_type': 'basic_sync',
            'display_mode': 'minimal_table',
            'batch_size': 50,
            'use_streaming': False,
            'use_pagination': True,
            'page_size': 50,
            'memory_limit_mb': 100,
            'minimal_features': True,
            'safe_mode': True,
            'priority': 'reliability'
        }
    
    def _predict_switch_improvement(self, old_strategy: Dict, new_strategy: Dict, trigger: str) -> Dict:
        """预测策略切换的改进效果"""
        improvements = {
            'memory_pressure': {
                'memory_usage_reduction': '30-50%',
                'stability_improvement': 'high',
                'performance_trade_off': 'moderate_slowdown'
            },
            'processing_timeout': {
                'speed_improvement': '20-40%',
                'memory_usage_increase': 'moderate',
                'ui_responsiveness': 'may_decrease'
            },
            'ui_freeze': {
                'ui_responsiveness_improvement': 'significant',
                'user_experience': 'much_better',
                'processing_speed': 'may_decrease'
            },
            'high_error_rate': {
                'stability_improvement': 'high',
                'error_rate_reduction': '70-90%',
                'performance': 'conservative'
            }
        }
        
        return improvements.get(trigger, {
            'general_improvement': 'expected',
            'trade_offs': 'minimal'
        })

    def reset_strategy_state(self):
        """重置策略状态"""
        self.current_strategy = None
        self.switch_count = 0
        self.last_switch_time = 0
        self.strategy_history.clear()
        print("策略状态已重置")