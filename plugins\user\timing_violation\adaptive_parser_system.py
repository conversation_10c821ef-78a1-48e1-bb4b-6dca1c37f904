"""
自适应解析器系统

基于违例数量智能选择最优解析策略，支持动态策略切换和性能监控。
"""

import os
import time
import psutil
from typing import Dict, List, Optional, Callable, Any, Union
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

try:
    from .parser import VioLogParser, AsyncVioLogParser, HighPerformanceVioLogParser, HighPerformanceAsyncParser
    from .performance_optimizer import PerformanceOptimizer
    from .enhanced_batch_processor import MemoryAwareBatchProcessor, ProgressiveBatchSizeOptimizer
    from .enhanced_streaming_parser import EnhancedStreamingParser, EnhancedStreamingAsyncParser
except ImportError:
    # Fallback for direct execution
    from parser import VioLogParser, AsyncVioLogParser, HighPerformanceVioLogParser, HighPerformanceAsyncParser
    from performance_optimizer import PerformanceOptimizer
    from enhanced_batch_processor import MemoryAwareBatchProcessor, ProgressiveBatchSizeOptimizer
    from enhanced_streaming_parser import EnhancedStreamingParser, EnhancedStreamingAsyncParser


class ParserPerformanceMonitor(QObject):
    """解析器性能监控器"""
    
    # 信号定义
    performance_warning = pyqtSignal(str, dict)  # 警告类型, 性能数据
    strategy_switch_recommended = pyqtSignal(str, str)  # 当前策略, 推荐策略
    
    def __init__(self):
        super().__init__()
        self.monitoring_active = False
        self.current_metrics = {}
        self.performance_history = []
        self.switch_cooldown = 0
        
        # 性能阈值
        self.thresholds = {
            'memory_usage_critical': 0.85,  # 85% 内存使用率
            'processing_speed_low': 0.3,    # 处理速度低于预期30%
            'ui_response_slow': 0.2,        # UI响应时间超过200ms
            'error_rate_high': 0.05         # 错误率超过5%
        }
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._collect_metrics)
        
    def start_monitoring(self, parser_instance: Any, expected_performance: Dict):
        """开始监控解析器性能
        
        Args:
            parser_instance: 解析器实例
            expected_performance: 预期性能指标
        """
        self.monitoring_active = True
        self.parser_instance = parser_instance
        self.expected_performance = expected_performance
        self.start_time = time.time()
        self.monitor_timer.start(1000)  # 每秒监控一次
        
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring_active = False
        self.monitor_timer.stop()
        
    def _collect_metrics(self):
        """收集性能指标"""
        if not self.monitoring_active:
            return
            
        try:
            # 收集系统指标
            memory_info = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info()
            
            current_time = time.time()
            elapsed_time = current_time - self.start_time
            
            metrics = {
                'timestamp': current_time,
                'elapsed_time': elapsed_time,
                'memory_usage_percent': memory_info.percent,
                'process_memory_mb': process_memory.rss / (1024 ** 2),
                'cpu_usage_percent': process.cpu_percent(),
                'available_memory_gb': memory_info.available / (1024 ** 3)
            }
            
            # 计算处理速度（如果有进度信息）
            if hasattr(self.parser_instance, 'parse_stats'):
                stats = self.parser_instance.parse_stats
                if stats.get('processed_violations', 0) > 0 and elapsed_time > 0:
                    processing_speed = stats['processed_violations'] / elapsed_time
                    expected_speed = self.expected_performance.get('expected_violations_per_sec', 1000)
                    metrics['processing_speed_ratio'] = processing_speed / expected_speed
                    metrics['violations_per_second'] = processing_speed
            
            self.current_metrics = metrics
            self.performance_history.append(metrics)
            
            # 检查是否需要发出警告或建议切换策略
            self._check_performance_issues(metrics)
            
        except Exception as e:
            print(f"性能监控收集指标失败: {str(e)}")
    
    def _check_performance_issues(self, metrics: Dict):
        """检查性能问题并发出相应信号
        
        Args:
            metrics: 当前性能指标
        """
        # 检查内存使用
        if metrics['memory_usage_percent'] > self.thresholds['memory_usage_critical'] * 100:
            self.performance_warning.emit('memory_critical', metrics)
            if self.switch_cooldown <= 0:
                self.strategy_switch_recommended.emit('current', 'memory_efficient')
                self.switch_cooldown = 30  # 30秒冷却期
        
        # 检查处理速度
        processing_speed_ratio = metrics.get('processing_speed_ratio', 1.0)
        if processing_speed_ratio < self.thresholds['processing_speed_low']:
            self.performance_warning.emit('processing_slow', metrics)
            if self.switch_cooldown <= 0:
                self.strategy_switch_recommended.emit('current', 'high_performance')
                self.switch_cooldown = 30
        
        # 减少冷却时间
        if self.switch_cooldown > 0:
            self.switch_cooldown -= 1
    
    def get_current_performance(self) -> Dict:
        """获取当前性能指标
        
        Returns:
            Dict: 当前性能指标
        """
        return self.current_metrics.copy()
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要
        
        Returns:
            Dict: 性能摘要
        """
        if not self.performance_history:
            return {}
        
        # 计算平均值和趋势
        avg_memory = sum(m['memory_usage_percent'] for m in self.performance_history) / len(self.performance_history)
        avg_cpu = sum(m.get('cpu_usage_percent', 0) for m in self.performance_history) / len(self.performance_history)
        
        processing_speeds = [m.get('violations_per_second', 0) for m in self.performance_history if m.get('violations_per_second', 0) > 0]
        avg_processing_speed = sum(processing_speeds) / len(processing_speeds) if processing_speeds else 0
        
        return {
            'total_monitoring_time': self.performance_history[-1]['elapsed_time'],
            'average_memory_usage_percent': avg_memory,
            'average_cpu_usage_percent': avg_cpu,
            'average_processing_speed': avg_processing_speed,
            'peak_memory_usage': max(m['memory_usage_percent'] for m in self.performance_history),
            'performance_stable': self._is_performance_stable(),
            'recommendations': self._generate_performance_recommendations()
        }
    
    def _is_performance_stable(self) -> bool:
        """判断性能是否稳定
        
        Returns:
            bool: 性能是否稳定
        """
        if len(self.performance_history) < 5:
            return True
        
        recent_metrics = self.performance_history[-5:]
        memory_variance = max(m['memory_usage_percent'] for m in recent_metrics) - min(m['memory_usage_percent'] for m in recent_metrics)
        
        return memory_variance < 10  # 内存使用变化小于10%认为稳定
    
    def _generate_performance_recommendations(self) -> List[str]:
        """生成性能优化建议
        
        Returns:
            List[str]: 优化建议列表
        """
        recommendations = []
        
        if not self.performance_history:
            return recommendations
        
        avg_memory = sum(m['memory_usage_percent'] for m in self.performance_history) / len(self.performance_history)
        
        if avg_memory > 80:
            recommendations.append("内存使用率较高，建议启用内存优化模式")
        
        processing_speeds = [m.get('violations_per_second', 0) for m in self.performance_history if m.get('violations_per_second', 0) > 0]
        if processing_speeds:
            avg_speed = sum(processing_speeds) / len(processing_speeds)
            if avg_speed < 500:  # 低于500违例/秒
                recommendations.append("处理速度较慢，建议检查系统资源或优化解析策略")
        
        if not self._is_performance_stable():
            recommendations.append("性能波动较大，建议使用更稳定的解析策略")
        
        return recommendations


class AdaptiveParserSystem(QObject):
    """自适应解析器系统
    
    基于违例数量和系统性能智能选择最优解析策略
    """
    
    # 信号定义
    strategy_selected = pyqtSignal(str, dict)  # 策略名称, 策略配置
    strategy_switched = pyqtSignal(str, str, str)  # 原策略, 新策略, 切换原因
    parsing_progress = pyqtSignal(int, str)  # 进度百分比, 状态信息
    parsing_completed = pyqtSignal(list, dict)  # 解析结果, 性能统计
    parsing_failed = pyqtSignal(str, dict)  # 错误信息, 错误详情
    
    def __init__(self):
        super().__init__()
        
        # 核心组件
        self.performance_optimizer = PerformanceOptimizer()
        self.performance_monitor = ParserPerformanceMonitor()
        self.batch_processor = MemoryAwareBatchProcessor()
        self.batch_optimizer = ProgressiveBatchSizeOptimizer()
        
        # 解析器实例缓存
        self.parser_instances = {}
        self.current_parser = None
        self.current_strategy = None
        
        # 策略配置
        self.strategy_configs = self._initialize_strategy_configs()
        
        # 性能统计
        self.parsing_stats = {
            'total_parsings': 0,
            'successful_parsings': 0,
            'strategy_switches': 0,
            'average_performance': {},
            'batch_processing_stats': {}
        }
        
        # 连接信号
        self.performance_monitor.strategy_switch_recommended.connect(self._handle_strategy_switch_recommendation)
        self.batch_processor.batch_size_adjusted.connect(self._handle_batch_size_adjustment)
        self.batch_processor.memory_pressure_detected.connect(self._handle_memory_pressure)
        
    def _initialize_strategy_configs(self) -> Dict:
        """初始化策略配置
        
        Returns:
            Dict: 策略配置字典
        """
        return {
            'standard_async': {
                'parser_class': AsyncVioLogParser,
                'violation_range': (0, 2000),
                'description': '标准异步解析器，适用于小数据集',
                'memory_efficient': True,
                'processing_speed': 'medium',
                'ui_responsiveness': 'high'
            },
            'high_performance_async': {
                'parser_class': HighPerformanceAsyncParser,
                'violation_range': (2000, 20000),
                'description': '高性能异步解析器，适用于中等数据集',
                'memory_efficient': False,
                'processing_speed': 'high',
                'ui_responsiveness': 'medium'
            },
            'high_performance_streaming': {
                'parser_class': HighPerformanceVioLogParser,
                'violation_range': (20000, 50000),
                'description': '高性能流式解析器，适用于大数据集',
                'memory_efficient': True,
                'processing_speed': 'very_high',
                'ui_responsiveness': 'low',
                'use_streaming': True
            },
            'enhanced_streaming': {
                'parser_class': EnhancedStreamingAsyncParser,
                'violation_range': (50000, float('inf')),
                'description': '增强流式解析器，专用于超大数据集（>50K违例）',
                'memory_efficient': True,
                'processing_speed': 'ultra_high',
                'ui_responsiveness': 'low',
                'use_streaming': True,
                'violation_aware_chunking': True,
                'memory_pressure_detection': True
            }
        }
    
    def select_optimal_parser(self, file_path: str, violation_count: Optional[int] = None) -> Dict:
        """选择最优解析器
        
        Args:
            file_path: 文件路径
            violation_count: 违例数量（如果已知）
            
        Returns:
            Dict: 选择结果，包含策略名称和配置
        """
        try:
            # 分析文件性能特征
            file_analysis = self.performance_optimizer.analyze_file_performance(file_path)
            
            if not violation_count:
                violation_count = file_analysis.get('estimated_violations', 0)
            
            # 获取系统能力
            system_caps = file_analysis.get('system_capabilities', {})
            
            # 基于违例数量选择策略
            selected_strategy = self._select_strategy_by_violation_count(violation_count, system_caps)
            
            # 获取策略配置
            strategy_config = self.strategy_configs.get(selected_strategy)
            if not strategy_config:
                selected_strategy = 'standard_async'
                strategy_config = self.strategy_configs[selected_strategy]
            
            # 创建完整的选择结果
            selection_result = {
                'strategy_name': selected_strategy,
                'strategy_config': strategy_config,
                'violation_count': violation_count,
                'file_analysis': file_analysis,
                'selection_confidence': self._calculate_selection_confidence(violation_count, system_caps),
                'alternative_strategies': self._get_alternative_strategies(violation_count),
                'performance_prediction': self._predict_strategy_performance(selected_strategy, violation_count, system_caps)
            }
            
            self.current_strategy = selected_strategy
            self.strategy_selected.emit(selected_strategy, selection_result)
            
            return selection_result
            
        except Exception as e:
            error_msg = f"选择解析器失败: {str(e)}"
            print(error_msg)
            # 返回默认策略
            return {
                'strategy_name': 'standard_async',
                'strategy_config': self.strategy_configs['standard_async'],
                'error': error_msg
            }
    
    def _select_strategy_by_violation_count(self, violation_count: int, system_caps: Dict) -> str:
        """基于违例数量选择策略
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力
            
        Returns:
            str: 选择的策略名称
        """
        # 检查系统是否能处理大数据集
        can_handle_large = system_caps.get('can_handle_large_datasets', False)
        available_memory_gb = system_caps.get('available_memory_gb', 4)
        
        if violation_count < 2000:
            return 'standard_async'
        elif violation_count < 20000:
            # 中等数据集：根据系统能力选择
            if can_handle_large and available_memory_gb > 2:
                return 'high_performance_async'
            else:
                return 'standard_async'  # 系统能力不足时使用标准解析器
        elif violation_count < 50000:
            # 大数据集：使用高性能流式处理
            return 'high_performance_streaming'
        else:
            # 超大数据集：使用增强流式处理
            return 'enhanced_streaming'
    
    def create_parser_instance(self, strategy_name: str, file_path: str) -> Any:
        """创建解析器实例
        
        Args:
            strategy_name: 策略名称
            file_path: 文件路径
            
        Returns:
            Any: 解析器实例
        """
        try:
            strategy_config = self.strategy_configs.get(strategy_name)
            if not strategy_config:
                raise ValueError(f"未知的策略: {strategy_name}")
            
            parser_class = strategy_config['parser_class']
            
            # 创建解析器实例
            if strategy_name == 'high_performance_streaming':
                # 原有的高性能流式解析器不需要文件路径参数
                parser_instance = parser_class()
            elif strategy_name == 'enhanced_streaming':
                # 增强流式解析器需要文件路径参数
                parser_instance = parser_class(file_path)
            else:
                # 其他异步解析器需要文件路径参数
                parser_instance = parser_class(file_path)
            
            # 缓存实例
            self.parser_instances[strategy_name] = parser_instance
            self.current_parser = parser_instance
            
            return parser_instance
            
        except Exception as e:
            error_msg = f"创建解析器实例失败: {str(e)}"
            print(error_msg)
            # 创建默认解析器
            default_parser = AsyncVioLogParser(file_path)
            self.current_parser = default_parser
            return default_parser
    
    def parse_with_adaptive_strategy(self, file_path: str, 
                                   progress_callback: Optional[Callable[[int, str], None]] = None) -> List[Dict]:
        """使用自适应策略解析文件
        
        Args:
            file_path: 文件路径
            progress_callback: 进度回调函数
            
        Returns:
            List[Dict]: 解析结果
        """
        start_time = time.time()
        
        try:
            # 选择最优策略
            selection_result = self.select_optimal_parser(file_path)
            strategy_name = selection_result['strategy_name']
            violation_count = selection_result['violation_count']
            
            # 创建解析器实例
            parser_instance = self.create_parser_instance(strategy_name, file_path)
            
            # 开始性能监控
            expected_performance = selection_result.get('performance_prediction', {})
            self.performance_monitor.start_monitoring(parser_instance, expected_performance)
            
            # 创建进度回调包装器
            def wrapped_progress_callback(progress: int, message: str):
                self.parsing_progress.emit(progress, f"[{strategy_name}] {message}")
                if progress_callback:
                    progress_callback(progress, message)
            
            # 执行解析
            if strategy_name == 'high_performance_streaming':
                # 使用原有的高性能流式解析
                violations = parser_instance.parse_log_file_streaming(file_path, wrapped_progress_callback)
            elif strategy_name == 'enhanced_streaming':
                # 使用增强流式解析
                if hasattr(parser_instance, 'run'):
                    # 异步增强流式解析器
                    violations = self._run_async_parser(parser_instance, wrapped_progress_callback)
                else:
                    # 同步增强流式解析器（如果有的话）
                    violations = parser_instance.streaming_parser.parse_large_file_streaming(file_path, wrapped_progress_callback)
            else:
                # 使用异步解析
                if hasattr(parser_instance, 'run'):
                    # 异步解析器需要特殊处理
                    violations = self._run_async_parser(parser_instance, wrapped_progress_callback)
                else:
                    # 同步解析器
                    violations = parser_instance.parse_log_file(file_path, wrapped_progress_callback)
            
            # 停止性能监控
            self.performance_monitor.stop_monitoring()
            
            # 收集性能统计
            parse_time = time.time() - start_time
            performance_summary = self.performance_monitor.get_performance_summary()
            
            parsing_stats = {
                'strategy_used': strategy_name,
                'violation_count': len(violations),
                'parse_time': parse_time,
                'violations_per_second': len(violations) / parse_time if parse_time > 0 else 0,
                'performance_summary': performance_summary,
                'file_size_mb': os.path.getsize(file_path) / (1024 ** 2)
            }
            
            # 更新统计信息
            self.parsing_stats['total_parsings'] += 1
            self.parsing_stats['successful_parsings'] += 1
            
            self.parsing_completed.emit(violations, parsing_stats)
            
            return violations
            
        except Exception as e:
            # 停止性能监控
            self.performance_monitor.stop_monitoring()
            
            error_msg = f"自适应解析失败: {str(e)}"
            error_details = {
                'strategy_attempted': getattr(self, 'current_strategy', 'unknown'),
                'file_path': file_path,
                'parse_time': time.time() - start_time
            }
            
            print(error_msg)
            self.parsing_failed.emit(error_msg, error_details)
            
            # 尝试使用最基础的解析器作为最后的回退
            try:
                basic_parser = VioLogParser()
                return basic_parser.parse_log_file(file_path, progress_callback)
            except Exception as fallback_error:
                print(f"回退解析也失败: {str(fallback_error)}")
                raise Exception(f"所有解析策略都失败: {error_msg}, 回退错误: {str(fallback_error)}")
    
    def _run_async_parser(self, parser_instance: Any, progress_callback: Callable) -> List[Dict]:
        """运行异步解析器
        
        Args:
            parser_instance: 异步解析器实例
            progress_callback: 进度回调函数
            
        Returns:
            List[Dict]: 解析结果
        """
        # 这里需要实现异步解析器的同步运行逻辑
        # 由于这是一个复杂的异步处理，暂时使用简化实现
        
        results = []
        error_occurred = False
        
        def on_parsing_completed(violations):
            nonlocal results
            results = violations
        
        def on_parsing_failed(error_msg):
            nonlocal error_occurred
            error_occurred = True
            raise Exception(error_msg)
        
        def on_progress_updated(progress, message):
            progress_callback(progress, message)
        
        # 连接信号
        parser_instance.parsing_completed.connect(on_parsing_completed)
        parser_instance.parsing_failed.connect(on_parsing_failed)
        parser_instance.progress_updated.connect(on_progress_updated)
        
        # 启动解析
        parser_instance.start()
        
        # 等待完成（简化实现，实际应该使用事件循环）
        parser_instance.wait()
        
        if error_occurred:
            raise Exception("异步解析失败")
        
        return results
    
    def _handle_strategy_switch_recommendation(self, current_strategy: str, recommended_strategy: str):
        """处理策略切换建议
        
        Args:
            current_strategy: 当前策略
            recommended_strategy: 推荐策略
        """
        if self.current_strategy and recommended_strategy != self.current_strategy:
            print(f"建议切换策略: {self.current_strategy} -> {recommended_strategy}")
            self.strategy_switched.emit(self.current_strategy, recommended_strategy, "performance_optimization")
            self.parsing_stats['strategy_switches'] += 1
    
    def _handle_batch_size_adjustment(self, old_size: int, new_size: int, reason: str):
        """处理批大小调整
        
        Args:
            old_size: 旧批大小
            new_size: 新批大小
            reason: 调整原因
        """
        print(f"批大小调整: {old_size} -> {new_size} (原因: {reason})")
        
        # 更新统计信息
        if 'batch_adjustments' not in self.parsing_stats:
            self.parsing_stats['batch_adjustments'] = 0
        self.parsing_stats['batch_adjustments'] += 1
    
    def _handle_memory_pressure(self, memory_usage: float, pressure_level: str):
        """处理内存压力
        
        Args:
            memory_usage: 内存使用率
            pressure_level: 压力等级
        """
        print(f"检测到内存压力: {memory_usage:.1%} ({pressure_level})")
        
        # 根据内存压力等级采取相应措施
        if pressure_level == 'emergency':
            # 紧急情况：强制垃圾回收并切换到内存高效模式
            import gc
            gc.collect()
            if self.current_strategy != 'high_performance_streaming':
                self.strategy_switched.emit(self.current_strategy, 'high_performance_streaming', "memory_emergency")
        elif pressure_level == 'critical':
            # 严重情况：建议切换到内存高效策略
            if self.current_strategy == 'high_performance_async':
                self.strategy_switched.emit(self.current_strategy, 'high_performance_streaming', "memory_critical")
    
    def _calculate_selection_confidence(self, violation_count: int, system_caps: Dict) -> float:
        """计算策略选择的置信度
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        confidence = 0.8  # 基础置信度
        
        # 根据违例数量调整置信度
        if violation_count < 1000:
            confidence += 0.1  # 小数据集置信度高
        elif violation_count > 50000:
            confidence -= 0.1  # 超大数据集置信度稍低
        
        # 根据系统能力调整置信度
        if system_caps.get('can_handle_large_datasets', False):
            confidence += 0.1
        else:
            confidence -= 0.05
        
        return min(1.0, max(0.0, confidence))
    
    def _get_alternative_strategies(self, violation_count: int) -> List[str]:
        """获取备选策略
        
        Args:
            violation_count: 违例数量
            
        Returns:
            List[str]: 备选策略列表
        """
        alternatives = []
        
        for strategy_name, config in self.strategy_configs.items():
            min_violations, max_violations = config['violation_range']
            if min_violations <= violation_count <= max_violations:
                alternatives.append(strategy_name)
        
        return alternatives
    
    def _predict_strategy_performance(self, strategy_name: str, violation_count: int, system_caps: Dict) -> Dict:
        """预测策略性能
        
        Args:
            strategy_name: 策略名称
            violation_count: 违例数量
            system_caps: 系统能力
            
        Returns:
            Dict: 性能预测
        """
        strategy_config = self.strategy_configs.get(strategy_name, {})
        
        # 基础性能预测
        base_time_per_violation = {
            'standard_async': 0.002,
            'high_performance_async': 0.001,
            'high_performance_streaming': 0.0005,
            'enhanced_streaming': 0.0003
        }.get(strategy_name, 0.002)
        
        # 系统能力调整
        performance_multiplier = {
            'high': 0.7,
            'medium': 1.0,
            'low': 1.5
        }.get(system_caps.get('performance_tier', 'medium'), 1.0)
        
        predicted_time = violation_count * base_time_per_violation * performance_multiplier
        
        # 内存使用预测
        memory_per_violation = {
            'standard_async': 0.8,  # KB
            'high_performance_async': 1.2,
            'high_performance_streaming': 0.3,
            'enhanced_streaming': 0.2
        }.get(strategy_name, 1.0)
        
        predicted_memory_mb = violation_count * memory_per_violation / 1024
        
        return {
            'expected_parse_time': predicted_time,
            'expected_memory_usage_mb': predicted_memory_mb,
            'expected_violations_per_sec': violation_count / predicted_time if predicted_time > 0 else 0,
            'memory_efficient': strategy_config.get('memory_efficient', False),
            'processing_speed': strategy_config.get('processing_speed', 'medium'),
            'ui_responsiveness': strategy_config.get('ui_responsiveness', 'medium')
        }
    
    def process_violations_with_enhanced_batching(self, violations: List[Dict], 
                                                processor_func: Callable[[List[Dict]], List[Dict]],
                                                progress_callback: Optional[Callable[[int, str], None]] = None) -> List[Dict]:
        """使用增强批处理处理违例数据
        
        Args:
            violations: 违例数据列表
            processor_func: 处理函数
            progress_callback: 进度回调函数
            
        Returns:
            List[Dict]: 处理后的违例数据
        """
        try:
            # 使用内存感知批处理器处理违例
            processed_violations = self.batch_processor.process_violations_in_batches(
                violations, processor_func, progress_callback
            )
            
            # 获取批处理统计信息
            batch_stats = self.batch_processor.get_processing_statistics()
            self.parsing_stats['batch_processing_stats'] = batch_stats
            
            # 使用批大小优化器优化未来的批处理
            if hasattr(self.batch_processor, 'performance_history') and self.batch_processor.performance_history:
                system_constraints = {
                    'min_batch_size': self.batch_processor.batch_config['min_batch_size'],
                    'max_batch_size': self.batch_processor.batch_config['max_batch_size'],
                    'memory_limit_mb': 500  # 默认内存限制
                }
                
                optimized_batch_size = self.batch_optimizer.optimize_batch_size(
                    self.batch_processor.performance_history,
                    self.batch_processor.current_batch_size,
                    system_constraints
                )
                
                # 更新批处理器的批大小
                if optimized_batch_size != self.batch_processor.current_batch_size:
                    self.batch_processor.current_batch_size = optimized_batch_size
                    print(f"批大小已优化为: {optimized_batch_size}")
            
            return processed_violations
            
        except Exception as e:
            print(f"增强批处理失败: {str(e)}")
            # 回退到简单的批处理
            return self._simple_batch_processing(violations, processor_func, progress_callback)
    
    def _simple_batch_processing(self, violations: List[Dict], 
                                processor_func: Callable[[List[Dict]], List[Dict]],
                                progress_callback: Optional[Callable[[int, str], None]] = None) -> List[Dict]:
        """简单批处理（回退方案）
        
        Args:
            violations: 违例数据列表
            processor_func: 处理函数
            progress_callback: 进度回调函数
            
        Returns:
            List[Dict]: 处理后的违例数据
        """
        batch_size = 1000
        processed_violations = []
        total_batches = (len(violations) + batch_size - 1) // batch_size
        
        for i in range(0, len(violations), batch_size):
            batch = violations[i:i + batch_size]
            processed_batch = processor_func(batch)
            processed_violations.extend(processed_batch)
            
            if progress_callback:
                progress = int((i + batch_size) / len(violations) * 100)
                progress_callback(progress, f"处理批次 {i // batch_size + 1}/{total_batches}")
        
        return processed_violations
    
    def get_parsing_statistics(self) -> Dict:
        """获取解析统计信息
        
        Returns:
            Dict: 统计信息
        """
        stats = self.parsing_stats.copy()
        
        # 添加批处理优化统计
        if hasattr(self, 'batch_optimizer'):
            stats['batch_optimization'] = self.batch_optimizer.get_optimization_summary()
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.parsing_stats = {
            'total_parsings': 0,
            'successful_parsings': 0,
            'strategy_switches': 0,
            'average_performance': {},
            'batch_processing_stats': {}
        }
        
        # 重置批处理统计
        if hasattr(self, 'batch_processor'):
            self.batch_processor.reset_statistics()
        
        # 重置批优化统计
        if hasattr(self, 'batch_optimizer'):
            self.batch_optimizer.optimization_history.clear()
            self.batch_optimizer.optimization_attempts = 0