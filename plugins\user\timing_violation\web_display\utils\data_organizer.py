"""
数据组织工具

提供高效的数据组织和分组功能，专门针对时序违例数据的corner和case_name分组优化。
"""

import logging
from typing import Dict, List, Any, Tuple, Set, Optional
from collections import defaultdict, OrderedDict
import json


class ViolationDataOrganizer:
    """时序违例数据组织器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.corner_case_index = {}
        self.violation_index = {}
        self.statistics = {}
    
    def organize_violations_by_corner_case(self, violations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        按corner和case组织违例数据
        
        Args:
            violations: 违例数据列表
            
        Returns:
            Dict: 组织后的数据结构
        """
        try:
            self.logger.info(f"开始组织 {len(violations)} 条违例数据")
            
            # 创建分组结构
            organized_data = {
                'corners': {},
                'cases': {},
                'corner_case_mapping': {},
                'statistics': {},
                'metadata': {
                    'total_violations': len(violations),
                    'organization_method': 'corner_case_grouping'
                }
            }
            
            # 按corner和case分组
            corner_groups = defaultdict(lambda: defaultdict(list))
            corner_set = set()
            case_set = set()
            
            for violation in violations:
                corner = self._normalize_corner_name(violation.get('corner', ''))
                case = self._normalize_case_name(violation.get('case', ''))
                
                corner_groups[corner][case].append(violation)
                corner_set.add(corner)
                case_set.add(case)
            
            # 构建corner数据
            for corner in sorted(corner_set):
                corner_data = {
                    'name': corner,
                    'cases': {},
                    'total_violations': 0,
                    'confirmed_violations': 0
                }
                
                for case in sorted(corner_groups[corner].keys()):
                    case_violations = corner_groups[corner][case]
                    confirmed_count = len([v for v in case_violations if v.get('status', '').lower() == 'confirmed'])
                    
                    corner_data['cases'][case] = {
                        'name': case,
                        'violations': case_violations,
                        'total_violations': len(case_violations),
                        'confirmed_violations': confirmed_count
                    }
                    
                    corner_data['total_violations'] += len(case_violations)
                    corner_data['confirmed_violations'] += confirmed_count
                
                organized_data['corners'][corner] = corner_data
            
            # 构建case数据（跨corner的case统计）
            case_groups = defaultdict(list)
            for violation in violations:
                case = self._normalize_case_name(violation.get('case', ''))
                case_groups[case].append(violation)
            
            for case in sorted(case_set):
                case_violations = case_groups[case]
                confirmed_count = len([v for v in case_violations if v.get('status', '').lower() == 'confirmed'])
                
                organized_data['cases'][case] = {
                    'name': case,
                    'total_violations': len(case_violations),
                    'confirmed_violations': confirmed_count,
                    'corners': list(set(v.get('corner', '') for v in case_violations))
                }
            
            # 构建corner-case映射
            for corner in organized_data['corners']:
                organized_data['corner_case_mapping'][corner] = list(organized_data['corners'][corner]['cases'].keys())
            
            # 计算统计信息
            organized_data['statistics'] = self._calculate_organization_statistics(organized_data)
            
            self.logger.info(f"数据组织完成: {len(corner_set)} corners, {len(case_set)} cases")
            return organized_data
            
        except Exception as e:
            self.logger.error(f"数据组织失败: {e}")
            raise
    
    def create_efficient_lookup_index(self, organized_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建高效的查找索引
        
        Args:
            organized_data: 组织后的数据
            
        Returns:
            Dict: 查找索引
        """
        try:
            index = {
                'corner_index': {},
                'case_index': {},
                'corner_case_index': {},
                'violation_id_index': {},
                'status_index': defaultdict(list),
                'confirmer_index': defaultdict(list)
            }
            
            # 构建各种索引
            for corner_name, corner_data in organized_data['corners'].items():
                index['corner_index'][corner_name] = {
                    'total_violations': corner_data['total_violations'],
                    'confirmed_violations': corner_data['confirmed_violations'],
                    'cases': list(corner_data['cases'].keys())
                }
                
                for case_name, case_data in corner_data['cases'].items():
                    key = f"{corner_name}_{case_name}"
                    index['corner_case_index'][key] = {
                        'corner': corner_name,
                        'case': case_name,
                        'violation_count': case_data['total_violations'],
                        'confirmed_count': case_data['confirmed_violations']
                    }
                    
                    # 为每个违例创建ID索引
                    for i, violation in enumerate(case_data['violations']):
                        violation_id = f"{corner_name}_{case_name}_{i}"
                        index['violation_id_index'][violation_id] = violation
                        
                        # 状态索引
                        status = violation.get('status', '').lower()
                        index['status_index'][status].append(violation_id)
                        
                        # 确认人索引
                        confirmer = violation.get('confirmer', '').strip()
                        if confirmer:
                            index['confirmer_index'][confirmer].append(violation_id)
            
            # 构建case索引
            for case_name, case_data in organized_data['cases'].items():
                index['case_index'][case_name] = {
                    'total_violations': case_data['total_violations'],
                    'confirmed_violations': case_data['confirmed_violations'],
                    'corners': case_data['corners']
                }
            
            self.logger.info(f"查找索引创建完成: {len(index['violation_id_index'])} violations indexed")
            return index
            
        except Exception as e:
            self.logger.error(f"创建查找索引失败: {e}")
            raise
    
    def filter_violations_by_criteria(self, organized_data: Dict[str, Any], 
                                    corner: str = 'all', case: str = 'all',
                                    status: str = 'all') -> List[Dict[str, Any]]:
        """
        根据条件过滤违例数据
        
        Args:
            organized_data: 组织后的数据
            corner: corner过滤条件
            case: case过滤条件
            status: 状态过滤条件
            
        Returns:
            List: 过滤后的违例列表
        """
        try:
            filtered_violations = []
            
            # 确定要处理的corner范围
            if corner == 'all':
                corners_to_process = organized_data['corners'].keys()
            else:
                corners_to_process = [corner] if corner in organized_data['corners'] else []
            
            for corner_name in corners_to_process:
                corner_data = organized_data['corners'][corner_name]
                
                # 确定要处理的case范围
                if case == 'all':
                    cases_to_process = corner_data['cases'].keys()
                else:
                    cases_to_process = [case] if case in corner_data['cases'] else []
                
                for case_name in cases_to_process:
                    case_violations = corner_data['cases'][case_name]['violations']
                    
                    # 应用状态过滤
                    if status == 'all':
                        filtered_violations.extend(case_violations)
                    else:
                        status_filtered = [v for v in case_violations 
                                         if v.get('status', '').lower() == status.lower()]
                        filtered_violations.extend(status_filtered)
            
            self.logger.debug(f"过滤结果: {len(filtered_violations)} violations (corner={corner}, case={case}, status={status})")
            return filtered_violations
            
        except Exception as e:
            self.logger.error(f"过滤违例数据失败: {e}")
            return []
    
    def get_corner_case_summary(self, organized_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取corner-case汇总信息
        
        Args:
            organized_data: 组织后的数据
            
        Returns:
            Dict: 汇总信息
        """
        try:
            summary = {
                'corners': [],
                'cases': [],
                'corner_case_matrix': {},
                'totals': {
                    'total_corners': len(organized_data['corners']),
                    'total_cases': len(organized_data['cases']),
                    'total_violations': organized_data['metadata']['total_violations'],
                    'total_confirmed': 0
                }
            }
            
            # Corner汇总
            for corner_name, corner_data in organized_data['corners'].items():
                corner_summary = {
                    'name': corner_name,
                    'total_violations': corner_data['total_violations'],
                    'confirmed_violations': corner_data['confirmed_violations'],
                    'case_count': len(corner_data['cases']),
                    'confirmation_rate': (corner_data['confirmed_violations'] / corner_data['total_violations'] * 100) 
                                       if corner_data['total_violations'] > 0 else 0
                }
                summary['corners'].append(corner_summary)
                summary['totals']['total_confirmed'] += corner_data['confirmed_violations']
            
            # Case汇总
            for case_name, case_data in organized_data['cases'].items():
                case_summary = {
                    'name': case_name,
                    'total_violations': case_data['total_violations'],
                    'confirmed_violations': case_data['confirmed_violations'],
                    'corner_count': len(case_data['corners']),
                    'confirmation_rate': (case_data['confirmed_violations'] / case_data['total_violations'] * 100)
                                       if case_data['total_violations'] > 0 else 0
                }
                summary['cases'].append(case_summary)
            
            # Corner-Case矩阵
            for corner_name, corner_data in organized_data['corners'].items():
                summary['corner_case_matrix'][corner_name] = {}
                for case_name, case_data in corner_data['cases'].items():
                    summary['corner_case_matrix'][corner_name][case_name] = {
                        'total': case_data['total_violations'],
                        'confirmed': case_data['confirmed_violations']
                    }
            
            # 计算总体确认率
            if summary['totals']['total_violations'] > 0:
                summary['totals']['confirmation_rate'] = (
                    summary['totals']['total_confirmed'] / summary['totals']['total_violations'] * 100
                )
            else:
                summary['totals']['confirmation_rate'] = 0
            
            return summary
            
        except Exception as e:
            self.logger.error(f"获取汇总信息失败: {e}")
            return {}
    
    def export_organized_data_for_web(self, organized_data: Dict[str, Any], 
                                    output_dir: str) -> bool:
        """
        导出组织后的数据供网页使用
        
        Args:
            organized_data: 组织后的数据
            output_dir: 输出目录
            
        Returns:
            bool: 成功返回True
        """
        try:
            from pathlib import Path
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 导出corner列表
            corners_data = {
                'corners': [
                    {
                        'name': corner_name,
                        'total_violations': corner_data['total_violations'],
                        'confirmed_violations': corner_data['confirmed_violations'],
                        'cases': list(corner_data['cases'].keys())
                    }
                    for corner_name, corner_data in organized_data['corners'].items()
                ]
            }
            
            with open(output_path / 'corners_organized.json', 'w', encoding='utf-8') as f:
                json.dump(corners_data, f, ensure_ascii=False, indent=2)
            
            # 导出case列表
            cases_data = {
                'cases': [
                    {
                        'name': case_name,
                        'total_violations': case_data['total_violations'],
                        'confirmed_violations': case_data['confirmed_violations'],
                        'corners': case_data['corners']
                    }
                    for case_name, case_data in organized_data['cases'].items()
                ]
            }
            
            with open(output_path / 'cases_organized.json', 'w', encoding='utf-8') as f:
                json.dump(cases_data, f, ensure_ascii=False, indent=2)
            
            # 导出corner-case映射
            with open(output_path / 'corner_case_mapping.json', 'w', encoding='utf-8') as f:
                json.dump(organized_data['corner_case_mapping'], f, ensure_ascii=False, indent=2)
            
            # 导出汇总信息
            summary = self.get_corner_case_summary(organized_data)
            with open(output_path / 'organization_summary.json', 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"组织后的数据已导出到: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出组织数据失败: {e}")
            return False
    
    def _normalize_corner_name(self, corner: str) -> str:
        """标准化corner名称"""
        if not corner or corner.strip().lower() in ['', 'none', 'null', 'unknown']:
            return 'unknown'
        return corner.strip()
    
    def _normalize_case_name(self, case: str) -> str:
        """标准化case名称"""
        if not case or case.strip().lower() in ['', 'none', 'null', 'unknown']:
            return 'unknown'
        return case.strip()
    
    def _calculate_organization_statistics(self, organized_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算组织统计信息"""
        try:
            stats = {
                'total_corners': len(organized_data['corners']),
                'total_cases': len(organized_data['cases']),
                'total_violations': organized_data['metadata']['total_violations'],
                'total_confirmed': 0,
                'corner_distribution': {},
                'case_distribution': {},
                'confirmation_rates': {}
            }
            
            # 计算corner分布
            for corner_name, corner_data in organized_data['corners'].items():
                stats['corner_distribution'][corner_name] = corner_data['total_violations']
                stats['total_confirmed'] += corner_data['confirmed_violations']
                
                if corner_data['total_violations'] > 0:
                    stats['confirmation_rates'][corner_name] = (
                        corner_data['confirmed_violations'] / corner_data['total_violations'] * 100
                    )
            
            # 计算case分布
            for case_name, case_data in organized_data['cases'].items():
                stats['case_distribution'][case_name] = case_data['total_violations']
            
            # 计算总体确认率
            if stats['total_violations'] > 0:
                stats['overall_confirmation_rate'] = stats['total_confirmed'] / stats['total_violations'] * 100
            else:
                stats['overall_confirmation_rate'] = 0
            
            return stats
            
        except Exception as e:
            self.logger.error(f"计算统计信息失败: {e}")
            return {}


def organize_violations_for_web_display(violations: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    为网页显示组织违例数据的便捷函数
    
    Args:
        violations: 违例数据列表
        
    Returns:
        Dict: 组织后的数据
    """
    organizer = ViolationDataOrganizer()
    return organizer.organize_violations_by_corner_case(violations)