# 时序违例网页显示用户指南

## 概述

时序违例网页显示功能为用户提供了一个现代化的Web界面来查看和管理时序违例数据。通过这个功能，您可以：

- 在浏览器中查看所有时序违例记录
- 使用高级过滤和搜索功能
- 查看实时统计信息
- 享受响应式设计，支持各种设备

## 使用方法

### 方法1：通过GUI界面（推荐）

1. **打开时序违例插件**
   - 启动您的EDA工具
   - 打开时序违例确认插件

2. **加载数据**
   - 点击"选择文件"按钮
   - 选择您的 `vio_summary.log` 文件
   - 等待数据加载完成

3. **生成并打开网页**
   - 在工具栏中找到"网页显示"按钮
   - 点击该按钮
   - 系统会自动生成网页数据并在浏览器中打开

### 方法2：使用命令行脚本

如果您想独立生成网页数据，可以使用命令行脚本：

```bash
# 基本用法
python generate_web_data.py

# 指定VIOLATION_CHECK目录
python generate_web_data.py --violation-check-dir /path/to/VIOLATION_CHECK

# 启用详细日志
python generate_web_data.py --verbose

# 验证生成的数据
python generate_web_data.py --validate

# 显示详细信息
python generate_web_data.py --info

# 强制重新生成
python generate_web_data.py --force
```

### 方法3：手动打开网页

如果网页数据已经生成，您可以直接打开：

1. 导航到 `VIOLATION_CHECK/web_display/` 目录
2. 双击 `index.html` 文件
3. 或者使用本地服务器：
   ```bash
   cd VIOLATION_CHECK/web_display
   python -m http.server 8000
   ```
   然后在浏览器中访问 `http://localhost:8000`

## 网页功能介绍

### 1. 统计面板
- **总违例数**: 显示所有违例的总数
- **已确认**: 显示已确认的违例数量
- **待确认**: 显示待确认的违例数量
- **确认率**: 显示确认完成的百分比

### 2. 过滤控制
- **Corner过滤**: 按corner名称过滤违例
- **Case过滤**: 按case名称过滤违例（会根据选择的corner自动更新）
- **状态过滤**: 按确认状态过滤（全部/已确认/待确认）
- **清除过滤器**: 重置所有过滤条件
- **刷新数据**: 重新加载数据

### 3. 数据表格
- **排序**: 点击列标题进行排序
- **搜索**: 使用搜索框查找特定内容
- **分页**: 支持大数据集的分页显示
- **响应式**: 自动适配不同屏幕尺寸

### 4. 数据列说明
- **NUM**: 违例编号
- **Hier**: 层级路径
- **Time (ns)**: 时间（纳秒）
- **Check Info**: 检查信息
- **Status**: 确认状态（带颜色标识）
- **Confirmer**: 确认人
- **Result**: 确认结果
- **Reason**: 确认原因
- **Confirmed At**: 确认时间
- **Corner**: Corner标识
- **Case**: Case标识

## 数据更新

### 自动更新
当您在GUI中进行以下操作后，建议重新生成网页数据：
- 确认新的违例
- 修改确认信息
- 加载新的日志文件

### 手动更新
1. 在GUI中点击"网页显示"按钮重新生成
2. 或使用命令行：`python generate_web_data.py --force`

## 性能优化

### 大数据集处理
- 系统自动检测数据集大小并应用相应优化
- 超过10,000条记录时启用分页和虚拟滚动
- 智能缓存机制减少加载时间

### 浏览器兼容性
- 支持现代浏览器（Chrome 90+, Firefox 88+, Safari 14+）
- 移动设备友好的响应式设计
- 优雅降级，确保基本功能在所有环境下可用

## 故障排除

### 常见问题

#### 1. 网页显示按钮不可用
**原因**: 没有加载数据或模块缺失
**解决方案**: 
- 确保已选择并加载了vio_summary.log文件
- 检查web_display模块是否正确安装

#### 2. 浏览器没有自动打开
**原因**: 系统浏览器配置问题
**解决方案**: 
- 手动打开 `VIOLATION_CHECK/web_display/index.html`
- 或使用本地服务器方式

#### 3. 网页显示"无数据"
**原因**: 数据导出失败或数据文件损坏
**解决方案**: 
- 重新生成数据：`python generate_web_data.py --force`
- 检查VIOLATION_CHECK目录权限
- 查看日志文件了解详细错误

#### 4. 网页加载缓慢
**原因**: 数据量过大或网络问题
**解决方案**: 
- 使用过滤器减少显示的数据量
- 使用本地服务器而不是直接打开文件
- 清除浏览器缓存

### 日志文件
- GUI操作日志：查看插件的控制台输出
- 命令行日志：`web_data_generation.log`
- 浏览器日志：按F12打开开发者工具查看

## 高级功能

### 1. 数据导出
网页界面支持：
- 复制表格数据
- 打印报告
- 保存过滤后的视图

### 2. 自定义样式
您可以修改 `VIOLATION_CHECK/web_display/css/custom.css` 来自定义界面样式。

### 3. 数据API
生成的JSON数据文件可以被其他工具使用：
- `data/index.json`: 元数据和统计信息
- `data/violations/*.json`: 违例数据文件
- `data/corners/*.json`: Corner相关数据

## 技术支持

如果您遇到问题：

1. **检查日志**: 查看相关日志文件了解详细错误信息
2. **验证数据**: 使用 `--validate` 参数检查数据完整性
3. **重新生成**: 使用 `--force` 参数强制重新生成数据
4. **联系支持**: 提供错误日志和系统信息

## 更新说明

### 版本特性
- **v1.0**: 基础网页显示功能
- 支持多数据源（Excel文件和数据库）
- 响应式设计和性能优化
- 完整的过滤和搜索功能

### 未来计划
- 实时数据同步
- 更多图表和可视化
- 批量操作支持
- 多语言界面

---

*本指南涵盖了时序违例网页显示功能的所有主要用法。如有疑问，请参考相关技术文档或联系技术支持。*