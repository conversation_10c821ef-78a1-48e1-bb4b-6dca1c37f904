"""
综合性能监控系统

集成违例处理性能监控、报告系统和优化建议引擎，提供完整的性能管理解决方案。
"""

import time
from typing import Dict, List, Optional, Any
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QProgressBar, QTextEdit

try:
    from .violation_performance_monitor import ViolationPerformanceMonitor, ViolationProcessingMetrics
    from .performance_reporting_system import PerformanceReportingSystem, PerformanceReport
    from .optimization_suggestion_engine import OptimizationSuggestionEngine, OptimizationPlan
except ImportError:
    # Fallback for direct execution
    from violation_performance_monitor import ViolationPerformanceMonitor, ViolationProcessingMetrics
    from performance_reporting_system import PerformanceReportingSystem, PerformanceReport
    from optimization_suggestion_engine import OptimizationSuggestionEngine, OptimizationPlan


class PerformanceStatusWidget(QWidget):
    """性能状态显示控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title_label = QLabel("性能监控状态")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 性能指标显示
        metrics_layout = QHBoxLayout()
        
        self.violations_per_second_label = QLabel("违例处理速度: -- violations/s")
        self.ui_response_label = QLabel("UI响应时间: -- ms")
        self.memory_usage_label = QLabel("内存使用: -- MB")
        
        metrics_layout.addWidget(self.violations_per_second_label)
        metrics_layout.addWidget(self.ui_response_label)
        metrics_layout.addWidget(self.memory_usage_label)
        
        layout.addLayout(metrics_layout)
        
        # 性能评分
        self.performance_score_label = QLabel("性能评分: --/100")
        self.performance_score_label.setStyleSheet("font-weight: bold; font-size: 12px; margin: 10px 0;")
        layout.addWidget(self.performance_score_label)
        
        # 优化建议按钮
        self.optimization_button = QPushButton("查看优化建议")
        self.optimization_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        layout.addWidget(self.optimization_button)
        
    def update_metrics(self, metrics: Dict):
        """更新性能指标显示"""
        violations_per_second = metrics.get('violations_per_second', 0)
        ui_response_time = metrics.get('ui_response_time_ms', 0)
        memory_usage = metrics.get('memory_usage_mb', 0)
        performance_score = metrics.get('performance_score', 0)
        
        self.violations_per_second_label.setText(f"违例处理速度: {violations_per_second:.0f} violations/s")
        self.ui_response_label.setText(f"UI响应时间: {ui_response_time:.0f} ms")
        self.memory_usage_label.setText(f"内存使用: {memory_usage:.0f} MB")
        self.performance_score_label.setText(f"性能评分: {performance_score:.0f}/100")
        
        # 根据性能评分设置颜色
        if performance_score >= 80:
            color = "#4CAF50"  # 绿色
        elif performance_score >= 60:
            color = "#FF9800"  # 橙色
        else:
            color = "#F44336"  # 红色
            
        self.performance_score_label.setStyleSheet(f"font-weight: bold; font-size: 12px; margin: 10px 0; color: {color};")


class ComprehensivePerformanceSystem(QObject):
    """综合性能监控系统"""
    
    # 信号定义
    performance_status_updated = pyqtSignal(dict)  # 性能状态更新
    optimization_recommended = pyqtSignal(dict)    # 推荐优化
    performance_alert = pyqtSignal(str, dict)      # 性能警报
    system_ready = pyqtSignal()                    # 系统就绪
    
    def __init__(self):
        super().__init__()
        
        # 初始化子系统
        self.performance_monitor = ViolationPerformanceMonitor()
        self.reporting_system = PerformanceReportingSystem()
        self.optimization_engine = OptimizationSuggestionEngine()
        
        # 系统状态
        self.system_active = False
        self.current_session_id = None
        self.current_violation_count = 0
        self.last_performance_metrics = {}
        
        # 性能状态控件
        self.status_widget = None
        
        # 自动优化配置
        self.auto_optimization_enabled = True
        self.performance_threshold_for_optimization = 60.0
        
        # 连接信号
        self._connect_signals()
        
        # 状态更新定时器
        self.status_update_timer = QTimer()
        self.status_update_timer.timeout.connect(self._update_performance_status)
        self.status_update_timer.start(2000)  # 每2秒更新一次状态
        
        print("Comprehensive Performance System initialized")
        
    def _connect_signals(self):
        """连接子系统信号"""
        # 性能监控器信号
        self.performance_monitor.performance_metrics_updated.connect(self._on_metrics_updated)
        self.performance_monitor.performance_warning.connect(self._on_performance_warning)
        self.performance_monitor.baseline_established.connect(self._on_baseline_established)
        
        # 报告系统信号
        self.reporting_system.report_generated.connect(self._on_report_generated)
        self.reporting_system.trend_analysis_updated.connect(self._on_trend_analysis_updated)
        
        # 优化引擎信号
        self.optimization_engine.suggestions_generated.connect(self._on_suggestions_generated)
        self.optimization_engine.optimization_applied.connect(self._on_optimization_applied)
        self.optimization_engine.optimization_plan_completed.connect(self._on_optimization_completed)
        
        # 设置子系统之间的引用
        self.optimization_engine.set_performance_monitor(self.performance_monitor)
        self.optimization_engine.set_reporting_system(self.reporting_system)
        
    def start_performance_monitoring(self, operation_type: str, violation_count: int,
                                   processing_stage: str = 'unknown') -> str:
        """开始性能监控会话
        
        Args:
            operation_type: 操作类型
            violation_count: 违例数量
            processing_stage: 处理阶段
            
        Returns:
            str: 会话ID
        """
        self.system_active = True
        self.current_violation_count = violation_count
        
        # 开始性能监控
        session_id = self.performance_monitor.start_monitoring_session(
            operation_type, violation_count, processing_stage
        )
        self.current_session_id = session_id
        
        # 建立性能基线
        self.performance_monitor.establish_performance_baseline(violation_count)
        
        print(f"Started performance monitoring session: {session_id}")
        return session_id
        
    def stop_performance_monitoring(self) -> Dict:
        """停止性能监控会话并生成报告
        
        Returns:
            Dict: 会话摘要和建议
        """
        if not self.system_active or not self.current_session_id:
            return {}
        
        # 停止性能监控
        session_summary = self.performance_monitor.stop_monitoring_session()
        
        # 获取性能指标历史
        performance_metrics = self.performance_monitor.get_performance_history(100)
        
        # 生成性能报告
        performance_report = self.reporting_system.generate_session_report(
            session_summary, 
            [ViolationProcessingMetrics(**m) for m in performance_metrics]
        )
        
        # 分析并生成优化建议
        optimization_plan = None
        if session_summary.get('avg_performance_score', 100) < self.performance_threshold_for_optimization:
            optimization_plan = self.optimization_engine.analyze_and_suggest(
                self.current_violation_count,
                {
                    'overall_score': session_summary.get('avg_performance_score', 0),
                    'ui_response_time_ms': session_summary.get('avg_ui_response_time_ms', 0),
                    'memory_usage_mb': session_summary.get('peak_memory_usage_mb', 0),
                    'violations_per_second': session_summary.get('avg_violations_per_second', 0),
                    'load_time_seconds': session_summary.get('duration_seconds', 0)
                }
            )
        
        # 重置状态
        self.system_active = False
        self.current_session_id = None
        self.current_violation_count = 0
        
        result = {
            'session_summary': session_summary,
            'performance_report': performance_report,
            'optimization_plan': optimization_plan
        }
        
        print(f"Performance monitoring session completed")
        return result
        
    def get_performance_status_widget(self) -> PerformanceStatusWidget:
        """获取性能状态显示控件"""
        if self.status_widget is None:
            self.status_widget = PerformanceStatusWidget()
            self.status_widget.optimization_button.clicked.connect(self._show_optimization_suggestions)
        
        return self.status_widget
        
    def track_ui_interaction(self, operation: str, response_time_ms: float):
        """跟踪UI交互性能
        
        Args:
            operation: 操作类型
            response_time_ms: 响应时间（毫秒）
        """
        if self.system_active:
            self.performance_monitor.track_ui_response_time(operation, response_time_ms)
            
    def track_violation_processing(self, processed_violations: int, elapsed_time: float):
        """跟踪违例处理性能
        
        Args:
            processed_violations: 已处理的违例数量
            elapsed_time: 经过的时间（秒）
        """
        if self.system_active:
            self.performance_monitor.track_violations_per_second(processed_violations, elapsed_time)
            
    def generate_performance_report(self, report_type: str = 'summary', **kwargs) -> Dict:
        """生成性能报告
        
        Args:
            report_type: 报告类型 ('summary', 'trend', 'comparison')
            **kwargs: 其他参数
            
        Returns:
            Dict: 性能报告
        """
        if report_type == 'summary':
            return self.reporting_system.get_performance_summary(
                kwargs.get('violation_count')
            )
        elif report_type == 'trend':
            return self.reporting_system.generate_trend_analysis_report(
                kwargs.get('days', 7)
            )
        elif report_type == 'comparison':
            return self.reporting_system.generate_strategy_comparison_report(
                kwargs.get('strategies', []),
                kwargs.get('violation_count_range', (0, float('inf')))
            )
        else:
            return {'error': f'Unknown report type: {report_type}'}
            
    def apply_optimization_suggestions(self, plan_id: str = None, 
                                     selected_suggestions: List[str] = None) -> bool:
        """应用优化建议
        
        Args:
            plan_id: 优化计划ID，None表示使用当前计划
            selected_suggestions: 选中的建议ID列表
            
        Returns:
            bool: 是否成功开始执行
        """
        plan = self.optimization_engine.current_optimization_plan
        if plan_id:
            # 这里应该从历史记录中查找指定的计划
            # 暂时使用当前计划
            pass
        
        if not plan:
            print("No optimization plan available")
            return False
        
        return self.optimization_engine.apply_optimization_plan(
            plan, selected_suggestions, require_confirmation=True
        )
        
    def get_optimization_suggestions(self, violation_count: int = None) -> List[Dict]:
        """获取优化建议
        
        Args:
            violation_count: 违例数量，None表示使用当前违例数量
            
        Returns:
            List[Dict]: 优化建议列表
        """
        if violation_count is None:
            violation_count = self.current_violation_count
        
        if violation_count == 0:
            return []
        
        suggestions = self.optimization_engine.get_suggestion_for_violation_count(violation_count)
        return [
            {
                'id': s.suggestion_id,
                'title': s.title,
                'description': s.description,
                'priority': s.priority,
                'expected_improvement': s.expected_improvement,
                'complexity': s.implementation_complexity,
                'risk_level': s.risk_level
            }
            for s in suggestions
        ]
        
    def export_performance_data(self, format: str = 'json') -> str:
        """导出性能数据
        
        Args:
            format: 导出格式
            
        Returns:
            str: 导出的数据
        """
        return self.reporting_system.export_performance_data(format, include_raw_metrics=True)
        
    def _update_performance_status(self):
        """更新性能状态"""
        if not self.system_active:
            return
        
        # 获取当前性能摘要
        current_summary = self.performance_monitor.get_current_performance_summary()
        
        if current_summary:
            self.last_performance_metrics = current_summary
            
            # 发出状态更新信号
            self.performance_status_updated.emit(current_summary)
            
            # 更新状态控件
            if self.status_widget:
                self.status_widget.update_metrics({
                    'violations_per_second': current_summary.get('avg_violations_per_second', 0),
                    'ui_response_time_ms': current_summary.get('avg_ui_response_time_ms', 0),
                    'memory_usage_mb': current_summary.get('peak_memory_usage_mb', 0),
                    'performance_score': self._calculate_performance_score(current_summary)
                })
                
    def _calculate_performance_score(self, metrics: Dict) -> float:
        """计算综合性能评分"""
        # 基于多个指标计算综合评分
        violations_per_second = metrics.get('avg_violations_per_second', 0)
        ui_response_time = metrics.get('avg_ui_response_time_ms', 0)
        memory_usage = metrics.get('peak_memory_usage_mb', 0)
        
        # 处理速度评分 (0-40分)
        speed_score = min(40, (violations_per_second / 1000) * 40)
        
        # UI响应性评分 (0-30分)
        ui_score = max(0, 30 - (ui_response_time / 100) * 30)
        
        # 内存效率评分 (0-30分)
        memory_score = max(0, 30 - (memory_usage / 1000) * 30)
        
        return speed_score + ui_score + memory_score
        
    def _show_optimization_suggestions(self):
        """显示优化建议"""
        suggestions = self.get_optimization_suggestions()
        if suggestions:
            self.optimization_recommended.emit({
                'suggestions': suggestions,
                'violation_count': self.current_violation_count
            })
        
    def _on_metrics_updated(self, metrics: Dict):
        """处理性能指标更新"""
        # 检查是否需要发出警报
        ui_response_time = metrics.get('ui_response_time_ms', 0)
        memory_usage = metrics.get('memory_usage_mb', 0)
        
        if ui_response_time > 200:
            self.performance_alert.emit('ui_response_critical', metrics)
        elif memory_usage > 1000:
            self.performance_alert.emit('memory_usage_critical', metrics)
            
    def _on_performance_warning(self, warning_type: str, data: Dict):
        """处理性能警告"""
        self.performance_alert.emit(warning_type, data)
        
    def _on_baseline_established(self, category: str, baseline_data: Dict):
        """处理基线建立"""
        print(f"Performance baseline established for {category}: {baseline_data}")
        
    def _on_report_generated(self, report_data: Dict):
        """处理报告生成"""
        print(f"Performance report generated: {report_data.get('report_id', 'unknown')}")
        
    def _on_trend_analysis_updated(self, trend_data: Dict):
        """处理趋势分析更新"""
        print(f"Trend analysis updated: {trend_data.get('report_id', 'unknown')}")
        
    def _on_suggestions_generated(self, suggestion_data: Dict):
        """处理建议生成"""
        print(f"Optimization suggestions generated: {suggestion_data}")
        
        # 如果启用自动优化且性能较差，自动应用低风险建议
        if (self.auto_optimization_enabled and 
            suggestion_data.get('auto_applicable_count', 0) > 0 and
            self.last_performance_metrics.get('performance_score', 100) < 50):
            
            print("Auto-applying low-risk optimizations")
            # 这里可以自动应用低风险的优化建议
            
    def _on_optimization_applied(self, suggestion_id: str, success: bool):
        """处理优化应用结果"""
        status = "成功" if success else "失败"
        print(f"Optimization {suggestion_id} applied: {status}")
        
    def _on_optimization_completed(self, completion_data: Dict):
        """处理优化完成"""
        print(f"Optimization plan completed: {completion_data}")
        
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        return {
            'system_active': self.system_active,
            'current_session_id': self.current_session_id,
            'current_violation_count': self.current_violation_count,
            'auto_optimization_enabled': self.auto_optimization_enabled,
            'last_performance_metrics': self.last_performance_metrics,
            'monitoring_active': self.performance_monitor.monitoring_active if self.performance_monitor else False
        }
        
    def configure_system(self, config: Dict):
        """配置系统参数"""
        if 'auto_optimization_enabled' in config:
            self.auto_optimization_enabled = config['auto_optimization_enabled']
            
        if 'performance_threshold_for_optimization' in config:
            self.performance_threshold_for_optimization = config['performance_threshold_for_optimization']
            
        if 'status_update_interval_ms' in config:
            self.status_update_timer.setInterval(config['status_update_interval_ms'])
            
        print(f"System configured with: {config}")
        
    def reset_system(self):
        """重置系统状态"""
        if self.system_active:
            self.stop_performance_monitoring()
        
        self.performance_monitor = ViolationPerformanceMonitor()
        self.reporting_system = PerformanceReportingSystem()
        self.optimization_engine = OptimizationSuggestionEngine()
        
        self._connect_signals()
        
        print("Performance system has been reset")