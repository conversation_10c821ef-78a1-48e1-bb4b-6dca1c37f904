[{"id": 104, "num": 1, "hier": "tb.pipeline.memory_ctrl.mmu[29].cache_unit[12]", "time_fs": 6073694, "time_display": "6073694 FS", "check_info": "Removal time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 105, "num": 2, "hier": "tb.debug_unit.pcie_if.interrupt_ctrl.branch_pred[5].interrupt_ctrl", "time_fs": 8838296, "time_display": "8838296 FS", "check_info": "Recovery time violation on signal done[4]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 106, "num": 3, "hier": "tb.decode_unit[8].writeback_unit.cache_unit.cache_unit[18].decode_unit.interrupt_ctrl", "time_fs": 5741857, "time_display": "5741857 FS", "check_info": "Skew violation on signal rst_n", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 107, "num": 4, "hier": "tb.debug_unit.debug_unit.memory_ctrl.cpu_top[10].cache_unit[23].interrupt_ctrl", "time_fs": 893095, "time_display": "893095 FS", "check_info": "Recovery time violation on signal wr_en[51]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:22", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 108, "num": 5, "hier": "tb.interrupt_ctrl.debug_unit.decode_unit.debug_unit.pcie_if[13]", "time_fs": 7589943, "time_display": "7589943 FS", "check_info": "Pulse width violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 109, "num": 6, "hier": "tb.pipeline.fetch_unit.pipeline[23].cpu_top.branch_pred", "time_fs": 462150, "time_display": "462150 FS", "check_info": "Period violation on signal status", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:22", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 110, "num": 7, "hier": "tb.pipeline.cpu_top[5].memory_ctrl[26].writeback_unit.tlb", "time_fs": 8156062, "time_display": "8156062 FS", "check_info": "Skew violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 111, "num": 8, "hier": "tb.branch_pred.mmu[24].branch_pred.execute_unit.interrupt_ctrl", "time_fs": 8160570, "time_display": "8160570 FS", "check_info": "Setup time violation on signal data[2]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 112, "num": 9, "hier": "tb.fetch_unit[25].tlb[15].cache_unit[0]", "time_fs": 4434342, "time_display": "4434342 FS", "check_info": "Hold time violation on signal cs", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:22", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 113, "num": 10, "hier": "tb.writeback_unit.branch_pred.decode_unit[21].pipeline", "time_fs": 9917000, "time_display": "9917 PS", "check_info": "Pulse width violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 114, "num": 11, "hier": "tb.cache_unit.writeback_unit.execute_unit", "time_fs": 6402000, "time_display": "6402 PS", "check_info": "Recovery time violation on signal intr[51]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 115, "num": 12, "hier": "tb.cpu_top.tlb.tlb.mmu[26].memory_ctrl[14].branch_pred", "time_fs": 7045831, "time_display": "7045831 FS", "check_info": "Pulse width violation on signal enable[9]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 116, "num": 13, "hier": "tb.pcie_if.memory_ctrl.tlb.fetch_unit.debug_unit[1]", "time_fs": 1470278, "time_display": "1470278 FS", "check_info": "Removal time violation on signal error[60]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:23", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 117, "num": 14, "hier": "tb.interrupt_ctrl[18].debug_unit.interrupt_ctrl.pcie_if[9].mmu", "time_fs": 3827036, "time_display": "3827036 FS", "check_info": "Pulse width violation on signal rd_en", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:23", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 118, "num": 15, "hier": "tb.interrupt_ctrl.interrupt_ctrl.debug_unit[0].pipeline", "time_fs": 5721000, "time_display": "5721 PS", "check_info": "Period violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 119, "num": 16, "hier": "tb.mmu.cache_unit[16].memory_ctrl", "time_fs": 2466051, "time_display": "2466051 FS", "check_info": "Setup time violation on signal ack", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:23", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 120, "num": 17, "hier": "tb.cache_unit.tlb.execute_unit[24].interrupt_ctrl.fetch_unit.decode_unit", "time_fs": 3278704, "time_display": "3278704 FS", "check_info": "Recovery time violation on signal error", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:23", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 121, "num": 18, "hier": "tb.interrupt_ctrl.pipeline.execute_unit.writeback_unit.tlb.debug_unit", "time_fs": 4136000, "time_display": "4136 PS", "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:24", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 122, "num": 19, "hier": "tb.pipeline.debug_unit[15].decode_unit", "time_fs": 5379671, "time_display": "5379671 FS", "check_info": "Recovery time violation on signal busy[11]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 123, "num": 20, "hier": "tb.cpu_top[2].execute_unit.interrupt_ctrl.fetch_unit[8].memory_ctrl.mmu", "time_fs": 3265057, "time_display": "3265057 FS", "check_info": "Setup time violation on signal idle", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:24", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 124, "num": 21, "hier": "tb.mmu.ddr_ctrl[20].mmu.ddr_ctrl.branch_pred.writeback_unit", "time_fs": 8361996, "time_display": "8361996 FS", "check_info": "Recovery time violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 125, "num": 22, "hier": "tb.debug_unit.decode_unit.cache_unit[3].execute_unit[26]", "time_fs": 8493000, "time_display": "8493 PS", "check_info": "Period violation on signal grant", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 126, "num": 23, "hier": "tb.cpu_top.mmu.pcie_if[9].pcie_if.cpu_top.interrupt_ctrl", "time_fs": 1037695, "time_display": "1037695 FS", "check_info": "Removal time violation on signal ack", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:24", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 127, "num": 24, "hier": "tb.pcie_if.branch_pred.decode_unit.pipeline.interrupt_ctrl.branch_pred", "time_fs": 2002711, "time_display": "2002711 FS", "check_info": "Period violation on signal grant", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:24", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 128, "num": 25, "hier": "tb.execute_unit.cache_unit.fetch_unit.writeback_unit.memory_ctrl[2]", "time_fs": 2046921, "time_display": "2046921 FS", "check_info": "Period violation on signal ack", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:25", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 129, "num": 26, "hier": "tb.interrupt_ctrl[5].decode_unit.memory_ctrl.cpu_top[23].writeback_unit.decode_unit", "time_fs": 6541000, "time_display": "6541 PS", "check_info": "Hold time violation on signal valid[58]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 130, "num": 27, "hier": "tb.branch_pred.writeback_unit.pcie_if.decode_unit[7]", "time_fs": 9694065, "time_display": "9694065 FS", "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 131, "num": 28, "hier": "tb.debug_unit[5].debug_unit[7].ddr_ctrl", "time_fs": 2214000, "time_display": "2214 PS", "check_info": "Skew violation on signal enable", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:25", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 132, "num": 29, "hier": "tb.tlb.cache_unit.debug_unit.memory_ctrl", "time_fs": 7341278, "time_display": "7341278 FS", "check_info": "Removal time violation on signal error[45]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 133, "num": 30, "hier": "tb.pipeline.decode_unit.fetch_unit.mmu[24].execute_unit.branch_pred[25]", "time_fs": 9327144, "time_display": "9327144 FS", "check_info": "Recovery time violation on signal grant[32]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 134, "num": 31, "hier": "tb.cpu_top[4].execute_unit[2].mmu.writeback_unit.debug_unit", "time_fs": 8645230, "time_display": "8645230 FS", "check_info": "Pulse width violation on signal data[19]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 135, "num": 32, "hier": "tb.tlb.ddr_ctrl.cache_unit", "time_fs": 6016467, "time_display": "6016467 FS", "check_info": "Setup time violation on signal rst_n[11]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 136, "num": 33, "hier": "tb.interrupt_ctrl.tlb[5].tlb[13].fetch_unit[12].pipeline[12]", "time_fs": 8285000, "time_display": "8.285 NS", "check_info": "Period violation on signal data", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 137, "num": 34, "hier": "tb.fetch_unit.writeback_unit.pipeline.cpu_top[1].mmu[24].pipeline", "time_fs": 5608969, "time_display": "5608969 FS", "check_info": "Hold time violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 138, "num": 35, "hier": "tb.memory_ctrl.cache_unit.pipeline", "time_fs": 8882245, "time_display": "8882245 FS", "check_info": "Removal time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 139, "num": 36, "hier": "tb.fetch_unit.memory_ctrl.fetch_unit.interrupt_ctrl", "time_fs": 1261000, "time_display": "1261 PS", "check_info": "Pulse width violation on signal enable", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:25", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 140, "num": 37, "hier": "tb.fetch_unit[28].mmu.execute_unit[15].writeback_unit.debug_unit.interrupt_ctrl", "time_fs": 9454000, "time_display": "9454 PS", "check_info": "Period violation on signal clk[50]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 141, "num": 38, "hier": "tb.fetch_unit.cache_unit[13].mmu.pipeline[4]", "time_fs": 9652000, "time_display": "9652 PS", "check_info": "Period violation on signal busy", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 142, "num": 39, "hier": "tb.mmu.branch_pred[7].cpu_top[0].branch_pred[20].cache_unit", "time_fs": 3693453, "time_display": "3693453 FS", "check_info": "Skew violation on signal mode[31]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:25", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 143, "num": 40, "hier": "tb.debug_unit[31].cache_unit.memory_ctrl.tlb", "time_fs": 9463669, "time_display": "9463669 FS", "check_info": "Setup time violation on signal rst_n[6]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 144, "num": 41, "hier": "tb.fetch_unit[21].mmu.branch_pred.interrupt_ctrl.debug_unit", "time_fs": 3813917, "time_display": "3813917 FS", "check_info": "Removal time violation on signal valid[19]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:26", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 145, "num": 42, "hier": "tb.fetch_unit.writeback_unit[19].pipeline[6].cpu_top[10].decode_unit.ddr_ctrl", "time_fs": 2978951, "time_display": "2978951 FS", "check_info": "Period violation on signal addr", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:26", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 146, "num": 43, "hier": "tb.cache_unit.pcie_if.fetch_unit.execute_unit.ddr_ctrl", "time_fs": 6754428, "time_display": "6754428 FS", "check_info": "Pulse width violation on signal intr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 147, "num": 44, "hier": "tb.decode_unit.cache_unit.cache_unit.pcie_if.cpu_top[18]", "time_fs": 2993739, "time_display": "2993739 FS", "check_info": "Removal time violation on signal intr[36]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:26", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 148, "num": 45, "hier": "tb.branch_pred.pcie_if.mmu[22].debug_unit", "time_fs": 6441000, "time_display": "6441 PS", "check_info": "Recovery time violation on signal mode[22]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 149, "num": 46, "hier": "tb.pipeline[29].branch_pred.memory_ctrl[13]", "time_fs": 2996531, "time_display": "2996531 FS", "check_info": "Period violation on signal mode", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:26", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 150, "num": 47, "hier": "tb.ddr_ctrl.pcie_if[22].pipeline[18]", "time_fs": 5938838, "time_display": "5938838 FS", "check_info": "Setup time violation on signal addr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 151, "num": 48, "hier": "tb.branch_pred.mmu.interrupt_ctrl.memory_ctrl[29]", "time_fs": 5389000, "time_display": "5389 PS", "check_info": "Removal time violation on signal ready", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 152, "num": 49, "hier": "tb.fetch_unit.branch_pred[11].execute_unit.interrupt_ctrl.pcie_if.pipeline[19]", "time_fs": 729587, "time_display": "729587 FS", "check_info": "Hold time violation on signal error[17]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:26", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 153, "num": 50, "hier": "tb.tlb[13].memory_ctrl.pipeline.mmu.decode_unit.memory_ctrl", "time_fs": 4348884, "time_display": "4348884 FS", "check_info": "Period violation on signal intr[13]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:27", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 154, "num": 51, "hier": "tb.writeback_unit.cpu_top.ddr_ctrl", "time_fs": 963000, "time_display": "963 PS", "check_info": "Period violation on signal rd_en[20]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:27", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 155, "num": 52, "hier": "tb.memory_ctrl[14].writeback_unit[1].memory_ctrl[20]", "time_fs": 2703663, "time_display": "2703663 FS", "check_info": "Skew violation on signal ready[2]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:27", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 156, "num": 53, "hier": "tb.tlb.interrupt_ctrl[6].memory_ctrl[1].memory_ctrl[5]", "time_fs": 4739000, "time_display": "4739 PS", "check_info": "Hold time violation on signal clk[50]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:27", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 157, "num": 54, "hier": "tb.decode_unit.fetch_unit.pipeline.tlb[16].decode_unit", "time_fs": 9397971, "time_display": "9397971 FS", "check_info": "Period violation on signal mode[33]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 158, "num": 55, "hier": "tb.debug_unit.pcie_if.memory_ctrl.cache_unit.execute_unit.pcie_if", "time_fs": 3245386, "time_display": "3245386 FS", "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:27", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 159, "num": 56, "hier": "tb.cpu_top.tlb[28].fetch_unit[17]", "time_fs": 2231000, "time_display": "2231 PS", "check_info": "Period violation on signal clk[3]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:28", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 160, "num": 57, "hier": "tb.tlb[17].ddr_ctrl[2].debug_unit.ddr_ctrl.ddr_ctrl", "time_fs": 6875765, "time_display": "6875765 FS", "check_info": "Removal time violation on signal rd_en[61]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 161, "num": 58, "hier": "tb.mmu.ddr_ctrl.ddr_ctrl", "time_fs": 7256000, "time_display": "7256 PS", "check_info": "Setup time violation on signal rst_n", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 162, "num": 59, "hier": "tb.tlb[20].interrupt_ctrl[15].pcie_if.cache_unit.decode_unit.interrupt_ctrl", "time_fs": 5785000, "time_display": "5.785 NS", "check_info": "Setup time violation on signal intr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 163, "num": 60, "hier": "tb.branch_pred[3].memory_ctrl.decode_unit[7].mmu", "time_fs": 7267576, "time_display": "7267576 FS", "check_info": "Hold time violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 164, "num": 61, "hier": "tb.tlb[12].debug_unit[1].interrupt_ctrl[12].ddr_ctrl", "time_fs": 6544000, "time_display": "6.544 NS", "check_info": "Setup time violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 165, "num": 62, "hier": "tb.interrupt_ctrl.branch_pred.cpu_top.cache_unit.tlb.writeback_unit", "time_fs": 8155818, "time_display": "8155818 FS", "check_info": "Skew violation on signal idle[19]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:28", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 166, "num": 63, "hier": "tb.branch_pred[7].interrupt_ctrl.cpu_top.tlb.writeback_unit.tlb", "time_fs": 1678165, "time_display": "1678165 FS", "check_info": "Period violation on signal grant[43]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:28", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 167, "num": 64, "hier": "tb.tlb[16].mmu.tlb.mmu[12]", "time_fs": 376220, "time_display": "376220 FS", "check_info": "Period violation on signal req", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:28", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 168, "num": 65, "hier": "tb.writeback_unit[27].pcie_if.debug_unit.ddr_ctrl", "time_fs": 4864152, "time_display": "4864152 FS", "check_info": "Hold time violation on signal error[1]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:29", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 169, "num": 66, "hier": "tb.fetch_unit.pcie_if.memory_ctrl.writeback_unit", "time_fs": 1162898, "time_display": "1162898 FS", "check_info": "Setup time violation on signal data", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:29", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 170, "num": 67, "hier": "tb.pipeline[9].branch_pred.tlb.pcie_if.execute_unit", "time_fs": 6777464, "time_display": "6777464 FS", "check_info": "Recovery time violation on signal ack", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:29", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 171, "num": 68, "hier": "tb.ddr_ctrl.cache_unit.cache_unit", "time_fs": 9921402, "time_display": "9921402 FS", "check_info": "Skew violation on signal enable", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:29", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 172, "num": 69, "hier": "tb.fetch_unit.cache_unit.cache_unit.tlb.pipeline.execute_unit", "time_fs": 9795209, "time_display": "9795209 FS", "check_info": "Hold time violation on signal clk", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:29", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 173, "num": 70, "hier": "tb.pipeline.fetch_unit.cpu_top[7].branch_pred.fetch_unit[25]", "time_fs": 9242802, "time_display": "9242802 FS", "check_info": "Period violation on signal addr[58]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:30", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 174, "num": 71, "hier": "tb.mmu.execute_unit.debug_unit", "time_fs": 4371015, "time_display": "4371015 FS", "check_info": "Setup time violation on signal clk[0]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:30", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 175, "num": 72, "hier": "tb.execute_unit.ddr_ctrl.tlb.mmu.cache_unit.branch_pred[17]", "time_fs": 4761932, "time_display": "4761932 FS", "check_info": "Hold time violation on signal addr[23]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:30", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 176, "num": 73, "hier": "tb.branch_pred.pcie_if[22].tlb.cache_unit.tlb[19].execute_unit", "time_fs": 4709000, "time_display": "4709 PS", "check_info": "Pulse width violation on signal data", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:30", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 177, "num": 74, "hier": "tb.pipeline.debug_unit[6].cache_unit.cache_unit.branch_pred[10]", "time_fs": 3125558, "time_display": "3125558 FS", "check_info": "Removal time violation on signal done[52]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:30", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 178, "num": 75, "hier": "tb.branch_pred[24].pipeline.tlb.ddr_ctrl[2].branch_pred", "time_fs": 5228133, "time_display": "5228133 FS", "check_info": "Pulse width violation on signal grant[19]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:31", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 179, "num": 76, "hier": "tb.pipeline[19].pcie_if[4].cache_unit.decode_unit[25].debug_unit[6]", "time_fs": 2730000, "time_display": "2730 PS", "check_info": "Pulse width violation on signal wr_en", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:31", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 180, "num": 77, "hier": "tb.debug_unit.fetch_unit.pipeline[21]", "time_fs": 6835257, "time_display": "6835257 FS", "check_info": "Setup time violation on signal busy[33]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:31", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 181, "num": 78, "hier": "tb.ddr_ctrl.writeback_unit[6].fetch_unit[15].writeback_unit[17]", "time_fs": 9485334, "time_display": "9485334 FS", "check_info": "Pulse width violation on signal wr_en[55]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:31", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 182, "num": 79, "hier": "tb.pipeline.branch_pred.tlb.memory_ctrl", "time_fs": 2135298, "time_display": "2135298 FS", "check_info": "Recovery time violation on signal valid[21]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:31", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 183, "num": 80, "hier": "tb.execute_unit.memory_ctrl[15].debug_unit.pipeline.mmu[13]", "time_fs": 4531255, "time_display": "4531255 FS", "check_info": "Skew violation on signal rst_n[24]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:32", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 184, "num": 81, "hier": "tb.decode_unit[18].pipeline.cache_unit[15]", "time_fs": 7567369, "time_display": "7567369 FS", "check_info": "Period violation on signal busy[47]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:32", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 185, "num": 82, "hier": "tb.interrupt_ctrl.execute_unit[20].ddr_ctrl[27].memory_ctrl.tlb", "time_fs": 5923723, "time_display": "5923723 FS", "check_info": "Period violation on signal error[24]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:32", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 186, "num": 83, "hier": "tb.cpu_top[30].pipeline.pcie_if.mmu.cache_unit[21].pipeline", "time_fs": 2121000, "time_display": "2.121 NS", "check_info": "Period violation on signal wr_en[55]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:32", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 187, "num": 84, "hier": "tb.fetch_unit.writeback_unit.execute_unit.ddr_ctrl.debug_unit[13].cache_unit", "time_fs": 6752000, "time_display": "6752 PS", "check_info": "Pulse width violation on signal rst_n", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:32", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 188, "num": 85, "hier": "tb.interrupt_ctrl.tlb.debug_unit.pipeline.writeback_unit", "time_fs": 493199, "time_display": "493199 FS", "check_info": "Setup time violation on signal ready[11]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:33", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 189, "num": 86, "hier": "tb.interrupt_ctrl[17].mmu.cpu_top[24].tlb.memory_ctrl", "time_fs": 973166, "time_display": "973166 FS", "check_info": "Recovery time violation on signal req", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:33", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 190, "num": 87, "hier": "tb.writeback_unit.cache_unit.branch_pred.ddr_ctrl.branch_pred[5].decode_unit[6]", "time_fs": 5328000, "time_display": "5.328 NS", "check_info": "Recovery time violation on signal mode[26]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:33", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 191, "num": 88, "hier": "tb.execute_unit[14].ddr_ctrl.cache_unit.branch_pred.cache_unit.debug_unit", "time_fs": 3889423, "time_display": "3889423 FS", "check_info": "Hold time violation on signal ack", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:33", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 192, "num": 89, "hier": "tb.debug_unit[13].ddr_ctrl.mmu", "time_fs": 1874000, "time_display": "1874 PS", "check_info": "Removal time violation on signal idle[27]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:34", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 193, "num": 90, "hier": "tb.memory_ctrl.debug_unit.interrupt_ctrl", "time_fs": 7527944, "time_display": "7527944 FS", "check_info": "Period violation on signal data[53]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:34", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 194, "num": 91, "hier": "tb.interrupt_ctrl.mmu.branch_pred[15].cache_unit[16].mmu", "time_fs": 6159095, "time_display": "6159095 FS", "check_info": "Removal time violation on signal wr_en", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:34", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 195, "num": 92, "hier": "tb.ddr_ctrl.cpu_top.fetch_unit.writeback_unit", "time_fs": 7547946, "time_display": "7547946 FS", "check_info": "Skew violation on signal valid[20]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:34", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 196, "num": 93, "hier": "tb.memory_ctrl.fetch_unit.writeback_unit.cache_unit.tlb.ddr_ctrl", "time_fs": 2095826, "time_display": "2095826 FS", "check_info": "Hold time violation on signal idle", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:34", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 197, "num": 94, "hier": "tb.pipeline[22].fetch_unit.decode_unit.execute_unit", "time_fs": 5797086, "time_display": "5797086 FS", "check_info": "Setup time violation on signal error", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:35", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 198, "num": 95, "hier": "tb.debug_unit.writeback_unit.pipeline", "time_fs": 6902551, "time_display": "6902551 FS", "check_info": "Setup time violation on signal grant[2]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:35", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 199, "num": 96, "hier": "tb.decode_unit[20].cpu_top.mmu.debug_unit", "time_fs": 5940000, "time_display": "5940 PS", "check_info": "Pulse width violation on signal intr[24]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:35", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 200, "num": 97, "hier": "tb.tlb.branch_pred.mmu[0].memory_ctrl", "time_fs": 3282533, "time_display": "3282533 FS", "check_info": "Skew violation on signal valid", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:36", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 201, "num": 98, "hier": "tb.tlb[31].decode_unit.cpu_top.cache_unit.debug_unit.writeback_unit", "time_fs": 7714725, "time_display": "7714725 FS", "check_info": "Pulse width violation on signal intr[56]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:36", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 202, "num": 99, "hier": "tb.decode_unit.fetch_unit[13].cache_unit.pipeline.debug_unit", "time_fs": 2945000, "time_display": "2945 PS", "check_info": "Skew violation on signal idle[52]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:36", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 203, "num": 100, "hier": "tb.decode_unit.tlb.debug_unit.branch_pred", "time_fs": 1634876, "time_display": "1634876 FS", "check_info": "Period violation on signal data[30]", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:36", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 204, "num": 101, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[26]", "time_fs": 866002233, "time_display": "866002233 FS", "check_info": "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:7869100 FS, negedge SE:7735500 FS,0.1621 : 262100FS, -0.0241 : -24100 FS )", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 205, "num": 102, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[27]", "time_fs": 866002234, "time_display": "866002234 FS", "check_info": "setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:7869100 FS, negedge SE:7735500 FS,0.1621 : 262100FS, -0.0241 : -24100 FS )", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 16:10:03", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 206, "num": 103, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[28]", "time_fs": 866002234, "time_display": "866002234 FS", "check_info": "setup( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )", "status": "confirmed", "confirmer": "jiadong.he2", "result": "pass", "reason": "test", "confirmed_at": "2025-08-07 16:10:37", "corner": "npg_f2_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}]