#!/usr/bin/env python3
"""
Test script to check which performance components can be imported successfully.
"""

import sys
import os

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_import(module_name, class_name=None):
    """Test importing a module and optionally a class from it."""
    try:
        module = __import__(module_name)
        if class_name:
            getattr(module, class_name)
        print(f"✓ {module_name} imported successfully")
        return True
    except ImportError as e:
        print(f"✗ {module_name} import failed: {e}")
        return False
    except AttributeError as e:
        print(f"✗ {module_name}.{class_name} not found: {e}")
        return False
    except Exception as e:
        print(f"✗ {module_name} failed with error: {e}")
        return False

def main():
    """Test all performance component imports."""
    print("Testing performance component imports...")
    print("=" * 50)
    
    # Test basic components first
    test_import('comprehensive_performance_system', 'ComprehensivePerformanceSystem')
    test_import('adaptive_parser_system', 'AdaptiveParserSystem')
    test_import('smart_ui_renderer', 'SmartUIRenderer')
    test_import('configuration_manager', 'ConfigurationManager')
    test_import('integrated_memory_system', 'IntegratedMemorySystem')
    test_import('enhanced_batch_processor', 'MemoryAwareBatchProcessor')
    test_import('performance_optimizer', 'PerformanceOptimizer')
    test_import('component_interaction_optimizer', 'ComponentInteractionOptimizer')
    test_import('comprehensive_error_handler', 'ComprehensiveErrorHandler')
    
    print("=" * 50)
    print("Import testing completed.")

if __name__ == "__main__":
    main()