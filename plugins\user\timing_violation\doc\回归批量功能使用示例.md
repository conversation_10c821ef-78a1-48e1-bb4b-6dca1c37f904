# 回归批量时序违例抓取功能使用示例

## 功能概述

回归批量时序违例抓取功能允许用户一次性扫描和处理多个回归用例的时序违例日志，大大提高了回归测试中违例分析的效率。

## 使用步骤

### 步骤1：启动回归批量扫描

1. 打开时序违例确认插件主界面
2. 在工具栏中找到"回归批量扫描"按钮
3. 点击按钮打开回归批量扫描对话框

### 步骤2：设置回归目录

1. 在"回归根目录"输入框中输入回归目录路径
   - 默认路径：`./regression`
   - 可以点击"浏览..."按钮选择目录
2. 点击"开始扫描"按钮

### 步骤3：等待扫描完成

- 系统会递归扫描指定目录
- 进度条显示扫描进度
- 扫描完成后显示统计信息

### 步骤4：使用过滤器

在左侧面板使用过滤器缩小文件范围：

1. **子系统过滤器**：选择特定的子系统（如 cpu_sys、memory_sys）
2. **工艺角过滤器**：选择特定的工艺角（如 ss、ff、tt）
3. **用例过滤器**：选择特定的用例名称
4. 点击"应用过滤器"按钮

### 步骤5：选择文件

在文件树中选择需要处理的文件：

1. **层级结构**：子系统 → 工艺角 → 用例 → 种子
2. **选择方式**：
   - 勾选单个文件
   - 勾选整个用例（包含所有种子）
   - 勾选整个工艺角（包含所有用例）
   - 勾选整个子系统（包含所有内容）
3. **批量操作**：
   - 点击"全选"选择所有过滤后的文件
   - 点击"清空选择"取消所有选择

### 步骤6：查看选择统计

在右侧面板查看选择统计信息：

- **总文件数**：扫描发现的文件总数
- **已选择**：当前选中的文件数
- **预估违例数**：基于文件大小估算的违例数量
- **预估处理时间**：估算的处理时间

### 步骤7：确认并开始处理

1. 检查选中文件列表
2. 确认统计信息
3. 点击"确定"按钮开始批量处理

### 步骤8：批量处理

- 系统显示处理进度对话框
- 逐个解析选中的文件
- 显示处理状态和进度

### 步骤9：查看结果

处理完成后：

1. 显示处理结果统计
2. 所有违例合并到主界面
3. 可以进行统一的确认操作

## 目录结构要求

### 标准目录格式

```
./regression/
├── cpu_sys/                    # 子系统目录
│   ├── cpu_test_ss/           # 用例_工艺角目录
│   │   ├── cpu_test_seed1/    # 用例_种子目录
│   │   │   └── log/
│   │   │       └── vio_summary.log
│   │   └── cpu_test_seed2/
│   │       └── log/
│   │           └── vio_summary.log
│   └── cpu_test_ff/
│       └── cpu_test_seed1/
│           └── log/
│               └── vio_summary.log
├── memory_sys/
│   └── mem_test_tt/
│       └── mem_test_seed1/
│           └── log/
│               └── vio_summary.log
└── top/                       # 特殊子系统目录
    └── integration_test_ss/
        └── integration_test_seed1/
            └── log/
                └── vio_summary.log
```

### 命名规则

1. **子系统目录**：
   - 以 `_sys` 结尾（如 `cpu_sys`、`memory_sys`）
   - 或者是 `top` 目录
   - 或者以 `_subsys` 结尾

2. **用例_工艺角目录**：
   - 格式：`<case_name>_<corner_name>`
   - 示例：`cpu_test_ss`、`mem_test_ff`

3. **用例_种子目录**：
   - 格式：`<case_name>_<seed_number>`
   - 示例：`cpu_test_seed1`、`mem_test_seed2`

4. **日志文件**：
   - 固定路径：`log/vio_summary.log`

## 使用技巧

### 1. 高效选择

- 使用过滤器先缩小范围
- 按层级批量选择
- 利用统计信息评估工作量

### 2. 性能优化

- 避免一次选择过多文件（建议<100个）
- 大文件集合分批处理
- 关注内存使用情况

### 3. 错误处理

- 检查目录权限
- 确认文件格式正确
- 注意网络目录的访问速度

## 常见问题

### Q1：扫描不到文件怎么办？

**A1：** 检查以下几点：
- 目录路径是否正确
- 目录结构是否符合要求
- 文件名是否为 `vio_summary.log`
- 是否有访问权限

### Q2：处理速度慢怎么办？

**A2：** 可以尝试：
- 减少同时处理的文件数量
- 检查网络连接（如果是网络目录）
- 关闭其他占用资源的程序

### Q3：部分文件处理失败怎么办？

**A3：** 系统会：
- 记录失败的文件
- 继续处理其他文件
- 在结果中显示失败统计
- 成功的文件仍然可以正常使用

### Q4：如何处理大量违例？

**A4：** 建议：
- 使用分页功能查看违例
- 利用自动确认功能处理复位期间违例
- 使用批量确认功能
- 分批处理不同类型的违例

## 最佳实践

### 1. 目录组织

- 保持标准的目录结构
- 使用清晰的命名规则
- 定期清理无效文件

### 2. 批量处理

- 先小规模测试
- 分类处理不同子系统
- 记录处理结果

### 3. 确认策略

- 制定统一的确认标准
- 利用历史模式功能
- 建立确认模板

通过以上步骤和技巧，您可以高效地使用回归批量时序违例抓取功能，大大提高回归测试中违例分析的效率。
