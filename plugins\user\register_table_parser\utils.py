"""
寄存器表格解析器工具函数

提供各种实用工具函数，包括：
- 数字格式转换
- 地址格式化
- 位操作工具
- 验证函数
"""

import re
from typing import Union, Optional, Tuple
from enum import Enum

try:
    from .models import NumberFormat
except ImportError:
    from models import NumberFormat


class NumberFormatConverter:
    """数字格式转换器"""
    
    @staticmethod
    def to_binary(value: int, width: int = None) -> str:
        """转换为二进制格式
        
        Args:
            value: 整数值
            width: 位宽，如果指定则补零到指定宽度
            
        Returns:
            str: 二进制字符串，带0b前缀
        """
        if width:
            return f"0b{value:0{width}b}"
        else:
            return f"0b{value:b}"
    
    @staticmethod
    def to_decimal(value: int) -> str:
        """转换为十进制格式
        
        Args:
            value: 整数值
            
        Returns:
            str: 十进制字符串
        """
        return str(value)
    
    @staticmethod
    def to_hexadecimal(value: int, width: int = None) -> str:
        """转换为十六进制格式
        
        Args:
            value: 整数值
            width: 位宽，如果指定则补零到指定宽度（以4位为单位）
            
        Returns:
            str: 十六进制字符串，带0x前缀
        """
        if width:
            hex_width = (width + 3) // 4  # 向上取整到4的倍数
            return f"0x{value:0{hex_width}X}"
        else:
            return f"0x{value:X}"
    
    @staticmethod
    def from_string(value_str: str) -> int:
        """从字符串解析整数值
        
        Args:
            value_str: 数值字符串，支持二进制(0b)、十六进制(0x)、十进制
            
        Returns:
            int: 解析得到的整数值
            
        Raises:
            ValueError: 解析失败
        """
        if not value_str:
            return 0
        
        value_str = value_str.strip()
        
        # 二进制格式
        if value_str.lower().startswith('0b'):
            return int(value_str, 2)
        
        # 十六进制格式
        if value_str.lower().startswith('0x'):
            return int(value_str, 16)
        
        # 十进制格式
        return int(value_str, 10)
    
    @staticmethod
    def convert_format(value: int, target_format: NumberFormat, width: int = None) -> str:
        """转换数字格式
        
        Args:
            value: 整数值
            target_format: 目标格式
            width: 位宽
            
        Returns:
            str: 转换后的字符串
        """
        if target_format == NumberFormat.BINARY:
            return NumberFormatConverter.to_binary(value, width)
        elif target_format == NumberFormat.DECIMAL:
            return NumberFormatConverter.to_decimal(value)
        elif target_format == NumberFormat.HEXADECIMAL:
            return NumberFormatConverter.to_hexadecimal(value, width)
        else:
            return str(value)
    
    @staticmethod
    def detect_format(value_str: str) -> NumberFormat:
        """检测数字格式
        
        Args:
            value_str: 数值字符串
            
        Returns:
            NumberFormat: 检测到的格式
        """
        if not value_str:
            return NumberFormat.DECIMAL
        
        value_str = value_str.strip().lower()
        
        if value_str.startswith('0b'):
            return NumberFormat.BINARY
        elif value_str.startswith('0x'):
            return NumberFormat.HEXADECIMAL
        else:
            return NumberFormat.DECIMAL


class AddressFormatter:
    """地址格式化工具"""
    
    @staticmethod
    def normalize_address(address: Union[str, int]) -> str:
        """标准化地址格式
        
        Args:
            address: 地址值（字符串或整数）
            
        Returns:
            str: 标准化的地址字符串（十六进制格式）
        """
        if isinstance(address, int):
            return f"0x{address:04X}"
        
        if isinstance(address, str):
            address = address.strip()
            
            # 如果已经是十六进制格式
            if address.lower().startswith('0x'):
                try:
                    addr_int = int(address, 16)
                    return f"0x{addr_int:04X}"
                except ValueError:
                    return address
            
            # 尝试作为十进制解析
            try:
                addr_int = int(address, 10)
                return f"0x{addr_int:04X}"
            except ValueError:
                return address
        
        return "0x0000"
    
    @staticmethod
    def parse_address(address_str: str) -> int:
        """解析地址字符串为整数
        
        Args:
            address_str: 地址字符串
            
        Returns:
            int: 地址整数值
            
        Raises:
            ValueError: 解析失败
        """
        if not address_str:
            return 0
        
        address_str = address_str.strip()
        
        if address_str.lower().startswith('0x'):
            return int(address_str, 16)
        else:
            return int(address_str, 10)


class BitRangeParser:
    """位范围解析工具"""
    
    @staticmethod
    def parse_bit_range(bit_range: str) -> Tuple[int, int]:
        """解析位范围字符串
        
        Args:
            bit_range: 位范围字符串，如 "31:24" 或 "15"
            
        Returns:
            Tuple[int, int]: (高位, 低位)
            
        Raises:
            ValueError: 解析失败
        """
        if not bit_range:
            raise ValueError("位范围不能为空")
        
        bit_range = bit_range.strip()
        
        if ':' in bit_range:
            # 范围格式，如 "31:24"
            parts = bit_range.split(':')
            if len(parts) != 2:
                raise ValueError(f"位范围格式错误: {bit_range}")
            
            high_bit = int(parts[0].strip())
            low_bit = int(parts[1].strip())
            
            if high_bit < low_bit:
                raise ValueError(f"高位不能小于低位: {bit_range}")
            
            return (high_bit, low_bit)
        else:
            # 单个位，如 "15"
            bit = int(bit_range)
            return (bit, bit)
    
    @staticmethod
    def get_bit_width(bit_range: str) -> int:
        """获取位范围的宽度
        
        Args:
            bit_range: 位范围字符串
            
        Returns:
            int: 位宽
        """
        high_bit, low_bit = BitRangeParser.parse_bit_range(bit_range)
        return high_bit - low_bit + 1
    
    @staticmethod
    def validate_bit_range(bit_range: str, max_width: int = 32) -> bool:
        """验证位范围是否有效
        
        Args:
            bit_range: 位范围字符串
            max_width: 最大位宽
            
        Returns:
            bool: 是否有效
        """
        try:
            high_bit, low_bit = BitRangeParser.parse_bit_range(bit_range)
            return 0 <= low_bit <= high_bit < max_width
        except (ValueError, TypeError):
            return False


class FieldValueCalculator:
    """字段值计算工具"""
    
    @staticmethod
    def extract_field_value(register_value: int, bit_range: str) -> int:
        """从寄存器值中提取字段值
        
        Args:
            register_value: 寄存器值
            bit_range: 字段位范围
            
        Returns:
            int: 字段值
        """
        high_bit, low_bit = BitRangeParser.parse_bit_range(bit_range)
        
        # 创建掩码
        width = high_bit - low_bit + 1
        mask = (1 << width) - 1
        
        # 提取字段值
        field_value = (register_value >> low_bit) & mask
        return field_value
    
    @staticmethod
    def insert_field_value(register_value: int, field_value: int, bit_range: str) -> int:
        """将字段值插入到寄存器值中
        
        Args:
            register_value: 原寄存器值
            field_value: 字段值
            bit_range: 字段位范围
            
        Returns:
            int: 更新后的寄存器值
        """
        high_bit, low_bit = BitRangeParser.parse_bit_range(bit_range)
        
        # 创建掩码
        width = high_bit - low_bit + 1
        mask = (1 << width) - 1
        
        # 确保字段值在范围内
        field_value = field_value & mask
        
        # 清除原字段值
        clear_mask = ~(mask << low_bit)
        register_value = register_value & clear_mask
        
        # 插入新字段值
        register_value = register_value | (field_value << low_bit)
        
        return register_value
    
    @staticmethod
    def validate_field_value(field_value: int, bit_range: str) -> bool:
        """验证字段值是否在有效范围内
        
        Args:
            field_value: 字段值
            bit_range: 字段位范围
            
        Returns:
            bool: 是否有效
        """
        try:
            width = BitRangeParser.get_bit_width(bit_range)
            max_value = (1 << width) - 1
            return 0 <= field_value <= max_value
        except (ValueError, TypeError):
            return False


class ValidationUtils:
    """验证工具"""
    
    @staticmethod
    def is_valid_register_name(name: str) -> bool:
        """验证寄存器名称是否有效
        
        Args:
            name: 寄存器名称
            
        Returns:
            bool: 是否有效
        """
        if not name or not isinstance(name, str):
            return False
        
        name = name.strip()
        if not name:
            return False
        
        # 检查是否包含有效字符（字母、数字、下划线）
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*$'
        return bool(re.match(pattern, name))
    
    @staticmethod
    def is_valid_field_name(name: str) -> bool:
        """验证字段名称是否有效
        
        Args:
            name: 字段名称
            
        Returns:
            bool: 是否有效
        """
        if not name or not isinstance(name, str):
            return False
        
        name = name.strip()
        if not name:
            return False
        
        # 字段名可以包含更多字符
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_\-\[\]]*$'
        return bool(re.match(pattern, name))
    
    @staticmethod
    def is_reserved_field(name: str) -> bool:
        """检查是否为保留字段
        
        Args:
            name: 字段名称
            
        Returns:
            bool: 是否为保留字段
        """
        if not name or not isinstance(name, str):
            return False
        
        return name.lower().strip() == 'reserved'


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小
    
    Args:
        size_bytes: 文件大小（字节）
        
    Returns:
        str: 格式化的文件大小字符串
    """
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / (1024 * 1024):.1f} MB"


def truncate_text(text: str, max_length: int = 50) -> str:
    """截断文本
    
    Args:
        text: 原始文本
        max_length: 最大长度
        
    Returns:
        str: 截断后的文本
    """
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - 3] + "..."