"""
时序违例插件自动配置文件优化器

提供基于用户使用模式和系统性能的自动配置文件优化功能。
"""

import os
import json
import time
import statistics
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

try:
    from .configuration_manager import ConfigurationManager, PerformanceProfile
except ImportError:
    from configuration_manager import ConfigurationManager, PerformanceProfile


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    violation_count: int
    profile_name: str
    load_time: float
    memory_usage_mb: float
    ui_response_time_ms: float
    processing_speed_ratio: float  # 实际速度/预期速度
    error_rate: float
    user_satisfaction: float  # 用户满意度评分 (0-10)
    system_capabilities: Dict[str, Any]


@dataclass
class ProfileRecommendation:
    """配置文件推荐数据类"""
    recommended_profile: str
    confidence_score: float  # 推荐置信度 (0-1)
    reason: str
    expected_improvement: Dict[str, float]  # 预期改进指标
    alternative_profiles: List[str]
    risk_assessment: str  # 风险评估


class ProfileOptimizer(QObject):
    """自动配置文件优化器"""
    
    # 信号定义
    optimization_available = pyqtSignal(str, float)  # 推荐配置文件, 置信度
    profile_learned = pyqtSignal(str, dict)  # 学习到的配置文件, 学习数据
    performance_improved = pyqtSignal(str, dict)  # 性能改进信息
    
    def __init__(self, config_manager: ConfigurationManager, data_dir: str = None):
        super().__init__()
        
        self.config_manager = config_manager
        
        # 数据存储目录
        self.data_dir = data_dir or os.path.join(
            os.path.dirname(__file__), 'optimization_data'
        )
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 数据文件路径
        self.metrics_file = os.path.join(self.data_dir, 'performance_metrics.json')
        self.learning_file = os.path.join(self.data_dir, 'learning_data.json')
        self.recommendations_file = os.path.join(self.data_dir, 'recommendations.json')
        
        # 性能指标历史
        self.performance_history: List[PerformanceMetrics] = []
        
        # 学习数据
        self.learning_data = {
            'user_patterns': {},  # 用户使用模式
            'system_patterns': {},  # 系统性能模式
            'profile_effectiveness': {},  # 配置文件效果
            'optimization_history': [],  # 优化历史
            'user_preferences': {}  # 用户偏好
        }
        
        # 优化参数
        self.optimization_config = {
            'min_samples_for_learning': 10,  # 学习所需的最小样本数
            'confidence_threshold': 0.7,  # 推荐置信度阈值
            'performance_improvement_threshold': 0.15,  # 性能改进阈值
            'learning_rate': 0.1,  # 学习率
            'max_history_size': 1000,  # 最大历史记录数
            'optimization_interval': 300,  # 优化检查间隔（秒）
        }
        
        # 初始化
        self._load_data()
        
        # 定期优化检查定时器
        self.optimization_timer = QTimer()
        self.optimization_timer.timeout.connect(self._periodic_optimization_check)
        self.optimization_timer.start(self.optimization_config['optimization_interval'] * 1000)
    
    def record_performance_metrics(self, violation_count: int, profile_name: str,
                                 load_time: float, memory_usage_mb: float,
                                 ui_response_time_ms: float, processing_speed_ratio: float,
                                 error_rate: float = 0.0, user_satisfaction: float = 5.0,
                                 system_capabilities: Dict = None):
        """记录性能指标
        
        Args:
            violation_count: 违例数量
            profile_name: 使用的配置文件名称
            load_time: 加载时间（秒）
            memory_usage_mb: 内存使用量（MB）
            ui_response_time_ms: UI响应时间（毫秒）
            processing_speed_ratio: 处理速度比率
            error_rate: 错误率
            user_satisfaction: 用户满意度评分
            system_capabilities: 系统能力信息
        """
        try:
            metrics = PerformanceMetrics(
                timestamp=time.time(),
                violation_count=violation_count,
                profile_name=profile_name,
                load_time=load_time,
                memory_usage_mb=memory_usage_mb,
                ui_response_time_ms=ui_response_time_ms,
                processing_speed_ratio=processing_speed_ratio,
                error_rate=error_rate,
                user_satisfaction=user_satisfaction,
                system_capabilities=system_capabilities or {}
            )
            
            # 添加到历史记录
            self.performance_history.append(metrics)
            
            # 限制历史记录大小
            max_size = self.optimization_config['max_history_size']
            if len(self.performance_history) > max_size:
                self.performance_history = self.performance_history[-max_size:]
            
            # 更新学习数据
            self._update_learning_data(metrics)
            
            # 保存数据
            self._save_data()
            
            # 检查是否需要优化
            self._check_optimization_opportunity(metrics)
            
            print(f"性能指标已记录: {profile_name}, 违例数: {violation_count:,}, 加载时间: {load_time:.2f}s")
            
        except Exception as e:
            print(f"记录性能指标失败: {str(e)}")
    
    def _update_learning_data(self, metrics: PerformanceMetrics):
        """更新学习数据"""
        try:
            # 更新用户使用模式
            self._update_user_patterns(metrics)
            
            # 更新系统性能模式
            self._update_system_patterns(metrics)
            
            # 更新配置文件效果
            self._update_profile_effectiveness(metrics)
            
            # 更新用户偏好
            self._update_user_preferences(metrics)
            
        except Exception as e:
            print(f"更新学习数据失败: {str(e)}")
    
    def _update_user_patterns(self, metrics: PerformanceMetrics):
        """更新用户使用模式"""
        # 按违例数量范围分组
        violation_range = self._get_violation_range(metrics.violation_count)
        
        if violation_range not in self.learning_data['user_patterns']:
            self.learning_data['user_patterns'][violation_range] = {
                'preferred_profiles': {},
                'usage_frequency': 0,
                'average_satisfaction': 0.0,
                'common_system_configs': {}
            }
        
        pattern = self.learning_data['user_patterns'][violation_range]
        
        # 更新偏好配置文件
        if metrics.profile_name not in pattern['preferred_profiles']:
            pattern['preferred_profiles'][metrics.profile_name] = {
                'usage_count': 0,
                'satisfaction_scores': [],
                'performance_scores': []
            }
        
        profile_data = pattern['preferred_profiles'][metrics.profile_name]
        profile_data['usage_count'] += 1
        profile_data['satisfaction_scores'].append(metrics.user_satisfaction)
        
        # 计算性能评分
        performance_score = self._calculate_performance_score(metrics)
        profile_data['performance_scores'].append(performance_score)
        
        # 更新使用频率
        pattern['usage_frequency'] += 1
        
        # 更新平均满意度
        all_satisfaction_scores = []
        for profile_info in pattern['preferred_profiles'].values():
            all_satisfaction_scores.extend(profile_info['satisfaction_scores'])
        
        if all_satisfaction_scores:
            pattern['average_satisfaction'] = statistics.mean(all_satisfaction_scores)
    
    def _update_system_patterns(self, metrics: PerformanceMetrics):
        """更新系统性能模式"""
        # 根据系统能力创建系统配置键
        system_key = self._get_system_key(metrics.system_capabilities)
        
        if system_key not in self.learning_data['system_patterns']:
            self.learning_data['system_patterns'][system_key] = {
                'optimal_profiles': {},
                'performance_baselines': {},
                'resource_utilization': {}
            }
        
        system_pattern = self.learning_data['system_patterns'][system_key]
        
        # 更新最优配置文件
        violation_range = self._get_violation_range(metrics.violation_count)
        if violation_range not in system_pattern['optimal_profiles']:
            system_pattern['optimal_profiles'][violation_range] = {}
        
        if metrics.profile_name not in system_pattern['optimal_profiles'][violation_range]:
            system_pattern['optimal_profiles'][violation_range][metrics.profile_name] = {
                'performance_metrics': [],
                'success_rate': 0.0,
                'average_performance': 0.0
            }
        
        profile_perf = system_pattern['optimal_profiles'][violation_range][metrics.profile_name]
        performance_score = self._calculate_performance_score(metrics)
        profile_perf['performance_metrics'].append(performance_score)
        
        # 计算平均性能
        if profile_perf['performance_metrics']:
            profile_perf['average_performance'] = statistics.mean(profile_perf['performance_metrics'])
        
        # 计算成功率（基于错误率）
        success_rate = max(0, 1 - metrics.error_rate)
        profile_perf['success_rate'] = success_rate
    
    def _update_profile_effectiveness(self, metrics: PerformanceMetrics):
        """更新配置文件效果"""
        if metrics.profile_name not in self.learning_data['profile_effectiveness']:
            self.learning_data['profile_effectiveness'][metrics.profile_name] = {
                'total_usage': 0,
                'performance_history': [],
                'effectiveness_by_violation_count': {},
                'user_satisfaction_history': [],
                'optimal_violation_ranges': []
            }
        
        effectiveness = self.learning_data['profile_effectiveness'][metrics.profile_name]
        effectiveness['total_usage'] += 1
        
        # 记录性能历史
        performance_score = self._calculate_performance_score(metrics)
        effectiveness['performance_history'].append(performance_score)
        effectiveness['user_satisfaction_history'].append(metrics.user_satisfaction)
        
        # 按违例数量范围记录效果
        violation_range = self._get_violation_range(metrics.violation_count)
        if violation_range not in effectiveness['effectiveness_by_violation_count']:
            effectiveness['effectiveness_by_violation_count'][violation_range] = {
                'usage_count': 0,
                'performance_scores': [],
                'satisfaction_scores': []
            }
        
        range_data = effectiveness['effectiveness_by_violation_count'][violation_range]
        range_data['usage_count'] += 1
        range_data['performance_scores'].append(performance_score)
        range_data['satisfaction_scores'].append(metrics.user_satisfaction)
    
    def _update_user_preferences(self, metrics: PerformanceMetrics):
        """更新用户偏好"""
        # 分析用户偏好模式
        preferences = self.learning_data['user_preferences']
        
        # 偏好的优化目标
        if 'optimization_preferences' not in preferences:
            preferences['optimization_preferences'] = {
                'speed_preference': 0.0,
                'memory_preference': 0.0,
                'stability_preference': 0.0,
                'responsiveness_preference': 0.0
            }
        
        # 根据用户满意度和性能指标推断偏好
        if metrics.user_satisfaction >= 7:  # 高满意度
            if metrics.load_time < 5:  # 快速加载
                preferences['optimization_preferences']['speed_preference'] += 0.1
            if metrics.memory_usage_mb < 200:  # 低内存使用
                preferences['optimization_preferences']['memory_preference'] += 0.1
            if metrics.error_rate < 0.01:  # 低错误率
                preferences['optimization_preferences']['stability_preference'] += 0.1
            if metrics.ui_response_time_ms < 100:  # 快速响应
                preferences['optimization_preferences']['responsiveness_preference'] += 0.1
        
        # 归一化偏好值
        prefs = preferences['optimization_preferences']
        total = sum(prefs.values())
        if total > 0:
            for key in prefs:
                prefs[key] = min(1.0, prefs[key])
    
    def _calculate_performance_score(self, metrics: PerformanceMetrics) -> float:
        """计算性能评分"""
        try:
            # 各项指标的权重
            weights = {
                'load_time': 0.3,
                'memory_usage': 0.2,
                'ui_response': 0.2,
                'processing_speed': 0.2,
                'error_rate': 0.1
            }
            
            # 计算各项评分（0-10分）
            load_time_score = max(0, 10 - metrics.load_time)  # 加载时间越短越好
            memory_score = max(0, 10 - metrics.memory_usage_mb / 100)  # 内存使用越少越好
            ui_response_score = max(0, 10 - metrics.ui_response_time_ms / 50)  # 响应时间越短越好
            speed_score = min(10, metrics.processing_speed_ratio * 10)  # 处理速度越快越好
            error_score = max(0, 10 - metrics.error_rate * 100)  # 错误率越低越好
            
            # 加权平均
            total_score = (
                load_time_score * weights['load_time'] +
                memory_score * weights['memory_usage'] +
                ui_response_score * weights['ui_response'] +
                speed_score * weights['processing_speed'] +
                error_score * weights['error_rate']
            )
            
            return min(10.0, max(0.0, total_score))
            
        except Exception as e:
            print(f"计算性能评分失败: {str(e)}")
            return 5.0  # 默认中等评分
    
    def _get_violation_range(self, violation_count: int) -> str:
        """获取违例数量范围标识"""
        if violation_count < 2000:
            return "small"
        elif violation_count < 20000:
            return "medium"
        elif violation_count < 50000:
            return "large"
        else:
            return "very_large"
    
    def _get_system_key(self, system_capabilities: Dict) -> str:
        """获取系统配置键"""
        try:
            memory_gb = system_capabilities.get('available_memory_gb', 4)
            cpu_cores = system_capabilities.get('cpu_cores', 4)
            performance_tier = system_capabilities.get('performance_tier', 'medium')
            
            # 创建系统配置键
            memory_tier = "high" if memory_gb >= 8 else "medium" if memory_gb >= 4 else "low"
            cpu_tier = "high" if cpu_cores >= 8 else "medium" if cpu_cores >= 4 else "low"
            
            return f"{performance_tier}_{memory_tier}_{cpu_tier}"
            
        except Exception as e:
            print(f"获取系统配置键失败: {str(e)}")
            return "medium_medium_medium"
    
    def _check_optimization_opportunity(self, metrics: PerformanceMetrics):
        """检查优化机会"""
        try:
            # 检查是否有足够的数据进行优化
            if len(self.performance_history) < self.optimization_config['min_samples_for_learning']:
                return
            
            # 获取推荐配置文件
            recommendation = self.get_profile_recommendation(
                metrics.violation_count, metrics.system_capabilities
            )
            
            if recommendation and recommendation.confidence_score >= self.optimization_config['confidence_threshold']:
                # 检查推荐的配置文件是否与当前不同
                if recommendation.recommended_profile != metrics.profile_name:
                    # 检查预期改进是否显著
                    expected_improvement = recommendation.expected_improvement.get('overall', 0)
                    if expected_improvement >= self.optimization_config['performance_improvement_threshold']:
                        # 发出优化建议信号
                        self.optimization_available.emit(
                            recommendation.recommended_profile,
                            recommendation.confidence_score
                        )
                        
                        print(f"发现优化机会: 推荐 {recommendation.recommended_profile} "
                              f"(置信度: {recommendation.confidence_score:.2f}, "
                              f"预期改进: {expected_improvement:.1%})")
            
        except Exception as e:
            print(f"检查优化机会失败: {str(e)}")
    
    def get_profile_recommendation(self, violation_count: int, 
                                 system_capabilities: Dict = None) -> Optional[ProfileRecommendation]:
        """获取配置文件推荐
        
        Args:
            violation_count: 违例数量
            system_capabilities: 系统能力信息
            
        Returns:
            Optional[ProfileRecommendation]: 配置文件推荐，如果没有推荐则返回None
        """
        try:
            if len(self.performance_history) < self.optimization_config['min_samples_for_learning']:
                return None
            
            # 获取违例范围和系统配置
            violation_range = self._get_violation_range(violation_count)
            system_key = self._get_system_key(system_capabilities or {})
            
            # 分析历史数据找到最佳配置文件
            best_profiles = self._analyze_best_profiles(violation_range, system_key)
            
            if not best_profiles:
                return None
            
            # 选择最佳推荐
            recommended_profile = best_profiles[0]['profile_name']
            confidence_score = best_profiles[0]['confidence']
            
            # 计算预期改进
            expected_improvement = self._calculate_expected_improvement(
                recommended_profile, violation_range, system_key
            )
            
            # 生成推荐原因
            reason = self._generate_recommendation_reason(
                recommended_profile, violation_range, system_key, best_profiles[0]
            )
            
            # 获取备选配置文件
            alternative_profiles = [p['profile_name'] for p in best_profiles[1:3]]
            
            # 风险评估
            risk_assessment = self._assess_recommendation_risk(
                recommended_profile, violation_count, system_capabilities
            )
            
            return ProfileRecommendation(
                recommended_profile=recommended_profile,
                confidence_score=confidence_score,
                reason=reason,
                expected_improvement=expected_improvement,
                alternative_profiles=alternative_profiles,
                risk_assessment=risk_assessment
            )
            
        except Exception as e:
            print(f"获取配置文件推荐失败: {str(e)}")
            return None
    
    def _analyze_best_profiles(self, violation_range: str, system_key: str) -> List[Dict]:
        """分析最佳配置文件"""
        try:
            profile_scores = {}
            
            # 从用户使用模式中获取数据
            user_patterns = self.learning_data['user_patterns'].get(violation_range, {})
            preferred_profiles = user_patterns.get('preferred_profiles', {})
            
            for profile_name, profile_data in preferred_profiles.items():
                if profile_data['usage_count'] >= 3:  # 至少使用3次
                    # 计算综合评分
                    satisfaction_score = statistics.mean(profile_data['satisfaction_scores'])
                    performance_score = statistics.mean(profile_data['performance_scores'])
                    usage_weight = min(1.0, profile_data['usage_count'] / 10)  # 使用频率权重
                    
                    # 综合评分
                    total_score = (
                        satisfaction_score * 0.4 +
                        performance_score * 0.4 +
                        usage_weight * 10 * 0.2
                    )
                    
                    profile_scores[profile_name] = {
                        'score': total_score,
                        'satisfaction': satisfaction_score,
                        'performance': performance_score,
                        'usage_count': profile_data['usage_count']
                    }
            
            # 从系统性能模式中获取数据
            system_patterns = self.learning_data['system_patterns'].get(system_key, {})
            optimal_profiles = system_patterns.get('optimal_profiles', {}).get(violation_range, {})
            
            for profile_name, profile_data in optimal_profiles.items():
                if profile_name not in profile_scores:
                    profile_scores[profile_name] = {
                        'score': 0,
                        'satisfaction': 5.0,
                        'performance': 0,
                        'usage_count': 0
                    }
                
                # 添加系统性能评分
                system_performance = profile_data.get('average_performance', 0)
                success_rate = profile_data.get('success_rate', 0)
                
                profile_scores[profile_name]['score'] += system_performance * 0.3 + success_rate * 10 * 0.2
                profile_scores[profile_name]['performance'] = max(
                    profile_scores[profile_name]['performance'], system_performance
                )
            
            # 排序并返回最佳配置文件
            sorted_profiles = sorted(
                profile_scores.items(),
                key=lambda x: x[1]['score'],
                reverse=True
            )
            
            result = []
            for profile_name, scores in sorted_profiles[:5]:  # 返回前5个
                confidence = min(0.95, scores['score'] / 10)  # 转换为置信度
                result.append({
                    'profile_name': profile_name,
                    'confidence': confidence,
                    'scores': scores
                })
            
            return result
            
        except Exception as e:
            print(f"分析最佳配置文件失败: {str(e)}")
            return []
    
    def _calculate_expected_improvement(self, recommended_profile: str, 
                                     violation_range: str, system_key: str) -> Dict[str, float]:
        """计算预期改进"""
        try:
            # 获取推荐配置文件的历史性能
            effectiveness = self.learning_data['profile_effectiveness'].get(recommended_profile, {})
            range_data = effectiveness.get('effectiveness_by_violation_count', {}).get(violation_range, {})
            
            if not range_data or not range_data.get('performance_scores'):
                return {'overall': 0.0}
            
            # 计算推荐配置文件的平均性能
            recommended_performance = statistics.mean(range_data['performance_scores'])
            
            # 计算当前所有配置文件在该范围的平均性能
            all_performances = []
            for profile_name, profile_effectiveness in self.learning_data['profile_effectiveness'].items():
                profile_range_data = profile_effectiveness.get('effectiveness_by_violation_count', {}).get(violation_range, {})
                if profile_range_data and profile_range_data.get('performance_scores'):
                    all_performances.extend(profile_range_data['performance_scores'])
            
            if not all_performances:
                return {'overall': 0.0}
            
            current_average = statistics.mean(all_performances)
            
            # 计算改进百分比
            if current_average > 0:
                improvement = (recommended_performance - current_average) / current_average
            else:
                improvement = 0.0
            
            return {
                'overall': max(0.0, improvement),
                'performance': improvement,
                'load_time': improvement * 0.3,
                'memory_usage': improvement * 0.2,
                'ui_response': improvement * 0.2
            }
            
        except Exception as e:
            print(f"计算预期改进失败: {str(e)}")
            return {'overall': 0.0}
    
    def _generate_recommendation_reason(self, recommended_profile: str, 
                                      violation_range: str, system_key: str,
                                      profile_data: Dict) -> str:
        """生成推荐原因"""
        try:
            reasons = []
            
            # 基于性能评分
            if profile_data['scores']['performance'] > 7:
                reasons.append(f"在{violation_range}数据集上表现优异")
            
            # 基于用户满意度
            if profile_data['scores']['satisfaction'] > 7:
                reasons.append("用户满意度高")
            
            # 基于使用频率
            if profile_data['scores']['usage_count'] > 5:
                reasons.append("使用频率高，经过验证")
            
            # 基于系统适配性
            system_patterns = self.learning_data['system_patterns'].get(system_key, {})
            if system_patterns:
                reasons.append("适合当前系统配置")
            
            # 基于用户偏好
            preferences = self.learning_data['user_preferences'].get('optimization_preferences', {})
            if preferences:
                max_pref = max(preferences.items(), key=lambda x: x[1])
                if max_pref[1] > 0.5:
                    pref_name = {
                        'speed_preference': '处理速度',
                        'memory_preference': '内存效率',
                        'stability_preference': '系统稳定性',
                        'responsiveness_preference': 'UI响应性'
                    }.get(max_pref[0], '性能')
                    reasons.append(f"符合您对{pref_name}的偏好")
            
            if not reasons:
                reasons.append("基于历史数据分析的最佳选择")
            
            return "；".join(reasons)
            
        except Exception as e:
            print(f"生成推荐原因失败: {str(e)}")
            return "基于性能分析推荐"
    
    def _assess_recommendation_risk(self, recommended_profile: str, 
                                  violation_count: int, system_capabilities: Dict) -> str:
        """评估推荐风险"""
        try:
            risk_factors = []
            
            # 检查配置文件的使用历史
            effectiveness = self.learning_data['profile_effectiveness'].get(recommended_profile, {})
            total_usage = effectiveness.get('total_usage', 0)
            
            if total_usage < 5:
                risk_factors.append("使用历史较少")
            
            # 检查错误率
            recent_metrics = [m for m in self.performance_history[-20:] 
                            if m.profile_name == recommended_profile]
            if recent_metrics:
                avg_error_rate = statistics.mean([m.error_rate for m in recent_metrics])
                if avg_error_rate > 0.05:
                    risk_factors.append("历史错误率较高")
            
            # 检查系统兼容性
            system_key = self._get_system_key(system_capabilities or {})
            system_patterns = self.learning_data['system_patterns'].get(system_key, {})
            if not system_patterns:
                risk_factors.append("缺少当前系统配置的历史数据")
            
            # 检查违例数量范围
            violation_range = self._get_violation_range(violation_count)
            range_data = effectiveness.get('effectiveness_by_violation_count', {}).get(violation_range, {})
            if not range_data or range_data.get('usage_count', 0) < 3:
                risk_factors.append("在当前违例数量范围内使用经验不足")
            
            # 评估风险等级
            if not risk_factors:
                return "低风险"
            elif len(risk_factors) <= 2:
                return f"中等风险：{'、'.join(risk_factors)}"
            else:
                return f"高风险：{'、'.join(risk_factors[:2])}等"
                
        except Exception as e:
            print(f"评估推荐风险失败: {str(e)}")
            return "风险未知"
    
    def _periodic_optimization_check(self):
        """定期优化检查"""
        try:
            if len(self.performance_history) < self.optimization_config['min_samples_for_learning']:
                return
            
            # 获取最近的性能指标
            recent_metrics = self.performance_history[-10:]
            if not recent_metrics:
                return
            
            # 检查是否有性能下降趋势
            performance_scores = [self._calculate_performance_score(m) for m in recent_metrics]
            if len(performance_scores) >= 5:
                recent_avg = statistics.mean(performance_scores[-5:])
                earlier_avg = statistics.mean(performance_scores[:-5])
                
                if recent_avg < earlier_avg * 0.9:  # 性能下降超过10%
                    print("检测到性能下降趋势，寻找优化机会...")
                    
                    # 为最近使用的配置寻找更好的替代
                    latest_metrics = recent_metrics[-1]
                    recommendation = self.get_profile_recommendation(
                        latest_metrics.violation_count,
                        latest_metrics.system_capabilities
                    )
                    
                    if recommendation and recommendation.confidence_score >= 0.6:
                        self.optimization_available.emit(
                            recommendation.recommended_profile,
                            recommendation.confidence_score
                        )
            
        except Exception as e:
            print(f"定期优化检查失败: {str(e)}")
    
    def learn_from_user_feedback(self, profile_name: str, violation_count: int,
                               satisfaction_score: float, feedback_notes: str = ""):
        """从用户反馈中学习
        
        Args:
            profile_name: 配置文件名称
            violation_count: 违例数量
            satisfaction_score: 用户满意度评分 (0-10)
            feedback_notes: 反馈备注
        """
        try:
            # 更新用户满意度数据
            violation_range = self._get_violation_range(violation_count)
            
            if violation_range not in self.learning_data['user_patterns']:
                self.learning_data['user_patterns'][violation_range] = {
                    'preferred_profiles': {},
                    'usage_frequency': 0,
                    'average_satisfaction': 0.0
                }
            
            pattern = self.learning_data['user_patterns'][violation_range]
            
            if profile_name not in pattern['preferred_profiles']:
                pattern['preferred_profiles'][profile_name] = {
                    'usage_count': 0,
                    'satisfaction_scores': [],
                    'performance_scores': []
                }
            
            # 添加满意度评分
            profile_data = pattern['preferred_profiles'][profile_name]
            profile_data['satisfaction_scores'].append(satisfaction_score)
            
            # 记录学习事件
            learning_event = {
                'timestamp': time.time(),
                'profile_name': profile_name,
                'violation_count': violation_count,
                'satisfaction_score': satisfaction_score,
                'feedback_notes': feedback_notes,
                'learning_type': 'user_feedback'
            }
            
            self.learning_data['optimization_history'].append(learning_event)
            
            # 保存数据
            self._save_data()
            
            # 发出学习信号
            self.profile_learned.emit(profile_name, learning_event)
            
            print(f"用户反馈已记录: {profile_name}, 满意度: {satisfaction_score}")
            
        except Exception as e:
            print(f"从用户反馈学习失败: {str(e)}")
    
    def get_optimization_statistics(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        try:
            stats = {
                'total_metrics_recorded': len(self.performance_history),
                'learning_data_size': len(self.learning_data['optimization_history']),
                'user_patterns_count': len(self.learning_data['user_patterns']),
                'system_patterns_count': len(self.learning_data['system_patterns']),
                'profile_effectiveness_data': len(self.learning_data['profile_effectiveness']),
                'optimization_opportunities': 0,
                'recent_performance_trend': 'stable',
                'most_effective_profiles': {},
                'user_preferences': self.learning_data['user_preferences'].copy()
            }
            
            # 计算最近的优化机会数量
            recent_history = self.learning_data['optimization_history'][-50:]
            stats['optimization_opportunities'] = len([
                h for h in recent_history if h.get('learning_type') == 'optimization_suggestion'
            ])
            
            # 分析性能趋势
            if len(self.performance_history) >= 10:
                recent_scores = [self._calculate_performance_score(m) for m in self.performance_history[-10:]]
                earlier_scores = [self._calculate_performance_score(m) for m in self.performance_history[-20:-10]]
                
                if recent_scores and earlier_scores:
                    recent_avg = statistics.mean(recent_scores)
                    earlier_avg = statistics.mean(earlier_scores)
                    
                    if recent_avg > earlier_avg * 1.1:
                        stats['recent_performance_trend'] = 'improving'
                    elif recent_avg < earlier_avg * 0.9:
                        stats['recent_performance_trend'] = 'declining'
            
            # 获取最有效的配置文件
            for profile_name, effectiveness in self.learning_data['profile_effectiveness'].items():
                if effectiveness['performance_history']:
                    avg_performance = statistics.mean(effectiveness['performance_history'])
                    avg_satisfaction = statistics.mean(effectiveness['user_satisfaction_history'])
                    
                    stats['most_effective_profiles'][profile_name] = {
                        'average_performance': avg_performance,
                        'average_satisfaction': avg_satisfaction,
                        'total_usage': effectiveness['total_usage']
                    }
            
            return stats
            
        except Exception as e:
            print(f"获取优化统计信息失败: {str(e)}")
            return {}
    
    def _load_data(self):
        """加载数据"""
        try:
            # 加载性能指标历史
            if os.path.exists(self.metrics_file):
                with open(self.metrics_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.performance_history = [
                        PerformanceMetrics(**item) for item in data
                    ]
            
            # 加载学习数据
            if os.path.exists(self.learning_file):
                with open(self.learning_file, 'r', encoding='utf-8') as f:
                    self.learning_data = json.load(f)
            
            print(f"已加载 {len(self.performance_history)} 条性能记录和学习数据")
            
        except Exception as e:
            print(f"加载数据失败: {str(e)}")
    
    def _save_data(self):
        """保存数据"""
        try:
            # 保存性能指标历史
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                data = [asdict(metrics) for metrics in self.performance_history]
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 保存学习数据
            with open(self.learning_file, 'w', encoding='utf-8') as f:
                json.dump(self.learning_data, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            print(f"保存数据失败: {str(e)}")
    
    def reset_learning_data(self):
        """重置学习数据"""
        try:
            self.performance_history.clear()
            self.learning_data = {
                'user_patterns': {},
                'system_patterns': {},
                'profile_effectiveness': {},
                'optimization_history': [],
                'user_preferences': {}
            }
            
            # 删除数据文件
            for file_path in [self.metrics_file, self.learning_file, self.recommendations_file]:
                if os.path.exists(file_path):
                    os.remove(file_path)
            
            print("学习数据已重置")
            
        except Exception as e:
            print(f"重置学习数据失败: {str(e)}")