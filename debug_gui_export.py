#!/usr/bin/env python3
"""
调试GUI数据导出问题
"""

import sys
import os
import logging
from pathlib import Path

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 添加插件路径
sys.path.insert(0, str(Path(__file__).parent / "plugins" / "user" / "timing_violation"))

def debug_export_process():
    """调试导出过程"""
    try:
        print("=== 调试GUI数据导出过程 ===")
        
        # 1. 检查路径
        violation_check_dir = Path.cwd() / "VIOLATION_CHECK"
        print(f"1. VIOLATION_CHECK目录: {violation_check_dir}")
        print(f"   存在: {violation_check_dir.exists()}")
        
        if violation_check_dir.exists():
            db_path = violation_check_dir / "timing_violations.db"
            print(f"   数据库文件: {db_path}")
            print(f"   数据库存在: {db_path.exists()}")
        
        # 2. 测试数据导出器初始化
        print("\n2. 初始化数据导出器...")
        from web_display.data_exporter import DataExporter
        exporter = DataExporter(str(violation_check_dir))
        print("   ✅ 数据导出器初始化成功")
        
        # 3. 测试GUI数据
        print("\n3. 准备测试数据...")
        test_violations = [
            {
                "id": 1,
                "num": 1,
                "hier": "debug/test/module1",
                "time_ns": 1.5,
                "time_fs": 1500000,
                "check_info": "setup (clk: 2.5ns, data: 1.2ns, slack: -0.1ns)",
                "status": "pending",
                "confirmer": "",
                "result": "",
                "reason": "",
                "confirmed_at": "",
                "corner": "debug_corner",
                "case": "debug_case",
                "source": "gui"
            }
        ]
        print(f"   测试数据: {len(test_violations)} 条记录")
        
        # 4. 测试元数据提取
        print("\n4. 测试元数据提取...")
        exporter.violations_data = test_violations
        exporter._extract_metadata_from_data()
        print(f"   提取的corners: {exporter.corners}")
        print(f"   提取的cases: {exporter.cases}")
        
        # 5. 测试离线HTML创建
        print("\n5. 测试离线HTML创建...")
        try:
            exporter._create_offline_html_with_data()
            offline_path = exporter.web_display_dir / "offline.html"
            print(f"   离线HTML路径: {offline_path}")
            print(f"   文件存在: {offline_path.exists()}")
            
            if offline_path.exists():
                size = offline_path.stat().st_size
                print(f"   文件大小: {size} bytes")
                
                # 检查是否包含数据
                with open(offline_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "EMBEDDED_VIOLATION_DATA" in content:
                        print("   ✅ 包含嵌入数据")
                    else:
                        print("   ❌ 不包含嵌入数据")
        except Exception as e:
            print(f"   ❌ 离线HTML创建失败: {e}")
        
        # 6. 完整导出测试
        print("\n6. 完整导出测试...")
        success = exporter.export_all_data(gui_violations=test_violations)
        print(f"   导出结果: {'成功' if success else '失败'}")
        
        # 7. 检查生成的文件
        print("\n7. 检查生成的文件...")
        web_dir = violation_check_dir / "web_display"
        files_to_check = [
            "offline.html",
            "index.html", 
            "data/index.json",
            "data/violations.json",
            "data/statistics.json"
        ]
        
        for file_path in files_to_check:
            full_path = web_dir / file_path
            if full_path.exists():
                size = full_path.stat().st_size
                print(f"   ✅ {file_path} ({size} bytes)")
            else:
                print(f"   ❌ {file_path} (不存在)")
        
        print(f"\n=== 调试完成 ===")
        print(f"网页目录: {web_dir.absolute()}")
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_export_process()