#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
时序违例插件模糊匹配功能演示

这个脚本演示了如何实现检查信息的模糊匹配功能，
使得相同类型但时间戳不同的时序违例能够通过历史记录自动确认。
"""

def normalize_check_info(check_info: str) -> str:
    """
    标准化检查信息，用于模糊匹配
    
    规则：
    1. 层级路径必须完全匹配
    2. 检查信息括号内容按逗号分割为三部分：
       - 第一部分：冒号后面的时间信息忽略，只匹配冒号前面的内容
       - 第二部分：冒号后面的时间信息忽略，只匹配冒号前面的内容  
       - 第三部分：整个部分都忽略（不参与匹配）
    
    Args:
        check_info: 原始检查信息
        
    Returns:
        str: 标准化后的检查信息
    """
    try:
        # 查找括号内容
        start_idx = check_info.find('(')
        end_idx = check_info.rfind(')')
        
        if start_idx == -1 or end_idx == -1 or start_idx >= end_idx:
            # 如果没有找到括号，返回原始信息
            return check_info
        
        # 提取括号前的部分和括号内的部分
        prefix = check_info[:start_idx + 1]  # 包含开括号
        bracket_content = check_info[start_idx + 1:end_idx]
        
        # 按逗号分割括号内容
        parts = bracket_content.split(',')
        
        if len(parts) < 3:
            # 如果分割后少于3部分，返回原始信息
            return check_info
        
        normalized_parts = []
        
        # 处理第一部分：移除冒号后的时间信息
        part1 = parts[0].strip()
        colon_idx = part1.find(':')
        if colon_idx != -1:
            part1 = part1[:colon_idx].strip()
        normalized_parts.append(part1)
        
        # 处理第二部分：移除冒号后的时间信息
        part2 = parts[1].strip()
        colon_idx = part2.find(':')
        if colon_idx != -1:
            part2 = part2[:colon_idx].strip()
        normalized_parts.append(part2)
        
        # 第三部分忽略，不添加到标准化结果中
        
        # 重新组装标准化的检查信息
        normalized_bracket_content = ', '.join(normalized_parts)
        normalized_check_info = prefix + normalized_bracket_content + ')'
        
        return normalized_check_info
        
    except Exception as e:
        print(f"标准化检查信息失败: {str(e)}, 原始信息: {check_info}")
        return check_info

def demo_fuzzy_matching():
    """演示模糊匹配功能"""
    
    print("=" * 80)
    print("时序违例插件模糊匹配功能演示")
    print("=" * 80)
    print()
    
    # 示例：历史记录中的检查信息
    historical_check = "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )"
    
    print("1. 历史记录中的检查信息:")
    print(f"   {historical_check}")
    print()
    
    # 标准化历史记录
    historical_normalized = normalize_check_info(historical_check)
    print("2. 历史记录标准化后:")
    print(f"   {historical_normalized}")
    print()
    
    # 新的违例检查信息（时间戳不同）
    new_violations = [
        "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:1234567 FS, negedge SE:7654321 FS,0.2000 : 200000FS, -0.0100 : -10000 FS )",
        "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:9999999 FS, negedge SE:8888888 FS,0.0999 : 99900FS, -0.0333 : -33300 FS )",
        "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:5555555 FS, negedge SE:4444444 FS,0.1500 : 150000FS, -0.0200 : -20000 FS )"
    ]
    
    print("3. 新的违例检查信息（时间戳不同）:")
    for i, new_check in enumerate(new_violations, 1):
        print(f"   {i}. {new_check}")
        
        # 标准化新的检查信息
        new_normalized = normalize_check_info(new_check)
        print(f"      标准化后: {new_normalized}")
        
        # 检查是否匹配
        is_match = historical_normalized == new_normalized
        match_status = "✓ 匹配成功" if is_match else "✗ 匹配失败"
        print(f"      匹配结果: {match_status}")
        
        if is_match:
            print("      → 可以自动应用历史确认记录")
        print()
    
    # 演示不匹配的情况
    print("4. 不匹配的情况演示:")
    non_matching_cases = [
        {
            'desc': 'setuphold<hold> vs setuphold<setup> (不同子类型)',
            'check': 'setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:7869100 FS, negedge SE:7735500 FS,0.1621 : 262100FS, -0.0241 : -24100 FS )'
        },
        {
            'desc': 'setup vs setuphold<setup> (不同检查类型)',
            'check': 'setup( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )'
        },
        {
            'desc': 'width vs setuphold<setup> (完全不同的检查)',
            'check': 'width( signal:12345 FS, min_width, extra_info )'
        },
        {
            'desc': 'period vs setuphold<setup> (完全不同的检查)',
            'check': 'period( clk:555666 PS, period_value, additional_data )'
        }
    ]

    for i, case in enumerate(non_matching_cases, 1):
        print(f"   {i}. {case['desc']}")
        print(f"      检查信息: {case['check']}")

        # 标准化
        non_matching_normalized = normalize_check_info(case['check'])
        print(f"      标准化后: {non_matching_normalized}")

        # 检查是否匹配
        is_match = historical_normalized == non_matching_normalized
        match_status = "✗ 意外匹配!" if is_match else "✓ 正确不匹配"
        print(f"      匹配结果: {match_status}")

        if is_match:
            print("      ⚠️  这是一个问题！不同类型的检查不应该匹配")
        print()
    
    print("=" * 80)
    print("演示完成")
    print()
    print("总结:")
    print("- 相同类型但时间戳不同的违例可以成功匹配")
    print("- 不同类型的违例正确地不会匹配")
    print("- 模糊匹配功能可以减少重复的手动确认工作")
    print("=" * 80)

if __name__ == "__main__":
    demo_fuzzy_matching()
