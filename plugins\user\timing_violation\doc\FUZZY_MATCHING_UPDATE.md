# 时序违例插件模糊匹配功能更新

## 更新概述

针对时序违例插件的历史记录自动匹配确认功能进行了重要改进，解决了因时间戳信息不同导致相同类型违例无法自动确认的问题。

## 问题描述

### 原始问题
历史记录自动匹配确认功能要求层级路径和检查信息完全匹配才会自动确认违例。但是检查信息中包含时间戳信息，导致相同类型的违例因为时间戳不同而无法自动确认。

### 具体示例
违例检查信息格式示例：
```
setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )
```

在这个例子中，`6869100 FS`、`6735500 FS`、`162100FS`、`-26100 FS` 等都是时间戳信息，会因为不同的仿真运行而变化。

## 解决方案

### 模糊匹配规则
实现了检查信息的模糊匹配功能，具体规则如下：

1. **层级路径必须完全匹配** - 这个条件保持不变
2. **括号前的内容必须完全匹配** - 检查类型必须相同（如 setuphold<setup> vs setuphold<hold> 不匹配）
3. **检查信息括号内容按逗号分割为三部分：**
   - **第一部分**：冒号后面的时间信息忽略，只匹配冒号前面的内容
   - **第二部分**：冒号后面的时间信息忽略，只匹配冒号前面的内容
   - **第三部分**：整个部分都忽略（不参与匹配）

### 标准化示例

**原始检查信息：**
```
setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )
```

**标准化后：**
```
setuphold<setup>( negedge cpN && nCD nD SI SDFCHK, negedge SE)
```

**匹配逻辑：**
- 括号前：`setuphold<setup>(` → 保持不变，必须完全匹配
- 第一部分：`negedge cpN && nCD nD SI SDFCHK:6869100 FS` → `negedge cpN && nCD nD SI SDFCHK`
- 第二部分：`negedge SE:6735500 FS` → `negedge SE`
- 第三部分：`0.1621 : 162100FS, -0.0261 : -26100 FS` → 完全忽略

### 不匹配示例

以下情况不会匹配：

**示例1：不同的检查子类型**
```
历史记录: setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:7869100 FS, negedge SE:7735500 FS,... )
新违例:   setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,... )
```
结果：不匹配（hold vs setup）

**示例2：不同的检查类型**
```
历史记录: setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,... )
新违例:   setup( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,... )
```
结果：不匹配（setuphold vs setup）

## 代码修改

### 1. 新增标准化函数

在 `models.py` 中新增了 `_normalize_check_info()` 方法：

```python
def _normalize_check_info(self, check_info: str) -> str:
    """标准化检查信息，用于模糊匹配"""
    # 实现检查信息的标准化逻辑
    # 详细实现见代码
```

### 2. 修改模式建议函数

更新了 `get_pattern_suggestions()` 方法：
- 首先尝试精确匹配
- 如果精确匹配失败，则尝试模糊匹配
- 对历史模式进行标准化比较

### 3. 修改历史确认应用函数

更新了 `apply_historical_confirmations()` 方法：
- 支持精确匹配和模糊匹配
- 提供详细的匹配日志信息
- 正确更新匹配统计信息

## 功能特性

### 1. 向后兼容
- 保持原有的精确匹配功能
- 只有在精确匹配失败时才使用模糊匹配
- 不影响现有的历史记录

### 2. 智能匹配
- 自动识别检查信息的结构
- 忽略时间戳相关信息
- 保留关键的信号和检查类型信息

### 3. 详细日志
- 提供匹配过程的详细日志
- 区分精确匹配和模糊匹配
- 便于调试和验证

## 使用方法

### 自动应用历史确认
1. 在时序违例插件中选择日志文件
2. 点击"应用历史确认"按钮
3. 系统会自动：
   - 首先尝试精确匹配
   - 对于无法精确匹配的违例，尝试模糊匹配
   - 应用匹配成功的历史确认记录

### 手动确认时的建议
1. 在手动确认违例时
2. 系统会自动提供历史模式建议
3. 建议基于模糊匹配算法
4. 减少重复输入确认信息的工作

## 测试验证

### 演示脚本
提供了 `fuzzy_matching_demo.py` 演示脚本，展示：
- 标准化功能的工作原理
- 模糊匹配的效果
- 不匹配情况的正确处理

### 运行演示
```bash
cd plugins/user/timing_violation
python fuzzy_matching_demo.py
```

## 预期效果

### 1. 提高效率
- 相同类型但时间戳不同的违例可以自动确认
- 减少重复的手动确认工作
- 提高时序违例处理的效率

### 2. 保持准确性
- 只有真正相同类型的违例才会匹配
- 不同类型的违例不会误匹配
- 保持确认的准确性和可靠性

### 3. 用户体验
- 无需修改现有工作流程
- 自动化程度更高
- 减少重复性工作

## 注意事项

1. **首次使用**：需要先建立一些历史确认记录，模糊匹配才能发挥作用
2. **检查格式**：模糊匹配基于特定的检查信息格式，如果格式发生变化可能需要调整
3. **验证结果**：建议在使用初期验证自动确认的结果是否符合预期

## 技术细节

- **实现语言**：Python
- **数据库**：SQLite
- **匹配算法**：基于字符串解析和标准化
- **性能影响**：最小化，只在精确匹配失败时才进行模糊匹配
