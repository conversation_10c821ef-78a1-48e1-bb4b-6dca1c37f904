#!/usr/bin/env python3
"""
验证批量操作功能的基本实现
"""
import sys
import time
from typing import Dict, List

# 添加utils路径
sys.path.append('utils')

try:
    from utils.batch_operations_manager import (
        BatchOperationConfig, 
        BatchOperationProgress, 
        ViolationBatchProcessor
    )
    print("✓ 成功导入批量操作管理器")
except ImportError as e:
    print(f"✗ 导入批量操作管理器失败: {e}")
    sys.exit(1)


class MockDataModel:
    """模拟数据模型"""
    
    def __init__(self):
        self.confirmations = {}
        self.patterns = []
        self.update_count = 0
    
    def update_confirmation(self, violation_id, status, confirmer, result, reason, is_auto=False):
        """模拟更新确认"""
        self.update_count += 1
        
        # 模拟95%的成功率
        import random
        success = random.random() < 0.95
        
        if success:
            self.confirmations[violation_id] = {
                'status': status,
                'confirmer': confirmer,
                'result': result,
                'reason': reason,
                'is_auto': is_auto
            }
        
        return success
    
    def save_pattern(self, hier, check_info, confirmer, result, reason):
        """模拟保存模式"""
        self.patterns.append({
            'hier': hier,
            'check_info': check_info,
            'confirmer': confirmer,
            'result': result,
            'reason': reason
        })


def generate_test_violations(count: int) -> List[Dict]:
    """生成测试违例数据"""
    violations = []
    for i in range(count):
        violation = {
            'id': i + 1,
            'hier': f'test/hierarchy/path_{i}',
            'check_info': f'setup_check_{i % 10}',
            'status': 'pending',
            'slack': f'-{i % 100}ps',
            'required': f'{1000 + i}ps',
            'actual': f'{1000 + i + (i % 100)}ps',
            'confirmer': '',
            'result': '',
            'reason': '',
            'timestamp': f'2024-01-01 10:{i % 60:02d}:00'
        }
        violations.append(violation)
    return violations


def test_batch_operation_config():
    """测试批量操作配置"""
    print("\n=== 测试批量操作配置 ===")
    
    config = BatchOperationConfig()
    print(f"默认分块大小: {config.chunk_size}")
    print(f"启用取消功能: {config.enable_cancellation}")
    print(f"启用恢复功能: {config.enable_recovery}")
    print(f"取消粒度: {config.cancellation_granularity}")
    print("✓ 批量操作配置测试通过")


def test_batch_operation_progress():
    """测试批量操作进度"""
    print("\n=== 测试批量操作进度 ===")
    
    progress = BatchOperationProgress()
    progress.total_items = 1000
    progress.processed_items = 250
    progress.success_count = 240
    progress.error_count = 10
    progress.elapsed_time = 30.0
    
    print(f"进度百分比: {progress.progress_percentage:.1f}%")
    print(f"成功率: {progress.success_rate:.1f}%")
    print(f"处理速度: {progress.items_per_second:.1f} 项/秒")
    
    assert progress.progress_percentage == 25.0
    assert progress.success_rate == 96.0
    assert abs(progress.items_per_second - 8.33) < 0.1
    
    print("✓ 批量操作进度测试通过")


def test_violation_batch_processor():
    """测试违例批量处理器"""
    print("\n=== 测试违例批量处理器 ===")
    
    # 创建模拟数据模型
    data_model = MockDataModel()
    
    # 创建批量处理器
    processor = ViolationBatchProcessor(data_model)
    
    # 生成测试数据
    violations = generate_test_violations(50)
    confirmation_data = {
        'confirmer': 'test_user',
        'result': 'waived',
        'reason': '测试批量确认'
    }
    
    print(f"生成测试违例: {len(violations)} 个")
    print(f"确认数据: {confirmation_data}")
    
    # 测试分块大小计算
    chunk_size = processor._calculate_optimal_chunk_size(len(violations))
    print(f"计算的最优分块大小: {chunk_size}")
    
    assert chunk_size == 25  # 对于50个违例，应该是25
    
    print("✓ 违例批量处理器基本功能测试通过")


def test_chunk_size_calculation():
    """测试分块大小计算"""
    print("\n=== 测试分块大小计算 ===")
    
    data_model = MockDataModel()
    processor = ViolationBatchProcessor(data_model)
    
    test_cases = [
        (100, 25),    # 小数据集
        (1000, 50),   # 中等数据集
        (5000, 100),  # 大数据集
        (20000, 200)  # 超大数据集
    ]
    
    for violation_count, expected_chunk_size in test_cases:
        actual_chunk_size = processor._calculate_optimal_chunk_size(violation_count)
        print(f"违例数量: {violation_count:,}, 期望分块大小: {expected_chunk_size}, 实际分块大小: {actual_chunk_size}")
        assert actual_chunk_size == expected_chunk_size, f"分块大小计算错误: {actual_chunk_size} != {expected_chunk_size}"
    
    print("✓ 分块大小计算测试通过")


def test_export_chunk_size_calculation():
    """测试导出分块大小计算"""
    print("\n=== 测试导出分块大小计算 ===")
    
    data_model = MockDataModel()
    processor = ViolationBatchProcessor(data_model)
    
    test_cases = [
        (1000, 'csv', 50),
        (1000, 'json', 100),  # JSON处理较快，分块更大
        (1000, 'excel', 50),  # Excel处理较慢，分块较小
    ]
    
    for violation_count, export_format, expected_min_chunk_size in test_cases:
        actual_chunk_size = processor._calculate_export_chunk_size(violation_count, export_format)
        print(f"违例数量: {violation_count:,}, 导出格式: {export_format}, 分块大小: {actual_chunk_size}")
        
        # 验证分块大小在合理范围内
        assert actual_chunk_size > 0, "分块大小必须大于0"
        assert actual_chunk_size <= 1000, "分块大小不应超过1000"
    
    print("✓ 导出分块大小计算测试通过")


def main():
    """主函数"""
    print("开始验证批量操作功能...")
    
    try:
        test_batch_operation_config()
        test_batch_operation_progress()
        test_violation_batch_processor()
        test_chunk_size_calculation()
        test_export_chunk_size_calculation()
        
        print("\n" + "="*50)
        print("✓ 所有测试通过！批量操作功能实现正确。")
        print("="*50)
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()