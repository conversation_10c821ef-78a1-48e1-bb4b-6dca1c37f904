"""
回归批量处理UI界面

提供回归目录扫描和文件选择的用户界面。
"""

import os
from typing import List, Dict, Optional
from PyQt5.QtWidgets import (
    QWidget, QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QTableWidget, QTableWidgetItem,
    QFileDialog, QMessageBox, QProgressBar, QGroupBox, QHeaderView,
    QCheckBox, QTextEdit, QTreeWidget, QTreeWidgetItem, QSplitter,
    QTabWidget, QListWidget, QListWidgetItem, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon

from .regression_scanner import AsyncRegressionScanner, RegressionFileInfo, RegressionScanResult
from .regression_batch_manager import RegressionBatchManager, BatchProcessConfig


class RegressionBatchDialog(QDialog):
    """回归批量处理对话框"""
    
    # 信号定义
    files_selected = pyqtSignal(list)  # 文件选择完成，返回选中的文件列表
    
    # 类级别的扫描结果缓存
    _scan_cache = {}  # {path: (scan_result, timestamp)}
    _cache_timeout = 300  # 缓存5分钟
    
    def __init__(self, parent=None, default_regression_path: str = "./regression"):
        super().__init__(parent)
        self.setWindowTitle("回归批量时序违例扫描")
        self.resize(1200, 800)
        self.setMinimumSize(1000, 600)
        
        # 数据管理
        self.batch_manager = RegressionBatchManager()
        self.scanner = None
        self.scan_result = None
        
        # 默认回归路径
        self.default_regression_path = default_regression_path
        
        # 初始化UI
        self.init_ui()
        self.connect_signals()
        
        # 设置默认路径
        self.regression_path_edit.setText(self.default_regression_path)
        
        # 检查是否有缓存的扫描结果
        self._check_cached_scan_result()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建顶部控制区域
        control_group = self.create_control_panel()
        layout.addWidget(control_group)
        
        # 创建主要内容区域
        content_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：文件树和过滤器
        left_panel = self.create_left_panel()
        content_splitter.addWidget(left_panel)
        
        # 右侧：选择结果和统计
        right_panel = self.create_right_panel()
        content_splitter.addWidget(right_panel)
        
        # 设置分割比例
        content_splitter.setSizes([400, 800])
        layout.addWidget(content_splitter)
        
        # 底部按钮区域
        button_layout = self.create_button_panel()
        layout.addLayout(button_layout)
    
    def create_control_panel(self) -> QGroupBox:
        """创建控制面板"""
        group = QGroupBox("回归目录扫描")
        layout = QVBoxLayout(group)
        
        # 路径选择行
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("回归根目录:"))
        
        self.regression_path_edit = QLineEdit()
        self.regression_path_edit.setPlaceholderText("请输入或选择回归根目录路径")
        path_layout.addWidget(self.regression_path_edit)
        
        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_regression_directory)
        path_layout.addWidget(self.browse_btn)
        
        self.scan_btn = QPushButton("开始扫描")
        self.scan_btn.clicked.connect(self.start_scan)
        path_layout.addWidget(self.scan_btn)
        
        layout.addLayout(path_layout)
        
        # 扫描模式选择和状态信息行 - 将两个元素放在同一行
        mode_status_layout = QHBoxLayout()
        self.standard_structure_checkbox = QCheckBox("标准回归目录结构")
        self.standard_structure_checkbox.setChecked(True)  # 默认选中
        self.standard_structure_checkbox.setToolTip(
            "标准模式: ./regression/<subsys>/.../<case_name>_<corner_name>/<case_name>_<seed_number>/log/vio_summary.log\n"
            "通用模式: ./regression/任意层级目录/<case_name>_<seed_number>/log/vio_summary.log"
        )
        mode_status_layout.addWidget(self.standard_structure_checkbox)
        
        # 状态标签 - 减小高度，放在同一行
        self.status_label = QLabel("请选择回归目录并开始扫描")
        self.status_label.setMaximumHeight(20)  # 进一步限制状态标签高度
        self.status_label.setStyleSheet("font-size: 12px;")  # 减小字体
        mode_status_layout.addWidget(self.status_label)
        mode_status_layout.addStretch()  # 添加弹性空间，让元素左对齐
        
        layout.addLayout(mode_status_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 设置整个group的最大高度
        group.setMaximumHeight(120)  # 进一步减小整个扫描组的高度
        
        return group
    
    def create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        # 过滤器组
        filter_group = QGroupBox("文件过滤器")
        filter_layout = QGridLayout(filter_group)
        
        # 子系统过滤器
        filter_layout.addWidget(QLabel("子系统:"), 0, 0)
        self.subsys_combo = QComboBox()
        self.subsys_combo.setEditable(True)
        self.subsys_combo.addItem("全部")
        filter_layout.addWidget(self.subsys_combo, 0, 1)
        
        # 工艺角过滤器
        filter_layout.addWidget(QLabel("工艺角:"), 1, 0)
        self.corner_combo = QComboBox()
        self.corner_combo.setEditable(True)
        self.corner_combo.addItem("全部")
        filter_layout.addWidget(self.corner_combo, 1, 1)
        
        # 用例过滤器
        filter_layout.addWidget(QLabel("用例:"), 2, 0)
        self.case_combo = QComboBox()
        self.case_combo.setEditable(True)
        self.case_combo.addItem("全部")
        filter_layout.addWidget(self.case_combo, 2, 1)
        
        # 用例状态过滤器
        filter_layout.addWidget(QLabel("用例状态:"), 3, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItem("PASS")  # 默认选择PASS
        self.status_combo.addItem("全部")
        self.status_combo.addItem("FAIL")
        self.status_combo.setCurrentText("PASS")  # 设置默认值为PASS
        filter_layout.addWidget(self.status_combo, 3, 1)
        
        # 应用过滤器按钮
        self.apply_filter_btn = QPushButton("应用过滤器")
        self.apply_filter_btn.clicked.connect(self.apply_filters)
        filter_layout.addWidget(self.apply_filter_btn, 4, 0, 1, 2)
        
        layout.addWidget(filter_group)
        
        # 文件树
        tree_group = QGroupBox("文件结构")
        tree_layout = QVBoxLayout(tree_group)
        
        self.file_tree = QTreeWidget()
        self.file_tree.setHeaderLabels(["名称", "类型", "数量", "大小"])
        self.file_tree.itemChanged.connect(self.on_tree_item_changed)
        tree_layout.addWidget(self.file_tree)
        
        # 树操作按钮
        tree_btn_layout = QHBoxLayout()
        self.expand_all_btn = QPushButton("展开全部")
        self.expand_all_btn.clicked.connect(self.file_tree.expandAll)
        tree_btn_layout.addWidget(self.expand_all_btn)
        
        self.collapse_all_btn = QPushButton("折叠全部")
        self.collapse_all_btn.clicked.connect(self.file_tree.collapseAll)
        tree_btn_layout.addWidget(self.collapse_all_btn)
        
        tree_layout.addLayout(tree_btn_layout)
        layout.addWidget(tree_group)
        
        return widget
    
    def create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        widget = QFrame()
        layout = QVBoxLayout(widget)
        
        # 选择统计
        stats_group = QGroupBox("选择统计")
        stats_layout = QGridLayout(stats_group)
        
        self.total_files_label = QLabel("总文件数: 0")
        stats_layout.addWidget(self.total_files_label, 0, 0)
        
        self.selected_files_label = QLabel("已选择: 0")
        stats_layout.addWidget(self.selected_files_label, 0, 1)
        
        self.estimated_violations_label = QLabel("预估违例数: 0")
        stats_layout.addWidget(self.estimated_violations_label, 1, 0)
        
        self.estimated_time_label = QLabel("预估处理时间: 0s")
        stats_layout.addWidget(self.estimated_time_label, 1, 1)
        
        layout.addWidget(stats_group)
        
        # 选择操作
        selection_group = QGroupBox("选择操作")
        selection_layout = QHBoxLayout(selection_group)
        
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_filtered)
        selection_layout.addWidget(self.select_all_btn)
        
        self.clear_selection_btn = QPushButton("清空选择")
        self.clear_selection_btn.clicked.connect(self.clear_selection)
        selection_layout.addWidget(self.clear_selection_btn)
        
        layout.addWidget(selection_group)
        
        # 选中文件列表
        files_group = QGroupBox("选中文件")
        files_layout = QVBoxLayout(files_group)
        
        self.selected_files_table = QTableWidget()
        self.selected_files_table.setColumnCount(6)
        self.selected_files_table.setHorizontalHeaderLabels([
            "文件路径", "子系统", "工艺角", "用例", "种子", "状态"
        ])
        self.selected_files_table.horizontalHeader().setStretchLastSection(True)
        files_layout.addWidget(self.selected_files_table)
        
        layout.addWidget(files_group)
        
        return widget
    
    def create_button_panel(self) -> QHBoxLayout:
        """创建底部按钮面板"""
        layout = QHBoxLayout()
        layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        layout.addWidget(self.cancel_btn)
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept_selection)
        self.ok_btn.setEnabled(False)
        layout.addWidget(self.ok_btn)
        
        return layout
    
    def connect_signals(self):
        """连接信号"""
        # 批量管理器信号
        self.batch_manager.selection_changed.connect(self.on_selection_changed)
        
        # 过滤器变更信号
        self.subsys_combo.currentTextChanged.connect(self.on_filter_changed)
        self.corner_combo.currentTextChanged.connect(self.on_filter_changed)
        self.case_combo.currentTextChanged.connect(self.on_filter_changed)
        self.status_combo.currentTextChanged.connect(self.on_filter_changed)
        
        # 扫描模式变更信号
        self.standard_structure_checkbox.stateChanged.connect(self.on_scan_mode_changed)

    def browse_regression_directory(self):
        """浏览回归目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择回归根目录", self.regression_path_edit.text()
        )
        if directory:
            self.regression_path_edit.setText(directory)

    def start_scan(self):
        """开始扫描"""
        regression_path = self.regression_path_edit.text().strip()
        if not regression_path:
            QMessageBox.warning(self, "警告", "请输入回归目录路径")
            return

        if not os.path.exists(regression_path):
            QMessageBox.warning(self, "警告", "回归目录不存在")
            return

        # 获取扫描模式
        use_standard_structure = self.standard_structure_checkbox.isChecked()
        
        # 检查缓存（包含扫描模式）
        cache_key = f"{regression_path}_{use_standard_structure}"
        
        # 检查目录修改时间，如果目录有更新则强制重新扫描
        try:
            current_dir_mtime = os.path.getmtime(regression_path)
            if self._is_scan_cached(cache_key):
                cached_result, cache_timestamp = self._scan_cache[cache_key]
                # 如果目录修改时间晚于缓存时间，则需要重新扫描
                if current_dir_mtime > cache_timestamp:
                    print(f"目录已更新，清除缓存并重新扫描: {regression_path}")
                    del self._scan_cache[cache_key]
                else:
                    scan_mode = "标准模式" if use_standard_structure else "通用模式"
                    self.status_label.setText(f"使用缓存的扫描结果 ({scan_mode})")
                    # 直接使用缓存结果
                    self.on_scan_completed(cached_result)
                    return
        except Exception as e:
            print(f"检查目录修改时间失败: {e}")
            # 如果检查失败，清除缓存以确保数据准确性
            if cache_key in self._scan_cache:
                del self._scan_cache[cache_key]

        # 禁用控件
        self.scan_btn.setEnabled(False)
        self.browse_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 创建异步扫描器
        self.scanner = AsyncRegressionScanner(regression_path, use_standard_structure)
        self.scanner.scan_progress.connect(self.on_scan_progress)
        self.scanner.scan_completed.connect(self.on_scan_completed)
        self.scanner.scan_failed.connect(self.on_scan_failed)

        # 开始扫描
        self.scanner.start()
        scan_mode = "标准模式" if use_standard_structure else "通用模式"
        self.status_label.setText(f"正在扫描回归目录 ({scan_mode})...")

    def on_scan_progress(self, progress: int, message: str):
        """扫描进度更新"""
        if progress >= 0:
            self.progress_bar.setValue(progress)
        self.status_label.setText(message)

    def on_scan_completed(self, scan_result: RegressionScanResult):
        """扫描完成"""
        self.scan_result = scan_result
        self.batch_manager.set_scan_result(scan_result)

        # 缓存扫描结果（包含扫描模式）
        import time
        regression_path = self.regression_path_edit.text().strip()
        use_standard_structure = self.standard_structure_checkbox.isChecked()
        cache_key = f"{regression_path}_{use_standard_structure}"
        self._scan_cache[cache_key] = (scan_result, time.time())

        # 更新UI
        self.update_filter_combos()
        self.update_file_tree()
        self.update_statistics()
        
        # 自动应用默认的PASS过滤器
        self.apply_filters()

        # 恢复控件
        self.scan_btn.setEnabled(True)
        self.browse_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

        # 更新状态
        self.status_label.setText(
            f"扫描完成：发现 {len(scan_result.valid_files)} 个有效文件，"
            f"耗时 {scan_result.scan_time:.2f} 秒"
        )

        # 只显示一个扫描完成的消息框（移除重复的信号连接导致的重复弹窗）
        # 使用QTimer.singleShot确保只弹出一次
        if not hasattr(self, '_completion_dialog_shown'):
            self._completion_dialog_shown = True
            QTimer.singleShot(100, lambda: self._show_completion_dialog(scan_result))
    
    def _show_completion_dialog(self, scan_result: RegressionScanResult):
        """显示扫描完成对话框（确保只显示一次）"""
        try:
            QMessageBox.information(
                self, "扫描完成",
                f"成功扫描回归目录！\n\n"
                f"发现 {len(scan_result.valid_files)} 个有效的 vio_summary.log 文件\n"
                f"子系统数量: {len(scan_result.subsys_groups)}\n"
                f"工艺角数量: {len(scan_result.corner_groups)}\n"
                f"用例数量: {len(scan_result.case_groups)}\n"
                f"扫描耗时: {scan_result.scan_time:.2f} 秒"
            )
        finally:
            # 重置标志，允许下次扫描时再次显示
            if hasattr(self, '_completion_dialog_shown'):
                delattr(self, '_completion_dialog_shown')

    def on_scan_failed(self, error_message: str):
        """扫描失败"""
        # 恢复控件
        self.scan_btn.setEnabled(True)
        self.browse_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

        self.status_label.setText(f"扫描失败: {error_message}")
        QMessageBox.critical(self, "扫描失败", f"扫描回归目录失败：\n{error_message}")

    def update_filter_combos(self):
        """更新过滤器下拉框"""
        if not self.scan_result:
            return

        # 更新子系统下拉框
        self.subsys_combo.clear()
        self.subsys_combo.addItem("全部")
        subsys_list = self.batch_manager.get_subsys_list()
        self.subsys_combo.addItems(subsys_list)

        # 更新工艺角下拉框
        self.corner_combo.clear()
        self.corner_combo.addItem("全部")
        corner_list = self.batch_manager.get_corner_list()
        self.corner_combo.addItems(corner_list)

        # 更新用例下拉框
        self.case_combo.clear()
        self.case_combo.addItem("全部")
        case_list = self.batch_manager.get_case_list()
        self.case_combo.addItems(case_list)

        # 更新用例状态下拉框
        self.status_combo.clear()
        self.status_combo.addItem("PASS")  # 默认选择PASS
        self.status_combo.addItem("全部")
        self.status_combo.addItem("FAIL")
        self.status_combo.setCurrentText("PASS")  # 重新设置默认值为PASS

    def update_file_tree(self):
        """更新文件树"""
        self.file_tree.clear()

        if not self.scan_result:
            return

        # 按子系统组织树结构
        for subsys, files in self.scan_result.subsys_groups.items():
            subsys_item = QTreeWidgetItem([subsys, "子系统", str(len(files)), ""])
            subsys_item.setCheckState(0, Qt.Unchecked)
            subsys_item.setData(0, Qt.UserRole, {'type': 'subsys', 'name': subsys})

            # 按工艺角分组
            corner_groups = {}
            for file_info in files:
                corner = file_info.corner_name
                if corner not in corner_groups:
                    corner_groups[corner] = []
                corner_groups[corner].append(file_info)

            for corner, corner_files in corner_groups.items():
                corner_item = QTreeWidgetItem([corner, "工艺角", str(len(corner_files)), ""])
                corner_item.setCheckState(0, Qt.Unchecked)
                corner_item.setData(0, Qt.UserRole, {'type': 'corner', 'name': corner, 'subsys': subsys})

                # 按用例分组
                case_groups = {}
                for file_info in corner_files:
                    case = file_info.case_name
                    if case not in case_groups:
                        case_groups[case] = []
                    case_groups[case].append(file_info)

                for case, case_files in case_groups.items():
                    case_item = QTreeWidgetItem([case, "用例", str(len(case_files)), ""])
                    case_item.setCheckState(0, Qt.Unchecked)
                    case_item.setData(0, Qt.UserRole, {'type': 'case', 'name': case, 'corner': corner, 'subsys': subsys})

                    # 添加具体文件
                    for file_info in case_files:
                        file_size_mb = file_info.file_size / (1024 * 1024)
                        file_item = QTreeWidgetItem([
                            f"seed_{file_info.seed} ({file_info.case_status})",
                            "文件",
                            "1",
                            f"{file_size_mb:.2f} MB"
                        ])
                        file_item.setCheckState(0, Qt.Unchecked)
                        
                        # 根据状态设置颜色
                        if file_info.case_status == "PASS":
                            file_item.setBackground(0, QColor(144, 238, 144))  # 浅绿色
                        else:
                            file_item.setBackground(0, QColor(255, 182, 193))  # 浅红色
                        
                        file_item.setData(0, Qt.UserRole, {
                            'type': 'file',
                            'file_info': file_info
                        })
                        case_item.addChild(file_item)

                    corner_item.addChild(case_item)

                subsys_item.addChild(corner_item)

            self.file_tree.addTopLevelItem(subsys_item)

        # 调整列宽
        for i in range(self.file_tree.columnCount()):
            self.file_tree.resizeColumnToContents(i)

    def on_tree_item_changed(self, item: QTreeWidgetItem, column: int):
        """树项目状态变更"""
        if column != 0:  # 只处理第一列的复选框
            return

        item_data = item.data(0, Qt.UserRole)
        if not item_data:
            return

        check_state = item.checkState(0)
        item_type = item_data.get('type')

        if item_type == 'file':
            # 文件级别的选择
            file_info = item_data.get('file_info')
            if file_info:
                if check_state == Qt.Checked:
                    self.batch_manager.select_files([file_info.file_path])
                else:
                    self.batch_manager.deselect_files([file_info.file_path])

        elif item_type in ['subsys', 'corner', 'case']:
            # 批量选择/取消选择
            self._update_children_check_state(item, check_state)

    def _update_children_check_state(self, item: QTreeWidgetItem, check_state: Qt.CheckState):
        """更新子项目的选择状态"""
        file_paths = []
        
        # 获取当前过滤后的文件路径集合
        filtered_files = self.batch_manager.get_files_by_filters()
        filtered_paths = {f.file_path for f in filtered_files}

        def collect_files(current_item):
            # 跳过隐藏的项目
            if current_item.isHidden():
                return
                
            item_data = current_item.data(0, Qt.UserRole)
            if item_data and item_data.get('type') == 'file':
                file_info = item_data.get('file_info')
                if file_info and file_info.file_path in filtered_paths:
                    # 只选择过滤后的文件
                    file_paths.append(file_info.file_path)
                    current_item.setCheckState(0, check_state)

            for i in range(current_item.childCount()):
                collect_files(current_item.child(i))

        collect_files(item)

        if file_paths:
            if check_state == Qt.Checked:
                self.batch_manager.select_files(file_paths)
            else:
                self.batch_manager.deselect_files(file_paths)

    def on_filter_changed(self):
        """过滤器变更"""
        # 这里可以实现实时过滤，暂时留空
        pass
    
    def on_scan_mode_changed(self, state):
        """扫描模式变更处理"""
        # 当扫描模式改变时，先清空当前扫描结果
        if hasattr(self, 'scan_result') and self.scan_result:
            # 清空当前结果
            self.scan_result = None
            self.batch_manager.set_scan_result(None)
            
            # 清空UI
            self.file_tree.clear()
            self.selected_files_table.setRowCount(0)
            
            # 重置统计
            self.total_files_label.setText("总文件数: 0")
            self.selected_files_label.setText("已选择: 0")
            self.estimated_violations_label.setText("预估违例数: 0")
            self.estimated_time_label.setText("预估处理时间: 0s")
            
            # 清空过滤器
            self.subsys_combo.clear()
            self.subsys_combo.addItem("全部")
            self.corner_combo.clear()
            self.corner_combo.addItem("全部")
            self.case_combo.clear()
            self.case_combo.addItem("全部")
            self.status_combo.clear()
            self.status_combo.addItem("PASS")
            self.status_combo.addItem("全部")
            self.status_combo.addItem("FAIL")
            self.status_combo.setCurrentText("PASS")
            
            # 禁用确定按钮
            self.ok_btn.setEnabled(False)
        
        # 检查新模式下是否有缓存的扫描结果
        self._check_cached_scan_result()
        
        # 如果没有缓存，显示提示信息
        if not (hasattr(self, 'scan_result') and self.scan_result):
            scan_mode = "标准模式" if self.standard_structure_checkbox.isChecked() else "通用模式"
            self.status_label.setText(f"扫描模式已切换到{scan_mode}，请重新扫描")

    def apply_filters(self):
        """应用过滤器"""
        if not self.scan_result:
            return

        # 获取过滤器值
        subsys_filter = []
        corner_filter = []
        case_filter = []
        status_filter = []

        subsys_text = self.subsys_combo.currentText()
        if subsys_text and subsys_text != "全部":
            subsys_filter.append(subsys_text)

        corner_text = self.corner_combo.currentText()
        if corner_text and corner_text != "全部":
            corner_filter.append(corner_text)

        case_text = self.case_combo.currentText()
        if case_text and case_text != "全部":
            case_filter.append(case_text)

        status_text = self.status_combo.currentText()
        if status_text and status_text != "全部":
            status_filter.append(status_text)

        # 应用过滤器
        self.batch_manager.set_subsys_filter(subsys_filter)
        self.batch_manager.set_corner_filter(corner_filter)
        self.batch_manager.set_case_filter(case_filter)
        self.batch_manager.set_status_filter(status_filter)

        # 更新文件树显示
        self.update_file_tree_visibility()

    def update_file_tree_visibility(self):
        """更新文件树的可见性"""
        # 获取过滤后的文件
        filtered_files = self.batch_manager.get_files_by_filters()
        filtered_paths = {f.file_path for f in filtered_files}

        # 更新树项目的可见性
        def update_visibility(item):
            item_data = item.data(0, Qt.UserRole)
            if item_data and item_data.get('type') == 'file':
                file_info = item_data.get('file_info')
                if file_info:
                    visible = file_info.file_path in filtered_paths
                    item.setHidden(not visible)
                    return visible
            else:
                # 对于非文件项目，检查是否有可见的子项目
                has_visible_child = False
                for i in range(item.childCount()):
                    child_visible = update_visibility(item.child(i))
                    has_visible_child = has_visible_child or child_visible

                item.setHidden(not has_visible_child)
                return has_visible_child

        for i in range(self.file_tree.topLevelItemCount()):
            update_visibility(self.file_tree.topLevelItem(i))

    def select_all_filtered(self):
        """选择所有过滤后的文件"""
        self.batch_manager.select_all_filtered()

    def clear_selection(self):
        """清空选择"""
        self.batch_manager.clear_selection()

        # 更新树的选择状态
        def clear_tree_selection(item):
            item.setCheckState(0, Qt.Unchecked)
            for i in range(item.childCount()):
                clear_tree_selection(item.child(i))

        for i in range(self.file_tree.topLevelItemCount()):
            clear_tree_selection(self.file_tree.topLevelItem(i))

    def on_selection_changed(self, selected_files: List[RegressionFileInfo]):
        """选择变更处理"""
        self.update_statistics()
        self.update_selected_files_table(selected_files)

        # 更新确定按钮状态
        self.ok_btn.setEnabled(len(selected_files) > 0)

    def update_statistics(self):
        """更新统计信息"""
        stats = self.batch_manager.get_statistics()

        self.total_files_label.setText(f"总文件数: {stats['total_files']}")
        self.selected_files_label.setText(f"已选择: {stats['selected_files']}")
        self.estimated_violations_label.setText(f"预估违例数: {stats['estimated_violations']:,}")
        self.estimated_time_label.setText(f"预估处理时间: {stats['estimated_processing_time']:.1f}s")

    def update_selected_files_table(self, selected_files: List[RegressionFileInfo]):
        """更新选中文件表格"""
        self.selected_files_table.setRowCount(len(selected_files))

        for row, file_info in enumerate(selected_files):
            # 文件路径
            path_item = QTableWidgetItem(file_info.relative_path)
            path_item.setToolTip(file_info.file_path)
            self.selected_files_table.setItem(row, 0, path_item)

            # 子系统
            self.selected_files_table.setItem(row, 1, QTableWidgetItem(file_info.subsys))

            # 工艺角
            self.selected_files_table.setItem(row, 2, QTableWidgetItem(file_info.corner_name))

            # 用例
            self.selected_files_table.setItem(row, 3, QTableWidgetItem(file_info.case_name))

            # 种子
            self.selected_files_table.setItem(row, 4, QTableWidgetItem(file_info.seed))

            # 状态
            status_item = QTableWidgetItem(file_info.case_status)
            # 根据状态设置颜色
            if file_info.case_status == "PASS":
                status_item.setBackground(QColor(144, 238, 144))  # 浅绿色
            else:
                status_item.setBackground(QColor(255, 182, 193))  # 浅红色
            self.selected_files_table.setItem(row, 5, status_item)

        # 调整列宽
        self.selected_files_table.resizeColumnsToContents()

    def accept_selection(self):
        """确认选择"""
        selected_files = self.batch_manager.get_selected_files()
        if not selected_files:
            QMessageBox.warning(self, "警告", "请至少选择一个文件")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self, "确认选择",
            f"您选择了 {len(selected_files)} 个文件进行批量处理。\n"
            f"预估违例数: {self.batch_manager.get_statistics()['estimated_violations']:,}\n"
            f"预估处理时间: {self.batch_manager.get_statistics()['estimated_processing_time']:.1f} 秒\n\n"
            f"是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            # 不发送信号，直接关闭对话框，让主窗口处理
            self.accept()

    def get_selected_files(self) -> List[RegressionFileInfo]:
        """获取选中的文件列表"""
        return self.batch_manager.get_selected_files()

    def _check_cached_scan_result(self):
        """检查是否有缓存的扫描结果"""
        regression_path = self.regression_path_edit.text().strip()
        
        # 如果路径为空，不检查缓存
        if not regression_path:
            return
        
        # 检查两种模式下的缓存，优先检查当前模式
        use_standard_structure = self.standard_structure_checkbox.isChecked()
        current_cache_key = f"{regression_path}_{use_standard_structure}"
        alternative_cache_key = f"{regression_path}_{not use_standard_structure}"
        
        # 首先检查当前模式的缓存
        if self._is_scan_cached(current_cache_key):
            cached_result, timestamp = self._scan_cache[current_cache_key]
            import time
            age_minutes = (time.time() - timestamp) / 60
            scan_mode = "标准模式" if use_standard_structure else "通用模式"
            self.status_label.setText(f"发现缓存的扫描结果 ({scan_mode}, {age_minutes:.1f}分钟前)")
            
            # 自动加载缓存结果
            self.scan_result = cached_result
            self.batch_manager.set_scan_result(cached_result)
            self.update_filter_combos()
            self.update_file_tree()
            self.update_statistics()
            return
        
        # 如果当前模式没有缓存，检查另一种模式的缓存
        if self._is_scan_cached(alternative_cache_key):
            cached_result, timestamp = self._scan_cache[alternative_cache_key]
            import time
            age_minutes = (time.time() - timestamp) / 60
            alternative_mode = "通用模式" if use_standard_structure else "标准模式"
            
            # 询问用户是否要切换到有缓存的模式
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "发现缓存",
                f"在{alternative_mode}下发现了该目录的缓存结果 ({age_minutes:.1f}分钟前)。\n"
                f"是否切换到{alternative_mode}并加载缓存？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                # 切换模式并加载缓存
                self.standard_structure_checkbox.setChecked(not use_standard_structure)
                self.scan_result = cached_result
                self.batch_manager.set_scan_result(cached_result)
                self.update_filter_combos()
                self.update_file_tree()
                self.update_statistics()
                
                scan_mode = "标准模式" if not use_standard_structure else "通用模式"
                self.status_label.setText(f"已切换到{scan_mode}并加载缓存结果 ({age_minutes:.1f}分钟前)")
    
    def _is_scan_cached(self, cache_key: str) -> bool:
        """检查扫描结果是否已缓存且未过期"""
        if cache_key not in self._scan_cache:
            return False
        
        import time
        _, timestamp = self._scan_cache[cache_key]
        return (time.time() - timestamp) < self._cache_timeout
    
    def _clear_expired_cache(self):
        """清理过期的缓存"""
        import time
        current_time = time.time()
        expired_keys = []
        
        for path, (_, timestamp) in self._scan_cache.items():
            if (current_time - timestamp) >= self._cache_timeout:
                expired_keys.append(path)
        
        for key in expired_keys:
            del self._scan_cache[key]
    


    def closeEvent(self, event):
        """关闭事件"""
        # 取消正在进行的扫描
        if self.scanner and self.scanner.isRunning():
            self.scanner.cancel()
            self.scanner.wait(3000)  # 等待最多3秒

        # 清理过期缓存
        self._clear_expired_cache()

        event.accept()
