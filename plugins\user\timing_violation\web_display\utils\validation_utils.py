"""
Data validation utilities for timing violation web display.

This module provides utility functions for validating violation data,
ensuring data integrity, and performing data quality checks.
"""

import logging
import re
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime


class ValidationUtils:
    """Utility class for data validation operations."""
    
    def __init__(self):
        """Initialize validation utilities."""
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def validate_violation_record(record: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate a single violation record.
        
        Args:
            record: Violation record dictionary
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        # Required fields
        required_fields = ['num', 'hier', 'time_ns', 'check_info', 'corner', 'case']
        for field in required_fields:
            if field not in record:
                errors.append(f"Missing required field: {field}")
            elif record[field] is None:
                errors.append(f"Field '{field}' cannot be None")
            elif isinstance(record[field], str) and not record[field].strip():
                errors.append(f"Field '{field}' cannot be empty")
        
        # Validate specific field types and formats
        if 'num' in record and record['num'] is not None:
            if not ValidationUtils.is_valid_integer(record['num']):
                errors.append("Field 'num' must be a valid integer")
            elif record['num'] <= 0:
                errors.append("Field 'num' must be positive")
        
        if 'time_ns' in record and record['time_ns'] is not None:
            if not ValidationUtils.is_valid_number(record['time_ns']):
                errors.append("Field 'time_ns' must be a valid number")
        
        if 'hier' in record and record['hier']:
            if not ValidationUtils.is_valid_hierarchy_path(record['hier']):
                errors.append("Field 'hier' contains invalid hierarchy path format")
        
        if 'confirmed_at' in record and record['confirmed_at']:
            if not ValidationUtils.is_valid_date_string(record['confirmed_at']):
                errors.append("Field 'confirmed_at' contains invalid date format")
        
        if 'status' in record and record['status']:
            if not ValidationUtils.is_valid_status(record['status']):
                errors.append("Field 'status' contains invalid status value")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_violation_dataset(violations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate entire violation dataset.
        
        Args:
            violations: List of violation records
            
        Returns:
            Validation report dictionary
        """
        report = {
            'valid': True,
            'total_records': len(violations),
            'valid_records': 0,
            'invalid_records': 0,
            'errors': [],
            'warnings': [],
            'statistics': {
                'duplicate_nums': 0,
                'missing_confirmations': 0,
                'invalid_dates': 0,
                'empty_fields': 0
            }
        }
        
        if not violations:
            report['warnings'].append("Dataset is empty")
            return report
        
        seen_nums = set()
        
        for i, record in enumerate(violations):
            is_valid, record_errors = ValidationUtils.validate_violation_record(record)
            
            if is_valid:
                report['valid_records'] += 1
            else:
                report['invalid_records'] += 1
                report['valid'] = False
                for error in record_errors:
                    report['errors'].append(f"Record {i+1}: {error}")
            
            # Check for duplicates
            if 'num' in record and record['num'] in seen_nums:
                report['statistics']['duplicate_nums'] += 1
                report['warnings'].append(f"Duplicate violation number: {record['num']}")
            elif 'num' in record:
                seen_nums.add(record['num'])
            
            # Check for missing confirmations
            if 'status' in record and record['status'] != 'confirmed':
                report['statistics']['missing_confirmations'] += 1
            
            # Check for invalid dates
            if 'confirmed_at' in record and record['confirmed_at']:
                if not ValidationUtils.is_valid_date_string(record['confirmed_at']):
                    report['statistics']['invalid_dates'] += 1
            
            # Check for empty fields
            for field, value in record.items():
                if value is None or (isinstance(value, str) and not value.strip()):
                    report['statistics']['empty_fields'] += 1
                    break
        
        return report
    
    @staticmethod
    def is_valid_integer(value: Any) -> bool:
        """
        Check if value can be converted to integer.
        
        Args:
            value: Value to check
            
        Returns:
            True if value is valid integer
        """
        if isinstance(value, int):
            return True
        
        if isinstance(value, float):
            return value.is_integer()
        
        if isinstance(value, str):
            try:
                int(value)
                return True
            except ValueError:
                return False
        
        return False
    
    @staticmethod
    def is_valid_number(value: Any) -> bool:
        """
        Check if value is a valid number.
        
        Args:
            value: Value to check
            
        Returns:
            True if value is valid number
        """
        if isinstance(value, (int, float)):
            return not (isinstance(value, float) and (value != value))  # Check for NaN
        
        if isinstance(value, str):
            try:
                float(value)
                return True
            except ValueError:
                return False
        
        return False
    
    @staticmethod
    def is_valid_hierarchy_path(path: str) -> bool:
        """
        Check if hierarchy path is valid.
        
        Args:
            path: Hierarchy path string
            
        Returns:
            True if path is valid
        """
        if not isinstance(path, str) or not path.strip():
            return False
        
        # Basic validation - should contain valid path characters
        # Allow alphanumeric, underscore, slash, dot, brackets
        pattern = r'^[a-zA-Z0-9_/.\[\]]+$'
        return bool(re.match(pattern, path.strip()))
    
    @staticmethod
    def is_valid_date_string(date_string: str) -> bool:
        """
        Check if date string is valid.
        
        Args:
            date_string: Date string to validate
            
        Returns:
            True if date string is valid
        """
        if not isinstance(date_string, str) or not date_string.strip():
            return False
        
        # Try common date formats
        date_formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d %H:%M:%S.%f",
            "%Y/%m/%d %H:%M:%S",
            "%Y-%m-%d",
            "%Y/%m/%d",
            "%d-%m-%Y %H:%M:%S",
            "%d/%m/%Y %H:%M:%S",
            "%d-%m-%Y",
            "%d/%m/%Y",
        ]
        
        for fmt in date_formats:
            try:
                datetime.strptime(date_string.strip(), fmt)
                return True
            except ValueError:
                continue
        
        return False
    
    @staticmethod
    def is_valid_status(status: str) -> bool:
        """
        Check if status value is valid.
        
        Args:
            status: Status string to validate
            
        Returns:
            True if status is valid
        """
        if not isinstance(status, str):
            return False
        
        valid_statuses = {
            'confirmed', 'pending', 'rejected', 'under_review',
            'false_positive', 'real_violation', 'waived'
        }
        
        return status.lower().strip() in valid_statuses
    
    @staticmethod
    def sanitize_string(value: str, max_length: int = 1000) -> str:
        """
        Sanitize string value for safe storage/display.
        
        Args:
            value: String to sanitize
            max_length: Maximum allowed length
            
        Returns:
            Sanitized string
        """
        if not isinstance(value, str):
            return str(value) if value is not None else ""
        
        # Remove control characters except newline and tab
        sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', value)
        
        # Trim whitespace
        sanitized = sanitized.strip()
        
        # Limit length
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length] + "..."
        
        return sanitized
    
    @staticmethod
    def normalize_corner_name(corner: str) -> str:
        """
        Normalize corner name for consistency.
        
        Args:
            corner: Corner name to normalize
            
        Returns:
            Normalized corner name
        """
        if not isinstance(corner, str):
            return str(corner) if corner is not None else ""
        
        # Convert to lowercase and remove extra whitespace
        normalized = corner.lower().strip()
        
        # Replace common variations
        replacements = {
            'slow_slow': 'ss',
            'fast_fast': 'ff',
            'slow_fast': 'sf',
            'fast_slow': 'fs',
            'typical': 'tt',
            'typ': 'tt'
        }
        
        for old, new in replacements.items():
            if normalized == old:
                return new
        
        return normalized
    
    @staticmethod
    def normalize_case_name(case: str) -> str:
        """
        Normalize case name for consistency.
        
        Args:
            case: Case name to normalize
            
        Returns:
            Normalized case name
        """
        if not isinstance(case, str):
            return str(case) if case is not None else ""
        
        # Remove common prefixes/suffixes and normalize
        normalized = case.strip()
        
        # Remove common test prefixes
        prefixes_to_remove = ['test_', 'case_', 'tc_']
        for prefix in prefixes_to_remove:
            if normalized.lower().startswith(prefix):
                normalized = normalized[len(prefix):]
                break
        
        return normalized
    
    @staticmethod
    def validate_json_structure(data: Any, expected_schema: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate JSON data against expected schema.
        
        Args:
            data: JSON data to validate
            expected_schema: Expected schema dictionary
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        if not isinstance(data, dict):
            errors.append("Data must be a dictionary")
            return False, errors
        
        # Check required keys
        for key, requirements in expected_schema.items():
            if requirements.get('required', False) and key not in data:
                errors.append(f"Missing required key: {key}")
                continue
            
            if key in data:
                value = data[key]
                expected_type = requirements.get('type')
                
                if expected_type and not isinstance(value, expected_type):
                    errors.append(f"Key '{key}' should be of type {expected_type.__name__}")
                
                # Check array elements if specified
                if expected_type == list and 'item_type' in requirements:
                    item_type = requirements['item_type']
                    for i, item in enumerate(value):
                        if not isinstance(item, item_type):
                            errors.append(f"Item {i} in '{key}' should be of type {item_type.__name__}")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def check_data_consistency(violations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Check data consistency across violation records.
        
        Args:
            violations: List of violation records
            
        Returns:
            Consistency report dictionary
        """
        report = {
            'consistent': True,
            'issues': [],
            'statistics': {
                'corner_case_combinations': {},
                'confirmer_patterns': {},
                'time_ranges': {},
                'status_distribution': {}
            }
        }
        
        if not violations:
            return report
        
        # Analyze corner-case combinations
        combinations = {}
        for record in violations:
            corner = record.get('corner', 'unknown')
            case = record.get('case', 'unknown')
            combo = f"{corner}_{case}"
            combinations[combo] = combinations.get(combo, 0) + 1
        
        report['statistics']['corner_case_combinations'] = combinations
        
        # Analyze confirmer patterns
        confirmer_stats = {}
        for record in violations:
            confirmer = record.get('confirmer', 'unknown')
            if confirmer not in confirmer_stats:
                confirmer_stats[confirmer] = {
                    'total': 0,
                    'corners': set(),
                    'cases': set()
                }
            
            confirmer_stats[confirmer]['total'] += 1
            confirmer_stats[confirmer]['corners'].add(record.get('corner', 'unknown'))
            confirmer_stats[confirmer]['cases'].add(record.get('case', 'unknown'))
        
        # Convert sets to lists for JSON serialization
        for confirmer, stats in confirmer_stats.items():
            stats['corners'] = list(stats['corners'])
            stats['cases'] = list(stats['cases'])
        
        report['statistics']['confirmer_patterns'] = confirmer_stats
        
        # Analyze time ranges
        times = [record.get('time_ns', 0) for record in violations if record.get('time_ns') is not None]
        if times:
            report['statistics']['time_ranges'] = {
                'min': min(times),
                'max': max(times),
                'avg': sum(times) / len(times)
            }
        
        # Analyze status distribution
        status_dist = {}
        for record in violations:
            status = record.get('status', 'unknown')
            status_dist[status] = status_dist.get(status, 0) + 1
        
        report['statistics']['status_distribution'] = status_dist
        
        return report