# Requirements Document

## Introduction

This feature adds a web-based display interface for the timing violation plugin to allow leaders to easily review confirmed timing violation records. Currently, leaders need to manually open individual Excel files in the VIOLATION_CHECK directory for each corner, which is cumbersome. The web interface will provide a centralized view with filtering capabilities to streamline the review process.

## Requirements

### Requirement 1

**User Story:** As a project leader, I want to view all confirmed timing violation records through a web interface, so that I can efficiently review violation confirmations without manually opening multiple Excel files.

#### Acceptance Criteria

1. WHEN the web interface is accessed THEN the system SHALL display a web page with filtering controls and a data table
2. WHEN the page loads THEN the system SHALL automatically scan and parse all Excel files in the VIOLATION_CHECK directory structure
3. WHEN violation data is loaded THEN the system SHALL display the total count of confirmed violations across all corners and cases

### Requirement 2

**User Story:** As a project leader, I want to filter timing violations by corner name, so that I can focus on specific corner conditions during review.

#### Acceptance Criteria

1. WHEN the page loads THEN the system SHALL populate a corner name dropdown with all available corners from the parsed data
2. WHEN a corner is selected from the dropdown THEN the system SHALL filter the violation table to show only violations from that corner
3. WHEN "All Corners" is selected THEN the system SHALL display violations from all corners
4. WHEN the corner filter changes THEN the case name dropdown SHALL update to show only cases available for the selected corner

### Requirement 3

**User Story:** As a project leader, I want to filter timing violations by case name, so that I can review violations for specific test cases.

#### Acceptance Criteria

1. WHEN a corner is selected THEN the system SHALL populate the case name dropdown with all cases available for that corner
2. WHEN a case name is selected THEN the system SHALL filter the violation table to show only violations from that case
3. WHEN "All Cases" is selected THEN the system SHALL display all violations for the selected corner
4. WHEN no corner is selected THEN the case dropdown SHALL show all available cases across all corners

### Requirement 4

**User Story:** As a project leader, I want to view detailed violation information in a table format, so that I can review the confirmation details for each violation.

#### Acceptance Criteria

1. WHEN violations are displayed THEN the table SHALL show columns for: NUM, Hier, Time, Check Info, Status, Confirmer, Result, Reason, Confirmed At
2. WHEN the table is populated THEN violations SHALL be sorted by NUM in ascending order
3. WHEN a violation has been confirmed THEN the status SHALL display as "Confirmed" with appropriate styling
4. WHEN violation data is extensive THEN the table SHALL support pagination or virtual scrolling for performance

### Requirement 5

**User Story:** As a system administrator, I want the web interface to automatically load violation data from available sources, so that the latest confirmation data is always available without manual intervention.

#### Acceptance Criteria

1. WHEN the web service starts THEN the system SHALL first scan the VIOLATION_CHECK directory for Excel files
2. WHEN Excel files are found THEN the system SHALL parse each file to extract violation confirmation data
3. WHEN no Excel files are found THEN the system SHALL fall back to reading data directly from the timing_violations.db database
4. WHEN parsing Excel files THEN the system SHALL extract corner and case information from the file path or filename
5. WHEN reading from database THEN the system SHALL query confirmed violations with their confirmation details
6. WHEN parsing fails for a file THEN the system SHALL log the error and continue processing other files or fall back to database
7. WHEN new Excel files are added to the directory THEN the system SHALL detect and parse them on the next page refresh

### Requirement 6

**User Story:** As a project leader, I want the web interface to be responsive and performant, so that I can efficiently navigate through large datasets of violation records.

#### Acceptance Criteria

1. WHEN the page loads THEN the initial load time SHALL be less than 3 seconds for datasets up to 10,000 violations
2. WHEN filters are applied THEN the table SHALL update within 1 second
3. WHEN the interface is accessed from different devices THEN the layout SHALL be responsive and usable on desktop, tablet, and mobile devices
4. WHEN large datasets are displayed THEN the system SHALL implement efficient rendering techniques to maintain smooth scrolling

### Requirement 7

**User Story:** As a project leader, I want to see summary statistics of violation confirmations, so that I can quickly understand the overall status of timing violation reviews.

#### Acceptance Criteria

1. WHEN the page loads THEN the system SHALL display summary statistics including total violations, confirmed violations, and pending violations
2. WHEN filters are applied THEN the summary statistics SHALL update to reflect the filtered data
3. WHEN viewing statistics THEN the system SHALL show breakdown by corner and case
4. WHEN statistics are displayed THEN they SHALL include confirmation rates and most recent confirmation dates