#!/usr/bin/env python3
"""
解压缩网页数据文件脚本

该脚本用于将压缩的.json.gz文件解压缩为.json文件，以便浏览器可以直接加载。
"""

import os
import sys
import gzip
import json
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any

def setup_logging(verbose: bool = False) -> None:
    """设置日志配置"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('decompress_web_data.log', encoding='utf-8')
        ]
    )

def decompress_file(gz_file_path: Path, output_path: Path = None) -> bool:
    """
    解压缩单个.gz文件
    
    Args:
        gz_file_path: 压缩文件路径
        output_path: 输出文件路径，如果为None则自动生成
        
    Returns:
        bool: 是否成功解压缩
    """
    logger = logging.getLogger(__name__)
    
    if not gz_file_path.exists():
        logger.error(f"压缩文件不存在: {gz_file_path}")
        return False
    
    if output_path is None:
        output_path = gz_file_path.with_suffix('')  # 移除.gz后缀
    
    try:
        with gzip.open(gz_file_path, 'rt', encoding='utf-8') as f_in:
            data = json.load(f_in)
        
        with open(output_path, 'w', encoding='utf-8') as f_out:
            json.dump(data, f_out, indent=2, ensure_ascii=False)
        
        logger.info(f"成功解压缩: {gz_file_path.name} -> {output_path.name}")
        return True
        
    except Exception as e:
        logger.error(f"解压缩失败 {gz_file_path.name}: {e}")
        return False

def decompress_web_data(web_display_dir: str, force: bool = False) -> bool:
    """
    解压缩网页数据目录中的所有.gz文件
    
    Args:
        web_display_dir: web_display目录路径
        force: 是否强制覆盖已存在的文件
        
    Returns:
        bool: 是否成功
    """
    logger = logging.getLogger(__name__)
    
    web_display_path = Path(web_display_dir)
    if not web_display_path.exists():
        logger.error(f"web_display目录不存在: {web_display_path}")
        return False
    
    violations_dir = web_display_path / "data" / "violations"
    if not violations_dir.exists():
        logger.error(f"violations目录不存在: {violations_dir}")
        return False
    
    # 查找所有.gz文件
    gz_files = list(violations_dir.glob("*.json.gz"))
    if not gz_files:
        logger.warning("未找到任何.json.gz文件")
        return True
    
    logger.info(f"找到 {len(gz_files)} 个压缩文件")
    
    success_count = 0
    skip_count = 0
    
    for gz_file in gz_files:
        output_file = gz_file.with_suffix('')  # 移除.gz后缀
        
        # 检查输出文件是否已存在
        if output_file.exists() and not force:
            logger.debug(f"跳过已存在的文件: {output_file.name}")
            skip_count += 1
            continue
        
        if decompress_file(gz_file, output_file):
            success_count += 1
    
    logger.info(f"解压缩完成: 成功 {success_count} 个, 跳过 {skip_count} 个")
    
    # 更新pagination_manifest.json以指向未压缩文件
    update_pagination_manifest(web_display_path)
    
    return success_count > 0 or skip_count > 0

def update_pagination_manifest(web_display_path: Path) -> bool:
    """
    更新pagination_manifest.json以指向未压缩文件
    
    Args:
        web_display_path: web_display目录路径
        
    Returns:
        bool: 是否成功更新
    """
    logger = logging.getLogger(__name__)
    
    manifest_path = web_display_path / "data" / "pagination_manifest.json"
    if not manifest_path.exists():
        logger.warning("pagination_manifest.json文件不存在")
        return False
    
    try:
        with open(manifest_path, 'r', encoding='utf-8') as f:
            manifest = json.load(f)
        
        # 更新压缩标志
        manifest['compressed'] = False
        
        # 更新文件名，移除.gz后缀
        for corner_case, info in manifest.get('corner_cases', {}).items():
            if 'files' in info:
                info['files'] = [f.replace('.gz', '') for f in info['files']]
        
        # 保存更新后的manifest
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2, ensure_ascii=False)
        
        logger.info("成功更新pagination_manifest.json")
        return True
        
    except Exception as e:
        logger.error(f"更新pagination_manifest.json失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="解压缩时序违例网页数据文件")
    parser.add_argument(
        "--web-display-dir",
        default="VIOLATION_CHECK/web_display",
        help="web_display目录路径 (默认: VIOLATION_CHECK/web_display)"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="强制覆盖已存在的文件"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="启用详细日志输出"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    logger.info("开始解压缩网页数据文件...")
    logger.info(f"web_display目录: {args.web_display_dir}")
    
    try:
        success = decompress_web_data(args.web_display_dir, args.force)
        
        if success:
            logger.info("解压缩完成！现在可以在浏览器中正常查看网页数据。")
            return 0
        else:
            logger.error("解压缩失败")
            return 1
            
    except Exception as e:
        logger.error(f"解压缩过程出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
