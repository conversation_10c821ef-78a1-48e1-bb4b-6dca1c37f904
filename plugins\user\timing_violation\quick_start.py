#!/usr/bin/env python3
"""
时序违例网页展示快速启动脚本

这个脚本提供一键式的数据生成和Web服务器启动功能。
"""

import os
import sys
import time
import subprocess
import webbrowser
from pathlib import Path
import argparse


def run_command(command, description, check=True):
    """运行命令并显示进度"""
    print(f"🔄 {description}...")
    try:
        if isinstance(command, str):
            result = subprocess.run(command, shell=True, check=check, 
                                  capture_output=True, text=True, encoding='utf-8')
        else:
            result = subprocess.run(command, check=check, 
                                  capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ {description} 出现异常: {e}")
        return False


def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    # 检查Python模块
    required_modules = ['openpyxl', 'pathlib']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少必要的Python模块: {', '.join(missing_modules)}")
        print("请运行: pip install " + " ".join(missing_modules))
        return False
    
    print("✅ 依赖项检查通过")
    return True


def generate_data():
    """生成网页数据"""
    script_path = Path("plugins/user/timing_violation/generate_optimized_web_data.py")
    if not script_path.exists():
        print(f"❌ 数据生成脚本不存在: {script_path}")
        return False
    
    command = [sys.executable, str(script_path)]
    return run_command(command, "生成网页数据")


def start_web_server(port=8000, open_browser=True):
    """启动Web服务器"""
    server_script = Path("plugins/user/timing_violation/start_web_server.py")
    if not server_script.exists():
        print(f"❌ Web服务器脚本不存在: {server_script}")
        return False
    
    print(f"🚀 启动Web服务器 (端口: {port})...")
    
    # 构建命令
    command = [sys.executable, str(server_script), "--port", str(port)]
    if not open_browser:
        command.append("--no-browser")
    
    try:
        # 在后台启动服务器
        process = subprocess.Popen(command, 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True, encoding='utf-8')
        
        # 等待服务器启动
        time.sleep(2)
        
        if process.poll() is None:  # 进程仍在运行
            print(f"✅ Web服务器已启动")
            print(f"📱 访问地址: http://localhost:{port}")
            print(f"🧪 测试页面: http://localhost:{port}/standalone_test.html")
            print(f"🔧 调试页面: http://localhost:{port}/test_data_loading.html")
            
            if open_browser:
                time.sleep(1)
                webbrowser.open(f'http://localhost:{port}/standalone_test.html')
            
            print("\n按 Ctrl+C 停止服务器")
            
            try:
                # 等待用户中断
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 正在停止服务器...")
                process.terminate()
                process.wait()
                print("✅ 服务器已停止")
            
            return True
        else:
            print("❌ Web服务器启动失败")
            stdout, stderr = process.communicate()
            if stderr:
                print(f"错误信息: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 启动Web服务器时出现异常: {e}")
        return False


def check_data_files():
    """检查数据文件是否存在"""
    print("📁 检查数据文件...")
    
    data_dir = Path("VIOLATION_CHECK/web_display/data")
    required_files = [
        "violations.json",
        "index.json",
        "statistics.json"
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = data_dir / file_name
        if not file_path.exists():
            missing_files.append(file_name)
    
    if missing_files:
        print(f"❌ 缺少数据文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 数据文件检查通过")
    return True


def main():
    parser = argparse.ArgumentParser(
        description="时序违例网页展示快速启动工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python quick_start.py                    # 完整流程：生成数据 + 启动服务器
  python quick_start.py --skip-generate    # 跳过数据生成，直接启动服务器
  python quick_start.py --port 8080        # 使用指定端口
  python quick_start.py --no-browser       # 不自动打开浏览器
        """
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        default=8000,
        help='Web服务器端口 (默认: 8000)'
    )
    
    parser.add_argument(
        '--skip-generate',
        action='store_true',
        help='跳过数据生成步骤'
    )
    
    parser.add_argument(
        '--no-browser',
        action='store_true',
        help='不自动打开浏览器'
    )
    
    parser.add_argument(
        '--check-only',
        action='store_true',
        help='仅检查环境和数据文件'
    )
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🚀 时序违例网页展示 - 快速启动工具")
    print("=" * 60)
    
    # 检查依赖项
    if not check_dependencies():
        sys.exit(1)
    
    # 如果只是检查模式
    if args.check_only:
        check_data_files()
        print("✅ 环境检查完成")
        return
    
    # 生成数据（除非跳过）
    if not args.skip_generate:
        if not generate_data():
            print("❌ 数据生成失败，无法继续")
            sys.exit(1)
    else:
        # 检查数据文件是否存在
        if not check_data_files():
            print("❌ 数据文件不存在，请先运行数据生成")
            print("提示: 移除 --skip-generate 参数来自动生成数据")
            sys.exit(1)
    
    # 启动Web服务器
    if not start_web_server(port=args.port, open_browser=not args.no_browser):
        print("❌ Web服务器启动失败")
        sys.exit(1)


if __name__ == "__main__":
    main()