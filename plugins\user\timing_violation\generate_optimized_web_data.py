#!/usr/bin/env python3
"""
优化的时序违例网页数据生成脚本

这个脚本实现了数据库优先的数据整理策略，通过查找数据库方式对数据进行规整（corner、case_name），
形成确认表格，将解析VIOLATION_CHECK目录作为备选方案。

主要特性：
1. 数据库优先策略 - 优先从SQLite数据库读取已确认的违例数据
2. 智能数据组织 - 按corner和case_name进行高效分组
3. Excel文件备选 - 当数据库不可用时自动回退到Excel文件解析
4. 性能优化 - 支持大数据集的分页和压缩
5. 错误恢复 - 全面的错误处理和数据验证

使用方法：
    python generate_optimized_web_data.py [--violation-check-dir VIOLATION_CHECK]
"""

import os
import sys
import logging
import argparse
from pathlib import Path
from typing import Optional

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from web_display.data_exporter import DataExporter
    from web_display.parsers.database_reader import DatabaseReader
    from web_display.parsers.excel_parser import ExcelParser
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保web_display模块在正确的位置")
    sys.exit(1)


def setup_logging(verbose: bool = False) -> None:
    """设置日志配置"""
    log_level = logging.DEBUG if verbose else logging.INFO
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('web_data_generation_optimized.log', encoding='utf-8')
        ]
    )


def validate_environment(violation_check_dir: Path) -> bool:
    """验证环境和依赖"""
    logger = logging.getLogger(__name__)
    
    # 检查VIOLATION_CHECK目录
    if not violation_check_dir.exists():
        logger.warning(f"VIOLATION_CHECK目录不存在: {violation_check_dir}")
        logger.info("将创建目录并继续...")
        violation_check_dir.mkdir(parents=True, exist_ok=True)
    
    # 检查数据库文件
    db_path = violation_check_dir / "timing_violations.db"
    if db_path.exists():
        logger.info(f"找到数据库文件: {db_path}")
        
        # 测试数据库连接
        try:
            db_reader = DatabaseReader(str(db_path))
            if db_reader.test_connection():
                logger.info("数据库连接测试成功")
                return True
            else:
                logger.warning("数据库连接测试失败，将使用Excel文件作为数据源")
        except Exception as e:
            logger.warning(f"数据库测试出错: {e}")
    else:
        logger.info("未找到数据库文件，将使用Excel文件作为数据源")
    
    # 检查Excel文件
    excel_files = list(violation_check_dir.glob("*.xlsx")) + list(violation_check_dir.glob("*.xls"))
    if excel_files:
        logger.info(f"找到 {len(excel_files)} 个Excel文件")
        return True
    else:
        logger.warning("未找到Excel文件")
    
    return True  # 即使没有数据文件也继续，让DataExporter处理


def analyze_data_sources(violation_check_dir: Path) -> dict:
    """分析可用的数据源"""
    logger = logging.getLogger(__name__)
    analysis = {
        'database_available': False,
        'database_records': 0,
        'excel_files_count': 0,
        'excel_records_estimate': 0,
        'recommended_strategy': 'database'
    }
    
    # 分析数据库
    db_path = violation_check_dir / "timing_violations.db"
    if db_path.exists():
        try:
            db_reader = DatabaseReader(str(db_path))
            db_info = db_reader.get_database_info()
            
            if not db_info.get('error'):
                analysis['database_available'] = True
                analysis['database_records'] = db_info.get('confirmed_count', 0)
                logger.info(f"数据库包含 {analysis['database_records']} 条已确认违例")
        except Exception as e:
            logger.warning(f"数据库分析失败: {e}")
    
    # 分析Excel文件
    excel_files = list(violation_check_dir.glob("*.xlsx")) + list(violation_check_dir.glob("*.xls"))
    analysis['excel_files_count'] = len(excel_files)
    
    if excel_files:
        # 估算Excel文件中的记录数（基于文件大小的粗略估计）
        total_size = sum(f.stat().st_size for f in excel_files)
        analysis['excel_records_estimate'] = max(100, int(total_size / 10240))  # 粗略估计：10KB per record
        logger.info(f"找到 {len(excel_files)} 个Excel文件，估计包含约 {analysis['excel_records_estimate']} 条记录")
    
    # 确定推荐策略
    if analysis['database_available'] and analysis['database_records'] > 0:
        analysis['recommended_strategy'] = 'database'
        logger.info("推荐使用数据库优先策略")
    elif analysis['excel_files_count'] > 0:
        analysis['recommended_strategy'] = 'excel'
        logger.info("推荐使用Excel文件策略")
    else:
        analysis['recommended_strategy'] = 'none'
        logger.warning("未找到可用的数据源")
    
    return analysis


def generate_web_data_optimized(violation_check_dir: Path, gui_violations: Optional[list] = None) -> bool:
    """
    生成优化的网页数据
    
    Args:
        violation_check_dir: VIOLATION_CHECK目录路径
        gui_violations: 来自GUI的违例数据（可选）
        
    Returns:
        bool: 成功返回True，失败返回False
    """
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("开始生成优化的时序违例网页数据")
        logger.info(f"VIOLATION_CHECK目录: {violation_check_dir}")
        
        # 分析数据源
        data_analysis = analyze_data_sources(violation_check_dir)
        logger.info(f"数据源分析完成: {data_analysis}")
        
        # 创建优化的数据导出器
        exporter = DataExporter(
            violation_check_dir=str(violation_check_dir),
            enable_performance_monitoring=True
        )
        
        # 执行数据导出
        success = exporter.export_all_data(gui_violations)
        
        if success:
            web_display_dir = violation_check_dir / "web_display"
            logger.info(f"网页数据生成成功！")
            logger.info(f"访问地址: file://{web_display_dir.absolute()}/index.html")
            
            # 显示统计信息
            if hasattr(exporter, 'statistics') and exporter.statistics:
                stats = exporter.statistics
                logger.info(f"数据统计:")
                logger.info(f"  - 总违例数: {len(exporter.violations_data)}")
                logger.info(f"  - Corner数: {len(exporter.corners)}")
                logger.info(f"  - Case数: {len(exporter.cases)}")
                
                if hasattr(exporter, 'performance_metrics'):
                    metrics = exporter.performance_metrics
                    logger.info(f"性能指标:")
                    logger.info(f"  - 总耗时: {metrics.get('total_duration', 0):.2f}秒")
                    logger.info(f"  - 数据加载: {metrics.get('data_loading_time', 0):.2f}秒")
                    logger.info(f"  - 数据导出: {metrics.get('export_time', 0):.2f}秒")
            
            return True
        else:
            logger.error("网页数据生成失败")
            return False
            
    except Exception as e:
        logger.error(f"生成网页数据时发生错误: {e}")
        logger.exception("详细错误信息:")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="优化的时序违例网页数据生成工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python generate_optimized_web_data.py
  python generate_optimized_web_data.py --violation-check-dir ./VIOLATION_CHECK
  python generate_optimized_web_data.py --verbose
        """
    )
    
    parser.add_argument(
        '--violation-check-dir',
        type=str,
        default='VIOLATION_CHECK',
        help='VIOLATION_CHECK目录路径 (默认: VIOLATION_CHECK)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='启用详细日志输出'
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    # 转换路径
    violation_check_dir = Path(args.violation_check_dir).resolve()
    
    logger.info("=" * 60)
    logger.info("优化的时序违例网页数据生成工具")
    logger.info("=" * 60)
    logger.info(f"VIOLATION_CHECK目录: {violation_check_dir}")
    logger.info(f"详细日志: {'启用' if args.verbose else '禁用'}")
    logger.info("-" * 60)
    
    # 验证环境
    if not validate_environment(violation_check_dir):
        logger.error("环境验证失败")
        sys.exit(1)
    
    # 生成网页数据
    success = generate_web_data_optimized(violation_check_dir)
    
    if success:
        logger.info("=" * 60)
        logger.info("网页数据生成完成！")
        logger.info(f"请打开浏览器访问: file://{violation_check_dir.absolute()}/web_display/index.html")
        logger.info("=" * 60)
        sys.exit(0)
    else:
        logger.error("=" * 60)
        logger.error("网页数据生成失败！请查看日志了解详细信息。")
        logger.error("=" * 60)
        sys.exit(1)


if __name__ == "__main__":
    main()