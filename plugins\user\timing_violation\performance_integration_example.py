"""
性能监控系统集成示例

展示如何将综合性能监控系统集成到现有的时序违例插件中。
"""

from typing import Dict, List, Optional
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QMessageBox

try:
    from .comprehensive_performance_system import ComprehensivePerformanceSystem
except ImportError:
    from comprehensive_performance_system import ComprehensivePerformanceSystem


class PerformanceIntegratedViolationProcessor(QObject):
    """集成性能监控的违例处理器示例"""
    
    # 信号定义
    processing_started = pyqtSignal(int)  # 违例数量
    processing_completed = pyqtSignal(dict)  # 处理结果
    performance_alert = pyqtSignal(str, dict)  # 性能警报
    
    def __init__(self):
        super().__init__()
        
        # 初始化性能监控系统
        self.performance_system = ComprehensivePerformanceSystem()
        
        # 连接性能系统信号
        self.performance_system.performance_alert.connect(self.performance_alert)
        self.performance_system.optimization_recommended.connect(self._on_optimization_recommended)
        
        # 当前处理状态
        self.current_session_id = None
        self.processing_active = False
        
    def process_violation_file(self, file_path: str, violation_count: int) -> Dict:
        """处理违例文件（集成性能监控）
        
        Args:
            file_path: 文件路径
            violation_count: 违例数量
            
        Returns:
            Dict: 处理结果和性能报告
        """
        # 开始性能监控
        self.current_session_id = self.performance_system.start_performance_monitoring(
            operation_type='load',
            violation_count=violation_count,
            processing_stage='parsing'
        )
        
        self.processing_active = True
        self.processing_started.emit(violation_count)
        
        try:
            # 模拟文件处理过程
            import time
            start_time = time.time()
            
            # 模拟解析过程
            self._simulate_parsing_process(violation_count)
            
            # 跟踪处理性能
            elapsed_time = time.time() - start_time
            self.performance_system.track_violation_processing(violation_count, elapsed_time)
            
            # 模拟UI渲染过程
            self._simulate_ui_rendering(violation_count)
            
            # 停止性能监控并获取报告
            performance_result = self.performance_system.stop_performance_monitoring()
            
            processing_result = {
                'success': True,
                'violations_processed': violation_count,
                'processing_time': elapsed_time,
                'performance_data': performance_result
            }
            
            self.processing_completed.emit(processing_result)
            return processing_result
            
        except Exception as e:
            # 确保在异常情况下也停止监控
            if self.processing_active:
                self.performance_system.stop_performance_monitoring()
            
            error_result = {
                'success': False,
                'error': str(e),
                'violations_processed': 0
            }
            
            self.processing_completed.emit(error_result)
            return error_result
            
        finally:
            self.processing_active = False
            self.current_session_id = None
            
    def _simulate_parsing_process(self, violation_count: int):
        """模拟解析过程"""
        import time
        
        # 根据违例数量模拟不同的处理时间
        if violation_count < 2000:
            # 小数据集：快速处理
            time.sleep(0.1)
        elif violation_count < 20000:
            # 中等数据集：中等处理时间
            time.sleep(0.5)
        else:
            # 大数据集：较长处理时间
            time.sleep(1.5)
            
        # 模拟批量处理
        batch_size = min(1000, violation_count)
        processed = 0
        
        while processed < violation_count:
            batch_end = min(processed + batch_size, violation_count)
            batch_violations = batch_end - processed
            
            # 模拟批处理时间
            time.sleep(0.01 * (batch_violations / 100))
            
            processed = batch_end
            
            # 跟踪处理进度
            if self.processing_active:
                elapsed = time.time()
                self.performance_system.track_violation_processing(processed, elapsed)
                
    def _simulate_ui_rendering(self, violation_count: int):
        """模拟UI渲染过程"""
        import time
        
        # 模拟不同UI操作的响应时间
        ui_operations = [
            ('table_creation', 50 + (violation_count / 1000) * 10),
            ('data_binding', 30 + (violation_count / 1000) * 5),
            ('pagination_setup', 20 + (violation_count / 10000) * 50),
            ('ui_refresh', 40 + (violation_count / 1000) * 8)
        ]
        
        for operation, base_time_ms in ui_operations:
            # 模拟操作执行时间
            actual_time_ms = base_time_ms * (0.8 + 0.4 * __import__('random').random())
            time.sleep(actual_time_ms / 1000)  # 转换为秒
            
            # 跟踪UI响应时间
            if self.processing_active:
                self.performance_system.track_ui_interaction(operation, actual_time_ms)
                
    def _on_optimization_recommended(self, recommendation_data: Dict):
        """处理优化推荐"""
        suggestions = recommendation_data.get('suggestions', [])
        violation_count = recommendation_data.get('violation_count', 0)
        
        if suggestions:
            # 显示优化建议对话框
            self._show_optimization_dialog(suggestions, violation_count)
            
    def _show_optimization_dialog(self, suggestions: List[Dict], violation_count: int):
        """显示优化建议对话框"""
        message = f"检测到 {violation_count} 个违例的性能问题，建议进行以下优化：\n\n"
        
        for i, suggestion in enumerate(suggestions[:3], 1):  # 只显示前3个建议
            message += f"{i}. [{suggestion['priority'].upper()}] {suggestion['title']}\n"
            message += f"   {suggestion['description']}\n"
            message += f"   预期改进: {suggestion['expected_improvement']}\n\n"
        
        message += "是否要应用这些优化建议？"
        
        reply = QMessageBox.question(
            None, 
            "性能优化建议", 
            message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            # 应用优化建议
            success = self.performance_system.apply_optimization_suggestions()
            if success:
                QMessageBox.information(None, "优化完成", "性能优化已成功应用！")
            else:
                QMessageBox.warning(None, "优化失败", "性能优化应用失败，请检查系统状态。")
                
    def get_performance_status_widget(self) -> QWidget:
        """获取性能状态显示控件"""
        return self.performance_system.get_performance_status_widget()
        
    def generate_performance_report(self, report_type: str = 'summary') -> Dict:
        """生成性能报告"""
        return self.performance_system.generate_performance_report(report_type)
        
    def export_performance_data(self, format: str = 'json') -> str:
        """导出性能数据"""
        return self.performance_system.export_performance_data(format)


class PerformanceAwareMainWindow:
    """集成性能监控的主窗口示例"""
    
    def __init__(self):
        # 初始化性能集成处理器
        self.violation_processor = PerformanceIntegratedViolationProcessor()
        
        # 连接信号
        self.violation_processor.processing_started.connect(self._on_processing_started)
        self.violation_processor.processing_completed.connect(self._on_processing_completed)
        self.violation_processor.performance_alert.connect(self._on_performance_alert)
        
        # 性能状态控件
        self.performance_status_widget = None
        
    def load_violation_file(self, file_path: str, estimated_violation_count: int):
        """加载违例文件（性能监控版本）"""
        print(f"Loading violation file: {file_path} (estimated {estimated_violation_count} violations)")
        
        # 显示性能状态控件
        if self.performance_status_widget is None:
            self.performance_status_widget = self.violation_processor.get_performance_status_widget()
            # 这里应该将控件添加到主窗口的布局中
            
        # 开始处理
        result = self.violation_processor.process_violation_file(file_path, estimated_violation_count)
        
        return result
        
    def _on_processing_started(self, violation_count: int):
        """处理开始回调"""
        print(f"Processing started for {violation_count} violations")
        # 这里可以显示进度条或状态信息
        
    def _on_processing_completed(self, result: Dict):
        """处理完成回调"""
        if result.get('success'):
            print(f"Processing completed successfully in {result.get('processing_time', 0):.2f}s")
            
            # 显示性能摘要
            performance_data = result.get('performance_data', {})
            if performance_data:
                session_summary = performance_data.get('session_summary', {})
                print(f"Performance summary:")
                print(f"  - Average violations/second: {session_summary.get('avg_violations_per_second', 0):.0f}")
                print(f"  - Average UI response time: {session_summary.get('avg_ui_response_time_ms', 0):.0f}ms")
                print(f"  - Peak memory usage: {session_summary.get('peak_memory_usage_mb', 0):.0f}MB")
                
        else:
            print(f"Processing failed: {result.get('error', 'Unknown error')}")
            
    def _on_performance_alert(self, alert_type: str, data: Dict):
        """性能警报回调"""
        print(f"Performance alert: {alert_type}")
        
        if alert_type == 'ui_response_critical':
            print(f"Critical UI response time: {data.get('ui_response_time_ms', 0):.0f}ms")
        elif alert_type == 'memory_usage_critical':
            print(f"Critical memory usage: {data.get('memory_usage_mb', 0):.0f}MB")
            
        # 这里可以显示用户通知或自动应用优化
        
    def show_performance_report(self):
        """显示性能报告"""
        report = self.violation_processor.generate_performance_report('summary')
        
        if 'error' not in report:
            print("Performance Report Summary:")
            print(f"  - Total sessions: {report.get('total_sessions', 0)}")
            print(f"  - Average violations/second: {report.get('avg_violations_per_second', 0):.0f}")
            print(f"  - Average UI response time: {report.get('avg_ui_response_time_ms', 0):.0f}ms")
            print(f"  - Average memory usage: {report.get('avg_memory_usage_mb', 0):.0f}MB")
            print(f"  - Average performance score: {report.get('avg_performance_score', 0):.1f}/100")
            
            common_issues = report.get('common_issues', [])
            if common_issues:
                print("  - Common issues:")
                for issue in common_issues[:3]:
                    print(f"    * {issue}")
                    
            top_optimizations = report.get('top_optimizations', [])
            if top_optimizations:
                print("  - Top optimization suggestions:")
                for opt in top_optimizations[:3]:
                    print(f"    * {opt}")
        else:
            print(f"No performance data available: {report.get('error')}")


# 使用示例
def example_usage():
    """使用示例"""
    # 创建性能感知的主窗口
    main_window = PerformanceAwareMainWindow()
    
    # 模拟加载不同大小的违例文件
    test_cases = [
        ("small_violations.log", 1500),    # 小数据集
        ("medium_violations.log", 8000),   # 中等数据集
        ("large_violations.log", 25000),   # 大数据集
    ]
    
    for file_path, violation_count in test_cases:
        print(f"\n{'='*50}")
        print(f"Testing with {file_path} ({violation_count} violations)")
        print(f"{'='*50}")
        
        # 加载文件
        result = main_window.load_violation_file(file_path, violation_count)
        
        # 等待一段时间让性能监控收集数据
        import time
        time.sleep(1)
        
        # 显示性能报告
        main_window.show_performance_report()
        
        print(f"Test completed for {file_path}")


if __name__ == "__main__":
    # 运行示例（仅用于测试）
    example_usage()