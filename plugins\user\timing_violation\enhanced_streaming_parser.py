"""
增强流式解析器

专门用于处理超大违例数据集（>20K违例），支持违例感知分块和内存压力检测。
"""

import os
import gc
import time
import psutil
import re
from typing import List, Dict, Optional, Callable, Generator, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QThread


class ViolationAwareChunker(QObject):
    """违例感知分块器
    
    将大文件按违例记录进行智能分块，而不是按任意行数分块
    """
    
    def __init__(self):
        super().__init__()
        
        # 分块配置
        self.chunk_config = {
            'target_violations_per_chunk': 1000,  # 每块目标违例数
            'min_violations_per_chunk': 100,      # 每块最小违例数
            'max_violations_per_chunk': 5000,     # 每块最大违例数
            'buffer_size': 1024 * 1024,           # 1MB读取缓冲区
            'violation_separator': '----'          # 违例分隔符
        }
        
        # 统计信息
        self.chunking_stats = {
            'total_chunks': 0,
            'total_violations': 0,
            'average_violations_per_chunk': 0,
            'chunking_time': 0
        }
    
    def chunk_file_by_violations(self, file_path: str, 
                               target_violations_per_chunk: Optional[int] = None) -> Generator[Tuple[List[str], int], None, None]:
        """按违例记录分块文件
        
        Args:
            file_path: 文件路径
            target_violations_per_chunk: 每块目标违例数
            
        Yields:
            Tuple[List[str], int]: (违例行列表, 违例数量)
        """
        start_time = time.time()
        
        if target_violations_per_chunk:
            self.chunk_config['target_violations_per_chunk'] = target_violations_per_chunk
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                current_chunk_lines = []
                current_violation_count = 0
                current_violation_lines = []
                
                for line in f:
                    line = line.strip()
                    current_chunk_lines.append(line)
                    current_violation_lines.append(line)
                    
                    # 检测违例结束标记
                    if line.startswith(self.chunk_config['violation_separator']):
                        if current_violation_lines:
                            current_violation_count += 1
                            current_violation_lines = []
                        
                        # 检查是否达到分块大小
                        if current_violation_count >= self.chunk_config['target_violations_per_chunk']:
                            yield (current_chunk_lines, current_violation_count)
                            
                            # 更新统计
                            self.chunking_stats['total_chunks'] += 1
                            self.chunking_stats['total_violations'] += current_violation_count
                            
                            # 重置当前块
                            current_chunk_lines = []
                            current_violation_count = 0
                
                # 处理最后一个块
                if current_chunk_lines and current_violation_count > 0:
                    yield (current_chunk_lines, current_violation_count)
                    self.chunking_stats['total_chunks'] += 1
                    self.chunking_stats['total_violations'] += current_violation_count
        
        except Exception as e:
            print(f"文件分块失败: {str(e)}")
            raise
        
        # 更新统计信息
        self.chunking_stats['chunking_time'] = time.time() - start_time
        if self.chunking_stats['total_chunks'] > 0:
            self.chunking_stats['average_violations_per_chunk'] = (
                self.chunking_stats['total_violations'] / self.chunking_stats['total_chunks']
            )
    
    def estimate_chunks_count(self, file_path: str, target_violations_per_chunk: Optional[int] = None) -> int:
        """估算分块数量
        
        Args:
            file_path: 文件路径
            target_violations_per_chunk: 每块目标违例数
            
        Returns:
            int: 估算的分块数量
        """
        try:
            file_size = os.path.getsize(file_path)
            
            # 基于5行每违例的估算
            estimated_lines = file_size / 50  # 假设平均每行50字节
            estimated_violations = int(estimated_lines / 5)
            
            target_size = target_violations_per_chunk or self.chunk_config['target_violations_per_chunk']
            estimated_chunks = max(1, (estimated_violations + target_size - 1) // target_size)
            
            return estimated_chunks
            
        except Exception as e:
            print(f"估算分块数量失败: {str(e)}")
            return 1
    
    def get_chunking_statistics(self) -> Dict:
        """获取分块统计信息
        
        Returns:
            Dict: 分块统计信息
        """
        return self.chunking_stats.copy()
    
    def reset_statistics(self):
        """重置统计信息"""
        self.chunking_stats = {
            'total_chunks': 0,
            'total_violations': 0,
            'average_violations_per_chunk': 0,
            'chunking_time': 0
        }


class MemoryPressureDetector(QObject):
    """内存压力检测器"""
    
    # 信号定义
    memory_pressure_detected = pyqtSignal(float, str, dict)  # 内存使用率, 压力等级, 详细信息
    memory_pressure_relieved = pyqtSignal(float)  # 内存使用率
    
    def __init__(self):
        super().__init__()
        
        # 压力阈值
        self.pressure_thresholds = {
            'low': 0.6,      # 60% - 低压力
            'medium': 0.75,  # 75% - 中等压力
            'high': 0.85,    # 85% - 高压力
            'critical': 0.95 # 95% - 严重压力
        }
        
        # 检测配置
        self.detection_config = {
            'check_interval': 1.0,  # 检测间隔（秒）
            'pressure_duration_threshold': 3.0,  # 持续压力阈值（秒）
            'relief_threshold': 0.7  # 压力缓解阈值
        }
        
        # 状态跟踪
        self.current_pressure_level = 'normal'
        self.pressure_start_time = None
        self.last_check_time = 0
        self.pressure_history = []
    
    def check_memory_pressure(self) -> Dict:
        """检查内存压力
        
        Returns:
            Dict: 内存压力信息
        """
        try:
            current_time = time.time()
            
            # 获取内存信息
            memory_info = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info()
            
            memory_usage_percent = memory_info.percent / 100.0
            available_memory_gb = memory_info.available / (1024 ** 3)
            process_memory_mb = process_memory.rss / (1024 ** 2)
            
            # 确定压力等级
            pressure_level = self._determine_pressure_level(memory_usage_percent)
            
            # 创建压力信息
            pressure_info = {
                'timestamp': current_time,
                'memory_usage_percent': memory_usage_percent,
                'available_memory_gb': available_memory_gb,
                'process_memory_mb': process_memory_mb,
                'pressure_level': pressure_level,
                'total_memory_gb': memory_info.total / (1024 ** 3),
                'swap_usage_percent': psutil.swap_memory().percent / 100.0 if psutil.swap_memory().total > 0 else 0
            }
            
            # 记录历史
            self.pressure_history.append(pressure_info)
            if len(self.pressure_history) > 100:  # 保持最近100次检测记录
                self.pressure_history = self.pressure_history[-50:]
            
            # 检测压力变化
            self._handle_pressure_change(pressure_level, pressure_info)
            
            self.last_check_time = current_time
            return pressure_info
            
        except Exception as e:
            print(f"内存压力检测失败: {str(e)}")
            return {
                'timestamp': time.time(),
                'memory_usage_percent': 0.5,
                'pressure_level': 'unknown',
                'error': str(e)
            }
    
    def _determine_pressure_level(self, memory_usage_percent: float) -> str:
        """确定压力等级
        
        Args:
            memory_usage_percent: 内存使用百分比 (0.0-1.0)
            
        Returns:
            str: 压力等级
        """
        if memory_usage_percent >= self.pressure_thresholds['critical']:
            return 'critical'
        elif memory_usage_percent >= self.pressure_thresholds['high']:
            return 'high'
        elif memory_usage_percent >= self.pressure_thresholds['medium']:
            return 'medium'
        elif memory_usage_percent >= self.pressure_thresholds['low']:
            return 'low'
        else:
            return 'normal'
    
    def _handle_pressure_change(self, new_pressure_level: str, pressure_info: Dict):
        """处理压力等级变化
        
        Args:
            new_pressure_level: 新的压力等级
            pressure_info: 压力信息
        """
        current_time = time.time()
        
        if new_pressure_level != self.current_pressure_level:
            if new_pressure_level in ['medium', 'high', 'critical']:
                # 压力增加
                if self.current_pressure_level == 'normal':
                    self.pressure_start_time = current_time
                
                # 检查是否持续压力
                if (self.pressure_start_time and 
                    current_time - self.pressure_start_time >= self.detection_config['pressure_duration_threshold']):
                    self.memory_pressure_detected.emit(
                        pressure_info['memory_usage_percent'], 
                        new_pressure_level, 
                        pressure_info
                    )
            
            elif new_pressure_level in ['low', 'normal'] and self.current_pressure_level in ['medium', 'high', 'critical']:
                # 压力缓解
                self.memory_pressure_relieved.emit(pressure_info['memory_usage_percent'])
                self.pressure_start_time = None
            
            self.current_pressure_level = new_pressure_level
    
    def get_pressure_trend(self, window_size: int = 10) -> Dict:
        """获取压力趋势
        
        Args:
            window_size: 分析窗口大小
            
        Returns:
            Dict: 压力趋势信息
        """
        if len(self.pressure_history) < window_size:
            return {'trend': 'insufficient_data', 'confidence': 0.0}
        
        recent_history = self.pressure_history[-window_size:]
        memory_usages = [p['memory_usage_percent'] for p in recent_history]
        
        # 计算趋势
        if len(memory_usages) >= 2:
            trend_slope = (memory_usages[-1] - memory_usages[0]) / len(memory_usages)
            
            if trend_slope > 0.02:  # 内存使用增长超过2%
                trend = 'increasing'
            elif trend_slope < -0.02:  # 内存使用下降超过2%
                trend = 'decreasing'
            else:
                trend = 'stable'
        else:
            trend = 'stable'
        
        # 计算置信度
        variance = max(memory_usages) - min(memory_usages)
        confidence = max(0.0, min(1.0, 1.0 - variance))
        
        return {
            'trend': trend,
            'confidence': confidence,
            'current_usage': memory_usages[-1],
            'average_usage': sum(memory_usages) / len(memory_usages),
            'variance': variance
        }


class EnhancedStreamingParser(QObject):
    """增强流式解析器
    
    专门用于处理超大违例数据集（>20K违例）
    """
    
    # 信号定义
    parsing_progress = pyqtSignal(int, str, dict)  # 进度, 消息, 统计信息
    chunk_processed = pyqtSignal(int, int, list)   # 块索引, 总块数, 违例列表
    memory_pressure_handled = pyqtSignal(str, dict)  # 处理动作, 压力信息
    parsing_completed = pyqtSignal(list, dict)     # 解析结果, 统计信息
    parsing_failed = pyqtSignal(str, dict)         # 错误信息, 错误详情
    
    def __init__(self):
        super().__init__()
        
        # 核心组件
        self.chunker = ViolationAwareChunker()
        self.memory_detector = MemoryPressureDetector()
        
        # 解析配置
        self.parsing_config = {
            'violations_per_chunk': 1000,      # 每块违例数
            'memory_check_interval': 5,        # 内存检查间隔（块数）
            'gc_interval': 10,                 # 垃圾回收间隔（块数）
            'max_memory_usage': 0.85,          # 最大内存使用率
            'chunk_processing_timeout': 30.0,  # 块处理超时（秒）
            'enable_adaptive_chunking': True   # 启用自适应分块
        }
        
        # 时间单位转换正则表达式
        self.time_pattern = re.compile(r'(\d+(?:\.\d+)?)\s*([A-Z]*)')
        
        # 解析统计
        self.parsing_stats = {
            'total_chunks': 0,
            'processed_chunks': 0,
            'total_violations': 0,
            'processing_time': 0,
            'memory_pressure_events': 0,
            'gc_collections': 0,
            'chunk_processing_times': [],
            'peak_memory_usage': 0
        }
        
        # 连接信号
        self.memory_detector.memory_pressure_detected.connect(self._handle_memory_pressure)
        self.memory_detector.memory_pressure_relieved.connect(self._handle_memory_relief)
    
    def parse_large_file_streaming(self, file_path: str, 
                                 progress_callback: Optional[Callable[[int, str], None]] = None) -> List[Dict]:
        """流式解析大文件
        
        Args:
            file_path: 文件路径
            progress_callback: 进度回调函数
            
        Returns:
            List[Dict]: 解析结果
        """
        start_time = time.time()
        
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 ** 2)
            
            if progress_callback:
                progress_callback(0, f"开始流式解析大文件 ({file_size_mb:.1f}MB)...")
            
            # 估算分块数量
            estimated_chunks = self.chunker.estimate_chunks_count(
                file_path, self.parsing_config['violations_per_chunk']
            )
            
            self.parsing_stats['total_chunks'] = estimated_chunks
            
            if progress_callback:
                progress_callback(5, f"预计分为 {estimated_chunks} 个块进行处理...")
            
            # 开始分块解析
            all_violations = []
            chunk_index = 0
            
            for chunk_lines, violation_count in self.chunker.chunk_file_by_violations(
                file_path, self.parsing_config['violations_per_chunk']
            ):
                chunk_start_time = time.time()
                
                # 内存压力检测
                if chunk_index % self.parsing_config['memory_check_interval'] == 0:
                    pressure_info = self.memory_detector.check_memory_pressure()
                    self._update_peak_memory_usage(pressure_info.get('memory_usage_percent', 0))
                
                # 处理当前块
                try:
                    chunk_violations = self._process_chunk(chunk_lines, violation_count)
                    all_violations.extend(chunk_violations)
                    
                    # 记录块处理时间
                    chunk_time = time.time() - chunk_start_time
                    self.parsing_stats['chunk_processing_times'].append(chunk_time)
                    
                    # 发出块处理完成信号
                    self.chunk_processed.emit(chunk_index + 1, estimated_chunks, chunk_violations)
                    
                except Exception as chunk_error:
                    print(f"块 {chunk_index + 1} 处理失败: {str(chunk_error)}")
                    # 继续处理下一个块
                    continue
                
                # 更新进度
                chunk_index += 1
                self.parsing_stats['processed_chunks'] = chunk_index
                progress = int((chunk_index / estimated_chunks) * 90)  # 保留10%用于最终处理
                
                if progress_callback:
                    avg_chunk_time = sum(self.parsing_stats['chunk_processing_times']) / len(self.parsing_stats['chunk_processing_times'])
                    remaining_chunks = estimated_chunks - chunk_index
                    estimated_remaining_time = remaining_chunks * avg_chunk_time
                    
                    progress_callback(progress, 
                        f"已处理 {chunk_index}/{estimated_chunks} 块，"
                        f"获得 {len(all_violations):,} 条违例，"
                        f"预计剩余 {estimated_remaining_time:.1f}秒")
                
                # 定期垃圾回收
                if chunk_index % self.parsing_config['gc_interval'] == 0:
                    self._perform_garbage_collection()
                
                # 发出进度信号
                chunk_stats = {
                    'chunk_violations': len(chunk_violations),
                    'total_violations': len(all_violations),
                    'processing_time': chunk_time,
                    'violations_per_second': len(chunk_violations) / chunk_time if chunk_time > 0 else 0
                }
                self.parsing_progress.emit(progress, f"块 {chunk_index} 处理完成", chunk_stats)
            
            # 最终处理
            if progress_callback:
                progress_callback(95, f"完成分块处理，正在整理结果...")
            
            # 更新最终统计
            total_time = time.time() - start_time
            self.parsing_stats.update({
                'total_violations': len(all_violations),
                'processing_time': total_time,
                'average_violations_per_second': len(all_violations) / total_time if total_time > 0 else 0,
                'average_chunk_time': sum(self.parsing_stats['chunk_processing_times']) / len(self.parsing_stats['chunk_processing_times']) if self.parsing_stats['chunk_processing_times'] else 0
            })
            
            if progress_callback:
                progress_callback(100, 
                    f"流式解析完成！共处理 {len(all_violations):,} 条违例，"
                    f"耗时 {total_time:.2f}秒，"
                    f"平均速度 {len(all_violations) / total_time:.0f} 违例/秒")
            
            # 发出完成信号
            self.parsing_completed.emit(all_violations, self.parsing_stats)
            
            return all_violations
            
        except Exception as e:
            error_msg = f"流式解析失败: {str(e)}"
            error_details = {
                'file_path': file_path,
                'processing_time': time.time() - start_time,
                'processed_chunks': self.parsing_stats.get('processed_chunks', 0),
                'total_violations': self.parsing_stats.get('total_violations', 0)
            }
            
            print(error_msg)
            self.parsing_failed.emit(error_msg, error_details)
            raise
    
    def _process_chunk(self, chunk_lines: List[str], expected_violations: int) -> List[Dict]:
        """处理单个块
        
        Args:
            chunk_lines: 块的行数据
            expected_violations: 预期违例数量
            
        Returns:
            List[Dict]: 处理后的违例列表
        """
        violations = []
        current_violation = {}
        
        for line in chunk_lines:
            if not line:
                continue
            
            # 检测违例结束标记
            if line.startswith('----'):
                if current_violation:
                    if self._validate_violation(current_violation):
                        processed_violation = self._process_violation(current_violation)
                        violations.append(processed_violation)
                    current_violation = {}
                continue
            
            # 解析键值对
            colon_pos = line.find(' : ')
            if colon_pos != -1:
                key = line[:colon_pos].strip()
                value = line[colon_pos + 3:].strip()
                current_violation[key] = value
            else:
                # 处理多行内容
                if current_violation and 'Check' in current_violation:
                    current_violation['Check'] += ' ' + line
        
        # 处理最后一个违例
        if current_violation:
            if self._validate_violation(current_violation):
                processed_violation = self._process_violation(current_violation)
                violations.append(processed_violation)
        
        return violations
    
    def _validate_violation(self, violation: Dict) -> bool:
        """验证违例条目的完整性
        
        Args:
            violation: 违例条目
            
        Returns:
            bool: 是否有效
        """
        required_fields = ['NUM', 'Hier', 'Time', 'Check']
        return all(field in violation and violation[field] for field in required_fields)
    
    def _process_violation(self, violation: Dict) -> Dict:
        """处理违例条目
        
        Args:
            violation: 原始违例条目
            
        Returns:
            Dict: 处理后的违例条目
        """
        processed = violation.copy()
        
        # 转换NUM为整数
        try:
            processed['NUM'] = int(violation['NUM'])
        except ValueError:
            processed['NUM'] = 0
        
        # 转换时间单位
        time_str = violation['Time']
        processed['time_fs'] = self._convert_time_to_fs(time_str)
        processed['time_ns'] = self._convert_time_to_ns(time_str)
        
        return processed
    
    def _convert_time_to_fs(self, time_str: str) -> int:
        """转换时间单位到飞秒"""
        time_str = time_str.upper().strip()
        match = self.time_pattern.match(time_str)
        if not match:
            return 0
        
        value_str, unit = match.groups()
        try:
            value = float(value_str)
        except ValueError:
            return 0
        
        unit_multipliers = {'FS': 1, '': 1, 'PS': 1000, 'NS': 1000000}
        return int(value * unit_multipliers.get(unit, 1))
    
    def _convert_time_to_ns(self, time_str: str) -> float:
        """转换时间单位到纳秒"""
        time_str = time_str.upper().strip()
        match = self.time_pattern.match(time_str)
        if not match:
            return 0.0
        
        value_str, unit = match.groups()
        try:
            value = float(value_str)
        except ValueError:
            return 0.0
        
        unit_divisors = {'NS': 1.0, '': 1.0, 'PS': 1000.0, 'FS': 1000000.0}
        return value / unit_divisors.get(unit, 1.0)
    
    def _handle_memory_pressure(self, memory_usage: float, pressure_level: str, pressure_info: Dict):
        """处理内存压力
        
        Args:
            memory_usage: 内存使用率
            pressure_level: 压力等级
            pressure_info: 压力详细信息
        """
        self.parsing_stats['memory_pressure_events'] += 1
        
        action_taken = "none"
        
        if pressure_level == 'critical':
            # 严重压力：强制垃圾回收并减小块大小
            self._perform_garbage_collection()
            if self.parsing_config['violations_per_chunk'] > 100:
                self.parsing_config['violations_per_chunk'] = max(100, 
                    int(self.parsing_config['violations_per_chunk'] * 0.5))
                action_taken = "reduced_chunk_size_and_gc"
            else:
                action_taken = "garbage_collection_only"
        
        elif pressure_level == 'high':
            # 高压力：减小块大小
            if self.parsing_config['violations_per_chunk'] > 200:
                self.parsing_config['violations_per_chunk'] = max(200, 
                    int(self.parsing_config['violations_per_chunk'] * 0.7))
                action_taken = "reduced_chunk_size"
        
        elif pressure_level == 'medium':
            # 中等压力：适度减小块大小
            if self.parsing_config['violations_per_chunk'] > 500:
                self.parsing_config['violations_per_chunk'] = max(500, 
                    int(self.parsing_config['violations_per_chunk'] * 0.8))
                action_taken = "slightly_reduced_chunk_size"
        
        print(f"内存压力处理: {pressure_level} ({memory_usage:.1%}) -> {action_taken}")
        self.memory_pressure_handled.emit(action_taken, pressure_info)
    
    def _handle_memory_relief(self, memory_usage: float):
        """处理内存压力缓解
        
        Args:
            memory_usage: 当前内存使用率
        """
        # 内存压力缓解时，可以适度增加块大小
        if (memory_usage < 0.6 and 
            self.parsing_config['violations_per_chunk'] < 1000):
            self.parsing_config['violations_per_chunk'] = min(1000, 
                int(self.parsing_config['violations_per_chunk'] * 1.2))
            print(f"内存压力缓解，增加块大小到: {self.parsing_config['violations_per_chunk']}")
    
    def _perform_garbage_collection(self):
        """执行垃圾回收"""
        try:
            gc.collect()
            self.parsing_stats['gc_collections'] += 1
        except Exception as e:
            print(f"垃圾回收失败: {str(e)}")
    
    def _update_peak_memory_usage(self, current_usage: float):
        """更新峰值内存使用"""
        if current_usage > self.parsing_stats['peak_memory_usage']:
            self.parsing_stats['peak_memory_usage'] = current_usage
    
    def get_parsing_statistics(self) -> Dict:
        """获取解析统计信息
        
        Returns:
            Dict: 统计信息
        """
        stats = self.parsing_stats.copy()
        
        # 添加分块统计
        stats['chunking_stats'] = self.chunker.get_chunking_statistics()
        
        # 添加内存压力趋势
        stats['memory_pressure_trend'] = self.memory_detector.get_pressure_trend()
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.parsing_stats = {
            'total_chunks': 0,
            'processed_chunks': 0,
            'total_violations': 0,
            'processing_time': 0,
            'memory_pressure_events': 0,
            'gc_collections': 0,
            'chunk_processing_times': [],
            'peak_memory_usage': 0
        }
        
        self.chunker.reset_statistics()
        self.memory_detector.pressure_history.clear()
        self.memory_detector.current_pressure_level = 'normal'


class EnhancedStreamingAsyncParser(QThread):
    """增强流式异步解析器"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str, dict)  # 进度, 消息, 统计信息
    parsing_completed = pyqtSignal(list, dict)     # 解析结果, 统计信息
    parsing_failed = pyqtSignal(str, dict)         # 错误信息, 错误详情
    
    def __init__(self, file_path: str):
        super().__init__()
        self.file_path = file_path
        self.streaming_parser = EnhancedStreamingParser()
        self._is_cancelled = False
        
        # 连接内部信号
        self.streaming_parser.parsing_progress.connect(self._forward_progress)
        self.streaming_parser.parsing_completed.connect(self._forward_completion)
        self.streaming_parser.parsing_failed.connect(self._forward_failure)
    
    def cancel(self):
        """取消解析"""
        self._is_cancelled = True
    
    def run(self):
        """异步解析线程主函数"""
        try:
            if self._is_cancelled:
                return
            
            # 创建进度回调
            def progress_callback(progress: int, message: str):
                if not self._is_cancelled:
                    self.progress_updated.emit(progress, f"[增强流式] {message}", {})
            
            # 执行流式解析
            violations = self.streaming_parser.parse_large_file_streaming(
                self.file_path, progress_callback
            )
            
            if not self._is_cancelled:
                stats = self.streaming_parser.get_parsing_statistics()
                self.parsing_completed.emit(violations, stats)
        
        except Exception as e:
            if not self._is_cancelled:
                error_details = {
                    'file_path': self.file_path,
                    'thread_id': int(self.currentThreadId())
                }
                self.parsing_failed.emit(str(e), error_details)
    
    def _forward_progress(self, progress: int, message: str, stats: Dict):
        """转发进度信号"""
        if not self._is_cancelled:
            self.progress_updated.emit(progress, message, stats)
    
    def _forward_completion(self, violations: List[Dict], stats: Dict):
        """转发完成信号"""
        if not self._is_cancelled:
            self.parsing_completed.emit(violations, stats)
    
    def _forward_failure(self, error_msg: str, error_details: Dict):
        """转发失败信号"""
        if not self._is_cancelled:
            self.parsing_failed.emit(error_msg, error_details)