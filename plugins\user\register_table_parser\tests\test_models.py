#!/usr/bin/env python3
"""
数据模型单元测试

测试所有数据模型类的功能和验证逻辑
"""

import unittest
import sys
import os

# 添加路径以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import (
    HeaderInfo, FieldInfo, RegisterInfo, RegisterTableData,
    NumberFormat, ParseError, ValidationError
)


class TestHeaderInfo(unittest.TestCase):
    """测试HeaderInfo数据模型"""
    
    def test_valid_header_creation(self):
        """测试有效表头信息创建"""
        header = HeaderInfo(
            project_name="测试项目",
            sub_system="测试子系统", 
            module_name="测试模块",
            base_addr="0x1000"
        )
        
        self.assertEqual(header.project_name, "测试项目")
        self.assertEqual(header.sub_system, "测试子系统")
        self.assertEqual(header.module_name, "测试模块")
        self.assertEqual(header.base_addr, "0x1000")
        self.assertTrue(header.is_valid())
    
    def test_empty_header_validation(self):
        """测试空表头信息验证"""
        header = HeaderInfo("", "", "", "")
        self.assertFalse(header.is_valid())
    
    def test_partial_header_validation(self):
        """测试部分表头信息验证"""
        header = HeaderInfo("项目", "", "模块", "0x1000")
        self.assertFalse(header.is_valid())


class TestFieldInfo(unittest.TestCase):
    """测试FieldInfo数据模型"""
    
    def test_valid_field_creation(self):
        """测试有效字段创建"""
        field = FieldInfo(
            name="TEST_FIELD",
            bit_range="31:24",
            rw_attribute="RW",
            reset_value="0x00",
            description="测试字段"
        )
        
        self.assertEqual(field.name, "TEST_FIELD")
        self.assertEqual(field.bit_range, "31:24")
        self.assertEqual(field.rw_attribute, "RW")
        self.assertEqual(field.reset_value, "0x00")
        self.assertEqual(field.description, "测试字段")
    
    def test_bit_position_parsing(self):
        """测试位位置解析"""
        # 测试范围格式
        field = FieldInfo("TEST", "31:24", "RW")
        self.assertEqual(field.bit_position, (31, 24))
        self.assertEqual(field.bit_width, 8)
        
        # 测试单位格式
        field_single = FieldInfo("TEST", "15", "RW")
        self.assertEqual(field_single.bit_position, (15, 15))
        self.assertEqual(field_single.bit_width, 1)
    
    def test_rw_attribute_properties(self):
        """测试读写属性"""
        rw_field = FieldInfo("RW_FIELD", "7:0", "RW")
        self.assertTrue(rw_field.is_writable)
        self.assertFalse(rw_field.is_read_only)
        
        ro_field = FieldInfo("RO_FIELD", "7:0", "RO")
        self.assertFalse(ro_field.is_writable)
        self.assertTrue(ro_field.is_read_only)
    
    def test_reserved_field_detection(self):
        """测试保留字段检测"""
        reserved_field = FieldInfo("Reserved", "23:16", "RO")
        self.assertTrue(reserved_field.is_reserved)
        
        normal_field = FieldInfo("NORMAL", "7:0", "RW")
        self.assertFalse(normal_field.is_reserved)
    
    def test_reset_value_parsing(self):
        """测试复位值解析"""
        # 十六进制
        field_hex = FieldInfo("TEST", "7:0", "RW", "0xFF")
        self.assertEqual(field_hex.reset_value_int, 255)
        
        # 十进制
        field_dec = FieldInfo("TEST", "7:0", "RW", "255")
        self.assertEqual(field_dec.reset_value_int, 255)
        
        # 二进制
        field_bin = FieldInfo("TEST", "7:0", "RW", "0b11111111")
        self.assertEqual(field_bin.reset_value_int, 255)
        
        # 空值
        field_empty = FieldInfo("TEST", "7:0", "RW", "")
        self.assertEqual(field_empty.reset_value_int, 0)


class TestRegisterInfo(unittest.TestCase):
    """测试RegisterInfo数据模型"""
    
    def setUp(self):
        """设置测试数据"""
        self.fields = [
            FieldInfo("FIELD_A", "31:24", "RW", "0x00", "字段A"),
            FieldInfo("Reserved", "23:16", "RO", "0x00", "保留"),
            FieldInfo("FIELD_B", "15:8", "RO", "0xFF", "字段B"),
            FieldInfo("FIELD_C", "7:0", "RW", "0x55", "字段C"),
        ]
    
    def test_register_creation(self):
        """测试寄存器创建"""
        register = RegisterInfo(
            offset="0x0000",
            name="TEST_REG",
            description="测试寄存器",
            width=32,
            fields=self.fields
        )
        
        self.assertEqual(register.offset, "0x0000")
        self.assertEqual(register.name, "TEST_REG")
        self.assertEqual(register.description, "测试寄存器")
        self.assertEqual(register.width, 32)
        self.assertEqual(len(register.fields), 4)
    
    def test_offset_parsing(self):
        """测试偏移地址解析"""
        # 十六进制
        reg_hex = RegisterInfo("0x1000", "TEST", "", 32, [])
        self.assertEqual(reg_hex.offset_int, 0x1000)
        
        # 十进制
        reg_dec = RegisterInfo("4096", "TEST", "", 32, [])
        self.assertEqual(reg_dec.offset_int, 4096)
    
    def test_non_reserved_fields(self):
        """测试非保留字段过滤"""
        register = RegisterInfo("0x0", "TEST", "", 32, self.fields)
        non_reserved = register.non_reserved_fields
        
        self.assertEqual(len(non_reserved), 3)
        field_names = [f.name for f in non_reserved]
        self.assertIn("FIELD_A", field_names)
        self.assertIn("FIELD_B", field_names)
        self.assertIn("FIELD_C", field_names)
        self.assertNotIn("Reserved", field_names)
    
    def test_writable_fields(self):
        """测试可写字段过滤"""
        register = RegisterInfo("0x0", "TEST", "", 32, self.fields)
        writable = register.writable_fields
        
        self.assertEqual(len(writable), 2)
        field_names = [f.name for f in writable]
        self.assertIn("FIELD_A", field_names)
        self.assertIn("FIELD_C", field_names)
        self.assertNotIn("FIELD_B", field_names)  # RO字段
    
    def test_default_register_value(self):
        """测试默认寄存器值计算"""
        register = RegisterInfo("0x0", "TEST", "", 32, self.fields)
        default_value = register.default_register_value
        
        # 计算期望值：FIELD_A(31:24)=0x00, FIELD_B(15:8)=0xFF, FIELD_C(7:0)=0x55
        expected = (0x00 << 24) | (0xFF << 8) | 0x55
        self.assertEqual(default_value, expected)


class TestRegisterTableData(unittest.TestCase):
    """测试RegisterTableData数据模型"""
    
    def setUp(self):
        """设置测试数据"""
        self.header = HeaderInfo("项目", "子系统", "模块", "0x1000")
        self.registers = [
            RegisterInfo("0x0000", "REG1", "寄存器1", 32, [
                FieldInfo("FIELD1", "7:0", "RW", "0x00")
            ]),
            RegisterInfo("0x0004", "REG2", "寄存器2", 32, [
                FieldInfo("FIELD2", "15:8", "RW", "0xFF"),
                FieldInfo("Reserved", "7:0", "RO", "0x00")
            ])
        ]
    
    def test_table_data_creation(self):
        """测试表格数据创建"""
        table_data = RegisterTableData(
            header=self.header,
            registers=self.registers
        )
        
        self.assertEqual(table_data.header, self.header)
        self.assertEqual(len(table_data.registers), 2)
        self.assertTrue(table_data.is_valid())
    
    def test_register_count(self):
        """测试寄存器计数"""
        table_data = RegisterTableData(self.header, self.registers)
        self.assertEqual(table_data.register_count, 2)
    
    def test_field_count(self):
        """测试字段计数"""
        table_data = RegisterTableData(self.header, self.registers)
        
        # 总字段数：3个
        self.assertEqual(table_data.total_field_count, 3)
        
        # 非保留字段数：2个
        self.assertEqual(table_data.non_reserved_field_count, 2)
    
    def test_register_lookup(self):
        """测试寄存器查找"""
        table_data = RegisterTableData(self.header, self.registers)
        
        # 按名称查找
        reg1 = table_data.get_register_by_name("REG1")
        self.assertIsNotNone(reg1)
        self.assertEqual(reg1.name, "REG1")
        
        # 按偏移查找
        reg2 = table_data.get_register_by_offset("0x0004")
        self.assertIsNotNone(reg2)
        self.assertEqual(reg2.name, "REG2")
        
        # 查找不存在的寄存器
        not_found = table_data.get_register_by_name("NOT_EXIST")
        self.assertIsNone(not_found)
    
    def test_validation(self):
        """测试数据验证"""
        # 有效数据
        valid_data = RegisterTableData(self.header, self.registers)
        self.assertTrue(valid_data.is_valid())
        
        # 无效表头
        invalid_header = HeaderInfo("", "", "", "")
        invalid_data = RegisterTableData(invalid_header, self.registers)
        self.assertFalse(invalid_data.is_valid())
        
        # 空寄存器列表
        empty_registers = RegisterTableData(self.header, [])
        self.assertFalse(empty_registers.is_valid())


class TestNumberFormat(unittest.TestCase):
    """测试NumberFormat枚举"""
    
    def test_format_values(self):
        """测试格式值"""
        self.assertEqual(NumberFormat.BINARY.value, "二进制")
        self.assertEqual(NumberFormat.DECIMAL.value, "十进制")
        self.assertEqual(NumberFormat.HEXADECIMAL.value, "十六进制")
    
    def test_format_from_string(self):
        """测试从字符串创建格式"""
        self.assertEqual(NumberFormat.from_string("二进制"), NumberFormat.BINARY)
        self.assertEqual(NumberFormat.from_string("十进制"), NumberFormat.DECIMAL)
        self.assertEqual(NumberFormat.from_string("十六进制"), NumberFormat.HEXADECIMAL)
        
        # 测试无效字符串
        with self.assertRaises(ValueError):
            NumberFormat.from_string("无效格式")


class TestExceptions(unittest.TestCase):
    """测试异常类"""
    
    def test_parse_error(self):
        """测试解析错误"""
        error = ParseError("解析失败", "test.xlsx", 5)
        self.assertEqual(str(error), "解析失败")
        self.assertEqual(error.file_path, "test.xlsx")
        self.assertEqual(error.line_number, 5)
    
    def test_validation_error(self):
        """测试验证错误"""
        error = ValidationError("验证失败", "TEST_FIELD")
        self.assertEqual(str(error), "验证失败")
        self.assertEqual(error.field_name, "TEST_FIELD")


if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromModule(sys.modules[__name__])
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print(f"\n✅ 所有测试通过！运行了 {result.testsRun} 个测试")
    else:
        print(f"\n❌ 测试失败！{len(result.failures)} 个失败，{len(result.errors)} 个错误")
        sys.exit(1)