#!/usr/bin/env python3
"""
简单的解压缩脚本
"""

import gzip
import json
import os
from pathlib import Path

def main():
    violations_dir = Path('data/violations')
    
    if not violations_dir.exists():
        print(f"目录不存在: {violations_dir}")
        return
    
    # 查找所有.gz文件
    gz_files = list(violations_dir.glob("*.json.gz"))
    print(f"找到 {len(gz_files)} 个压缩文件")
    
    success_count = 0
    
    for gz_file in gz_files:
        json_file = gz_file.with_suffix('')  # 移除.gz后缀
        
        try:
            with gzip.open(gz_file, 'rt', encoding='utf-8') as f_in:
                data = json.load(f_in)
            
            with open(json_file, 'w', encoding='utf-8') as f_out:
                json.dump(data, f_out, indent=2, ensure_ascii=False)
            
            print(f"✅ {gz_file.name} -> {json_file.name}")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 解压缩失败 {gz_file.name}: {e}")
    
    print(f"\n解压缩完成: {success_count}/{len(gz_files)} 个文件")
    
    # 更新pagination_manifest.json
    manifest_file = Path('data/pagination_manifest.json')
    if manifest_file.exists():
        try:
            with open(manifest_file, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            
            # 更新压缩标志
            manifest['compressed'] = False
            
            # 更新文件名，移除.gz后缀
            for corner_case, info in manifest.get('corner_cases', {}).items():
                if 'files' in info:
                    info['files'] = [f.replace('.gz', '') for f in info['files']]
            
            # 保存更新后的manifest
            with open(manifest_file, 'w', encoding='utf-8') as f:
                json.dump(manifest, f, indent=2, ensure_ascii=False)
            
            print("✅ pagination_manifest.json 已更新")
            
        except Exception as e:
            print(f"❌ 更新manifest失败: {e}")

if __name__ == "__main__":
    main()
