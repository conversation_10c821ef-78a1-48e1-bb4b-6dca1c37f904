#!/usr/bin/env python3
"""
从压缩文件中提取样本数据到violations.json
"""

import gzip
import json
import os
from pathlib import Path

def extract_sample_data():
    """从压缩文件中提取样本数据"""
    violations_dir = Path('data/violations')
    
    if not violations_dir.exists():
        print(f"目录不存在: {violations_dir}")
        return []
    
    all_violations = []
    
    # 从每个压缩文件中提取一些样本数据
    gz_files = list(violations_dir.glob("*.json.gz"))
    print(f"找到 {len(gz_files)} 个压缩文件")
    
    for gz_file in gz_files:
        try:
            with gzip.open(gz_file, 'rt', encoding='utf-8') as f:
                data = json.load(f)
            
            # 提取数据
            if isinstance(data, list):
                # 如果是数组，取前5条
                sample_data = data[:5]
                all_violations.extend(sample_data)
                print(f"从 {gz_file.name} 提取了 {len(sample_data)} 条数据")
            elif isinstance(data, dict) and 'violations' in data:
                # 如果是对象且有violations字段，取前5条
                sample_data = data['violations'][:5]
                all_violations.extend(sample_data)
                print(f"从 {gz_file.name} 提取了 {len(sample_data)} 条数据")
            else:
                print(f"⚠️ 无法识别 {gz_file.name} 的数据格式")
                
        except Exception as e:
            print(f"❌ 读取 {gz_file.name} 失败: {e}")
    
    return all_violations

def update_violations_json(sample_violations):
    """更新violations.json文件"""
    violations_file = Path('data/violations.json')
    
    # 创建新的violations.json内容
    violations_data = {
        'metadata': {
            'generated_at': '2025-08-07 19:15:00',
            'total_violations': len(sample_violations),
            'total_corners': 3,
            'total_cases': 1,
            'data_sources': ['sample_from_compressed_files'],
            'note': 'This is sample data extracted from compressed files for web display'
        },
        'corners': ['npg_f1_ffg', 'npg_f2_ffg', 'npg_f2_ssg'],
        'cases': ['page_test_027_test'],
        'violations': sample_violations,
        'statistics': {
            'total_violations': len(sample_violations),
            'confirmed_violations': len([v for v in sample_violations if v.get('status') == 'confirmed']),
            'pending_violations': len([v for v in sample_violations if v.get('status') == 'pending']),
            'confirmation_rate': 100 if sample_violations else 0,
            'latest_confirmation': sample_violations[-1].get('confirmed_at') if sample_violations else None
        }
    }
    
    try:
        with open(violations_file, 'w', encoding='utf-8') as f:
            json.dump(violations_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ violations.json 已更新，包含 {len(sample_violations)} 条样本数据")
        return True
        
    except Exception as e:
        print(f"❌ 更新violations.json失败: {e}")
        return False

def main():
    print("开始从压缩文件中提取样本数据...")
    
    # 提取样本数据
    sample_violations = extract_sample_data()
    
    if not sample_violations:
        print("❌ 没有提取到任何数据")
        return 1
    
    print(f"总共提取了 {len(sample_violations)} 条样本违例数据")
    
    # 更新violations.json
    if update_violations_json(sample_violations):
        print("\n✅ 完成！现在可以刷新浏览器查看结果。")
        print("💡 提示：这只是样本数据。要查看完整数据，请解压缩所有.gz文件。")
        return 0
    else:
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
