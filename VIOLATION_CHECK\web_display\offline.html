<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时序违例显示 - 离线版</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: #007bff; color: white; border-radius: 8px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .filters { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .filter-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px; }
        .filter-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .filter-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #343a40; color: white; }
        tr:hover { background: #f8f9fa; }
        .status-confirmed { background: #28a745; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.9em; }
        .status-pending { background: #ffc107; color: #212529; padding: 2px 8px; border-radius: 12px; font-size: 0.9em; }
        .loading { text-align: center; padding: 40px; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕐 时序违例显示系统</h1>
            <p>离线版本 - 查看和管理时序违例记录</p>
        </div>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-violations">0</div>
                <div>总违例数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="confirmed-violations">0</div>
                <div>已确认</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pending-violations">0</div>
                <div>待确认</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="confirmation-rate">0%</div>
                <div>确认率</div>
            </div>
        </div>
        <div class="filters">
            <h3>🔍 过滤控制</h3>
            <div class="filter-row">
                <div class="filter-group">
                    <label for="corner-filter">Corner名称</label>
                    <select id="corner-filter"><option value="all">所有Corner</option></select>
                </div>
                <div class="filter-group">
                    <label for="case-filter">Case名称</label>
                    <select id="case-filter"><option value="all">所有Case</option></select>
                </div>
                <div class="filter-group">
                    <label for="status-filter">状态</label>
                    <select id="status-filter">
                        <option value="all">所有状态</option>
                        <option value="confirmed">已确认</option>
                        <option value="pending">待确认</option>
                    </select>
                </div>
            </div>
        </div>
        <div id="loading" class="loading">正在加载违例数据...</div>
        <div id="error-display" class="error" style="display: none;"></div>
        <table id="violations-table" style="display: none;">
            <thead>
                <tr>
                    <th>编号</th>
                    <th>层级路径</th>
                    <th>时间(ns)</th>
                    <th>检查信息</th>
                    <th>状态</th>
                    <th>确认人</th>
                    <th>结果</th>
                    <th>原因</th>
                    <th>确认时间</th>
                    <th>Corner</th>
                    <th>Case</th>
                </tr>
            </thead>
            <tbody id="table-body"></tbody>
        </table>
    </div>
    <script>
        // 基本的离线JavaScript功能
        let allData = [];
        let filteredData = [];
        
        async function loadData() {
            try {
                // 尝试加载数据
                const sources = ['data/violations.json', 'data/index.json', 'test_violations.json'];
                for (const source of sources) {
                    try {
                        const response = await fetch(source);
                        if (response.ok) {
                            const data = await response.json();
                            if (Array.isArray(data)) {
                                allData = data;
                            } else if (data.violations) {
                                allData = data.violations;
                            }
                            if (allData.length > 0) break;
                        }
                    } catch (e) {
                        console.warn('Failed to load', source, e);
                    }
                }
                
                if (allData.length === 0) {
                    throw new Error('未找到违例数据文件');
                }
                
                populateFilters();
                applyFilters();
                updateStatistics();
                document.getElementById('loading').style.display = 'none';
                document.getElementById('violations-table').style.display = 'table';
                
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error-display').style.display = 'block';
                document.getElementById('error-display').textContent = '加载数据失败: ' + error.message;
            }
        }
        
        function populateFilters() {
            const corners = [...new Set(allData.map(v => v.corner).filter(Boolean))].sort();
            const cases = [...new Set(allData.map(v => v.case).filter(Boolean))].sort();
            
            const cornerSelect = document.getElementById('corner-filter');
            const caseSelect = document.getElementById('case-filter');
            
            corners.forEach(corner => {
                const option = document.createElement('option');
                option.value = corner;
                option.textContent = corner;
                cornerSelect.appendChild(option);
            });
            
            cases.forEach(caseName => {
                const option = document.createElement('option');
                option.value = caseName;
                option.textContent = caseName;
                caseSelect.appendChild(option);
            });
        }
        
        function applyFilters() {
            const cornerFilter = document.getElementById('corner-filter').value;
            const caseFilter = document.getElementById('case-filter').value;
            const statusFilter = document.getElementById('status-filter').value;
            
            filteredData = allData.filter(violation => {
                if (cornerFilter !== 'all' && violation.corner !== cornerFilter) return false;
                if (caseFilter !== 'all' && violation.case !== caseFilter) return false;
                if (statusFilter !== 'all') {
                    const status = (violation.status || '').toLowerCase();
                    if (statusFilter === 'confirmed' && status !== 'confirmed') return false;
                    if (statusFilter === 'pending' && status === 'confirmed') return false;
                }
                return true;
            });
            
            updateTable();
            updateStatistics();
        }
        
        function updateTable() {
            const tbody = document.getElementById('table-body');
            tbody.innerHTML = '';
            
            filteredData.forEach(violation => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${violation.num || ''}</td>
                    <td title="${violation.hier || ''}">${(violation.hier || '').substring(0, 40)}${(violation.hier || '').length > 40 ? '...' : ''}</td>
                    <td>${violation.time_ns ? parseFloat(violation.time_ns).toFixed(3) : ''}</td>
                    <td title="${violation.check_info || ''}">${(violation.check_info || '').substring(0, 30)}${(violation.check_info || '').length > 30 ? '...' : ''}</td>
                    <td><span class="status-${(violation.status || 'pending').toLowerCase()}">${violation.status || '待确认'}</span></td>
                    <td>${violation.confirmer || ''}</td>
                    <td>${violation.result || ''}</td>
                    <td title="${violation.reason || ''}">${(violation.reason || '').substring(0, 20)}${(violation.reason || '').length > 20 ? '...' : ''}</td>
                    <td>${violation.confirmed_at ? new Date(violation.confirmed_at).toLocaleString() : ''}</td>
                    <td>${violation.corner || ''}</td>
                    <td>${violation.case || ''}</td>
                `;
                tbody.appendChild(row);
            });
        }
        
        function updateStatistics() {
            const total = filteredData.length;
            const confirmed = filteredData.filter(v => (v.status || '').toLowerCase() === 'confirmed').length;
            const pending = total - confirmed;
            const rate = total > 0 ? ((confirmed / total) * 100).toFixed(1) : 0;
            
            document.getElementById('total-violations').textContent = total;
            document.getElementById('confirmed-violations').textContent = confirmed;
            document.getElementById('pending-violations').textContent = pending;
            document.getElementById('confirmation-rate').textContent = rate + '%';
        }
        
        // 设置事件监听器
        document.getElementById('corner-filter').addEventListener('change', applyFilters);
        document.getElementById('case-filter').addEventListener('change', applyFilters);
        document.getElementById('status-filter').addEventListener('change', applyFilters);
        
        // 初始化
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>