#!/usr/bin/env python3
"""
数据库合并功能演示脚本

演示如何使用时序违例插件的数据库合并功能。
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime

# 添加插件路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from PyQt5.QtWidgets import QApplication
    from models import ViolationDataModel
    from main_window import DatabaseMergeDialog
    PYQT_AVAILABLE = True
except ImportError:
    print("PyQt5不可用，将使用命令行演示")
    PYQT_AVAILABLE = False


def create_demo_database(db_path: str, user_name: str, case_count: int = 3):
    """创建演示数据库
    
    Args:
        db_path: 数据库路径
        user_name: 用户名
        case_count: 用例数量
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # 临时切换工作目录以使用ViolationDataModel
    original_cwd = os.getcwd()
    os.chdir(os.path.dirname(db_path))
    
    try:
        # 创建数据模型
        data_model = ViolationDataModel()
        
        # 准备测试数据
        violations_data = []
        for i in range(case_count):
            case_name = f"{user_name}_case_{i+1}"
            corner = ["npg_f1_ssg", "npg_f2_ffg", "npg_f3_tt"][i % 3]
            
            violations = [
                {
                    'NUM': 1,
                    'Hier': f'tb_top.cpu{i}.core',
                    'Time': f'{1500 + i*100} FS',
                    'time_fs': (1500 + i*100) * 1000,
                    'Check': 'setup(posedge clk, data)'
                },
                {
                    'NUM': 2,
                    'Hier': f'tb_top.mem{i}.ctrl',
                    'Time': f'{2000 + i*150} FS',
                    'time_fs': (2000 + i*150) * 1000,
                    'Check': 'hold(posedge clk, addr)'
                }
            ]
            
            violations_data.extend(violations)
        
        # 添加违例数据
        added_count = data_model.add_violations(
            violations_data, 
            f"{user_name}_project", 
            "npg_f1_ssg", 
            f"/path/to/{user_name}/vio_summary.log"
        )
        
        # 为部分违例添加确认信息
        if user_name != "user1":  # user1保持未确认状态
            violations = data_model.get_violations_by_case(f"{user_name}_project", "npg_f1_ssg")
            if violations:
                # 确认第一个违例
                first_violation = violations[0]
                data_model.update_confirmation(
                    first_violation['id'],
                    'confirmed',
                    user_name,
                    '通过',
                    f'{user_name}确认：复位期间时序违例，可以忽略'
                )
                
                # 保存确认模式
                data_model.save_pattern(
                    first_violation['hier'],
                    first_violation['check_info'],
                    user_name,
                    '通过',
                    f'{user_name}的确认模式'
                )
        
        print(f"✓ 创建演示数据库成功: {db_path}")
        print(f"  用户: {user_name}, 添加违例: {added_count}")
        
        return True
        
    except Exception as e:
        print(f"✗ 创建演示数据库失败: {str(e)}")
        return False
    finally:
        os.chdir(original_cwd)


def demo_command_line_merge():
    """命令行演示数据库合并"""
    print("=" * 60)
    print("数据库合并功能命令行演示")
    print("=" * 60)
    
    # 创建临时目录
    demo_dir = tempfile.mkdtemp(prefix="timing_violation_demo_")
    print(f"演示目录: {demo_dir}")
    
    try:
        # 创建多个用户的数据库
        users = ["user1", "user2", "user3"]
        db_paths = {}
        
        print("\n1. 创建演示数据库...")
        for user in users:
            db_path = os.path.join(demo_dir, user, "VIOLATION_CHECK", "timing_violations.db")
            if create_demo_database(db_path, user):
                db_paths[user] = db_path
        
        if len(db_paths) < 2:
            print("✗ 创建演示数据库失败，无法继续演示")
            return
        
        # 选择主数据库（user1）
        main_user = "user1"
        main_db = db_paths[main_user]
        source_dbs = [db_paths[user] for user in users if user != main_user]
        
        print(f"\n2. 数据库合并演示...")
        print(f"主数据库: {main_user}")
        print(f"源数据库: {[user for user in users if user != main_user]}")
        
        # 切换到主数据库目录
        original_cwd = os.getcwd()
        os.chdir(os.path.dirname(main_db))
        
        try:
            # 创建数据模型
            data_model = ViolationDataModel()
            
            # 获取合并前统计
            print(f"\n3. 合并前统计信息...")
            for user in users:
                if user in db_paths:
                    stats = data_model.get_database_statistics(db_paths[user])
                    print(f"  {user}: 违例={stats['total_violations']}, "
                          f"已确认={stats['confirmed_violations']}, "
                          f"模式={stats['total_patterns']}")
            
            # 执行合并
            print(f"\n4. 执行数据库合并...")
            
            def progress_callback(current, total, message):
                print(f"  进度 [{current}/{total}]: {message}")
            
            merge_result = data_model.merge_databases(source_dbs, progress_callback)
            
            # 显示合并结果
            print(f"\n5. 合并结果:")
            print(f"  合并成功: {merge_result['success']}")
            print(f"  处理数据库: {merge_result['processed_databases']}")
            print(f"  新增违例: {merge_result['total_violations_added']}")
            print(f"  更新确认: {merge_result['total_confirmations_updated']}")
            print(f"  合并模式: {merge_result['total_patterns_merged']}")
            print(f"  备份文件: {os.path.basename(merge_result['backup_path'])}")
            
            if merge_result['errors']:
                print(f"  警告信息: {len(merge_result['errors'])} 个")
                for error in merge_result['errors'][:3]:
                    print(f"    - {error}")
            
            # 获取合并后统计
            print(f"\n6. 合并后统计信息...")
            final_stats = data_model.get_database_statistics()
            print(f"  最终数据库: 违例={final_stats['total_violations']}, "
                  f"已确认={final_stats['confirmed_violations']}, "
                  f"模式={final_stats['total_patterns']}, "
                  f"用例={final_stats['unique_cases']}")
            
            # 验证备份文件
            if merge_result['backup_path'] and os.path.exists(merge_result['backup_path']):
                backup_stats = data_model.get_database_statistics(merge_result['backup_path'])
                print(f"  备份验证: 成功 (违例={backup_stats['total_violations']})")
            else:
                print(f"  备份验证: 失败")
            
        finally:
            os.chdir(original_cwd)
        
        print(f"\n7. 演示完成!")
        print(f"   演示文件保存在: {demo_dir}")
        print(f"   可以手动检查合并结果和备份文件")
        
    except Exception as e:
        print(f"✗ 演示过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 询问是否保留演示文件
        try:
            keep_files = input("\n是否保留演示文件? (y/N): ").lower().strip()
            if keep_files != 'y':
                shutil.rmtree(demo_dir)
                print(f"演示文件已清理: {demo_dir}")
            else:
                print(f"演示文件已保留: {demo_dir}")
        except:
            # 自动清理
            shutil.rmtree(demo_dir)
            print(f"演示文件已自动清理: {demo_dir}")


def demo_gui_merge():
    """GUI演示数据库合并"""
    print("=" * 60)
    print("数据库合并功能GUI演示")
    print("=" * 60)
    
    # 创建临时目录和演示数据
    demo_dir = tempfile.mkdtemp(prefix="timing_violation_gui_demo_")
    print(f"演示目录: {demo_dir}")
    
    try:
        # 创建演示数据库
        users = ["main_user", "colleague1", "colleague2"]
        db_paths = {}
        
        print("\n创建演示数据库...")
        for user in users:
            db_path = os.path.join(demo_dir, user, "VIOLATION_CHECK", "timing_violations.db")
            if create_demo_database(db_path, user, case_count=2):
                db_paths[user] = db_path
        
        if len(db_paths) < 2:
            print("✗ 创建演示数据库失败")
            return
        
        # 设置主数据库
        main_db = db_paths["main_user"]
        source_dbs = [db_paths[user] for user in users if user != "main_user"]
        
        # 切换到主数据库目录
        original_cwd = os.getcwd()
        os.chdir(os.path.dirname(main_db))
        
        try:
            # 创建Qt应用
            app = QApplication(sys.argv)
            
            # 创建数据模型
            data_model = ViolationDataModel()
            
            # 创建合并对话框
            dialog = DatabaseMergeDialog(data_model)
            
            # 预加载源数据库
            dialog.selected_databases = source_dbs
            dialog.update_database_list()
            
            print(f"\n打开数据库合并对话框...")
            print(f"主数据库: {main_db}")
            print(f"预加载源数据库: {len(source_dbs)} 个")
            print(f"\n请在对话框中:")
            print(f"1. 查看当前数据库信息")
            print(f"2. 确认预加载的源数据库")
            print(f"3. 点击'开始合并'执行合并")
            print(f"4. 查看合并结果")
            
            # 显示对话框
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                print(f"\n✓ 数据库合并完成")
            else:
                print(f"\n- 用户取消了合并操作")
            
        finally:
            os.chdir(original_cwd)
        
    except Exception as e:
        print(f"✗ GUI演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理演示文件
        try:
            shutil.rmtree(demo_dir)
            print(f"演示文件已清理: {demo_dir}")
        except:
            print(f"清理演示文件失败: {demo_dir}")


def main():
    """主函数"""
    print("时序违例插件 - 数据库合并功能演示")
    print("=" * 60)
    
    if PYQT_AVAILABLE:
        print("检测到PyQt5环境，支持GUI演示")
        print("\n选择演示模式:")
        print("1. 命令行演示")
        print("2. GUI演示")
        
        try:
            choice = input("\n请选择 (1/2): ").strip()
            if choice == "2":
                demo_gui_merge()
            else:
                demo_command_line_merge()
        except KeyboardInterrupt:
            print("\n演示已取消")
        except:
            print("\n使用默认命令行演示")
            demo_command_line_merge()
    else:
        print("PyQt5不可用，使用命令行演示")
        demo_command_line_merge()


if __name__ == "__main__":
    main()
