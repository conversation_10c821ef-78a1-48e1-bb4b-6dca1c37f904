#!/usr/bin/env python3
"""
工具函数单元测试

测试NumberFormatConverter和其他工具函数
"""

import unittest
import sys
import os

# 添加路径以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils import NumberFormatConverter, BitRangeParser
from models import NumberFormat


class TestNumberFormatConverter(unittest.TestCase):
    """测试数字格式转换器"""
    
    def test_to_binary_conversion(self):
        """测试转换为二进制"""
        # 基本转换
        self.assertEqual(NumberFormatConverter.to_binary(0), "0b0")
        self.assertEqual(NumberFormatConverter.to_binary(1), "0b1")
        self.assertEqual(NumberFormatConverter.to_binary(15), "0b1111")
        self.assertEqual(NumberFormatConverter.to_binary(255), "0b11111111")
        
        # 带位宽转换
        self.assertEqual(NumberFormatConverter.to_binary(1, 4), "0b0001")
        self.assertEqual(NumberFormatConverter.to_binary(15, 8), "0b00001111")
        self.assertEqual(NumberFormatConverter.to_binary(255, 16), "0b0000000011111111")
    
    def test_to_decimal_conversion(self):
        """测试转换为十进制"""
        self.assertEqual(NumberFormatConverter.to_decimal(0), "0")
        self.assertEqual(NumberFormatConverter.to_decimal(1), "1")
        self.assertEqual(NumberFormatConverter.to_decimal(255), "255")
        self.assertEqual(NumberFormatConverter.to_decimal(4095), "4095")
    
    def test_to_hexadecimal_conversion(self):
        """测试转换为十六进制"""
        # 基本转换
        self.assertEqual(NumberFormatConverter.to_hexadecimal(0), "0x0")
        self.assertEqual(NumberFormatConverter.to_hexadecimal(1), "0x1")
        self.assertEqual(NumberFormatConverter.to_hexadecimal(15), "0xF")
        self.assertEqual(NumberFormatConverter.to_hexadecimal(255), "0xFF")
        
        # 带位宽转换
        self.assertEqual(NumberFormatConverter.to_hexadecimal(1, 4), "0x1")
        self.assertEqual(NumberFormatConverter.to_hexadecimal(15, 8), "0x0F")
        self.assertEqual(NumberFormatConverter.to_hexadecimal(255, 16), "0x00FF")
        self.assertEqual(NumberFormatConverter.to_hexadecimal(4095, 32), "0x00000FFF")
    
    def test_from_string_parsing(self):
        """测试从字符串解析"""
        # 二进制格式
        self.assertEqual(NumberFormatConverter.from_string("0b1010"), 10)
        self.assertEqual(NumberFormatConverter.from_string("0B1111"), 15)
        self.assertEqual(NumberFormatConverter.from_string("0b0"), 0)
        
        # 十六进制格式
        self.assertEqual(NumberFormatConverter.from_string("0x1A"), 26)
        self.assertEqual(NumberFormatConverter.from_string("0X2F"), 47)
        self.assertEqual(NumberFormatConverter.from_string("0xff"), 255)
        self.assertEqual(NumberFormatConverter.from_string("0xFF"), 255)
        
        # 十进制格式
        self.assertEqual(NumberFormatConverter.from_string("255"), 255)
        self.assertEqual(NumberFormatConverter.from_string("0"), 0)
        self.assertEqual(NumberFormatConverter.from_string("4095"), 4095)
        
        # 带空格的输入
        self.assertEqual(NumberFormatConverter.from_string("  0xFF  "), 255)
        self.assertEqual(NumberFormatConverter.from_string("  255  "), 255)
    
    def test_from_string_invalid_input(self):
        """测试无效输入解析"""
        invalid_inputs = [
            "invalid",
            "0xGG",
            "0b2",
            "",
            "0x",
            "0b"
        ]
        
        for invalid_input in invalid_inputs:
            with self.assertRaises(ValueError):
                NumberFormatConverter.from_string(invalid_input)
    
    def test_detect_format(self):
        """测试格式检测"""
        # 二进制格式
        self.assertEqual(NumberFormatConverter.detect_format("0b1010"), NumberFormat.BINARY)
        self.assertEqual(NumberFormatConverter.detect_format("0B1111"), NumberFormat.BINARY)
        
        # 十六进制格式
        self.assertEqual(NumberFormatConverter.detect_format("0x1A"), NumberFormat.HEXADECIMAL)
        self.assertEqual(NumberFormatConverter.detect_format("0X2F"), NumberFormat.HEXADECIMAL)
        
        # 十进制格式
        self.assertEqual(NumberFormatConverter.detect_format("255"), NumberFormat.DECIMAL)
        self.assertEqual(NumberFormatConverter.detect_format("0"), NumberFormat.DECIMAL)
        
        # 空字符串默认为十进制
        self.assertEqual(NumberFormatConverter.detect_format(""), NumberFormat.DECIMAL)
        
        # 带空格的输入
        self.assertEqual(NumberFormatConverter.detect_format("  0xFF  "), NumberFormat.HEXADECIMAL)
    
    def test_convert_format(self):
        """测试格式转换"""
        test_value = 255
        
        # 转换为二进制
        binary_result = NumberFormatConverter.convert_format(test_value, NumberFormat.BINARY)
        self.assertEqual(binary_result, "0b11111111")
        
        binary_result_8bit = NumberFormatConverter.convert_format(test_value, NumberFormat.BINARY, 16)
        self.assertEqual(binary_result_8bit, "0b0000000011111111")
        
        # 转换为十进制
        decimal_result = NumberFormatConverter.convert_format(test_value, NumberFormat.DECIMAL)
        self.assertEqual(decimal_result, "255")
        
        # 转换为十六进制
        hex_result = NumberFormatConverter.convert_format(test_value, NumberFormat.HEXADECIMAL)
        self.assertEqual(hex_result, "0xFF")
        
        hex_result_16bit = NumberFormatConverter.convert_format(test_value, NumberFormat.HEXADECIMAL, 16)
        self.assertEqual(hex_result_16bit, "0x00FF")
    
    def test_round_trip_conversion(self):
        """测试往返转换"""
        test_values = [0, 1, 15, 255, 4095, 65535]
        formats = [NumberFormat.BINARY, NumberFormat.DECIMAL, NumberFormat.HEXADECIMAL]
        
        for value in test_values:
            for format_type in formats:
                # 转换为字符串
                str_value = NumberFormatConverter.convert_format(value, format_type)
                
                # 从字符串解析回来
                parsed_value = NumberFormatConverter.from_string(str_value)
                
                # 验证往返转换的一致性
                self.assertEqual(parsed_value, value, 
                    f"往返转换失败: {value} -> {str_value} -> {parsed_value}")
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 零值
        self.assertEqual(NumberFormatConverter.to_binary(0), "0b0")
        self.assertEqual(NumberFormatConverter.to_decimal(0), "0")
        self.assertEqual(NumberFormatConverter.to_hexadecimal(0), "0x0")
        
        # 最大32位值
        max_32bit = 0xFFFFFFFF
        self.assertEqual(NumberFormatConverter.from_string("0xFFFFFFFF"), max_32bit)
        
        # 负数处理（应该抛出异常或处理为无符号数）
        with self.assertRaises((ValueError, OverflowError)):
            NumberFormatConverter.to_binary(-1)


class TestBitRangeParser(unittest.TestCase):
    """测试位范围解析器"""
    
    def setUp(self):
        """设置测试环境"""
        self.parser = BitRangeParser()
    
    def test_parse_bit_range(self):
        """测试位范围解析"""
        # 范围格式
        self.assertEqual(self.parser.parse_bit_range("31:24"), (31, 24))
        self.assertEqual(self.parser.parse_bit_range("15:8"), (15, 8))
        self.assertEqual(self.parser.parse_bit_range("7:0"), (7, 0))
        
        # 单位格式
        self.assertEqual(self.parser.parse_bit_range("15"), (15, 15))
        self.assertEqual(self.parser.parse_bit_range("0"), (0, 0))
        self.assertEqual(self.parser.parse_bit_range("31"), (31, 31))
    
    def test_parse_bit_range_invalid(self):
        """测试无效位范围解析"""
        invalid_ranges = [
            "32:24",    # 超出32位
            "24:31",    # 顺序错误
            "invalid",  # 无效格式
            "",         # 空字符串
            "15:8:0",   # 多个冒号
            "-1:0",     # 负数
        ]
        
        for invalid_range in invalid_ranges:
            with self.assertRaises(ValueError):
                self.parser.parse_bit_range(invalid_range)
    
    def test_get_bit_width(self):
        """测试位宽计算"""
        # 范围格式
        self.assertEqual(self.parser.get_bit_width("31:24"), 8)
        self.assertEqual(self.parser.get_bit_width("15:8"), 8)
        self.assertEqual(self.parser.get_bit_width("7:0"), 8)
        self.assertEqual(self.parser.get_bit_width("31:0"), 32)
        
        # 单位格式
        self.assertEqual(self.parser.get_bit_width("15"), 1)
        self.assertEqual(self.parser.get_bit_width("0"), 1)
    
    def test_get_bit_mask(self):
        """测试位掩码计算"""
        # 8位掩码
        self.assertEqual(self.parser.get_bit_mask("7:0"), 0xFF)
        self.assertEqual(self.parser.get_bit_mask("31:24"), 0xFF000000)
        
        # 4位掩码
        self.assertEqual(self.parser.get_bit_mask("3:0"), 0xF)
        self.assertEqual(self.parser.get_bit_mask("15:12"), 0xF000)
        
        # 单位掩码
        self.assertEqual(self.parser.get_bit_mask("0"), 0x1)
        self.assertEqual(self.parser.get_bit_mask("31"), 0x80000000)
    
    def test_extract_field_value(self):
        """测试字段值提取"""
        register_value = 0x12345678
        
        # 提取不同字段
        self.assertEqual(self.parser.extract_field_value(register_value, "31:24"), 0x12)
        self.assertEqual(self.parser.extract_field_value(register_value, "23:16"), 0x34)
        self.assertEqual(self.parser.extract_field_value(register_value, "15:8"), 0x56)
        self.assertEqual(self.parser.extract_field_value(register_value, "7:0"), 0x78)
        
        # 提取单位
        self.assertEqual(self.parser.extract_field_value(0x80000000, "31"), 1)
        self.assertEqual(self.parser.extract_field_value(0x00000001, "0"), 1)
    
    def test_set_field_value(self):
        """测试字段值设置"""
        register_value = 0x00000000
        
        # 设置不同字段
        result = self.parser.set_field_value(register_value, "31:24", 0x12)
        self.assertEqual(result, 0x12000000)
        
        result = self.parser.set_field_value(result, "23:16", 0x34)
        self.assertEqual(result, 0x12340000)
        
        result = self.parser.set_field_value(result, "15:8", 0x56)
        self.assertEqual(result, 0x12345600)
        
        result = self.parser.set_field_value(result, "7:0", 0x78)
        self.assertEqual(result, 0x12345678)
    
    def test_field_value_overflow(self):
        """测试字段值溢出处理"""
        # 4位字段最大值为0xF
        with self.assertRaises(ValueError):
            self.parser.set_field_value(0, "3:0", 0x10)  # 超出4位范围
        
        # 8位字段最大值为0xFF
        with self.assertRaises(ValueError):
            self.parser.set_field_value(0, "7:0", 0x100)  # 超出8位范围
    
    def test_validate_bit_range(self):
        """测试位范围验证"""
        # 有效范围
        self.assertTrue(self.parser.validate_bit_range("31:0"))
        self.assertTrue(self.parser.validate_bit_range("15:8"))
        self.assertTrue(self.parser.validate_bit_range("7"))
        
        # 无效范围
        self.assertFalse(self.parser.validate_bit_range("32:0"))   # 超出32位
        self.assertFalse(self.parser.validate_bit_range("8:15"))   # 顺序错误
        self.assertFalse(self.parser.validate_bit_range("invalid")) # 格式错误


class TestUtilsPerformance(unittest.TestCase):
    """测试工具函数性能"""
    
    def test_conversion_performance(self):
        """测试转换性能"""
        import time
        
        # 测试大量转换操作的性能
        test_values = list(range(10000))
        
        # 测试二进制转换性能
        start_time = time.time()
        for value in test_values:
            NumberFormatConverter.to_binary(value)
        binary_time = time.time() - start_time
        
        # 测试十六进制转换性能
        start_time = time.time()
        for value in test_values:
            NumberFormatConverter.to_hexadecimal(value)
        hex_time = time.time() - start_time
        
        # 测试解析性能
        hex_strings = [f"0x{value:X}" for value in test_values[:1000]]
        start_time = time.time()
        for hex_str in hex_strings:
            NumberFormatConverter.from_string(hex_str)
        parse_time = time.time() - start_time
        
        # 性能要求：每种操作应该在合理时间内完成
        self.assertLess(binary_time, 1.0, f"二进制转换性能过慢: {binary_time:.2f}秒")
        self.assertLess(hex_time, 1.0, f"十六进制转换性能过慢: {hex_time:.2f}秒")
        self.assertLess(parse_time, 1.0, f"字符串解析性能过慢: {parse_time:.2f}秒")
    
    def test_bit_range_performance(self):
        """测试位范围操作性能"""
        import time
        
        parser = BitRangeParser()
        register_value = 0x12345678
        
        # 测试大量位操作的性能
        bit_ranges = [f"{i+7}:{i}" for i in range(0, 25, 8)]  # 8位字段
        
        start_time = time.time()
        for _ in range(10000):
            for bit_range in bit_ranges:
                # 提取和设置字段值
                field_value = parser.extract_field_value(register_value, bit_range)
                parser.set_field_value(0, bit_range, field_value)
        
        operation_time = time.time() - start_time
        
        # 性能要求：大量位操作应该在合理时间内完成
        self.assertLess(operation_time, 2.0, f"位操作性能过慢: {operation_time:.2f}秒")


if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromModule(sys.modules[__name__])
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print(f"\n✅ 所有测试通过！运行了 {result.testsRun} 个测试")
    else:
        print(f"\n❌ 测试失败！{len(result.failures)} 个失败，{len(result.errors)} 个错误")
        sys.exit(1)