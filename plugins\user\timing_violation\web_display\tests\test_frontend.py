"""
Frontend functionality tests for timing violation web display.

These tests verify the JavaScript functionality and web interface behavior.
Note: These are Python-based tests that validate the frontend logic and data structures.
For full browser testing, additional tools like Selenium would be needed.
"""

import unittest
import tempfile
import os
import json
from pathlib import Path

# Import modules to test
import sys
sys.path.append(str(Path(__file__).parent.parent))

from data_exporter import DataExporter
from utils.file_utils import FileUtils


class TestFrontend(unittest.TestCase):
    """Test cases for frontend functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.violation_check_dir = os.path.join(self.temp_dir, "VIOLATION_CHECK")
        os.makedirs(self.violation_check_dir)
        
        self.exporter = DataExporter(self.violation_check_dir)
        
        # Create sample violation data for frontend testing
        self.sample_violations = [
            {
                'num': 1,
                'hier': 'cpu/core/reg1',
                'time_ns': 1.5,
                'check_info': 'setup check',
                'status': 'confirmed',
                'confirmer': 'john_doe',
                'result': 'false_positive',
                'reason': 'Clock skew issue',
                'confirmed_at': '2024-01-15 10:30:00',
                'corner': 'ss',
                'case': 'test1',
                'source': 'excel'
            },
            {
                'num': 2,
                'hier': 'cpu/core/reg2',
                'time_ns': 2.3,
                'check_info': 'hold check',
                'status': 'confirmed',
                'confirmer': 'jane_smith',
                'result': 'real_violation',
                'reason': 'Timing constraint violation',
                'confirmed_at': '2024-01-16 14:20:00',
                'corner': 'ff',
                'case': 'test2',
                'source': 'database'
            },
            {
                'num': 3,
                'hier': 'cpu/mem/reg3',
                'time_ns': 0.8,
                'check_info': 'setup check',
                'status': 'confirmed',
                'confirmer': 'bob_wilson',
                'result': 'waived',
                'reason': 'Design exception approved',
                'confirmed_at': '2024-01-17 09:15:00',
                'corner': 'ss',
                'case': 'test2',
                'source': 'excel'
            }
        ]
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_json_data_structure_for_frontend(self):
        """Test that JSON data structure is suitable for frontend consumption."""
        # Set up data and export
        self.exporter.violations_data = self.sample_violations
        self.exporter.corners = {'ss', 'ff'}
        self.exporter.cases = {'test1', 'test2'}
        
        # Export data
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Read the generated index.json
        index_file = self.exporter.data_dir / "index.json"
        self.assertTrue(index_file.exists())
        
        with open(index_file, 'r') as f:
            index_data = json.load(f)
        
        # Verify structure expected by frontend
        self.assertIn('metadata', index_data)
        self.assertIn('corners', index_data)
        self.assertIn('cases', index_data)
        self.assertIn('statistics', index_data)
        
        # Check metadata structure
        metadata = index_data['metadata']
        required_metadata_fields = [
            'total_violations', 'total_corners', 'total_cases',
            'export_timestamp', 'data_sources'
        ]
        for field in required_metadata_fields:
            self.assertIn(field, metadata)
        
        # Check that corners and cases are arrays (for dropdown population)
        self.assertIsInstance(index_data['corners'], list)
        self.assertIsInstance(index_data['cases'], list)
        
        # Verify corner and case data
        self.assertIn('ss', index_data['corners'])
        self.assertIn('ff', index_data['corners'])
        self.assertIn('test1', index_data['cases'])
        self.assertIn('test2', index_data['cases'])
    
    def test_corner_specific_data_files(self):
        """Test that corner-specific data files are created for frontend filtering."""
        # Set up data and export
        self.exporter.violations_data = self.sample_violations
        self.exporter.corners = {'ss', 'ff'}
        self.exporter.cases = {'test1', 'test2'}
        
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Check that corner-specific files exist
        corners_dir = self.exporter.data_dir / "corners"
        self.assertTrue(corners_dir.exists())
        
        # Check for corner-specific case files
        expected_corner_files = ['ss_cases.json', 'ff_cases.json']
        for corner_file in expected_corner_files:
            corner_file_path = corners_dir / corner_file
            self.assertTrue(corner_file_path.exists(), f"Missing corner file: {corner_file}")
            
            # Verify file content
            with open(corner_file_path, 'r') as f:
                corner_data = json.load(f)
            
            self.assertIsInstance(corner_data, list)
            self.assertGreater(len(corner_data), 0)
    
    def test_violation_data_pagination_structure(self):
        """Test that violation data is structured for pagination."""
        # Create larger dataset to test pagination
        large_violations = []
        for i in range(50):  # Create 50 violations
            violation = self.sample_violations[0].copy()
            violation['num'] = i + 1
            violation['hier'] = f'cpu/core/reg{i}'
            violation['corner'] = 'ss' if i % 2 == 0 else 'ff'
            violation['case'] = f'test{i % 3 + 1}'
            large_violations.append(violation)
        
        self.exporter.violations_data = large_violations
        self.exporter.corners = {'ss', 'ff'}
        self.exporter.cases = {'test1', 'test2', 'test3'}
        
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Check violations directory structure
        violations_dir = self.exporter.data_dir / "violations"
        self.assertTrue(violations_dir.exists())
        
        # Should have violation files for different corner-case combinations
        violation_files = list(violations_dir.glob("*.json"))
        self.assertGreater(len(violation_files), 0)
        
        # Check structure of violation data files
        for violation_file in violation_files:
            with open(violation_file, 'r') as f:
                violation_data = json.load(f)
            
            self.assertIsInstance(violation_data, list)
            
            # Each violation should have required fields for frontend display
            if violation_data:  # If file is not empty
                violation = violation_data[0]
                required_fields = [
                    'num', 'hier', 'time_ns', 'check_info', 'status',
                    'confirmer', 'result', 'reason', 'confirmed_at',
                    'corner', 'case'
                ]
                for field in required_fields:
                    self.assertIn(field, violation)
    
    def test_statistics_data_for_frontend_display(self):
        """Test that statistics data is properly formatted for frontend display."""
        self.exporter.violations_data = self.sample_violations
        self.exporter.corners = {'ss', 'ff'}
        self.exporter.cases = {'test1', 'test2'}
        
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Read index data
        index_file = self.exporter.data_dir / "index.json"
        with open(index_file, 'r') as f:
            index_data = json.load(f)
        
        statistics = index_data['statistics']
        
        # Check summary statistics
        self.assertIn('summary', statistics)
        summary = statistics['summary']
        
        required_summary_fields = [
            'total_violations', 'confirmed_violations', 'pending_violations',
            'confirmation_rate', 'latest_confirmation'
        ]
        for field in required_summary_fields:
            self.assertIn(field, summary)
        
        # Check by_corner statistics
        self.assertIn('by_corner', statistics)
        by_corner = statistics['by_corner']
        
        for corner in ['ss', 'ff']:
            self.assertIn(corner, by_corner)
            corner_stats = by_corner[corner]
            self.assertIn('total', corner_stats)
            self.assertIn('confirmed', corner_stats)
        
        # Check by_case statistics
        self.assertIn('by_case', statistics)
        by_case = statistics['by_case']
        
        for case in ['test1', 'test2']:
            self.assertIn(case, by_case)
            case_stats = by_case[case]
            self.assertIn('total', case_stats)
            self.assertIn('confirmed', case_stats)
    
    def test_html_template_data_integration(self):
        """Test that HTML template is properly integrated with data."""
        self.exporter.violations_data = self.sample_violations
        self.exporter.corners = {'ss', 'ff'}
        self.exporter.cases = {'test1', 'test2'}
        
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Check that HTML file exists
        html_file = self.exporter.web_display_dir / "index.html"
        self.assertTrue(html_file.exists())
        
        # Read HTML content
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Check for essential HTML elements expected by frontend
        essential_elements = [
            '<select id="cornerFilter"',
            '<select id="caseFilter"',
            '<table id="violationsTable"',
            '<div id="statisticsPanel"',
            'data/index.json'  # Data source reference
        ]
        
        for element in essential_elements:
            self.assertIn(element, html_content, f"Missing HTML element: {element}")
        
        # Check for CSS and JS file references
        expected_files = [
            'css/bootstrap.min.css',
            'css/custom.css',
            'js/jquery.min.js',
            'js/bootstrap.min.js',
            'js/datatables.min.js',
            'js/app.js'
        ]
        
        for file_ref in expected_files:
            self.assertIn(file_ref, html_content, f"Missing file reference: {file_ref}")
    
    def test_javascript_app_file_structure(self):
        """Test that JavaScript app file has proper structure."""
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Check that app.js exists
        app_js_file = self.exporter.web_display_dir / "js" / "app.js"
        self.assertTrue(app_js_file.exists())
        
        # Read JavaScript content
        with open(app_js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for essential JavaScript functions/classes
        essential_js_elements = [
            'ViolationDataManager',
            'loadData',
            'populateFilters',
            'filterViolations',
            'updateTable',
            'updateStatistics',
            'handleFilterChange'
        ]
        
        for element in essential_js_elements:
            self.assertIn(element, js_content, f"Missing JS element: {element}")
        
        # Check for data loading logic
        self.assertIn('data/index.json', js_content)
        self.assertIn('fetch(', js_content)  # Should use fetch API for data loading
    
    def test_css_files_exist(self):
        """Test that CSS files are properly copied."""
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        css_dir = self.exporter.web_display_dir / "css"
        self.assertTrue(css_dir.exists())
        
        expected_css_files = ['bootstrap.min.css', 'custom.css']
        for css_file in expected_css_files:
            css_file_path = css_dir / css_file
            self.assertTrue(css_file_path.exists(), f"Missing CSS file: {css_file}")
            
            # Check that files are not empty
            self.assertGreater(css_file_path.stat().st_size, 0)
    
    def test_responsive_design_elements(self):
        """Test that responsive design elements are present."""
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Read HTML content
        html_file = self.exporter.web_display_dir / "index.html"
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Check for responsive design elements
        responsive_elements = [
            'viewport',  # Meta viewport tag
            'container-fluid',  # Bootstrap responsive container
            'col-',  # Bootstrap responsive columns
            'table-responsive'  # Responsive table wrapper
        ]
        
        for element in responsive_elements:
            self.assertIn(element, html_content, f"Missing responsive element: {element}")
    
    def test_data_table_configuration(self):
        """Test that DataTables configuration is suitable for large datasets."""
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Read JavaScript content
        app_js_file = self.exporter.web_display_dir / "js" / "app.js"
        with open(app_js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for DataTables performance optimizations
        datatable_features = [
            'DataTable',  # DataTables initialization
            'pageLength',  # Pagination configuration
            'searching',  # Search functionality
            'ordering',  # Column sorting
            'responsive'  # Responsive tables
        ]
        
        for feature in datatable_features:
            self.assertIn(feature, js_content, f"Missing DataTable feature: {feature}")
    
    def test_error_handling_in_frontend(self):
        """Test that frontend has proper error handling."""
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Read JavaScript content
        app_js_file = self.exporter.web_display_dir / "js" / "app.js"
        with open(app_js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for error handling patterns
        error_handling_patterns = [
            'catch',  # Promise error handling
            'error',  # Error handling
            'console.error'  # Error logging
        ]
        
        for pattern in error_handling_patterns:
            self.assertIn(pattern, js_content, f"Missing error handling pattern: {pattern}")
    
    def test_filter_functionality_data_structure(self):
        """Test that data structure supports filter functionality."""
        self.exporter.violations_data = self.sample_violations
        self.exporter.corners = {'ss', 'ff'}
        self.exporter.cases = {'test1', 'test2'}
        
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Test corner filtering data
        corners_dir = self.exporter.data_dir / "corners"
        
        # Check ss corner cases
        ss_cases_file = corners_dir / "ss_cases.json"
        self.assertTrue(ss_cases_file.exists())
        
        with open(ss_cases_file, 'r') as f:
            ss_cases = json.load(f)
        
        # Should contain cases that have ss corner violations
        expected_ss_cases = ['test1', 'test2']  # Based on sample data
        for case in expected_ss_cases:
            self.assertIn(case, ss_cases)
        
        # Check ff corner cases
        ff_cases_file = corners_dir / "ff_cases.json"
        self.assertTrue(ff_cases_file.exists())
        
        with open(ff_cases_file, 'r') as f:
            ff_cases = json.load(f)
        
        # Should contain cases that have ff corner violations
        self.assertIn('test2', ff_cases)  # Based on sample data
    
    def test_performance_data_structure(self):
        """Test that data structure supports performance requirements."""
        # Create larger dataset
        large_violations = []
        for i in range(1000):
            violation = self.sample_violations[0].copy()
            violation['num'] = i + 1
            violation['corner'] = 'ss' if i % 2 == 0 else 'ff'
            violation['case'] = f'test{i % 10 + 1}'
            large_violations.append(violation)
        
        self.exporter.violations_data = large_violations
        self.exporter.corners = {'ss', 'ff'}
        self.exporter.cases = {f'test{i}' for i in range(1, 11)}
        
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Check that data is properly partitioned
        violations_dir = self.exporter.data_dir / "violations"
        violation_files = list(violations_dir.glob("*.json"))
        
        # Should have multiple files for different corner-case combinations
        self.assertGreater(len(violation_files), 1)
        
        # Check file sizes are reasonable (not too large for frontend)
        for violation_file in violation_files:
            file_size = violation_file.stat().st_size
            # Each file should be less than 1MB for good frontend performance
            self.assertLess(file_size, 1024 * 1024, f"File too large: {violation_file}")
    
    def test_accessibility_features(self):
        """Test that accessibility features are present in HTML."""
        result = self.exporter.export_all_data()
        self.assertTrue(result)
        
        # Read HTML content
        html_file = self.exporter.web_display_dir / "index.html"
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Check for accessibility features
        accessibility_features = [
            'aria-label',  # ARIA labels
            'role=',  # ARIA roles
            'alt=',  # Alt text for images (if any)
            'lang=',  # Language attribute
        ]
        
        # At least some accessibility features should be present
        accessibility_found = sum(1 for feature in accessibility_features if feature in html_content)
        self.assertGreater(accessibility_found, 0, "No accessibility features found")


if __name__ == '__main__':
    unittest.main()