#!/usr/bin/env python3
"""
Test script for data exporter performance optimization features.

This script tests the enhanced data exporter with performance monitoring,
compression, and memory optimization features.
"""

import os
import sys
import logging
import tempfile
import shutil
from pathlib import Path

# Add the web_display directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from data_exporter import DataExporter
from utils.file_utils import FileUtils
from utils.date_utils import DateUtils


def setup_logging():
    """Setup logging for the test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def create_test_data(temp_dir: Path, num_violations: int = 15000) -> None:
    """
    Create test data for performance testing.
    
    Args:
        temp_dir: Temporary directory for test data
        num_violations: Number of test violations to create
    """
    print(f"Creating test data with {num_violations} violations...")
    
    # Create VIOLATION_CHECK directory structure
    violation_check_dir = temp_dir / "VIOLATION_CHECK"
    violation_check_dir.mkdir(exist_ok=True)
    
    # Create test database (minimal structure for testing)
    import sqlite3
    db_path = violation_check_dir / "timing_violations.db"
    
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # Create tables
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS timing_violations (
            id INTEGER PRIMARY KEY,
            num INTEGER,
            hier TEXT,
            time_ns REAL,
            check_info TEXT,
            corner TEXT,
            case_name TEXT
        )
    """)
    
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS confirmation_records (
            id INTEGER PRIMARY KEY,
            violation_id INTEGER,
            status TEXT,
            confirmer TEXT,
            result TEXT,
            reason TEXT,
            confirmed_at TEXT,
            FOREIGN KEY (violation_id) REFERENCES timing_violations (id)
        )
    """)
    
    # Insert test data
    corners = ['ss_corner', 'ff_corner', 'tt_corner', 'sf_corner', 'fs_corner']
    cases = ['basic_test', 'stress_test', 'power_test', 'timing_test', 'functional_test']
    
    violation_id = 1
    for corner in corners:
        for case in cases:
            violations_per_case = num_violations // (len(corners) * len(cases))
            
            for i in range(violations_per_case):
                # Insert violation
                cursor.execute("""
                    INSERT INTO timing_violations 
                    (id, num, hier, time_ns, check_info, corner, case_name)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    violation_id,
                    i + 1,
                    f"top.cpu.core{i % 4}.path{i % 10}",
                    1000.0 + (i * 10.5),
                    f"Setup time violation {i}",
                    corner,
                    case
                ))
                
                # Insert confirmation (mark as confirmed)
                cursor.execute("""
                    INSERT INTO confirmation_records
                    (violation_id, status, confirmer, result, reason, confirmed_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    violation_id,
                    'confirmed',
                    f'engineer_{i % 5}',
                    'waived',
                    f'Test violation {i} - acceptable for this corner',
                    DateUtils.get_current_timestamp()
                ))
                
                violation_id += 1
    
    conn.commit()
    conn.close()
    
    print(f"Created test database with {violation_id - 1} violations")


def test_basic_export(temp_dir: Path) -> bool:
    """
    Test basic export functionality.
    
    Args:
        temp_dir: Temporary directory for test
        
    Returns:
        True if test passes
    """
    print("\n=== Testing Basic Export ===")
    
    try:
        exporter = DataExporter(str(temp_dir / "VIOLATION_CHECK"))
        
        # Test export
        success = exporter.export_all_data()
        
        if not success:
            print("❌ Export failed")
            return False
        
        # Check output files
        data_dir = temp_dir / "VIOLATION_CHECK" / "web_display" / "data"
        
        required_files = [
            "index.json",
            "statistics.json",
            "pagination_manifest.json",
            "performance_metrics.json"
        ]
        
        for file_name in required_files:
            file_path = data_dir / file_name
            if not file_path.exists():
                print(f"❌ Missing required file: {file_name}")
                return False
            print(f"✅ Found {file_name}")
        
        # Check directories
        required_dirs = ["corners", "violations"]
        for dir_name in required_dirs:
            dir_path = data_dir / dir_name
            if not dir_path.exists():
                print(f"❌ Missing required directory: {dir_name}")
                return False
            
            file_count = len(list(dir_path.glob("*")))
            print(f"✅ Found {dir_name} directory with {file_count} files")
        
        print("✅ Basic export test passed")
        return True
        
    except Exception as e:
        print(f"❌ Basic export test failed: {e}")
        return False


def test_performance_monitoring(temp_dir: Path) -> bool:
    """
    Test performance monitoring features.
    
    Args:
        temp_dir: Temporary directory for test
        
    Returns:
        True if test passes
    """
    print("\n=== Testing Performance Monitoring ===")
    
    try:
        exporter = DataExporter(str(temp_dir / "VIOLATION_CHECK"), 
                               enable_performance_monitoring=True)
        
        # Test export with monitoring
        success = exporter.export_all_data()
        
        if not success:
            print("❌ Export with monitoring failed")
            return False
        
        # Check performance metrics
        performance_summary = exporter.get_performance_summary()
        
        required_metrics = ['total_duration', 'violations_processed', 'stage_summary']
        for metric in required_metrics:
            if metric not in performance_summary:
                print(f"❌ Missing performance metric: {metric}")
                return False
        
        print(f"✅ Performance monitoring captured {len(performance_summary['stage_summary'])} stages")
        print(f"✅ Total duration: {performance_summary['total_duration']:.2f}s")
        print(f"✅ Violations processed: {performance_summary['violations_processed']}")
        
        # Check performance metrics file
        metrics_file = temp_dir / "VIOLATION_CHECK" / "web_display" / "data" / "performance_metrics.json"
        if not metrics_file.exists():
            print("❌ Performance metrics file not created")
            return False
        
        print("✅ Performance monitoring test passed")
        return True
        
    except Exception as e:
        print(f"❌ Performance monitoring test failed: {e}")
        return False


def test_compression_and_pagination(temp_dir: Path) -> bool:
    """
    Test compression and pagination features.
    
    Args:
        temp_dir: Temporary directory for test
        
    Returns:
        True if test passes
    """
    print("\n=== Testing Compression and Pagination ===")
    
    try:
        exporter = DataExporter(str(temp_dir / "VIOLATION_CHECK"))
        
        # Test export
        success = exporter.export_all_data()
        
        if not success:
            print("❌ Export failed")
            return False
        
        # Check pagination manifest
        manifest_file = temp_dir / "VIOLATION_CHECK" / "web_display" / "data" / "pagination_manifest.json"
        if not manifest_file.exists():
            print("❌ Pagination manifest not created")
            return False
        
        manifest_data = FileUtils.read_json(manifest_file)
        
        required_manifest_fields = ['total_violations', 'page_size', 'compressed', 'corner_cases']
        for field in required_manifest_fields:
            if field not in manifest_data:
                print(f"❌ Missing manifest field: {field}")
                return False
        
        print(f"✅ Pagination manifest created with {manifest_data['total_violations']} violations")
        print(f"✅ Page size: {manifest_data['page_size']}")
        print(f"✅ Compression enabled: {manifest_data['compressed']}")
        print(f"✅ Corner-case combinations: {len(manifest_data['corner_cases'])}")
        
        # Check for compressed files if compression is enabled
        violations_dir = temp_dir / "VIOLATION_CHECK" / "web_display" / "data" / "violations"
        if manifest_data['compressed']:
            gz_files = list(violations_dir.glob("*.gz"))
            if not gz_files:
                print("❌ No compressed files found despite compression being enabled")
                return False
            print(f"✅ Found {len(gz_files)} compressed violation files")
        
        # Check pagination for large datasets
        paginated_files = list(violations_dir.glob("*_page*.json*"))
        if paginated_files:
            print(f"✅ Found {len(paginated_files)} paginated files")
        
        print("✅ Compression and pagination test passed")
        return True
        
    except Exception as e:
        print(f"❌ Compression and pagination test failed: {e}")
        return False


def test_memory_efficiency(temp_dir: Path) -> bool:
    """
    Test memory efficiency for large datasets.
    
    Args:
        temp_dir: Temporary directory for test
        
    Returns:
        True if test passes
    """
    print("\n=== Testing Memory Efficiency ===")
    
    try:
        # Clean up and recreate for larger dataset
        violation_check_dir = temp_dir / "VIOLATION_CHECK"
        if violation_check_dir.exists():
            shutil.rmtree(violation_check_dir)
        
        print("Creating larger test dataset...")
        create_test_data(temp_dir, num_violations=25000)
        
        exporter = DataExporter(str(temp_dir / "VIOLATION_CHECK"), 
                               enable_performance_monitoring=True)
        
        # Test export with large dataset
        success = exporter.export_all_data()
        
        if not success:
            print("❌ Large dataset export failed")
            return False
        
        # Check that memory-efficient export was used
        performance_summary = exporter.get_performance_summary()
        
        if 'stage_summary' in performance_summary:
            stages = list(performance_summary['stage_summary'].keys())
            print(f"✅ Processing stages: {stages}")
        
        # Verify output structure
        data_dir = temp_dir / "VIOLATION_CHECK" / "web_display" / "data"
        violations_dir = data_dir / "violations"
        
        total_files = len(list(violations_dir.glob("*")))
        print(f"✅ Generated {total_files} violation files for large dataset")
        
        # Check file sizes
        total_size = FileUtils.get_directory_size(data_dir)
        print(f"✅ Total output size: {FileUtils.format_file_size(total_size)}")
        
        print("✅ Memory efficiency test passed")
        return True
        
    except Exception as e:
        print(f"❌ Memory efficiency test failed: {e}")
        return False


def main():
    """Main test function."""
    setup_logging()
    
    print("🚀 Starting Data Exporter Performance Tests")
    print("=" * 50)
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        print(f"Using temporary directory: {temp_path}")
        
        # Create initial test data
        create_test_data(temp_path, num_violations=12000)
        
        # Run tests
        tests = [
            ("Basic Export", test_basic_export),
            ("Performance Monitoring", test_performance_monitoring),
            ("Compression and Pagination", test_compression_and_pagination),
            ("Memory Efficiency", test_memory_efficiency)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                if test_func(temp_path):
                    passed += 1
                    print(f"✅ {test_name} PASSED")
                else:
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                print(f"❌ {test_name} FAILED with exception: {e}")
        
        # Summary
        print(f"\n{'='*50}")
        print(f"🏁 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed!")
            return 0
        else:
            print("💥 Some tests failed!")
            return 1


if __name__ == "__main__":
    sys.exit(main())