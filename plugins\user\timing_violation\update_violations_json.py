#!/usr/bin/env python3
"""
更新violations.json文件，添加样本数据
"""

import json
import sys
from pathlib import Path

def main():
    # 读取第一个违例文件作为样本
    sample_file = Path('VIOLATION_CHECK/web_display/data/violations/npg_f1_ffg_page_test_027_test.json')
    violations_file = Path('VIOLATION_CHECK/web_display/data/violations.json')
    
    if not sample_file.exists():
        print(f"样本文件不存在: {sample_file}")
        return 1
    
    try:
        with open(sample_file, 'r', encoding='utf-8') as f:
            sample_data = json.load(f)
        
        # 只取前10条作为示例
        sample_violations = sample_data[:10] if isinstance(sample_data, list) else sample_data.get('violations', [])[:10]
        
        # 更新violations.json
        violations_data = {
            'metadata': {
                'generated_at': '2025-08-07 19:00:00',
                'total_violations': 12206,
                'total_corners': 3,
                'total_cases': 1,
                'data_sources': ['pagination_manifest', 'sample_data']
            },
            'corners': ['npg_f1_ffg', 'npg_f2_ffg', 'npg_f2_ssg'],
            'cases': ['page_test_027_test'],
            'violations': sample_violations,
            'statistics': {
                'total_violations': 12206,
                'confirmed_violations': 12206,
                'pending_violations': 0,
                'confirmation_rate': 100,
                'latest_confirmation': '2025-08-07 15:18:41'
            }
        }
        
        with open(violations_file, 'w', encoding='utf-8') as f:
            json.dump(violations_data, f, indent=2, ensure_ascii=False)
        
        print(f'violations.json 已更新，包含 {len(sample_violations)} 条样本数据')
        print(f'文件路径: {violations_file.absolute()}')
        return 0
        
    except Exception as e:
        print(f'更新失败: {e}')
        return 1

if __name__ == "__main__":
    sys.exit(main())
