"""
Unit tests for File utilities.
"""

import unittest
import tempfile
import os
import json
import gzip
from pathlib import Path
from datetime import datetime

# Import the module to test
import sys
sys.path.append(str(Path(__file__).parent.parent))

from utils.file_utils import FileUtils


class TestFileUtils(unittest.TestCase):
    """Test cases for FileUtils class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.test_data = {
            'test': 'data',
            'numbers': [1, 2, 3],
            'nested': {'key': 'value'}
        }
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_ensure_directory_new(self):
        """Test creating new directory."""
        new_dir = os.path.join(self.temp_dir, "new_directory")
        
        result = FileUtils.ensure_directory(new_dir)
        
        self.assertTrue(result.exists())
        self.assertTrue(result.is_dir())
        self.assertEqual(str(result), new_dir)
    
    def test_ensure_directory_existing(self):
        """Test with existing directory."""
        existing_dir = Path(self.temp_dir)
        
        result = FileUtils.ensure_directory(existing_dir)
        
        self.assertTrue(result.exists())
        self.assertTrue(result.is_dir())
        self.assertEqual(result, existing_dir)
    
    def test_ensure_directory_nested(self):
        """Test creating nested directories."""
        nested_dir = os.path.join(self.temp_dir, "level1", "level2", "level3")
        
        result = FileUtils.ensure_directory(nested_dir)
        
        self.assertTrue(result.exists())
        self.assertTrue(result.is_dir())
    
    def test_copy_file_success(self):
        """Test successful file copy."""
        source_file = os.path.join(self.temp_dir, "source.txt")
        dest_file = os.path.join(self.temp_dir, "dest.txt")
        
        # Create source file
        with open(source_file, 'w') as f:
            f.write("test content")
        
        FileUtils.copy_file(source_file, dest_file)
        
        self.assertTrue(Path(dest_file).exists())
        with open(dest_file, 'r') as f:
            self.assertEqual(f.read(), "test content")
    
    def test_copy_file_to_nested_directory(self):
        """Test copying file to nested directory."""
        source_file = os.path.join(self.temp_dir, "source.txt")
        dest_file = os.path.join(self.temp_dir, "nested", "dir", "dest.txt")
        
        # Create source file
        with open(source_file, 'w') as f:
            f.write("test content")
        
        FileUtils.copy_file(source_file, dest_file)
        
        self.assertTrue(Path(dest_file).exists())
        with open(dest_file, 'r') as f:
            self.assertEqual(f.read(), "test content")
    
    def test_copy_file_nonexistent_source(self):
        """Test copying non-existent source file."""
        source_file = os.path.join(self.temp_dir, "nonexistent.txt")
        dest_file = os.path.join(self.temp_dir, "dest.txt")
        
        with self.assertRaises(OSError) as context:
            FileUtils.copy_file(source_file, dest_file)
        
        self.assertIn("Source file does not exist", str(context.exception))
    
    def test_copy_directory_success(self):
        """Test successful directory copy."""
        source_dir = os.path.join(self.temp_dir, "source_dir")
        dest_dir = os.path.join(self.temp_dir, "dest_dir")
        
        # Create source directory with files
        os.makedirs(source_dir)
        with open(os.path.join(source_dir, "file1.txt"), 'w') as f:
            f.write("content1")
        
        sub_dir = os.path.join(source_dir, "subdir")
        os.makedirs(sub_dir)
        with open(os.path.join(sub_dir, "file2.txt"), 'w') as f:
            f.write("content2")
        
        FileUtils.copy_directory(source_dir, dest_dir)
        
        self.assertTrue(Path(dest_dir).exists())
        self.assertTrue(Path(dest_dir, "file1.txt").exists())
        self.assertTrue(Path(dest_dir, "subdir", "file2.txt").exists())
        
        with open(os.path.join(dest_dir, "file1.txt"), 'r') as f:
            self.assertEqual(f.read(), "content1")
    
    def test_copy_directory_nonexistent_source(self):
        """Test copying non-existent source directory."""
        source_dir = os.path.join(self.temp_dir, "nonexistent_dir")
        dest_dir = os.path.join(self.temp_dir, "dest_dir")
        
        with self.assertRaises(OSError) as context:
            FileUtils.copy_directory(source_dir, dest_dir)
        
        self.assertIn("Source directory does not exist", str(context.exception))
    
    def test_write_json_uncompressed(self):
        """Test writing uncompressed JSON."""
        json_file = os.path.join(self.temp_dir, "test.json")
        
        FileUtils.write_json(self.test_data, json_file)
        
        self.assertTrue(Path(json_file).exists())
        
        with open(json_file, 'r') as f:
            loaded_data = json.load(f)
        
        self.assertEqual(loaded_data, self.test_data)
    
    def test_write_json_compressed(self):
        """Test writing compressed JSON."""
        json_file = os.path.join(self.temp_dir, "test.json.gz")
        
        FileUtils.write_json(self.test_data, json_file, compressed=True)
        
        self.assertTrue(Path(json_file).exists())
        
        with gzip.open(json_file, 'rt', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        self.assertEqual(loaded_data, self.test_data)
    
    def test_write_json_nested_directory(self):
        """Test writing JSON to nested directory."""
        json_file = os.path.join(self.temp_dir, "nested", "dir", "test.json")
        
        FileUtils.write_json(self.test_data, json_file)
        
        self.assertTrue(Path(json_file).exists())
    
    def test_read_json_uncompressed(self):
        """Test reading uncompressed JSON."""
        json_file = os.path.join(self.temp_dir, "test.json")
        
        # Write test data
        with open(json_file, 'w') as f:
            json.dump(self.test_data, f)
        
        loaded_data = FileUtils.read_json(json_file)
        
        self.assertEqual(loaded_data, self.test_data)
    
    def test_read_json_compressed(self):
        """Test reading compressed JSON."""
        json_file = os.path.join(self.temp_dir, "test.json.gz")
        
        # Write test data
        with gzip.open(json_file, 'wt', encoding='utf-8') as f:
            json.dump(self.test_data, f)
        
        loaded_data = FileUtils.read_json(json_file, compressed=True)
        
        self.assertEqual(loaded_data, self.test_data)
    
    def test_read_json_nonexistent(self):
        """Test reading non-existent JSON file."""
        json_file = os.path.join(self.temp_dir, "nonexistent.json")
        
        with self.assertRaises(OSError) as context:
            FileUtils.read_json(json_file)
        
        self.assertIn("JSON file does not exist", str(context.exception))
    
    def test_find_files_recursive(self):
        """Test finding files recursively."""
        # Create test file structure
        os.makedirs(os.path.join(self.temp_dir, "subdir1"))
        os.makedirs(os.path.join(self.temp_dir, "subdir2"))
        
        Path(self.temp_dir, "file1.txt").touch()
        Path(self.temp_dir, "file2.py").touch()
        Path(self.temp_dir, "subdir1", "file3.txt").touch()
        Path(self.temp_dir, "subdir2", "file4.py").touch()
        
        # Find all .txt files
        txt_files = FileUtils.find_files(self.temp_dir, "*.txt", recursive=True)
        
        self.assertEqual(len(txt_files), 2)
        txt_names = [f.name for f in txt_files]
        self.assertIn("file1.txt", txt_names)
        self.assertIn("file3.txt", txt_names)
    
    def test_find_files_non_recursive(self):
        """Test finding files non-recursively."""
        # Create test file structure
        os.makedirs(os.path.join(self.temp_dir, "subdir1"))
        
        Path(self.temp_dir, "file1.txt").touch()
        Path(self.temp_dir, "subdir1", "file2.txt").touch()
        
        # Find .txt files (non-recursive)
        txt_files = FileUtils.find_files(self.temp_dir, "*.txt", recursive=False)
        
        self.assertEqual(len(txt_files), 1)
        self.assertEqual(txt_files[0].name, "file1.txt")
    
    def test_find_files_nonexistent_directory(self):
        """Test finding files in non-existent directory."""
        nonexistent_dir = os.path.join(self.temp_dir, "nonexistent")
        
        files = FileUtils.find_files(nonexistent_dir, "*.txt")
        
        self.assertEqual(len(files), 0)
    
    def test_get_file_size(self):
        """Test getting file size."""
        test_file = os.path.join(self.temp_dir, "test.txt")
        test_content = "Hello, World!"
        
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        size = FileUtils.get_file_size(test_file)
        
        self.assertEqual(size, len(test_content))
    
    def test_get_file_size_nonexistent(self):
        """Test getting size of non-existent file."""
        nonexistent_file = os.path.join(self.temp_dir, "nonexistent.txt")
        
        size = FileUtils.get_file_size(nonexistent_file)
        
        self.assertEqual(size, 0)
    
    def test_get_file_modified_time(self):
        """Test getting file modification time."""
        test_file = os.path.join(self.temp_dir, "test.txt")
        
        with open(test_file, 'w') as f:
            f.write("test")
        
        mod_time = FileUtils.get_file_modified_time(test_file)
        
        self.assertIsInstance(mod_time, datetime)
        self.assertLess(abs((datetime.now() - mod_time).total_seconds()), 10)  # Within 10 seconds
    
    def test_is_file_newer(self):
        """Test comparing file modification times."""
        file1 = os.path.join(self.temp_dir, "file1.txt")
        file2 = os.path.join(self.temp_dir, "file2.txt")
        
        # Create first file
        with open(file1, 'w') as f:
            f.write("test1")
        
        # Wait a bit and create second file
        import time
        time.sleep(0.1)
        
        with open(file2, 'w') as f:
            f.write("test2")
        
        self.assertTrue(FileUtils.is_file_newer(file2, file1))
        self.assertFalse(FileUtils.is_file_newer(file1, file2))
    
    def test_clean_filename(self):
        """Test filename cleaning."""
        self.assertEqual(FileUtils.clean_filename("normal_file.txt"), "normal_file.txt")
        self.assertEqual(FileUtils.clean_filename("file<with>invalid:chars"), "file_with_invalid_chars")
        self.assertEqual(FileUtils.clean_filename("  file with spaces  "), "file with spaces")
        self.assertEqual(FileUtils.clean_filename(""), "unnamed")
        self.assertEqual(FileUtils.clean_filename("..."), "unnamed")
    
    def test_get_relative_path(self):
        """Test getting relative path."""
        base_path = self.temp_dir
        file_path = os.path.join(self.temp_dir, "subdir", "file.txt")
        
        rel_path = FileUtils.get_relative_path(file_path, base_path)
        
        expected = os.path.join("subdir", "file.txt")
        self.assertEqual(rel_path, expected)
    
    def test_backup_file(self):
        """Test file backup."""
        test_file = os.path.join(self.temp_dir, "test.txt")
        test_content = "original content"
        
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        backup_path = FileUtils.backup_file(test_file)
        
        self.assertIsNotNone(backup_path)
        self.assertTrue(backup_path.exists())
        
        with open(backup_path, 'r') as f:
            self.assertEqual(f.read(), test_content)
    
    def test_backup_file_custom_suffix(self):
        """Test file backup with custom suffix."""
        test_file = os.path.join(self.temp_dir, "test.txt")
        
        with open(test_file, 'w') as f:
            f.write("content")
        
        backup_path = FileUtils.backup_file(test_file, "custom_backup")
        
        self.assertIsNotNone(backup_path)
        self.assertTrue("custom_backup" in str(backup_path))
    
    def test_backup_file_nonexistent(self):
        """Test backing up non-existent file."""
        nonexistent_file = os.path.join(self.temp_dir, "nonexistent.txt")
        
        backup_path = FileUtils.backup_file(nonexistent_file)
        
        self.assertIsNone(backup_path)
    
    def test_remove_file(self):
        """Test file removal."""
        test_file = os.path.join(self.temp_dir, "test.txt")
        
        with open(test_file, 'w') as f:
            f.write("test")
        
        result = FileUtils.remove_file(test_file)
        
        self.assertTrue(result)
        self.assertFalse(Path(test_file).exists())
    
    def test_remove_file_nonexistent(self):
        """Test removing non-existent file."""
        nonexistent_file = os.path.join(self.temp_dir, "nonexistent.txt")
        
        result = FileUtils.remove_file(nonexistent_file)
        
        self.assertTrue(result)  # Should succeed (ignore_errors=True by default)
    
    def test_remove_directory(self):
        """Test directory removal."""
        test_dir = os.path.join(self.temp_dir, "test_dir")
        os.makedirs(test_dir)
        
        # Add some files
        with open(os.path.join(test_dir, "file.txt"), 'w') as f:
            f.write("test")
        
        result = FileUtils.remove_directory(test_dir)
        
        self.assertTrue(result)
        self.assertFalse(Path(test_dir).exists())
    
    def test_get_directory_size(self):
        """Test getting directory size."""
        test_dir = os.path.join(self.temp_dir, "test_dir")
        os.makedirs(test_dir)
        
        # Create files with known sizes
        with open(os.path.join(test_dir, "file1.txt"), 'w') as f:
            f.write("12345")  # 5 bytes
        
        with open(os.path.join(test_dir, "file2.txt"), 'w') as f:
            f.write("1234567890")  # 10 bytes
        
        size = FileUtils.get_directory_size(test_dir)
        
        self.assertEqual(size, 15)  # 5 + 10 bytes
    
    def test_get_directory_size_nonexistent(self):
        """Test getting size of non-existent directory."""
        nonexistent_dir = os.path.join(self.temp_dir, "nonexistent")
        
        size = FileUtils.get_directory_size(nonexistent_dir)
        
        self.assertEqual(size, 0)
    
    def test_format_file_size(self):
        """Test file size formatting."""
        self.assertEqual(FileUtils.format_file_size(0), "0 B")
        self.assertEqual(FileUtils.format_file_size(512), "512.0 B")
        self.assertEqual(FileUtils.format_file_size(1024), "1.0 KB")
        self.assertEqual(FileUtils.format_file_size(1024 * 1024), "1.0 MB")
        self.assertEqual(FileUtils.format_file_size(1024 * 1024 * 1024), "1.0 GB")
        self.assertEqual(FileUtils.format_file_size(1536), "1.5 KB")  # 1.5 KB


if __name__ == '__main__':
    unittest.main()