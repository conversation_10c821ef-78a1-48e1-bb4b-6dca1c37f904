"""
Date and time utilities for timing violation web display.

This module provides utility functions for date/time formatting,
parsing, and manipulation.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Union
import re


class DateUtils:
    """Utility class for date and time operations."""
    
    # Common date formats
    DATE_FORMATS = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M:%S.%f",
        "%Y/%m/%d %H:%M:%S",
        "%Y-%m-%d",
        "%Y/%m/%d",
        "%d-%m-%Y %H:%M:%S",
        "%d/%m/%Y %H:%M:%S",
        "%d-%m-%Y",
        "%d/%m/%Y",
        "%m-%d-%Y %H:%M:%S",
        "%m/%d/%Y %H:%M:%S",
        "%m-%d-%Y",
        "%m/%d/%Y",
        "%Y%m%d_%H%M%S",
        "%Y%m%d%H%M%S",
        "%Y%m%d",
    ]
    
    def __init__(self):
        """Initialize date utilities."""
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def parse_date(date_string: str) -> Optional[datetime]:
        """
        Parse date string using various common formats.
        
        Args:
            date_string: Date string to parse
            
        Returns:
            Parsed datetime object or None if parsing fails
        """
        if not date_string or not isinstance(date_string, str):
            return None
        
        date_string = date_string.strip()
        if not date_string:
            return None
        
        # Try each format
        for fmt in DateUtils.DATE_FORMATS:
            try:
                return datetime.strptime(date_string, fmt)
            except ValueError:
                continue
        
        # Try to parse ISO format
        try:
            return datetime.fromisoformat(date_string.replace('Z', '+00:00'))
        except ValueError:
            pass
        
        # Try to extract timestamp from string
        timestamp_match = re.search(r'\d{4}-\d{2}-\d{2}[\s_]\d{2}:\d{2}:\d{2}', date_string)
        if timestamp_match:
            try:
                return datetime.strptime(timestamp_match.group(), "%Y-%m-%d %H:%M:%S")
            except ValueError:
                pass
        
        return None
    
    @staticmethod
    def format_date(date_obj: datetime, format_string: str = "%Y-%m-%d %H:%M:%S") -> str:
        """
        Format datetime object to string.
        
        Args:
            date_obj: Datetime object to format
            format_string: Format string
            
        Returns:
            Formatted date string
        """
        if not isinstance(date_obj, datetime):
            return ""
        
        try:
            return date_obj.strftime(format_string)
        except (ValueError, TypeError):
            return ""
    
    @staticmethod
    def format_date_for_web(date_obj: datetime) -> str:
        """
        Format datetime for web display (user-friendly format).
        
        Args:
            date_obj: Datetime object to format
            
        Returns:
            Web-friendly formatted date string
        """
        return DateUtils.format_date(date_obj, "%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def format_date_for_filename(date_obj: datetime) -> str:
        """
        Format datetime for use in filenames (no special characters).
        
        Args:
            date_obj: Datetime object to format
            
        Returns:
            Filename-safe formatted date string
        """
        return DateUtils.format_date(date_obj, "%Y%m%d_%H%M%S")
    
    @staticmethod
    def get_current_timestamp() -> str:
        """
        Get current timestamp as formatted string.
        
        Returns:
            Current timestamp string
        """
        return DateUtils.format_date_for_web(datetime.now())
    
    @staticmethod
    def get_current_timestamp_for_filename() -> str:
        """
        Get current timestamp for filename use.
        
        Returns:
            Current timestamp string for filenames
        """
        return DateUtils.format_date_for_filename(datetime.now())
    
    @staticmethod
    def is_valid_date_string(date_string: str) -> bool:
        """
        Check if string can be parsed as a valid date.
        
        Args:
            date_string: Date string to validate
            
        Returns:
            True if string is a valid date
        """
        return DateUtils.parse_date(date_string) is not None
    
    @staticmethod
    def get_date_range_string(start_date: datetime, end_date: datetime) -> str:
        """
        Get formatted date range string.
        
        Args:
            start_date: Start date
            end_date: End date
            
        Returns:
            Formatted date range string
        """
        if not isinstance(start_date, datetime) or not isinstance(end_date, datetime):
            return ""
        
        start_str = DateUtils.format_date_for_web(start_date)
        end_str = DateUtils.format_date_for_web(end_date)
        
        return f"{start_str} - {end_str}"
    
    @staticmethod
    def get_relative_time_string(date_obj: datetime) -> str:
        """
        Get relative time string (e.g., "2 hours ago", "yesterday").
        
        Args:
            date_obj: Datetime object
            
        Returns:
            Relative time string
        """
        if not isinstance(date_obj, datetime):
            return ""
        
        now = datetime.now()
        diff = now - date_obj
        
        if diff.total_seconds() < 0:
            return "in the future"
        
        seconds = int(diff.total_seconds())
        
        if seconds < 60:
            return f"{seconds} seconds ago"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        elif seconds < 86400:
            hours = seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif seconds < 604800:
            days = seconds // 86400
            return f"{days} day{'s' if days != 1 else ''} ago"
        elif seconds < 2592000:
            weeks = seconds // 604800
            return f"{weeks} week{'s' if weeks != 1 else ''} ago"
        elif seconds < 31536000:
            months = seconds // 2592000
            return f"{months} month{'s' if months != 1 else ''} ago"
        else:
            years = seconds // 31536000
            return f"{years} year{'s' if years != 1 else ''} ago"
    
    @staticmethod
    def add_time_delta(date_obj: datetime, **kwargs) -> datetime:
        """
        Add time delta to datetime object.
        
        Args:
            date_obj: Base datetime object
            **kwargs: Keyword arguments for timedelta (days, hours, minutes, etc.)
            
        Returns:
            New datetime object with added time
        """
        if not isinstance(date_obj, datetime):
            return date_obj
        
        try:
            delta = timedelta(**kwargs)
            return date_obj + delta
        except (TypeError, ValueError):
            return date_obj
    
    @staticmethod
    def get_start_of_day(date_obj: datetime) -> datetime:
        """
        Get start of day (00:00:00) for given date.
        
        Args:
            date_obj: Datetime object
            
        Returns:
            Datetime object at start of day
        """
        if not isinstance(date_obj, datetime):
            return date_obj
        
        return date_obj.replace(hour=0, minute=0, second=0, microsecond=0)
    
    @staticmethod
    def get_end_of_day(date_obj: datetime) -> datetime:
        """
        Get end of day (23:59:59.999999) for given date.
        
        Args:
            date_obj: Datetime object
            
        Returns:
            Datetime object at end of day
        """
        if not isinstance(date_obj, datetime):
            return date_obj
        
        return date_obj.replace(hour=23, minute=59, second=59, microsecond=999999)
    
    @staticmethod
    def is_same_day(date1: datetime, date2: datetime) -> bool:
        """
        Check if two datetime objects are on the same day.
        
        Args:
            date1: First datetime object
            date2: Second datetime object
            
        Returns:
            True if both dates are on the same day
        """
        if not isinstance(date1, datetime) or not isinstance(date2, datetime):
            return False
        
        return date1.date() == date2.date()
    
    @staticmethod
    def get_duration_string(start_date: datetime, end_date: datetime) -> str:
        """
        Get duration string between two dates.
        
        Args:
            start_date: Start datetime
            end_date: End datetime
            
        Returns:
            Duration string (e.g., "2h 30m", "1d 5h")
        """
        if not isinstance(start_date, datetime) or not isinstance(end_date, datetime):
            return ""
        
        if end_date < start_date:
            start_date, end_date = end_date, start_date
        
        diff = end_date - start_date
        total_seconds = int(diff.total_seconds())
        
        if total_seconds < 60:
            return f"{total_seconds}s"
        elif total_seconds < 3600:
            minutes = total_seconds // 60
            seconds = total_seconds % 60
            if seconds > 0:
                return f"{minutes}m {seconds}s"
            else:
                return f"{minutes}m"
        elif total_seconds < 86400:
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            if minutes > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{hours}h"
        else:
            days = total_seconds // 86400
            hours = (total_seconds % 86400) // 3600
            if hours > 0:
                return f"{days}d {hours}h"
            else:
                return f"{days}d"
    
    @staticmethod
    def normalize_date_string(date_string: str) -> str:
        """
        Normalize date string to standard format.
        
        Args:
            date_string: Input date string
            
        Returns:
            Normalized date string or original if parsing fails
        """
        parsed_date = DateUtils.parse_date(date_string)
        if parsed_date:
            return DateUtils.format_date_for_web(parsed_date)
        return date_string
    
    @staticmethod
    def get_date_boundaries(date_string: str) -> tuple:
        """
        Get start and end boundaries for a date string.
        Useful for date range queries.
        
        Args:
            date_string: Date string (can be partial like "2024-01")
            
        Returns:
            Tuple of (start_datetime, end_datetime) or (None, None) if invalid
        """
        if not date_string:
            return None, None
        
        date_string = date_string.strip()
        
        try:
            # Try to parse as full date first
            parsed_date = DateUtils.parse_date(date_string)
            if parsed_date:
                start = DateUtils.get_start_of_day(parsed_date)
                end = DateUtils.get_end_of_day(parsed_date)
                return start, end
            
            # Handle partial dates
            if re.match(r'^\d{4}$', date_string):  # Year only
                start = datetime(int(date_string), 1, 1)
                end = datetime(int(date_string), 12, 31, 23, 59, 59, 999999)
                return start, end
            
            elif re.match(r'^\d{4}-\d{2}$', date_string):  # Year-Month
                year, month = map(int, date_string.split('-'))
                start = datetime(year, month, 1)
                # Get last day of month
                if month == 12:
                    end = datetime(year + 1, 1, 1) - timedelta(microseconds=1)
                else:
                    end = datetime(year, month + 1, 1) - timedelta(microseconds=1)
                return start, end
            
        except (ValueError, TypeError):
            pass
        
        return None, None