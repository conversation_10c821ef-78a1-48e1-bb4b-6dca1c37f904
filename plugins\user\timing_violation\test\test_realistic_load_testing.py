"""
Realistic Load Testing Framework

Generates test datasets with realistic violation patterns and sizes,
implements stress testing with 50K+ violation datasets, and adds
concurrent operation testing for multiple users processing violations.
"""

import unittest
import tempfile
import os
import time
import random
import threading
import multiprocessing
import psutil
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor, ProcessPoolExecutor
from typing import List, Dict, Any, Tuple
from unittest.mock import Mock, patch

try:
    from .performance_optimizer import PerformanceOptimizer
    from .parser import VioLogParser, HighPerformanceVioLogParser, HighPerformanceAsyncParser
    from .models import ViolationDataModel
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.insert(0, os.path.dirname(__file__))
    from performance_optimizer import PerformanceOptimizer
    from parser import VioLogParser, HighPerformanceVioLogParser, HighPerformanceAsyncParser
    from models import ViolationDataModel


class RealisticLoadTestingFramework(unittest.TestCase):
    """Realistic load testing framework for violation processing"""
    
    def setUp(self):
        """Set up test environment"""
        self.performance_optimizer = PerformanceOptimizer()
        self.temp_dir = tempfile.mkdtemp()
        self.load_test_results = {}
        
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def _generate_realistic_violation_patterns(self) -> List[Dict[str, Any]]:
        """Generate realistic violation patterns based on actual timing analysis scenarios"""
        patterns = [
            {
                "name": "setup_violations",
                "description": "Setup time violations in high-speed paths",
                "frequency": 0.4,  # 40% of violations
                "slack_range": (-0.001, -0.5),  # -1ps to -500ps
                "path_patterns": [
                    "/design/cpu_core/pipeline/stage_{}/critical_path_{}",
                    "/design/memory_controller/ddr_interface/timing_path_{}",
                    "/design/pcie_controller/high_speed_serdes/path_{}"
                ]
            },
            {
                "name": "hold_violations",
                "description": "Hold time violations in clock domains",
                "frequency": 0.3,  # 30% of violations
                "slack_range": (-0.001, -0.2),  # -1ps to -200ps
                "path_patterns": [
                    "/design/clock_domain_{}/register_to_register_{}",
                    "/design/async_fifo/gray_counter/path_{}",
                    "/design/reset_synchronizer/flop_chain_{}"
                ]
            },
            {
                "name": "recovery_violations",
                "description": "Recovery time violations after reset",
                "frequency": 0.2,  # 20% of violations
                "slack_range": (-0.001, -0.3),  # -1ps to -300ps
                "path_patterns": [
                    "/design/reset_domain_{}/recovery_path_{}",
                    "/design/power_management/isolation_cell_{}",
                    "/design/scan_chain/recovery_flop_{}"
                ]
            },
            {
                "name": "removal_violations",
                "description": "Removal time violations in scan chains",
                "frequency": 0.1,  # 10% of violations
                "slack_range": (-0.001, -0.1),  # -1ps to -100ps
                "path_patterns": [
                    "/design/scan_chain_{}/removal_path_{}",
                    "/design/test_mode/scan_flop_{}",
                    "/design/boundary_scan/cell_{}"
                ]
            }
        ]
        return patterns
    
    def _generate_realistic_violation_file(self, violation_count: int, file_path: str, 
                                         complexity_level: str = "medium") -> Dict[str, Any]:
        """Generate a realistic violation file with specified characteristics
        
        Args:
            violation_count: Number of violations to generate
            file_path: Path to save the test file
            complexity_level: "simple", "medium", or "complex" - affects path complexity
            
        Returns:
            Dictionary with file generation statistics
        """
        patterns = self._generate_realistic_violation_patterns()
        
        # Adjust complexity based on level
        complexity_multipliers = {
            "simple": 1,
            "medium": 3,
            "complex": 10
        }
        path_complexity = complexity_multipliers.get(complexity_level, 3)
        
        violation_stats = {
            "total_violations": violation_count,
            "complexity_level": complexity_level,
            "pattern_distribution": {},
            "slack_distribution": {"min": 0, "max": 0, "avg": 0},
            "path_length_stats": {"min": 0, "max": 0, "avg": 0}
        }
        
        with open(file_path, 'w') as f:
            # Write realistic header
            f.write("# Timing Violation Report\n")
            f.write("# Generated by: Realistic Load Testing Framework\n")
            f.write(f"# Total Violations: {violation_count}\n")
            f.write(f"# Complexity Level: {complexity_level}\n")
            f.write("# Report Date: 2024-01-15 14:30:22\n")
            f.write("\n")
            
            slack_values = []
            path_lengths = []
            
            for i in range(violation_count):
                # Select violation pattern based on frequency
                pattern = random.choices(patterns, weights=[p["frequency"] for p in patterns])[0]
                
                # Update pattern distribution stats
                pattern_name = pattern["name"]
                violation_stats["pattern_distribution"][pattern_name] = \
                    violation_stats["pattern_distribution"].get(pattern_name, 0) + 1
                
                # Generate realistic slack value
                slack_min, slack_max = pattern["slack_range"]
                slack = random.uniform(slack_min, slack_max)
                slack_values.append(slack)
                
                # Generate realistic path
                path_template = random.choice(pattern["path_patterns"])
                # Count the number of placeholders in the template
                placeholder_count = path_template.count('{}')
                path_indices = [random.randint(0, 99) for _ in range(placeholder_count)]
                path = path_template.format(*path_indices)
                path_lengths.append(len(path))
                
                # Generate timing values based on violation type
                if "setup" in pattern_name:
                    setup_time = random.uniform(8.0, 12.0)
                    hold_time = random.uniform(1.0, 3.0)
                elif "hold" in pattern_name:
                    setup_time = random.uniform(9.0, 11.0)
                    hold_time = random.uniform(2.0, 4.0)
                else:
                    setup_time = random.uniform(7.0, 13.0)
                    hold_time = random.uniform(1.5, 3.5)
                
                # Write violation in correct vio_summary.log format
                f.write(f"NUM : {i+1}\n")
                f.write(f"Hier : {path}\n")
                f.write(f"Time : {abs(slack * 1000000):.0f} FS\n")  # Convert to positive femtoseconds
                f.write(f"Check : {pattern['description']}\n")
                f.write("------------------------------------------------------------\n")
            
            # Update statistics
            violation_stats["slack_distribution"] = {
                "min": min(slack_values),
                "max": max(slack_values),
                "avg": sum(slack_values) / len(slack_values)
            }
            violation_stats["path_length_stats"] = {
                "min": min(path_lengths),
                "max": max(path_lengths),
                "avg": sum(path_lengths) / len(path_lengths)
            }
        
        return violation_stats
    
    def _measure_system_resources(self) -> Dict[str, Any]:
        """Measure current system resource usage"""
        process = psutil.Process()
        
        return {
            "cpu_percent": psutil.cpu_percent(interval=0.1),
            "memory_percent": psutil.virtual_memory().percent,
            "memory_available_gb": psutil.virtual_memory().available / (1024**3),
            "process_memory_mb": process.memory_info().rss / (1024**2),
            "process_cpu_percent": process.cpu_percent(),
            "disk_io": psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {},
            "network_io": psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {}
        }
    
    def _stress_test_single_file(self, violation_count: int, complexity: str) -> Dict[str, Any]:
        """Perform stress test on a single file with specified parameters"""
        test_file = os.path.join(self.temp_dir, f"stress_test_{violation_count}_{complexity}_vio_summary.log")
        
        # Generate realistic test file
        generation_start = time.time()
        file_stats = self._generate_realistic_violation_file(violation_count, test_file, complexity)
        generation_time = time.time() - generation_start
        
        # Measure initial system state
        initial_resources = self._measure_system_resources()
        
        # Select appropriate parser based on violation count
        if violation_count < 2000:
            parser = VioLogParser()
            parse_method = parser.parse_log_file
        elif violation_count < 20000:
            parser = HighPerformanceVioLogParser()
            parse_method = parser.parse_log_file_streaming
        else:
            # For very large files, use the high performance streaming parser directly
            parser = HighPerformanceVioLogParser()
            parse_method = parser.parse_log_file_streaming
        
        # Perform parsing with resource monitoring
        parse_start = time.time()
        try:
            result = parse_method(test_file)
            parse_success = True
            parse_error = None
        except Exception as e:
            result = None
            parse_success = False
            parse_error = str(e)
        
        parse_time = time.time() - parse_start
        final_resources = self._measure_system_resources()
        
        # Calculate resource usage
        memory_delta = final_resources["process_memory_mb"] - initial_resources["process_memory_mb"]
        
        return {
            "violation_count": violation_count,
            "complexity": complexity,
            "file_generation_time": generation_time,
            "parse_time": parse_time,
            "parse_success": parse_success,
            "parse_error": parse_error,
            "memory_usage_mb": memory_delta,
            "violations_per_second": violation_count / parse_time if parse_time > 0 and parse_success else 0,
            "initial_resources": initial_resources,
            "final_resources": final_resources,
            "file_stats": file_stats,
            "result_count": len(result) if result else 0
        }
    
    def test_stress_testing_50k_plus_violations(self):
        """Stress test with 50K+ violation datasets"""
        stress_test_cases = [
            (50000, "medium"),
            (75000, "medium"),
            (100000, "complex"),
            (150000, "complex"),
            (200000, "complex")
        ]
        
        for violation_count, complexity in stress_test_cases:
            with self.subTest(violation_count=violation_count, complexity=complexity):
                print(f"Running stress test: {violation_count} violations, {complexity} complexity")
                
                result = self._stress_test_single_file(violation_count, complexity)
                
                # Verify parsing succeeded
                self.assertTrue(result["parse_success"], 
                    f"Parsing failed for {violation_count} violations: {result['parse_error']}")
                
                # Verify performance requirements
                self.assertLess(result["parse_time"], 60.0,
                    f"Parse time {result['parse_time']:.1f}s exceeds 60s limit for {violation_count} violations")
                
                # Verify memory usage stays below 1GB
                self.assertLess(result["memory_usage_mb"], 1024,
                    f"Memory usage {result['memory_usage_mb']:.1f}MB exceeds 1GB limit for {violation_count} violations")
                
                # Verify minimum throughput
                min_throughput = 1000  # violations per second
                self.assertGreater(result["violations_per_second"], min_throughput,
                    f"Throughput {result['violations_per_second']:.0f} violations/sec below {min_throughput} for {violation_count} violations")
                
                # Store results for reporting
                self.load_test_results[f"stress_{violation_count}_{complexity}"] = result
    
    def test_concurrent_operation_testing(self):
        """Test concurrent operations with multiple users processing violations"""
        
        def concurrent_parsing_task(task_id: int, violation_count: int) -> Dict[str, Any]:
            """Task function for concurrent parsing"""
            test_file = os.path.join(self.temp_dir, f"concurrent_test_{task_id}_{violation_count}_vio_summary.log")
            
            # Generate test file
            file_stats = self._generate_realistic_violation_file(violation_count, test_file, "medium")
            
            # Parse with appropriate parser
            if violation_count < 2000:
                parser = VioLogParser()
                parse_method = parser.parse_log_file
            elif violation_count < 20000:
                parser = HighPerformanceVioLogParser()
                parse_method = parser.parse_log_file_streaming
            else:
                # For very large files, use the high performance streaming parser directly
                parser = HighPerformanceVioLogParser()
                parse_method = parser.parse_log_file_streaming
            
            start_time = time.time()
            try:
                result = parse_method(test_file)
                success = True
                error = None
            except Exception as e:
                result = None
                success = False
                error = str(e)
            
            end_time = time.time()
            
            return {
                "task_id": task_id,
                "violation_count": violation_count,
                "execution_time": end_time - start_time,
                "success": success,
                "error": error,
                "result_count": len(result) if result else 0
            }
        
        # Test concurrent parsing with multiple threads
        concurrent_test_cases = [
            (4, 5000),   # 4 threads, 5K violations each
            (8, 2500),   # 8 threads, 2.5K violations each
            (16, 1250),  # 16 threads, 1.25K violations each
        ]
        
        for thread_count, violations_per_thread in concurrent_test_cases:
            with self.subTest(threads=thread_count, violations_per_thread=violations_per_thread):
                print(f"Running concurrent test: {thread_count} threads, {violations_per_thread} violations each")
                
                # Measure initial system resources
                initial_resources = self._measure_system_resources()
                
                # Execute concurrent tasks
                start_time = time.time()
                with ThreadPoolExecutor(max_workers=thread_count) as executor:
                    futures = [
                        executor.submit(concurrent_parsing_task, i, violations_per_thread)
                        for i in range(thread_count)
                    ]
                    
                    results = [future.result() for future in futures]
                
                end_time = time.time()
                total_time = end_time - start_time
                
                # Measure final system resources
                final_resources = self._measure_system_resources()
                
                # Verify most tasks succeeded (allow some failures under high concurrency)
                successful_tasks = [r for r in results if r["success"]]
                failed_tasks = [r for r in results if not r["success"]]
                
                # Print error details for debugging
                if failed_tasks:
                    print(f"Failed tasks ({len(failed_tasks)}):")
                    for task in failed_tasks[:3]:  # Show first 3 errors
                        print(f"  Task {task['task_id']}: {task['error']}")
                
                min_success_rate = 0.8 if thread_count <= 8 else 0.5  # Lower threshold for high concurrency
                min_successful = int(thread_count * min_success_rate)
                self.assertGreaterEqual(len(successful_tasks), min_successful,
                    f"Only {len(successful_tasks)}/{thread_count} concurrent tasks succeeded, expected at least {min_successful}")
                
                # Verify total processing time is reasonable
                total_violations = thread_count * violations_per_thread
                overall_throughput = total_violations / total_time
                
                # Should achieve at least 500 violations/sec overall throughput
                self.assertGreater(overall_throughput, 500,
                    f"Overall throughput {overall_throughput:.0f} violations/sec too low for concurrent processing")
                
                # Verify system didn't become overloaded
                memory_increase = final_resources["process_memory_mb"] - initial_resources["process_memory_mb"]
                self.assertLess(memory_increase, 2048,
                    f"Memory increase {memory_increase:.1f}MB too high for concurrent processing")
                
                # Store results
                self.load_test_results[f"concurrent_{thread_count}x{violations_per_thread}"] = {
                    "thread_count": thread_count,
                    "violations_per_thread": violations_per_thread,
                    "total_violations": total_violations,
                    "total_time": total_time,
                    "overall_throughput": overall_throughput,
                    "successful_tasks": len(successful_tasks),
                    "individual_results": results,
                    "memory_increase_mb": memory_increase
                }
    
    def test_realistic_violation_pattern_processing(self):
        """Test processing of realistic violation patterns and distributions"""
        
        # Test different complexity levels
        complexity_levels = ["simple", "medium", "complex"]
        violation_count = 10000
        
        for complexity in complexity_levels:
            with self.subTest(complexity=complexity):
                test_file = os.path.join(self.temp_dir, f"pattern_test_{complexity}_vio_summary.log")
                
                # Generate file with realistic patterns
                file_stats = self._generate_realistic_violation_file(violation_count, test_file, complexity)
                
                # Parse the file
                parser = HighPerformanceVioLogParser()
                start_time = time.time()
                result = parser.parse_log_file_streaming(test_file)
                parse_time = time.time() - start_time
                
                # Verify pattern distribution is realistic
                pattern_dist = file_stats["pattern_distribution"]
                total_patterns = sum(pattern_dist.values())
                
                # Setup violations should be most common (around 40%)
                setup_ratio = pattern_dist.get("setup_violations", 0) / total_patterns
                self.assertGreater(setup_ratio, 0.3, "Setup violations should be at least 30%")
                self.assertLess(setup_ratio, 0.5, "Setup violations should be less than 50%")
                
                # Hold violations should be second most common (around 30%)
                hold_ratio = pattern_dist.get("hold_violations", 0) / total_patterns
                self.assertGreater(hold_ratio, 0.2, "Hold violations should be at least 20%")
                self.assertLess(hold_ratio, 0.4, "Hold violations should be less than 40%")
                
                # Verify parsing performance scales with complexity
                complexity_multipliers = {"simple": 1, "medium": 3, "complex": 10}
                expected_multiplier = complexity_multipliers[complexity]
                
                # More complex patterns should not significantly impact parse time
                # (good parser should handle path complexity efficiently)
                max_expected_time = 5.0 * (1 + expected_multiplier * 0.1)  # 10% increase per complexity level
                self.assertLess(parse_time, max_expected_time,
                    f"Parse time {parse_time:.2f}s too high for {complexity} complexity")
    
    def test_memory_pressure_under_load(self):
        """Test system behavior under memory pressure conditions"""
        
        # Create multiple large files to simulate memory pressure
        file_configs = [
            (20000, "medium"),
            (30000, "medium"),
            (40000, "complex"),
            (50000, "complex")
        ]
        
        test_files = []
        for i, (violation_count, complexity) in enumerate(file_configs):
            test_file = os.path.join(self.temp_dir, f"memory_pressure_{i}_vio_summary.log")
            self._generate_realistic_violation_file(violation_count, test_file, complexity)
            test_files.append((test_file, violation_count))
        
        # Monitor memory usage while processing all files
        initial_memory = psutil.virtual_memory().percent
        process = psutil.Process()
        initial_process_memory = process.memory_info().rss / (1024**2)
        
        results = []
        for test_file, violation_count in test_files:
            # Check available memory before processing
            available_memory_gb = psutil.virtual_memory().available / (1024**3)
            
            if available_memory_gb < 0.5:  # Less than 0.5GB available (adjusted for test environment)
                print(f"Skipping {test_file} due to low memory: {available_memory_gb:.1f}GB available")
                continue
            
            # Process file
            parser = HighPerformanceVioLogParser()
            start_time = time.time()
            
            try:
                result = parser.parse_log_file_streaming(test_file)
                success = True
                error = None
            except MemoryError:
                result = None
                success = False
                error = "MemoryError"
            except Exception as e:
                result = None
                success = False
                error = str(e)
            
            end_time = time.time()
            
            current_memory = psutil.virtual_memory().percent
            current_process_memory = process.memory_info().rss / (1024**2)
            
            results.append({
                "file": test_file,
                "violation_count": violation_count,
                "success": success,
                "error": error,
                "execution_time": end_time - start_time,
                "system_memory_percent": current_memory,
                "process_memory_mb": current_process_memory,
                "result_count": len(result) if result else 0
            })
            
            # Verify system memory usage doesn't exceed 95% (adjusted for test environment)
            self.assertLess(current_memory, 95.0,
                f"System memory usage {current_memory:.1f}% exceeds 95% threshold")
            
            # Verify process memory usage doesn't exceed 1GB
            self.assertLess(current_process_memory, 1024,
                f"Process memory usage {current_process_memory:.1f}MB exceeds 1GB limit")
        
        # Verify at least some files were processed successfully
        successful_results = [r for r in results if r["success"]]
        self.assertGreater(len(successful_results), 0,
            "No files were processed successfully under memory pressure")
        
        self.load_test_results["memory_pressure"] = results
    
    def generate_load_test_report(self) -> str:
        """Generate comprehensive load test report"""
        report = []
        report.append("=== Realistic Load Testing Report ===\n")
        
        # Stress test results
        stress_results = {k: v for k, v in self.load_test_results.items() if k.startswith("stress_")}
        if stress_results:
            report.append("Stress Test Results (50K+ violations):")
            report.append("-" * 40)
            for test_name, result in stress_results.items():
                report.append(f"  {test_name}:")
                report.append(f"    Violations: {result['violation_count']:,}")
                report.append(f"    Parse Time: {result['parse_time']:.2f}s")
                report.append(f"    Memory Usage: {result['memory_usage_mb']:.1f}MB")
                report.append(f"    Throughput: {result['violations_per_second']:.0f} violations/sec")
                report.append(f"    Success: {result['parse_success']}")
                if not result['parse_success']:
                    report.append(f"    Error: {result['parse_error']}")
                report.append("")
        
        # Concurrent test results
        concurrent_results = {k: v for k, v in self.load_test_results.items() if k.startswith("concurrent_")}
        if concurrent_results:
            report.append("\nConcurrent Operation Results:")
            report.append("-" * 30)
            for test_name, result in concurrent_results.items():
                report.append(f"  {test_name}:")
                report.append(f"    Threads: {result['thread_count']}")
                report.append(f"    Total Violations: {result['total_violations']:,}")
                report.append(f"    Total Time: {result['total_time']:.2f}s")
                report.append(f"    Overall Throughput: {result['overall_throughput']:.0f} violations/sec")
                report.append(f"    Successful Tasks: {result['successful_tasks']}/{result['thread_count']}")
                report.append(f"    Memory Increase: {result['memory_increase_mb']:.1f}MB")
                report.append("")
        
        # Memory pressure results
        if "memory_pressure" in self.load_test_results:
            memory_results = self.load_test_results["memory_pressure"]
            report.append("\nMemory Pressure Test Results:")
            report.append("-" * 30)
            successful_files = [r for r in memory_results if r["success"]]
            report.append(f"  Files Processed Successfully: {len(successful_files)}/{len(memory_results)}")
            
            if successful_files:
                avg_memory = sum(r["process_memory_mb"] for r in successful_files) / len(successful_files)
                max_memory = max(r["process_memory_mb"] for r in successful_files)
                report.append(f"  Average Process Memory: {avg_memory:.1f}MB")
                report.append(f"  Peak Process Memory: {max_memory:.1f}MB")
        
        return "\n".join(report)


if __name__ == '__main__':
    # Run load tests with detailed output
    unittest.main(verbosity=2)