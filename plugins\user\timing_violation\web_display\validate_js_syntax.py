#!/usr/bin/env python3
"""
Simple JavaScript syntax validation script.
"""

import re
import sys
from pathlib import Path

def validate_js_syntax(js_file_path):
    """Validate basic JavaScript syntax."""
    try:
        with open(js_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Basic syntax checks
        errors = []
        
        # Check for balanced braces
        brace_count = 0
        paren_count = 0
        bracket_count = 0
        
        for i, char in enumerate(content):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
            elif char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
            elif char == '[':
                bracket_count += 1
            elif char == ']':
                bracket_count -= 1
        
        if brace_count != 0:
            errors.append(f"Unbalanced braces: {brace_count}")
        if paren_count != 0:
            errors.append(f"Unbalanced parentheses: {paren_count}")
        if bracket_count != 0:
            errors.append(f"Unbalanced brackets: {bracket_count}")
        
        # Check for common syntax errors
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('//') or line.startswith('/*'):
                continue
            
            # Check for missing semicolons after statements (basic check)
            if (line.endswith('}') and 
                not line.endswith('};') and 
                not line.endswith('} else {') and
                not line.endswith('} catch') and
                not line.endswith('} finally')):
                # This is likely a function/class/if block, not an error
                pass
            
            # Check for unterminated strings (basic check)
            single_quotes = line.count("'") - line.count("\\'")
            double_quotes = line.count('"') - line.count('\\"')
            
            if single_quotes % 2 != 0:
                errors.append(f"Line {line_num}: Possible unterminated single quote")
            if double_quotes % 2 != 0:
                errors.append(f"Line {line_num}: Possible unterminated double quote")
        
        # Check for class structure
        if 'class ViolationDataManager' not in content:
            errors.append("Missing ViolationDataManager class")
        
        # Check for essential methods
        essential_methods = [
            'constructor()',
            'init()',
            'loadInitialData()',
            'initializeDataTable()',
            'setupEventListeners()'
        ]
        
        for method in essential_methods:
            if method not in content:
                errors.append(f"Missing essential method: {method}")
        
        return errors
        
    except Exception as e:
        return [f"Error reading file: {e}"]

def main():
    js_file = Path(__file__).parent / "web_template" / "js" / "app.js"
    
    if not js_file.exists():
        print(f"❌ JavaScript file not found: {js_file}")
        return 1
    
    print(f"🔍 Validating JavaScript syntax: {js_file}")
    
    errors = validate_js_syntax(js_file)
    
    if errors:
        print("❌ JavaScript syntax validation failed:")
        for error in errors:
            print(f"  - {error}")
        return 1
    else:
        print("✅ JavaScript syntax validation passed!")
        print("📊 File statistics:")
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            
        print(f"  - Total lines: {len(lines)}")
        print(f"  - Total characters: {len(content)}")
        print(f"  - Classes found: {content.count('class ')}")
        print(f"  - Functions found: {content.count('function ')}")
        print(f"  - Async functions: {content.count('async ')}")
        
        return 0

if __name__ == "__main__":
    sys.exit(main())