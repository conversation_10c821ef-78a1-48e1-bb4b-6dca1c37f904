"""
Comprehensive Performance Testing Suite

Integrates all performance testing frameworks and provides a unified
test runner for violation-based performance testing.
"""

import unittest
import sys
import os
import time
import json
from typing import Dict, List, Any
from datetime import datetime

# Import all test modules
try:
    from .test_violation_count_scenarios import ViolationCountScenarioTests
    from .test_performance_regression import PerformanceRegressionTests
    from .test_realistic_load_testing import RealisticLoadTestingFramework
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.insert(0, os.path.dirname(__file__))
    from test_violation_count_scenarios import ViolationCountScenarioTests
    from test_performance_regression import PerformanceRegressionTests
    from test_realistic_load_testing import RealisticLoadTestingFramework


class ComprehensivePerformanceTestSuite:
    """Comprehensive test suite runner for all performance tests"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
    def run_all_tests(self, verbosity: int = 2) -> Dict[str, Any]:
        """Run all performance test suites
        
        Args:
            verbosity: Test output verbosity level
            
        Returns:
            Dictionary containing all test results and summary
        """
        self.start_time = time.time()
        print("=" * 80)
        print("COMPREHENSIVE PERFORMANCE TEST SUITE")
        print("=" * 80)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Run violation count scenario tests
        print("1. Running Violation Count Scenario Tests...")
        print("-" * 50)
        scenario_results = self._run_test_suite(ViolationCountScenarioTests, verbosity)
        self.test_results['violation_count_scenarios'] = scenario_results
        
        # Run performance regression tests
        print("\n2. Running Performance Regression Tests...")
        print("-" * 50)
        regression_results = self._run_test_suite(PerformanceRegressionTests, verbosity)
        self.test_results['performance_regression'] = regression_results
        
        # Run realistic load testing
        print("\n3. Running Realistic Load Testing...")
        print("-" * 50)
        load_test_results = self._run_test_suite(RealisticLoadTestingFramework, verbosity)
        self.test_results['realistic_load_testing'] = load_test_results
        
        self.end_time = time.time()
        
        # Generate comprehensive report
        self._generate_comprehensive_report()
        
        return self.test_results
    
    def _run_test_suite(self, test_class, verbosity: int) -> Dict[str, Any]:
        """Run a specific test suite and capture results
        
        Args:
            test_class: Test class to run
            verbosity: Test output verbosity level
            
        Returns:
            Dictionary containing test results
        """
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=verbosity, stream=sys.stdout)
        
        start_time = time.time()
        result = runner.run(suite)
        end_time = time.time()
        
        # Capture test results
        test_results = {
            'test_class': test_class.__name__,
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'skipped': len(result.skipped) if hasattr(result, 'skipped') else 0,
            'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun if result.testsRun > 0 else 0,
            'execution_time': end_time - start_time,
            'failure_details': [{'test': str(test), 'error': error} for test, error in result.failures],
            'error_details': [{'test': str(test), 'error': error} for test, error in result.errors]
        }
        
        return test_results
    
    def _generate_comprehensive_report(self) -> None:
        """Generate and save comprehensive test report"""
        total_execution_time = self.end_time - self.start_time
        
        report = {
            'test_suite': 'Comprehensive Performance Test Suite',
            'execution_date': datetime.now().isoformat(),
            'total_execution_time': total_execution_time,
            'summary': self._generate_summary(),
            'detailed_results': self.test_results,
            'recommendations': self._generate_recommendations()
        }
        
        # Save report to file
        report_file = os.path.join(os.path.dirname(__file__), 'comprehensive_performance_report.json')
        try:
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\nComprehensive report saved to: {report_file}")
        except IOError as e:
            print(f"Warning: Could not save report to file: {e}")
        
        # Print summary to console
        self._print_summary_report()
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate summary statistics across all test suites"""
        total_tests = sum(results['tests_run'] for results in self.test_results.values())
        total_failures = sum(results['failures'] for results in self.test_results.values())
        total_errors = sum(results['errors'] for results in self.test_results.values())
        total_skipped = sum(results['skipped'] for results in self.test_results.values())
        
        overall_success_rate = (total_tests - total_failures - total_errors) / total_tests if total_tests > 0 else 0
        
        return {
            'total_test_suites': len(self.test_results),
            'total_tests': total_tests,
            'total_failures': total_failures,
            'total_errors': total_errors,
            'total_skipped': total_skipped,
            'overall_success_rate': overall_success_rate,
            'suite_breakdown': {
                name: {
                    'tests': results['tests_run'],
                    'success_rate': results['success_rate'],
                    'execution_time': results['execution_time']
                }
                for name, results in self.test_results.items()
            }
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        # Check overall success rate
        summary = self._generate_summary()
        if summary['overall_success_rate'] < 0.95:
            recommendations.append(
                f"Overall success rate ({summary['overall_success_rate']:.1%}) is below 95%. "
                "Review failed tests and address performance issues."
            )
        
        # Check individual suite performance
        for suite_name, results in self.test_results.items():
            if results['success_rate'] < 0.90:
                recommendations.append(
                    f"{suite_name} has low success rate ({results['success_rate']:.1%}). "
                    "Focus on addressing failures in this test suite."
                )
            
            if results['execution_time'] > 300:  # 5 minutes
                recommendations.append(
                    f"{suite_name} took {results['execution_time']:.1f}s to complete. "
                    "Consider optimizing test execution time."
                )
        
        # Check for specific failure patterns
        all_failures = []
        all_errors = []
        
        for results in self.test_results.values():
            all_failures.extend(results['failure_details'])
            all_errors.extend(results['error_details'])
        
        # Memory-related issues
        memory_issues = [f for f in all_failures + all_errors if 'memory' in f['error'].lower()]
        if memory_issues:
            recommendations.append(
                f"Found {len(memory_issues)} memory-related test failures. "
                "Review memory optimization strategies and increase memory limits if necessary."
            )
        
        # Performance-related issues
        performance_issues = [f for f in all_failures + all_errors if any(
            keyword in f['error'].lower() for keyword in ['timeout', 'slow', 'performance', 'regression']
        )]
        if performance_issues:
            recommendations.append(
                f"Found {len(performance_issues)} performance-related test failures. "
                "Review performance optimization implementations and benchmark thresholds."
            )
        
        if not recommendations:
            recommendations.append("All tests passed successfully! Performance optimization is working well.")
        
        return recommendations
    
    def _print_summary_report(self) -> None:
        """Print summary report to console"""
        print("\n" + "=" * 80)
        print("COMPREHENSIVE PERFORMANCE TEST SUMMARY")
        print("=" * 80)
        
        summary = self._generate_summary()
        
        print(f"Total Execution Time: {self.end_time - self.start_time:.1f} seconds")
        print(f"Test Suites Run: {summary['total_test_suites']}")
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Failures: {summary['total_failures']}")
        print(f"Errors: {summary['total_errors']}")
        print(f"Skipped: {summary['total_skipped']}")
        print(f"Overall Success Rate: {summary['overall_success_rate']:.1%}")
        print()
        
        # Suite breakdown
        print("Suite Breakdown:")
        print("-" * 40)
        for suite_name, suite_data in summary['suite_breakdown'].items():
            print(f"  {suite_name}:")
            print(f"    Tests: {suite_data['tests']}")
            print(f"    Success Rate: {suite_data['success_rate']:.1%}")
            print(f"    Execution Time: {suite_data['execution_time']:.1f}s")
            print()
        
        # Recommendations
        recommendations = self._generate_recommendations()
        if recommendations:
            print("Recommendations:")
            print("-" * 20)
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
            print()
        
        print("=" * 80)
    
    def run_quick_smoke_test(self) -> bool:
        """Run a quick smoke test to verify basic functionality
        
        Returns:
            True if smoke test passes, False otherwise
        """
        print("Running Quick Smoke Test...")
        print("-" * 30)
        
        try:
            # Test basic violation count scenario
            scenario_test = ViolationCountScenarioTests()
            scenario_test.setUp()
            
            # Generate small test file with proper vio_summary.log format
            import tempfile
            temp_dir = tempfile.mkdtemp()
            test_file = os.path.join(temp_dir, "vio_summary.log")
            
            # Generate a proper vio_summary.log format file
            with open(test_file, 'w') as f:
                f.write("# Timing Violation Summary\n")
                f.write("# Test file for smoke test\n")
                f.write("\n")
                for i in range(10):
                    f.write(f"NUM: {i+1:03d}\n")
                    f.write(f"Hier: /design/test_module_{i}/signal_{i}\n")
                    f.write(f"Time: {10.5 + i * 0.1:.3f}ns\n")
                    f.write(f"Check: setup_check_{i}\n")
                    f.write("\n")
            
            # Test basic functionality without actual parsing (to avoid Qt issues)
            # Just verify file was created and has content
            if os.path.exists(test_file) and os.path.getsize(test_file) > 0:
                print("✓ Test file generation: PASSED")
            else:
                print("✗ Test file generation: FAILED")
                return False
            
            # Test performance optimizer initialization
            try:
                from performance_optimizer import PerformanceOptimizer
                optimizer = PerformanceOptimizer()
                print("✓ Performance optimizer initialization: PASSED")
            except Exception as e:
                print(f"✗ Performance optimizer initialization: FAILED - {e}")
                return False
            
            scenario_test.tearDown()
            
            # Clean up
            import shutil
            shutil.rmtree(temp_dir)
            
            print("✓ Smoke test passed")
            return True
            
        except Exception as e:
            print(f"✗ Smoke test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_performance_baseline_update(self) -> None:
        """Update performance baselines for regression testing"""
        print("Updating Performance Baselines...")
        print("-" * 35)
        
        regression_test = PerformanceRegressionTests()
        regression_test.setUp()
        regression_test.update_baseline_metrics()
        regression_test.tearDown()
        
        print("✓ Performance baselines updated")


def main():
    """Main entry point for comprehensive performance testing"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Comprehensive Performance Test Suite')
    parser.add_argument('--smoke-test', action='store_true', 
                       help='Run quick smoke test only')
    parser.add_argument('--update-baseline', action='store_true',
                       help='Update performance baselines')
    parser.add_argument('--verbosity', type=int, default=2, choices=[0, 1, 2],
                       help='Test output verbosity level')
    
    args = parser.parse_args()
    
    suite = ComprehensivePerformanceTestSuite()
    
    if args.smoke_test:
        success = suite.run_quick_smoke_test()
        sys.exit(0 if success else 1)
    
    if args.update_baseline:
        suite.run_performance_baseline_update()
        return
    
    # Run full test suite
    results = suite.run_all_tests(args.verbosity)
    
    # Exit with error code if tests failed
    summary = suite._generate_summary()
    success = summary['overall_success_rate'] >= 0.95
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()