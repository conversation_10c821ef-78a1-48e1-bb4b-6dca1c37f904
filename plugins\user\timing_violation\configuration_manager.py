"""
时序违例插件配置管理器

提供基于违例数量的性能配置文件管理功能。
"""

import os
import json
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from PyQt5.QtCore import QObject, pyqtSignal


@dataclass
class PerformanceProfile:
    """性能配置文件数据类"""
    name: str
    description: str
    violation_count_range: tuple  # (min, max) violation count range
    parser_config: Dict[str, Any]
    display_config: Dict[str, Any]
    memory_config: Dict[str, Any]
    ui_config: Dict[str, Any]
    optimization_config: Dict[str, Any]
    created_time: float = 0.0
    last_used_time: float = 0.0
    usage_count: int = 0
    performance_score: float = 0.0  # User satisfaction score


class ConfigurationManager(QObject):
    """配置管理器 - 管理基于违例数量的性能配置文件"""
    
    # 信号定义
    profile_changed = pyqtSignal(str)  # 配置文件名称
    profile_created = pyqtSignal(str)  # 新创建的配置文件名称
    profile_deleted = pyqtSignal(str)  # 删除的配置文件名称
    auto_profile_selected = pyqtSignal(str, int)  # 自动选择的配置文件名称, 违例数量
    
    def __init__(self, config_dir: str = None):
        super().__init__()
        
        # 配置文件存储目录
        self.config_dir = config_dir or os.path.join(
            os.path.dirname(__file__), 'config'
        )
        os.makedirs(self.config_dir, exist_ok=True)
        
        # 配置文件路径
        self.profiles_file = os.path.join(self.config_dir, 'performance_profiles.json')
        self.settings_file = os.path.join(self.config_dir, 'configuration_settings.json')
        self.usage_history_file = os.path.join(self.config_dir, 'usage_history.json')
        
        # 内存中的配置数据
        self.profiles: Dict[str, PerformanceProfile] = {}
        self.current_profile_name: str = ""
        self.settings: Dict[str, Any] = {}
        self.usage_history: List[Dict[str, Any]] = []
        
        # 违例数量阈值配置
        self.violation_thresholds = {
            'small_dataset': 2000,      # < 2K violations
            'medium_dataset': 20000,    # 2K-20K violations  
            'large_dataset': 50000,     # 20K-50K violations
            'very_large_dataset': 100000 # > 50K violations
        }
        
        # 初始化配置
        self._initialize_configuration()
    
    def _initialize_configuration(self):
        """初始化配置管理器"""
        try:
            # 加载现有配置
            self._load_profiles()
            self._load_settings()
            self._load_usage_history()
            
            # 如果没有配置文件，创建默认配置文件
            if not self.profiles:
                self._create_default_profiles()
                self._save_profiles()
            
            # 设置默认当前配置文件
            if not self.current_profile_name and self.profiles:
                self.current_profile_name = "balanced"
                
        except Exception as e:
            print(f"配置管理器初始化失败: {str(e)}")
            # 创建最基本的配置
            self._create_emergency_profile()
    
    def _create_default_profiles(self):
        """创建默认性能配置文件"""
        current_time = time.time()
        
        # 小数据集配置文件 (< 2K violations)
        self.profiles["small_dataset"] = PerformanceProfile(
            name="small_dataset",
            description="小数据集优化配置 (< 2,000个违例)",
            violation_count_range=(0, 2000),
            parser_config={
                "parser_type": "standard_async",
                "batch_size": 500,
                "use_streaming": False,
                "progress_interval": 1000,
                "gc_interval": 5000,
                "timeout_seconds": 30
            },
            display_config={
                "display_mode": "standard_table",
                "use_pagination": False,
                "page_size": 1000,
                "virtual_scrolling": False,
                "lazy_loading": False,
                "row_height": 35,
                "column_auto_resize": True
            },
            memory_config={
                "memory_limit_mb": 100,
                "enable_memory_monitoring": False,
                "aggressive_gc": False,
                "cache_size": 1000,
                "preload_data": True
            },
            ui_config={
                "ui_update_interval": 100,
                "show_progress_bar": True,
                "enable_animations": True,
                "responsive_ui": True,
                "background_processing": False
            },
            optimization_config={
                "priority": "user_experience",
                "enable_caching": True,
                "prefetch_data": False,
                "optimize_for": "responsiveness",
                "fallback_enabled": True
            },
            created_time=current_time
        )
        
        # 中等数据集配置文件 (2K-20K violations)
        self.profiles["medium_dataset"] = PerformanceProfile(
            name="medium_dataset",
            description="中等数据集优化配置 (2,000-20,000个违例)",
            violation_count_range=(2000, 20000),
            parser_config={
                "parser_type": "high_performance_async",
                "batch_size": 2000,
                "use_streaming": False,
                "progress_interval": 5000,
                "gc_interval": 10000,
                "timeout_seconds": 60
            },
            display_config={
                "display_mode": "high_performance_table",
                "use_pagination": True,
                "page_size": 200,
                "virtual_scrolling": True,
                "lazy_loading": True,
                "row_height": 35,
                "column_auto_resize": False
            },
            memory_config={
                "memory_limit_mb": 300,
                "enable_memory_monitoring": True,
                "aggressive_gc": False,
                "cache_size": 5000,
                "preload_data": False
            },
            ui_config={
                "ui_update_interval": 200,
                "show_progress_bar": True,
                "enable_animations": False,
                "responsive_ui": True,
                "background_processing": True
            },
            optimization_config={
                "priority": "balanced",
                "enable_caching": True,
                "prefetch_data": True,
                "optimize_for": "balanced",
                "fallback_enabled": True
            },
            created_time=current_time
        )
        
        # 大数据集配置文件 (20K-50K violations)
        self.profiles["large_dataset"] = PerformanceProfile(
            name="large_dataset",
            description="大数据集优化配置 (20,000-50,000个违例)",
            violation_count_range=(20000, 50000),
            parser_config={
                "parser_type": "high_performance_streaming",
                "batch_size": 5000,
                "use_streaming": True,
                "progress_interval": 10000,
                "gc_interval": 20000,
                "timeout_seconds": 120,
                "chunk_size": 10000
            },
            display_config={
                "display_mode": "virtual_table_with_lazy_loading",
                "use_pagination": True,
                "page_size": 100,
                "virtual_scrolling": True,
                "lazy_loading": True,
                "row_height": 35,
                "column_auto_resize": False
            },
            memory_config={
                "memory_limit_mb": 500,
                "enable_memory_monitoring": True,
                "aggressive_gc": True,
                "cache_size": 2000,
                "preload_data": False
            },
            ui_config={
                "ui_update_interval": 500,
                "show_progress_bar": True,
                "enable_animations": False,
                "responsive_ui": False,
                "background_processing": True
            },
            optimization_config={
                "priority": "memory_efficiency",
                "enable_caching": False,
                "prefetch_data": False,
                "optimize_for": "memory",
                "fallback_enabled": True
            },
            created_time=current_time
        )
        
        # 超大数据集配置文件 (> 50K violations)
        self.profiles["very_large_dataset"] = PerformanceProfile(
            name="very_large_dataset",
            description="超大数据集优化配置 (> 50,000个违例)",
            violation_count_range=(50000, float('inf')),
            parser_config={
                "parser_type": "streaming_with_chunking",
                "batch_size": 10000,
                "use_streaming": True,
                "progress_interval": 25000,
                "gc_interval": 50000,
                "timeout_seconds": 300,
                "chunk_size": 25000
            },
            display_config={
                "display_mode": "paginated_virtual_table",
                "use_pagination": True,
                "page_size": 50,
                "virtual_scrolling": True,
                "lazy_loading": True,
                "row_height": 35,
                "column_auto_resize": False
            },
            memory_config={
                "memory_limit_mb": 800,
                "enable_memory_monitoring": True,
                "aggressive_gc": True,
                "cache_size": 1000,
                "preload_data": False
            },
            ui_config={
                "ui_update_interval": 1000,
                "show_progress_bar": True,
                "enable_animations": False,
                "responsive_ui": False,
                "background_processing": True
            },
            optimization_config={
                "priority": "stability",
                "enable_caching": False,
                "prefetch_data": False,
                "optimize_for": "stability",
                "fallback_enabled": True
            },
            created_time=current_time
        )
        
        # 平衡配置文件 (通用)
        self.profiles["balanced"] = PerformanceProfile(
            name="balanced",
            description="平衡配置 (适用于大多数情况)",
            violation_count_range=(0, float('inf')),
            parser_config={
                "parser_type": "adaptive",
                "batch_size": 2000,
                "use_streaming": "auto",
                "progress_interval": 5000,
                "gc_interval": 15000,
                "timeout_seconds": 90
            },
            display_config={
                "display_mode": "adaptive",
                "use_pagination": "auto",
                "page_size": 150,
                "virtual_scrolling": "auto",
                "lazy_loading": "auto",
                "row_height": 35,
                "column_auto_resize": "auto"
            },
            memory_config={
                "memory_limit_mb": 400,
                "enable_memory_monitoring": True,
                "aggressive_gc": "auto",
                "cache_size": 3000,
                "preload_data": "auto"
            },
            ui_config={
                "ui_update_interval": 300,
                "show_progress_bar": True,
                "enable_animations": "auto",
                "responsive_ui": True,
                "background_processing": "auto"
            },
            optimization_config={
                "priority": "balanced",
                "enable_caching": True,
                "prefetch_data": "auto",
                "optimize_for": "balanced",
                "fallback_enabled": True
            },
            created_time=current_time
        )
        
        # 快速配置文件 (优先速度)
        self.profiles["fast"] = PerformanceProfile(
            name="fast",
            description="快速配置 (优先处理速度)",
            violation_count_range=(0, float('inf')),
            parser_config={
                "parser_type": "high_performance_async",
                "batch_size": 5000,
                "use_streaming": False,
                "progress_interval": 10000,
                "gc_interval": 20000,
                "timeout_seconds": 60
            },
            display_config={
                "display_mode": "high_performance_table",
                "use_pagination": True,
                "page_size": 300,
                "virtual_scrolling": False,
                "lazy_loading": False,
                "row_height": 30,
                "column_auto_resize": False
            },
            memory_config={
                "memory_limit_mb": 800,
                "enable_memory_monitoring": False,
                "aggressive_gc": False,
                "cache_size": 10000,
                "preload_data": True
            },
            ui_config={
                "ui_update_interval": 50,
                "show_progress_bar": False,
                "enable_animations": False,
                "responsive_ui": False,
                "background_processing": False
            },
            optimization_config={
                "priority": "speed",
                "enable_caching": True,
                "prefetch_data": True,
                "optimize_for": "speed",
                "fallback_enabled": False
            },
            created_time=current_time
        )
        
        # 内存优化配置文件 (优先内存效率)
        self.profiles["memory_efficient"] = PerformanceProfile(
            name="memory_efficient",
            description="内存优化配置 (最小内存使用)",
            violation_count_range=(0, float('inf')),
            parser_config={
                "parser_type": "memory_efficient_streaming",
                "batch_size": 500,
                "use_streaming": True,
                "progress_interval": 2000,
                "gc_interval": 5000,
                "timeout_seconds": 180,
                "chunk_size": 1000
            },
            display_config={
                "display_mode": "simple_table",
                "use_pagination": True,
                "page_size": 25,
                "virtual_scrolling": True,
                "lazy_loading": True,
                "row_height": 30,
                "column_auto_resize": False
            },
            memory_config={
                "memory_limit_mb": 100,
                "enable_memory_monitoring": True,
                "aggressive_gc": True,
                "cache_size": 100,
                "preload_data": False
            },
            ui_config={
                "ui_update_interval": 1000,
                "show_progress_bar": True,
                "enable_animations": False,
                "responsive_ui": False,
                "background_processing": True
            },
            optimization_config={
                "priority": "memory",
                "enable_caching": False,
                "prefetch_data": False,
                "optimize_for": "memory",
                "fallback_enabled": True
            },
            created_time=current_time
        )
    
    def _create_emergency_profile(self):
        """创建紧急配置文件（最基本的配置）"""
        current_time = time.time()
        
        self.profiles["emergency"] = PerformanceProfile(
            name="emergency",
            description="紧急配置 (最基本功能)",
            violation_count_range=(0, float('inf')),
            parser_config={
                "parser_type": "basic_sync",
                "batch_size": 100,
                "use_streaming": False,
                "progress_interval": 500,
                "gc_interval": 1000,
                "timeout_seconds": 300
            },
            display_config={
                "display_mode": "simple_table",
                "use_pagination": True,
                "page_size": 50,
                "virtual_scrolling": False,
                "lazy_loading": False,
                "row_height": 25,
                "column_auto_resize": False
            },
            memory_config={
                "memory_limit_mb": 50,
                "enable_memory_monitoring": False,
                "aggressive_gc": True,
                "cache_size": 50,
                "preload_data": False
            },
            ui_config={
                "ui_update_interval": 2000,
                "show_progress_bar": True,
                "enable_animations": False,
                "responsive_ui": False,
                "background_processing": False
            },
            optimization_config={
                "priority": "stability",
                "enable_caching": False,
                "prefetch_data": False,
                "optimize_for": "stability",
                "fallback_enabled": False
            },
            created_time=current_time
        )
        
        self.current_profile_name = "emergency"
    
    def select_profile_by_violation_count(self, violation_count: int, 
                                        system_capabilities: Dict = None) -> str:
        """基于违例数量自动选择最适合的配置文件
        
        Args:
            violation_count: 违例数量
            system_capabilities: 系统能力信息（可选）
            
        Returns:
            str: 选择的配置文件名称
        """
        try:
            # 记录选择历史
            selection_record = {
                'timestamp': time.time(),
                'violation_count': violation_count,
                'system_capabilities': system_capabilities or {},
                'selection_method': 'auto_by_violation_count'
            }
            
            # 基于违例数量范围选择配置文件
            selected_profile = None
            
            # 首先检查专用的数据集大小配置文件
            if violation_count < self.violation_thresholds['small_dataset']:
                selected_profile = "small_dataset"
            elif violation_count < self.violation_thresholds['medium_dataset']:
                selected_profile = "medium_dataset"
            elif violation_count < self.violation_thresholds['large_dataset']:
                selected_profile = "large_dataset"
            else:
                selected_profile = "very_large_dataset"
            
            # 检查系统能力，如果系统能力不足，降级到更保守的配置
            if system_capabilities:
                available_memory_gb = system_capabilities.get('available_memory_gb', 4)
                cpu_cores = system_capabilities.get('cpu_cores', 4)
                can_handle_large = system_capabilities.get('can_handle_large_datasets', False)
                
                # 如果系统资源不足，选择更保守的配置
                if not can_handle_large and violation_count > 10000:
                    selected_profile = "memory_efficient"
                elif available_memory_gb < 2 and violation_count > 5000:
                    selected_profile = "memory_efficient"
                elif cpu_cores < 4 and violation_count > 15000:
                    # CPU核心数不足，选择平衡配置
                    selected_profile = "balanced"
            
            # 确保选择的配置文件存在
            if selected_profile not in self.profiles:
                selected_profile = "balanced"  # 回退到平衡配置
            
            # 如果平衡配置也不存在，使用第一个可用的配置
            if selected_profile not in self.profiles and self.profiles:
                selected_profile = list(self.profiles.keys())[0]
            
            # 更新选择记录
            selection_record['selected_profile'] = selected_profile
            selection_record['selection_reason'] = self._get_selection_reason(
                violation_count, system_capabilities, selected_profile
            )
            
            # 记录使用历史
            self.usage_history.append(selection_record)
            self._save_usage_history()
            
            # 更新配置文件使用统计
            if selected_profile in self.profiles:
                profile = self.profiles[selected_profile]
                profile.last_used_time = time.time()
                profile.usage_count += 1
                self._save_profiles()
            
            # 发出信号
            self.auto_profile_selected.emit(selected_profile, violation_count)
            
            print(f"自动选择配置文件: {selected_profile} (违例数量: {violation_count:,})")
            
            return selected_profile
            
        except Exception as e:
            print(f"自动选择配置文件失败: {str(e)}")
            # 返回默认配置
            return "balanced" if "balanced" in self.profiles else list(self.profiles.keys())[0] if self.profiles else "emergency"
    
    def _get_selection_reason(self, violation_count: int, 
                            system_capabilities: Dict, selected_profile: str) -> str:
        """获取配置文件选择原因
        
        Args:
            violation_count: 违例数量
            system_capabilities: 系统能力
            selected_profile: 选择的配置文件
            
        Returns:
            str: 选择原因
        """
        reasons = []
        
        # 基于违例数量的原因
        if violation_count < 2000:
            reasons.append(f"小数据集({violation_count:,}个违例)")
        elif violation_count < 20000:
            reasons.append(f"中等数据集({violation_count:,}个违例)")
        elif violation_count < 50000:
            reasons.append(f"大数据集({violation_count:,}个违例)")
        else:
            reasons.append(f"超大数据集({violation_count:,}个违例)")
        
        # 基于系统能力的原因
        if system_capabilities:
            memory_gb = system_capabilities.get('available_memory_gb', 0)
            cpu_cores = system_capabilities.get('cpu_cores', 0)
            
            if memory_gb < 2:
                reasons.append("内存资源有限")
            elif memory_gb > 8:
                reasons.append("内存资源充足")
            
            if cpu_cores < 4:
                reasons.append("CPU核心数较少")
            elif cpu_cores > 8:
                reasons.append("CPU性能强劲")
        
        # 配置文件特定原因
        if selected_profile == "memory_efficient":
            reasons.append("优化内存使用")
        elif selected_profile == "fast":
            reasons.append("优化处理速度")
        elif selected_profile == "balanced":
            reasons.append("平衡性能和资源使用")
        
        return "; ".join(reasons)
    
    def switch_profile_based_on_analysis(self, violation_count: int, 
                                       current_performance: Dict,
                                       system_capabilities: Dict = None) -> bool:
        """基于实时分析切换配置文件
        
        Args:
            violation_count: 当前违例数量
            current_performance: 当前性能指标
            system_capabilities: 系统能力信息
            
        Returns:
            bool: 是否成功切换配置文件
        """
        try:
            # 分析当前性能状况
            performance_issues = self._analyze_performance_issues(current_performance)
            
            # 如果没有性能问题，不需要切换
            if not performance_issues:
                return False
            
            # 根据性能问题选择更合适的配置文件
            recommended_profile = self._recommend_profile_for_issues(
                performance_issues, violation_count, system_capabilities
            )
            
            if recommended_profile and recommended_profile != self.current_profile_name:
                # 切换到推荐的配置文件
                old_profile = self.current_profile_name
                success = self.set_current_profile(recommended_profile)
                
                if success:
                    print(f"性能优化: 从 {old_profile} 切换到 {recommended_profile}")
                    print(f"切换原因: {', '.join(performance_issues)}")
                    
                    # 记录切换历史
                    switch_record = {
                        'timestamp': time.time(),
                        'violation_count': violation_count,
                        'old_profile': old_profile,
                        'new_profile': recommended_profile,
                        'switch_reason': performance_issues,
                        'performance_metrics': current_performance,
                        'switch_method': 'auto_performance_optimization'
                    }
                    self.usage_history.append(switch_record)
                    self._save_usage_history()
                    
                    return True
            
            return False
            
        except Exception as e:
            print(f"基于分析切换配置文件失败: {str(e)}")
            return False
    
    def _analyze_performance_issues(self, performance_metrics: Dict) -> List[str]:
        """分析性能问题
        
        Args:
            performance_metrics: 性能指标
            
        Returns:
            List[str]: 发现的性能问题列表
        """
        issues = []
        
        # 检查内存使用
        memory_usage = performance_metrics.get('memory_usage_percent', 0)
        if memory_usage > 85:
            issues.append("内存使用过高")
        elif memory_usage > 70:
            issues.append("内存使用较高")
        
        # 检查处理速度
        processing_speed = performance_metrics.get('processing_speed_ratio', 1.0)
        if processing_speed < 0.3:
            issues.append("处理速度过慢")
        elif processing_speed < 0.6:
            issues.append("处理速度较慢")
        
        # 检查UI响应时间
        ui_response_time = performance_metrics.get('ui_response_time_ms', 0)
        if ui_response_time > 500:
            issues.append("UI响应缓慢")
        elif ui_response_time > 200:
            issues.append("UI响应较慢")
        
        # 检查加载时间
        load_time = performance_metrics.get('load_time_seconds', 0)
        if load_time > 10:
            issues.append("加载时间过长")
        elif load_time > 5:
            issues.append("加载时间较长")
        
        # 检查错误率
        error_rate = performance_metrics.get('error_rate', 0)
        if error_rate > 0.05:
            issues.append("错误率过高")
        
        return issues
    
    def _recommend_profile_for_issues(self, issues: List[str], 
                                    violation_count: int,
                                    system_capabilities: Dict = None) -> Optional[str]:
        """为性能问题推荐配置文件
        
        Args:
            issues: 性能问题列表
            violation_count: 违例数量
            system_capabilities: 系统能力
            
        Returns:
            Optional[str]: 推荐的配置文件名称
        """
        # 如果有内存相关问题，推荐内存优化配置
        memory_issues = [issue for issue in issues if "内存" in issue]
        if memory_issues:
            return "memory_efficient"
        
        # 如果有速度相关问题，根据数据集大小选择
        speed_issues = [issue for issue in issues if "速度" in issue or "时间" in issue]
        if speed_issues:
            if violation_count > 20000:
                return "large_dataset"  # 大数据集使用流式处理
            else:
                return "balanced"  # 中小数据集使用平衡配置
        
        # 如果有UI响应问题，选择更保守的配置
        ui_issues = [issue for issue in issues if "UI" in issue or "响应" in issue]
        if ui_issues:
            if violation_count > 10000:
                return "memory_efficient"  # 大数据集时优先内存效率
            else:
                return "balanced"
        
        # 如果有多种问题，选择最保守的配置
        if len(issues) > 2:
            return "memory_efficient"
        
        # 默认推荐平衡配置
        return "balanced"
    
    def get_current_profile(self) -> Optional[PerformanceProfile]:
        """获取当前配置文件
        
        Returns:
            Optional[PerformanceProfile]: 当前配置文件，如果不存在则返回None
        """
        return self.profiles.get(self.current_profile_name)
    
    def set_current_profile(self, profile_name: str) -> bool:
        """设置当前配置文件
        
        Args:
            profile_name: 配置文件名称
            
        Returns:
            bool: 是否设置成功
        """
        if profile_name in self.profiles:
            old_profile = self.current_profile_name
            self.current_profile_name = profile_name
            
            # 更新使用统计
            profile = self.profiles[profile_name]
            profile.last_used_time = time.time()
            profile.usage_count += 1
            
            # 保存配置
            self._save_profiles()
            self._save_settings()
            
            # 发出信号
            self.profile_changed.emit(profile_name)
            
            print(f"配置文件已切换: {old_profile} -> {profile_name}")
            return True
        else:
            print(f"配置文件不存在: {profile_name}")
            return False
    
    def get_all_profiles(self) -> Dict[str, PerformanceProfile]:
        """获取所有配置文件
        
        Returns:
            Dict[str, PerformanceProfile]: 所有配置文件
        """
        return self.profiles.copy()
    
    def get_profile_names(self) -> List[str]:
        """获取所有配置文件名称
        
        Returns:
            List[str]: 配置文件名称列表
        """
        return list(self.profiles.keys())
    
    def create_custom_profile(self, name: str, description: str,
                            violation_count_range: tuple,
                            parser_config: Dict,
                            display_config: Dict,
                            memory_config: Dict,
                            ui_config: Dict,
                            optimization_config: Dict) -> bool:
        """创建自定义配置文件
        
        Args:
            name: 配置文件名称
            description: 描述
            violation_count_range: 违例数量范围
            parser_config: 解析器配置
            display_config: 显示配置
            memory_config: 内存配置
            ui_config: UI配置
            optimization_config: 优化配置
            
        Returns:
            bool: 是否创建成功
        """
        try:
            if name in self.profiles:
                print(f"配置文件已存在: {name}")
                return False
            
            # 创建新的配置文件
            profile = PerformanceProfile(
                name=name,
                description=description,
                violation_count_range=violation_count_range,
                parser_config=parser_config,
                display_config=display_config,
                memory_config=memory_config,
                ui_config=ui_config,
                optimization_config=optimization_config,
                created_time=time.time()
            )
            
            self.profiles[name] = profile
            self._save_profiles()
            
            # 发出信号
            self.profile_created.emit(name)
            
            print(f"自定义配置文件已创建: {name}")
            return True
            
        except Exception as e:
            print(f"创建自定义配置文件失败: {str(e)}")
            return False
    
    def delete_profile(self, name: str) -> bool:
        """删除配置文件
        
        Args:
            name: 配置文件名称
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 不允许删除默认配置文件
            default_profiles = ["small_dataset", "medium_dataset", "large_dataset", 
                              "very_large_dataset", "balanced", "fast", "memory_efficient"]
            
            if name in default_profiles:
                print(f"不能删除默认配置文件: {name}")
                return False
            
            if name not in self.profiles:
                print(f"配置文件不存在: {name}")
                return False
            
            # 如果是当前配置文件，切换到默认配置
            if name == self.current_profile_name:
                self.current_profile_name = "balanced"
            
            # 删除配置文件
            del self.profiles[name]
            self._save_profiles()
            
            # 发出信号
            self.profile_deleted.emit(name)
            
            print(f"配置文件已删除: {name}")
            return True
            
        except Exception as e:
            print(f"删除配置文件失败: {str(e)}")
            return False
    
    def _load_profiles(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.profiles_file):
                with open(self.profiles_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    for name, profile_data in data.items():
                        # 转换为PerformanceProfile对象
                        profile = PerformanceProfile(**profile_data)
                        self.profiles[name] = profile
                        
                print(f"已加载 {len(self.profiles)} 个配置文件")
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
    
    def _save_profiles(self):
        """保存配置文件"""
        try:
            # 转换为可序列化的字典
            data = {}
            for name, profile in self.profiles.items():
                data[name] = asdict(profile)
            
            with open(self.profiles_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")
    
    def _load_settings(self):
        """加载设置"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    self.settings = json.load(f)
                    
                # 恢复当前配置文件
                self.current_profile_name = self.settings.get('current_profile', 'balanced')
                
        except Exception as e:
            print(f"加载设置失败: {str(e)}")
            self.settings = {}
    
    def _save_settings(self):
        """保存设置"""
        try:
            self.settings['current_profile'] = self.current_profile_name
            self.settings['last_updated'] = time.time()
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存设置失败: {str(e)}")
    
    def _load_usage_history(self):
        """加载使用历史"""
        try:
            if os.path.exists(self.usage_history_file):
                with open(self.usage_history_file, 'r', encoding='utf-8') as f:
                    self.usage_history = json.load(f)
                    
                # 限制历史记录数量
                if len(self.usage_history) > 1000:
                    self.usage_history = self.usage_history[-1000:]
                    
        except Exception as e:
            print(f"加载使用历史失败: {str(e)}")
            self.usage_history = []
    
    def _save_usage_history(self):
        """保存使用历史"""
        try:
            with open(self.usage_history_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_history, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存使用历史失败: {str(e)}")
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """获取使用统计信息
        
        Returns:
            Dict[str, Any]: 使用统计信息
        """
        try:
            stats = {
                'total_profiles': len(self.profiles),
                'current_profile': self.current_profile_name,
                'total_usage_records': len(self.usage_history),
                'profile_usage': {},
                'recent_selections': [],
                'performance_trends': {}
            }
            
            # 统计每个配置文件的使用情况
            for name, profile in self.profiles.items():
                stats['profile_usage'][name] = {
                    'usage_count': profile.usage_count,
                    'last_used_time': profile.last_used_time,
                    'performance_score': profile.performance_score,
                    'description': profile.description
                }
            
            # 获取最近的选择记录
            if self.usage_history:
                stats['recent_selections'] = self.usage_history[-10:]
            
            return stats
            
        except Exception as e:
            print(f"获取使用统计失败: {str(e)}")
            return {}