"""
Performance Integration System for Timing Violation Plugin

Integrates all performance components into the main application with backward compatibility
and comprehensive error handling.
"""

import time
import traceback
from typing import Dict, List, Optional, Any, Callable
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QWidget, QMessageBox

try:
    from .comprehensive_performance_system import ComprehensivePerformanceSystem
    from .adaptive_parser_system import AdaptiveParserSystem
    from .smart_ui_renderer import SmartUIRenderer, RenderingMode
    from .configuration_manager import ConfigurationManager
    from .integrated_memory_system import IntegratedMemorySystem
    from .enhanced_batch_processor import MemoryAwareBatchProcessor, ProgressiveBatchSizeOptimizer
    from .performance_optimizer import PerformanceOptimizer
    from .component_interaction_optimizer import ComponentInteractionOptimizer
    from .comprehensive_error_handler import ComprehensiveErrorHandler, UserNotificationManager
except ImportError as e:
    print(f"Warning: Relative imports failed ({e}), trying absolute imports...")
    # Try absolute imports as fallback
    try:
        from comprehensive_performance_system import ComprehensivePerformanceSystem
        from adaptive_parser_system import AdaptiveParserSystem
        from smart_ui_renderer import SmartUIRender<PERSON>, Ren<PERSON>M<PERSON>
        from configuration_manager import ConfigurationManager
        from integrated_memory_system import IntegratedMemorySystem
        from enhanced_batch_processor import MemoryAwareBatchProcessor, ProgressiveBatchSizeOptimizer
        from performance_optimizer import PerformanceOptimizer
        from component_interaction_optimizer import ComponentInteractionOptimizer
        from comprehensive_error_handler import ComprehensiveErrorHandler, UserNotificationManager
        print("✓ Absolute imports successful")
    except ImportError as e2:
        print(f"Warning: Absolute imports also failed ({e2}), using fallback None values")
        # Provide fallback imports
        ComprehensivePerformanceSystem = None
        AdaptiveParserSystem = None
        SmartUIRenderer = None
        RenderingMode = None
        ConfigurationManager = None
        IntegratedMemorySystem = None
        MemoryAwareBatchProcessor = None
        ProgressiveBatchSizeOptimizer = None
        PerformanceOptimizer = None
        ComponentInteractionOptimizer = None
        ComprehensiveErrorHandler = None
        UserNotificationManager = None


class PerformanceIntegrationSystem(QObject):
    """
    Main integration system that coordinates all performance components
    and provides a unified interface for the TimingViolationWindow.
    """
    
    # Signals for main window integration
    performance_status_changed = pyqtSignal(dict)  # Performance status update
    optimization_applied = pyqtSignal(str, str)    # Optimization type, description
    error_occurred = pyqtSignal(str, str)          # Error type, error message
    parsing_progress = pyqtSignal(int, str)        # Progress percentage, status message
    ui_mode_changed = pyqtSignal(str, dict)        # UI mode, configuration
    
    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
        
        # Integration state
        self.integration_active = False
        self.current_violation_count = 0
        self.current_file_path = ""
        self.current_performance_session = None
        
        # Performance components (initialized lazily)
        self._performance_system = None
        self._parser_system = None
        self._ui_renderer = None
        self._config_manager = None
        self._memory_system = None
        self._batch_processor = None
        self._performance_optimizer = None
        self._interaction_optimizer = None
        self._error_handler = None
        self._notification_manager = None
        
        # Backward compatibility flags
        self.enable_legacy_mode = True
        self.fallback_on_error = True
        
        # Error handling and recovery
        self.error_count = 0
        self.max_errors_before_fallback = 3
        self.component_health = {}
        
        # Performance monitoring
        self.integration_metrics = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'average_response_time': 0,
            'last_operation_time': 0
        }
        
        # Initialize components
        self._initialize_components()
        
    def _initialize_components(self):
        """Initialize performance components with error handling"""
        try:
            print("Initializing Performance Integration System...")
            
            # Initialize configuration manager first (needed by other components)
            if ConfigurationManager:
                self._config_manager = ConfigurationManager()
                self.component_health['config_manager'] = True
                print("✓ Configuration Manager initialized")
            else:
                print("⚠ Configuration Manager not available")
                self.component_health['config_manager'] = False
            
            # Initialize performance optimizer
            if PerformanceOptimizer:
                self._performance_optimizer = PerformanceOptimizer()
                self.component_health['performance_optimizer'] = True
                print("✓ Performance Optimizer initialized")
            else:
                print("⚠ Performance Optimizer not available")
                self.component_health['performance_optimizer'] = False
            
            # Initialize comprehensive performance system
            if ComprehensivePerformanceSystem:
                self._performance_system = ComprehensivePerformanceSystem()
                self._connect_performance_system_signals()
                self.component_health['performance_system'] = True
                print("✓ Comprehensive Performance System initialized")
            else:
                print("⚠ Comprehensive Performance System not available")
                self.component_health['performance_system'] = False
            
            # Initialize adaptive parser system
            if AdaptiveParserSystem:
                self._parser_system = AdaptiveParserSystem()
                self._connect_parser_system_signals()
                self.component_health['parser_system'] = True
                print("✓ Adaptive Parser System initialized")
            else:
                print("⚠ Adaptive Parser System not available")
                self.component_health['parser_system'] = False
            
            # Initialize smart UI renderer
            if SmartUIRenderer:
                self._ui_renderer = SmartUIRenderer(self.main_window)
                self._connect_ui_renderer_signals()
                self.component_health['ui_renderer'] = True
                print("✓ Smart UI Renderer initialized")
            else:
                print("⚠ Smart UI Renderer not available")
                self.component_health['ui_renderer'] = False
            
            # Initialize memory system
            if IntegratedMemorySystem:
                self._memory_system = IntegratedMemorySystem()
                self.component_health['memory_system'] = True
                print("✓ Integrated Memory System initialized")
            else:
                print("⚠ Integrated Memory System not available")
                self.component_health['memory_system'] = False
            
            # Initialize batch processor
            if MemoryAwareBatchProcessor:
                self._batch_processor = MemoryAwareBatchProcessor()
                self.component_health['batch_processor'] = True
                print("✓ Memory Aware Batch Processor initialized")
            else:
                print("⚠ Memory Aware Batch Processor not available")
                self.component_health['batch_processor'] = False
            
            # Initialize component interaction optimizer
            if ComponentInteractionOptimizer:
                self._interaction_optimizer = ComponentInteractionOptimizer()
                self._connect_interaction_optimizer_signals()
                self.component_health['interaction_optimizer'] = True
                print("✓ Component Interaction Optimizer initialized")
            else:
                print("⚠ Component Interaction Optimizer not available")
                self.component_health['interaction_optimizer'] = False
            
            # Initialize comprehensive error handler
            if ComprehensiveErrorHandler:
                self._error_handler = ComprehensiveErrorHandler()
                self._connect_error_handler_signals()
                self.component_health['error_handler'] = True
                print("✓ Comprehensive Error Handler initialized")
            else:
                print("⚠ Comprehensive Error Handler not available")
                self.component_health['error_handler'] = False
            
            # Initialize user notification manager
            if UserNotificationManager:
                self._notification_manager = UserNotificationManager(self.main_window)
                self.component_health['notification_manager'] = True
                print("✓ User Notification Manager initialized")
            else:
                print("⚠ User Notification Manager not available")
                self.component_health['notification_manager'] = False
            
            self.integration_active = True
            print("✓ Performance Integration System initialized successfully")
            
        except Exception as e:
            print(f"✗ Failed to initialize Performance Integration System: {e}")
            traceback.print_exc()
            self.integration_active = False
            self._handle_initialization_error(e)
    
    def _connect_performance_system_signals(self):
        """Connect performance system signals"""
        if self._performance_system:
            self._performance_system.performance_status_updated.connect(
                self.performance_status_changed.emit
            )
            self._performance_system.optimization_recommended.connect(
                self._handle_optimization_recommendation
            )
            self._performance_system.performance_alert.connect(
                self._handle_performance_alert
            )
    
    def _connect_parser_system_signals(self):
        """Connect parser system signals"""
        if self._parser_system:
            self._parser_system.parsing_progress.connect(
                self.parsing_progress.emit
            )
            self._parser_system.strategy_switched.connect(
                self._handle_strategy_switch
            )
            self._parser_system.parsing_failed.connect(
                self._handle_parsing_error
            )
    
    def _connect_ui_renderer_signals(self):
        """Connect UI renderer signals"""
        if self._ui_renderer:
            self._ui_renderer.rendering_mode_changed.connect(
                self.ui_mode_changed.emit
            )
            self._ui_renderer.optimization_applied.connect(
                self.optimization_applied.emit
            )
    
    def _connect_interaction_optimizer_signals(self):
        """Connect interaction optimizer signals"""
        if self._interaction_optimizer:
            self._interaction_optimizer.optimization_status_changed.connect(
                self._handle_interaction_optimization_status
            )
    
    def _connect_error_handler_signals(self):
        """Connect error handler signals"""
        if self._error_handler:
            self._error_handler.error_occurred.connect(self._handle_error_occurred)
            self._error_handler.recovery_attempted.connect(self._handle_recovery_attempted)
            self._error_handler.degradation_applied.connect(self._handle_degradation_applied)
            self._error_handler.user_notification_required.connect(self._handle_user_notification)
            
            # Connect notification manager if available
            if self._notification_manager:
                self._error_handler.user_notification_required.connect(
                    self._notification_manager.show_error_notification
                )
    
    def start_performance_session(self, operation_type: str, file_path: str, 
                                violation_count: int = None) -> bool:
        """
        Start a performance monitoring session for violation processing.
        
        Args:
            operation_type: Type of operation (e.g., 'file_parsing', 'batch_confirmation')
            file_path: Path to the file being processed
            violation_count: Number of violations (estimated if not known)
            
        Returns:
            bool: True if session started successfully
        """
        try:
            start_time = time.time()
            
            # Store session information
            self.current_file_path = file_path
            self.current_violation_count = violation_count or 0
            
            # Estimate violation count if not provided
            if not violation_count and self._performance_optimizer:
                file_analysis = self._performance_optimizer.analyze_file_performance(file_path)
                self.current_violation_count = file_analysis.get('estimated_violations', 0)
            
            # Select optimal configuration profile
            if self._config_manager and self.current_violation_count > 0:
                optimal_profile = self._config_manager.select_profile_by_violation_count(
                    self.current_violation_count
                )
                self._config_manager.set_current_profile(optimal_profile)
                print(f"Selected performance profile: {optimal_profile}")
            
            # Start comprehensive performance monitoring
            if self._performance_system:
                session_id = self._performance_system.start_performance_monitoring(
                    operation_type, self.current_violation_count, 'initialization'
                )
                self.current_performance_session = session_id
                print(f"Started performance session: {session_id}")
            
            # Initialize memory monitoring
            if self._memory_system:
                self._memory_system.start_monitoring_session(
                    f"{operation_type}_{self.current_violation_count}"
                )
            
            # Update metrics
            self.integration_metrics['total_operations'] += 1
            self.integration_metrics['last_operation_time'] = start_time
            
            return True
            
        except Exception as e:
            self._handle_error("start_performance_session", e)
            return False
    
    def parse_file_with_optimization(self, file_path: str, 
                                   progress_callback: Callable = None) -> List[Dict]:
        """
        Parse violation file using adaptive optimization strategies.
        
        Args:
            file_path: Path to the violation log file
            progress_callback: Optional progress callback function
            
        Returns:
            List[Dict]: Parsed violation data
        """
        try:
            start_time = time.time()
            
            # Track component interaction
            self._track_component_interaction_start('integration_system', 'parser_system', 'file_parsing')
            
            # Wrap progress callback to emit signals
            def integrated_progress_callback(progress: int, message: str):
                self.parsing_progress.emit(progress, message)
                if progress_callback:
                    progress_callback(progress, message)
            
            # Use adaptive parser system if available
            if self._parser_system:
                violations = self._parser_system.parse_with_adaptive_strategy(
                    file_path, integrated_progress_callback
                )
            else:
                # Fallback to basic parsing
                violations = self._fallback_parse_file(file_path, integrated_progress_callback)
            
            # Track parsing performance and component interaction
            parse_time = time.time() - start_time
            self._track_component_interaction_end('integration_system', 'parser_system', 'file_parsing', 
                                                parse_time * 1000, len(violations) * 100)
            
            if self._performance_system:
                self._performance_system.track_violation_processing(
                    len(violations), parse_time
                )
            
            # Update success metrics
            self.integration_metrics['successful_operations'] += 1
            self._update_average_response_time(parse_time)
            
            return violations
            
        except Exception as e:
            self._handle_error("parse_file_with_optimization", e, "parser_system")
            
            # Handle parsing failure with comprehensive error handler
            if self._error_handler:
                recovery_success = self._error_handler.handle_processing_failure(
                    "parser_system", "file_parsing", e, self.current_violation_count, file_path
                )
                
                if recovery_success:
                    # Retry with fallback parser
                    return self._fallback_parse_file(file_path, progress_callback)
            
            # Try fallback parsing
            if self.fallback_on_error:
                return self._fallback_parse_file(file_path, progress_callback)
            else:
                raise
    
    def create_optimized_table_view(self, violations_data: List[Dict], 
                                  parent_widget: QWidget = None) -> QWidget:
        """
        Create optimized table view based on violation count and system capabilities.
        
        Args:
            violations_data: Violation data to display
            parent_widget: Parent widget for the table
            
        Returns:
            QWidget: Optimized table widget
        """
        try:
            start_time = time.time()
            violation_count = len(violations_data)
            
            # Track component interaction
            self._track_component_interaction_start('integration_system', 'ui_renderer', 'table_creation')
            
            # Use smart UI renderer if available
            if self._ui_renderer:
                # Select optimal rendering mode
                system_caps = self._get_system_capabilities()
                rendering_mode = self._ui_renderer.select_rendering_mode(
                    violation_count, system_caps
                )
                
                # Create optimized table
                table_widget = self._ui_renderer.create_optimized_table(
                    rendering_mode, violations_data, parent_widget
                )
                
                print(f"Created optimized table with {rendering_mode.value} mode for {violation_count:,} violations")
                
            else:
                # Fallback to existing high-performance table
                table_widget = self._create_fallback_table(violations_data, parent_widget)
            
            # Track UI creation performance and component interaction
            creation_time = time.time() - start_time
            self._track_component_interaction_end('integration_system', 'ui_renderer', 'table_creation',
                                                creation_time * 1000, violation_count * 50)
            
            if self._performance_system:
                self._performance_system.track_ui_interaction("table_creation", creation_time * 1000)
            
            return table_widget
            
        except Exception as e:
            self._handle_error("create_optimized_table_view", e, "ui_renderer")
            
            # Handle UI rendering failure with comprehensive error handler
            if self._error_handler:
                recovery_success = self._error_handler.handle_processing_failure(
                    "ui_renderer", "table_creation", e, len(violations_data)
                )
                
                if recovery_success:
                    print("✓ UI rendering error recovered, using fallback table")
            
            # Return fallback table
            return self._create_fallback_table(violations_data, parent_widget)
    
    def process_batch_operations(self, violations: List[Dict], operation_type: str,
                               progress_callback: Callable = None) -> List[Dict]:
        """
        Process batch operations with enhanced performance optimization.
        
        Args:
            violations: Violation data to process
            operation_type: Type of batch operation
            progress_callback: Optional progress callback
            
        Returns:
            List[Dict]: Processed violation data
        """
        try:
            start_time = time.time()
            
            # Use enhanced batch processor if available
            if self._batch_processor:
                processed_violations = self._batch_processor.process_violations_optimized(
                    violations, operation_type, progress_callback
                )
            else:
                # Fallback to simple batch processing
                processed_violations = self._fallback_batch_process(
                    violations, operation_type, progress_callback
                )
            
            # Track batch processing performance
            process_time = time.time() - start_time
            if self._performance_system:
                self._performance_system.track_violation_processing(
                    len(processed_violations), process_time
                )
            
            return processed_violations
            
        except Exception as e:
            self._handle_error("process_batch_operations", e)
            # Return original data if processing fails
            return violations
    
    def handle_violation_count_change(self, new_violation_count: int, 
                                    violations_data: List[Dict]) -> bool:
        """
        Handle changes in violation count and adapt performance settings.
        
        Args:
            new_violation_count: New violation count
            violations_data: Updated violation data
            
        Returns:
            bool: True if adaptations were applied
        """
        try:
            adaptations_applied = False
            
            # Update current violation count
            old_count = self.current_violation_count
            self.current_violation_count = new_violation_count
            
            # Check if configuration profile should change
            if self._config_manager:
                current_profile = self._config_manager.current_profile_name
                optimal_profile = self._config_manager.select_profile_by_violation_count(
                    new_violation_count
                )
                
                if optimal_profile != current_profile:
                    self._config_manager.set_current_profile(optimal_profile)
                    adaptations_applied = True
                    print(f"Switched profile from {current_profile} to {optimal_profile}")
            
            # Check if UI rendering mode should change
            if self._ui_renderer:
                mode_changed = self._ui_renderer.handle_violation_count_change(
                    new_violation_count, violations_data
                )
                if mode_changed:
                    adaptations_applied = True
            
            # Update performance monitoring
            if self._performance_system and self.current_performance_session:
                # Update session with new violation count
                self._performance_system.current_violation_count = new_violation_count
            
            if adaptations_applied:
                self.optimization_applied.emit(
                    "violation_count_adaptation",
                    f"Adapted settings for violation count change: {old_count:,} → {new_violation_count:,}"
                )
            
            return adaptations_applied
            
        except Exception as e:
            self._handle_error("handle_violation_count_change", e)
            return False
    
    def stop_performance_session(self) -> Dict:
        """
        Stop the current performance session and generate summary.
        
        Returns:
            Dict: Performance session summary
        """
        try:
            session_summary = {}
            
            # Stop comprehensive performance monitoring
            if self._performance_system and self.current_performance_session:
                session_summary = self._performance_system.stop_performance_monitoring()
                self.current_performance_session = None
            
            # Stop memory monitoring
            if self._memory_system:
                memory_summary = self._memory_system.stop_monitoring_session()
                session_summary['memory_summary'] = memory_summary
            
            # Add integration metrics
            session_summary['integration_metrics'] = self.integration_metrics.copy()
            
            return session_summary
            
        except Exception as e:
            self._handle_error("stop_performance_session", e)
            return {'error': str(e)}
    
    def get_performance_status_widget(self) -> Optional[QWidget]:
        """
        Get performance status widget for display in main window.
        
        Returns:
            Optional[QWidget]: Performance status widget
        """
        try:
            if self._performance_system:
                return self._performance_system.get_performance_status_widget()
            return None
        except Exception as e:
            self._handle_error("get_performance_status_widget", e)
            return None
    
    def get_optimization_suggestions(self) -> List[Dict]:
        """
        Get current optimization suggestions.
        
        Returns:
            List[Dict]: List of optimization suggestions
        """
        try:
            suggestions = []
            
            if self._performance_system:
                suggestions = self._performance_system.get_optimization_suggestions(
                    self.current_violation_count
                )
            
            return suggestions
            
        except Exception as e:
            self._handle_error("get_optimization_suggestions", e)
            return []
    
    def apply_optimization_suggestions(self, suggestion_ids: List[str]) -> bool:
        """
        Apply selected optimization suggestions.
        
        Args:
            suggestion_ids: List of suggestion IDs to apply
            
        Returns:
            bool: True if suggestions were applied successfully
        """
        try:
            if self._performance_system:
                return self._performance_system.apply_optimization_suggestions(
                    selected_suggestions=suggestion_ids
                )
            return False
            
        except Exception as e:
            self._handle_error("apply_optimization_suggestions", e)
            return False
    
    def is_integration_healthy(self) -> bool:
        """
        Check if the integration system is healthy and functioning.
        
        Returns:
            bool: True if system is healthy
        """
        if not self.integration_active:
            return False
        
        # Check component health
        healthy_components = sum(1 for health in self.component_health.values() if health)
        total_components = len(self.component_health)
        
        # Consider system healthy if at least 50% of components are working
        health_ratio = healthy_components / total_components if total_components > 0 else 0
        
        return health_ratio >= 0.5 and self.error_count < self.max_errors_before_fallback
    
    def optimize_component_interactions(self, optimization_config: Dict = None) -> Dict:
        """
        Optimize interactions between all performance components.
        
        Args:
            optimization_config: Configuration for optimization
            
        Returns:
            Dict: Optimization results
        """
        if not self._interaction_optimizer:
            return {'error': 'Interaction optimizer not available'}
        
        if optimization_config is None:
            optimization_config = {
                'optimize_data_flow': True,
                'minimize_monitoring_overhead': True,
                'enable_automatic_optimizations': True,
                'enable_profiling': False,
                'profiling_duration': 30
            }
        
        # Get list of active components
        active_components = [name for name, health in self.component_health.items() if health]
        
        # Apply optimizations
        results = self._interaction_optimizer.optimize_component_interactions(
            active_components, optimization_config
        )
        
        return results
    
    def get_interaction_optimization_status(self) -> Dict:
        """Get current interaction optimization status"""
        if self._interaction_optimizer:
            return self._interaction_optimizer.get_optimization_status()
        else:
            return {'error': 'Interaction optimizer not available'}
    
    def handle_memory_pressure(self, memory_usage_percent: float, component: str = "integration_system") -> bool:
        """
        Handle memory pressure situations with comprehensive error handling.
        
        Args:
            memory_usage_percent: Current memory usage percentage
            component: Component experiencing memory pressure
            
        Returns:
            bool: True if memory pressure was resolved
        """
        if self._error_handler:
            return self._error_handler.handle_memory_pressure(
                memory_usage_percent, component, "memory_management"
            )
        else:
            # Fallback memory pressure handling
            if memory_usage_percent > 90:
                print(f"⚠ High memory pressure detected: {memory_usage_percent:.1f}%")
                # Force garbage collection
                import gc
                gc.collect()
                return True
            return False
    
    def get_error_statistics(self) -> Dict:
        """Get comprehensive error statistics"""
        if self._error_handler:
            return self._error_handler.get_error_statistics()
        else:
            return {
                'total_errors': self.error_count,
                'integration_errors': self.integration_metrics['failed_operations'],
                'error_handler_available': False
            }
    
    def get_system_status(self) -> Dict:
        """
        Get comprehensive system status information.
        
        Returns:
            Dict: System status information
        """
        status = {
            'integration_active': self.integration_active,
            'current_violation_count': self.current_violation_count,
            'current_file_path': self.current_file_path,
            'current_session': self.current_performance_session,
            'component_health': self.component_health.copy(),
            'error_count': self.error_count,
            'integration_metrics': self.integration_metrics.copy(),
            'is_healthy': self.is_integration_healthy()
        }
        
        # Add interaction optimization status if available
        if self._interaction_optimizer:
            status['interaction_optimization'] = self.get_interaction_optimization_status()
        
        # Add error statistics if available
        if self._error_handler:
            status['error_statistics'] = self.get_error_statistics()
        
        return status
    
    # Private helper methods
    
    def _handle_error(self, operation: str, error: Exception, component: str = "integration_system"):
        """Handle errors with comprehensive error handling and recovery"""
        self.error_count += 1
        self.integration_metrics['failed_operations'] += 1
        
        # Use comprehensive error handler if available
        if self._error_handler:
            error_context = self._error_handler.handle_error(
                component=component,
                operation=operation,
                error=error,
                violation_count=self.current_violation_count,
                file_path=self.current_file_path,
                additional_context={
                    'integration_metrics': self.integration_metrics.copy(),
                    'component_health': self.component_health.copy()
                }
            )
            
            # Check if we should switch to fallback mode based on error severity
            if (error_context.severity.value in ['high', 'critical'] and 
                self.error_count >= self.max_errors_before_fallback):
                print("⚠ Critical errors detected, switching to fallback mode")
                self.enable_legacy_mode = True
        else:
            # Fallback error handling
            error_msg = f"Error in {operation}: {str(error)}"
            print(f"✗ {error_msg}")
            
            # Emit error signal
            self.error_occurred.emit(operation, str(error))
            
            # Check if we should switch to fallback mode
            if self.error_count >= self.max_errors_before_fallback:
                print("⚠ Too many errors, switching to fallback mode")
                self.enable_legacy_mode = True
    
    def _handle_initialization_error(self, error: Exception):
        """Handle initialization errors"""
        print(f"Initialization error: {error}")
        self.enable_legacy_mode = True
        self.fallback_on_error = True
    
    def _handle_optimization_recommendation(self, recommendation_data: Dict):
        """Handle optimization recommendations from performance system"""
        suggestions = recommendation_data.get('suggestions', [])
        if suggestions:
            print(f"Received {len(suggestions)} optimization suggestions")
            # Auto-apply low-risk suggestions if enabled
            # This would be configurable in a real implementation
    
    def _handle_performance_alert(self, alert_type: str, alert_data: Dict):
        """Handle performance alerts"""
        print(f"Performance alert: {alert_type} - {alert_data}")
        self.error_occurred.emit("performance_alert", f"{alert_type}: {alert_data}")
    
    def _handle_strategy_switch(self, old_strategy: str, new_strategy: str, reason: str):
        """Handle parser strategy switches"""
        print(f"Parser strategy switched: {old_strategy} → {new_strategy} ({reason})")
        self.optimization_applied.emit("parser_strategy_switch", 
                                     f"Switched from {old_strategy} to {new_strategy}: {reason}")
    
    def _handle_parsing_error(self, error_msg: str, error_details: Dict):
        """Handle parsing errors"""
        print(f"Parsing error: {error_msg}")
        self.error_occurred.emit("parsing_error", error_msg)
    
    def _get_system_capabilities(self) -> Dict:
        """Get current system capabilities"""
        if self._performance_optimizer:
            return self._performance_optimizer.get_system_capabilities()
        else:
            # Return basic capabilities
            return {
                'available_memory_gb': 4,
                'cpu_cores': 4,
                'can_handle_large_datasets': True,
                'performance_tier': 'medium'
            }
    
    def _update_average_response_time(self, response_time: float):
        """Update average response time metric"""
        current_avg = self.integration_metrics['average_response_time']
        total_ops = self.integration_metrics['successful_operations']
        
        if total_ops <= 1:
            self.integration_metrics['average_response_time'] = response_time
        else:
            # Calculate running average
            self.integration_metrics['average_response_time'] = (
                (current_avg * (total_ops - 1) + response_time) / total_ops
            )
    
    # Fallback methods for backward compatibility
    
    def _fallback_parse_file(self, file_path: str, progress_callback: Callable = None) -> List[Dict]:
        """Fallback file parsing using basic parser"""
        try:
            from .parser import VioLogParser
            parser = VioLogParser()
            return parser.parse_log_file(file_path, progress_callback)
        except Exception as e:
            print(f"Fallback parsing failed: {e}")
            return []
    
    def _create_fallback_table(self, violations_data: List[Dict], parent: QWidget) -> QWidget:
        """Create fallback table using existing HighPerformanceTableView"""
        try:
            from .main_window import HighPerformanceTableView
            table = HighPerformanceTableView(parent)
            table.update_data(violations_data)
            return table
        except Exception as e:
            print(f"Fallback table creation failed: {e}")
            # Return basic QTableWidget as last resort
            from PyQt5.QtWidgets import QTableWidget
            return QTableWidget(parent)
    
    def _fallback_batch_process(self, violations: List[Dict], operation_type: str,
                              progress_callback: Callable = None) -> List[Dict]:
        """Fallback batch processing"""
        # Simple batch processing without optimization
        batch_size = 1000
        processed = []
        
        for i in range(0, len(violations), batch_size):
            batch = violations[i:i + batch_size]
            processed.extend(batch)  # No actual processing in fallback
            
            if progress_callback:
                progress = int((i + batch_size) / len(violations) * 100)
                progress_callback(progress, f"Processing batch {i // batch_size + 1}")
        
        return processed
    
    # Component interaction tracking methods
    
    def _track_component_interaction_start(self, source: str, target: str, operation: str):
        """Track the start of a component interaction"""
        if not hasattr(self, '_interaction_start_times'):
            self._interaction_start_times = {}
        
        interaction_key = f"{source}->{target}:{operation}"
        self._interaction_start_times[interaction_key] = time.time()
    
    def _track_component_interaction_end(self, source: str, target: str, operation: str,
                                       processing_time_ms: float, data_size_bytes: int = 0):
        """Track the end of a component interaction"""
        if self._interaction_optimizer:
            self._interaction_optimizer.track_interaction(
                source, target, operation, processing_time_ms, data_size_bytes
            )
    
    def _handle_interaction_optimization_status(self, status_data: Dict):
        """Handle interaction optimization status updates"""
        print(f"Interaction optimization status: {status_data}")
        
        # Apply automatic optimizations if bottlenecks are detected
        if status_data.get('detected_bottlenecks', 0) > 0:
            print(f"Detected {status_data['detected_bottlenecks']} bottlenecks, applying optimizations...")
            
            # Trigger automatic optimization
            optimization_config = {
                'optimize_data_flow': True,
                'minimize_monitoring_overhead': True,
                'enable_automatic_optimizations': True
            }
            self.optimize_component_interactions(optimization_config)
    
    def _handle_error_occurred(self, error_context):
        """Handle error occurrence from error handler"""
        print(f"Error handled by comprehensive error handler: {error_context.error_id}")
        
        # Update component health based on error
        if error_context.component in self.component_health:
            if error_context.severity.value in ['high', 'critical']:
                self.component_health[error_context.component] = False
                print(f"⚠ Disabled component {error_context.component} due to {error_context.severity.value} error")
    
    def _handle_recovery_attempted(self, strategy_name: str, success: bool, message: str):
        """Handle recovery attempt results"""
        if success:
            print(f"✓ Recovery successful with {strategy_name}: {message}")
            self.optimization_applied.emit("error_recovery", f"Recovered using {strategy_name}")
        else:
            print(f"✗ Recovery failed with {strategy_name}: {message}")
    
    def _handle_degradation_applied(self, degradation_type: str, description: str):
        """Handle graceful degradation application"""
        print(f"⚠ Graceful degradation applied: {degradation_type} - {description}")
        self.optimization_applied.emit("graceful_degradation", description)
        
        # Update system state to reflect degradation
        if 'memory_mode' in description:
            self.enable_legacy_mode = True
        elif 'parsing_mode' in description:
            # Switch to basic parsing
            if hasattr(self, '_parser_system'):
                self._parser_system = None
    
    def _handle_user_notification(self, title: str, message: str, severity: str):
        """Handle user notification requirements"""
        print(f"User notification [{severity.upper()}]: {title} - {message}")
        
        # Emit error signal for main window to handle
        self.error_occurred.emit(title, message)