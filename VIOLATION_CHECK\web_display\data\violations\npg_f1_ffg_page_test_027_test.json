[{"id": 1, "num": 1, "hier": "tb.pipeline.memory_ctrl.mmu[29].cache_unit[12]", "time_fs": 6073694, "time_display": "6073694 FS", "check_info": "Removal time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:41", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 2, "num": 2, "hier": "tb.debug_unit.pcie_if.interrupt_ctrl.branch_pred[5].interrupt_ctrl", "time_fs": 8838296, "time_display": "8838296 FS", "check_info": "Recovery time violation on signal done[4]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:42", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 3, "num": 3, "hier": "tb.decode_unit[8].writeback_unit.cache_unit.cache_unit[18].decode_unit.interrupt_ctrl", "time_fs": 5741857, "time_display": "5741857 FS", "check_info": "Skew violation on signal rst_n", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:42", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 4, "num": 4, "hier": "tb.debug_unit.debug_unit.memory_ctrl.cpu_top[10].cache_unit[23].interrupt_ctrl", "time_fs": 893095, "time_display": "893095 FS", "check_info": "Recovery time violation on signal wr_en[51]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 5, "num": 5, "hier": "tb.interrupt_ctrl.debug_unit.decode_unit.debug_unit.pcie_if[13]", "time_fs": 7589943, "time_display": "7589943 FS", "check_info": "Pulse width violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:42", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 6, "num": 6, "hier": "tb.pipeline.fetch_unit.pipeline[23].cpu_top.branch_pred", "time_fs": 462150, "time_display": "462150 FS", "check_info": "Period violation on signal status", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 7, "num": 7, "hier": "tb.pipeline.cpu_top[5].memory_ctrl[26].writeback_unit.tlb", "time_fs": 8156062, "time_display": "8156062 FS", "check_info": "Skew violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:42", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 8, "num": 8, "hier": "tb.branch_pred.mmu[24].branch_pred.execute_unit.interrupt_ctrl", "time_fs": 8160570, "time_display": "8160570 FS", "check_info": "Setup time violation on signal data[2]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:43", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 9, "num": 9, "hier": "tb.fetch_unit[25].tlb[15].cache_unit[0]", "time_fs": 4434342, "time_display": "4434342 FS", "check_info": "Hold time violation on signal cs", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 10, "num": 10, "hier": "tb.writeback_unit.branch_pred.decode_unit[21].pipeline", "time_fs": 9917000, "time_display": "9917 PS", "check_info": "Pulse width violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:43", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 11, "num": 11, "hier": "tb.cache_unit.writeback_unit.execute_unit", "time_fs": 6402000, "time_display": "6402 PS", "check_info": "Recovery time violation on signal intr[51]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:43", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 12, "num": 12, "hier": "tb.cpu_top.tlb.tlb.mmu[26].memory_ctrl[14].branch_pred", "time_fs": 7045831, "time_display": "7045831 FS", "check_info": "Pulse width violation on signal enable[9]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:43", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 13, "num": 13, "hier": "tb.pcie_if.memory_ctrl.tlb.fetch_unit.debug_unit[1]", "time_fs": 1470278, "time_display": "1470278 FS", "check_info": "Removal time violation on signal error[60]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 14, "num": 14, "hier": "tb.interrupt_ctrl[18].debug_unit.interrupt_ctrl.pcie_if[9].mmu", "time_fs": 3827036, "time_display": "3827036 FS", "check_info": "Pulse width violation on signal rd_en", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 15, "num": 15, "hier": "tb.interrupt_ctrl.interrupt_ctrl.debug_unit[0].pipeline", "time_fs": 5721000, "time_display": "5721 PS", "check_info": "Period violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:43", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 16, "num": 16, "hier": "tb.mmu.cache_unit[16].memory_ctrl", "time_fs": 2466051, "time_display": "2466051 FS", "check_info": "Setup time violation on signal ack", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 17, "num": 17, "hier": "tb.cache_unit.tlb.execute_unit[24].interrupt_ctrl.fetch_unit.decode_unit", "time_fs": 3278704, "time_display": "3278704 FS", "check_info": "Recovery time violation on signal error", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 18, "num": 18, "hier": "tb.interrupt_ctrl.pipeline.execute_unit.writeback_unit.tlb.debug_unit", "time_fs": 4136000, "time_display": "4136 PS", "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 19, "num": 19, "hier": "tb.pipeline.debug_unit[15].decode_unit", "time_fs": 5379671, "time_display": "5379671 FS", "check_info": "Recovery time violation on signal busy[11]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:44", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 20, "num": 20, "hier": "tb.cpu_top[2].execute_unit.interrupt_ctrl.fetch_unit[8].memory_ctrl.mmu", "time_fs": 3265057, "time_display": "3265057 FS", "check_info": "Setup time violation on signal idle", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 21, "num": 21, "hier": "tb.mmu.ddr_ctrl[20].mmu.ddr_ctrl.branch_pred.writeback_unit", "time_fs": 8361996, "time_display": "8361996 FS", "check_info": "Recovery time violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:44", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 22, "num": 22, "hier": "tb.debug_unit.decode_unit.cache_unit[3].execute_unit[26]", "time_fs": 8493000, "time_display": "8493 PS", "check_info": "Period violation on signal grant", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:44", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 23, "num": 23, "hier": "tb.cpu_top.mmu.pcie_if[9].pcie_if.cpu_top.interrupt_ctrl", "time_fs": 1037695, "time_display": "1037695 FS", "check_info": "Removal time violation on signal ack", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 24, "num": 24, "hier": "tb.pcie_if.branch_pred.decode_unit.pipeline.interrupt_ctrl.branch_pred", "time_fs": 2002711, "time_display": "2002711 FS", "check_info": "Period violation on signal grant", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 25, "num": 25, "hier": "tb.execute_unit.cache_unit.fetch_unit.writeback_unit.memory_ctrl[2]", "time_fs": 2046921, "time_display": "2046921 FS", "check_info": "Period violation on signal ack", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 26, "num": 26, "hier": "tb.interrupt_ctrl[5].decode_unit.memory_ctrl.cpu_top[23].writeback_unit.decode_unit", "time_fs": 6541000, "time_display": "6541 PS", "check_info": "Hold time violation on signal valid[58]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:44", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 27, "num": 27, "hier": "tb.branch_pred.writeback_unit.pcie_if.decode_unit[7]", "time_fs": 9694065, "time_display": "9694065 FS", "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:45", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 28, "num": 28, "hier": "tb.debug_unit[5].debug_unit[7].ddr_ctrl", "time_fs": 2214000, "time_display": "2214 PS", "check_info": "Skew violation on signal enable", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 29, "num": 29, "hier": "tb.tlb.cache_unit.debug_unit.memory_ctrl", "time_fs": 7341278, "time_display": "7341278 FS", "check_info": "Removal time violation on signal error[45]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:45", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 30, "num": 30, "hier": "tb.pipeline.decode_unit.fetch_unit.mmu[24].execute_unit.branch_pred[25]", "time_fs": 9327144, "time_display": "9327144 FS", "check_info": "Recovery time violation on signal grant[32]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:45", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 31, "num": 31, "hier": "tb.cpu_top[4].execute_unit[2].mmu.writeback_unit.debug_unit", "time_fs": 8645230, "time_display": "8645230 FS", "check_info": "Pulse width violation on signal data[19]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:45", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 32, "num": 32, "hier": "tb.tlb.ddr_ctrl.cache_unit", "time_fs": 6016467, "time_display": "6016467 FS", "check_info": "Setup time violation on signal rst_n[11]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:45", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 33, "num": 33, "hier": "tb.interrupt_ctrl.tlb[5].tlb[13].fetch_unit[12].pipeline[12]", "time_fs": 8285000, "time_display": "8.285 NS", "check_info": "Period violation on signal data", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:46", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 34, "num": 34, "hier": "tb.fetch_unit.writeback_unit.pipeline.cpu_top[1].mmu[24].pipeline", "time_fs": 5608969, "time_display": "5608969 FS", "check_info": "Hold time violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:46", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 35, "num": 35, "hier": "tb.memory_ctrl.cache_unit.pipeline", "time_fs": 8882245, "time_display": "8882245 FS", "check_info": "Removal time violation on signal valid", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:46", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 36, "num": 36, "hier": "tb.fetch_unit.memory_ctrl.fetch_unit.interrupt_ctrl", "time_fs": 1261000, "time_display": "1261 PS", "check_info": "Pulse width violation on signal enable", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 37, "num": 37, "hier": "tb.fetch_unit[28].mmu.execute_unit[15].writeback_unit.debug_unit.interrupt_ctrl", "time_fs": 9454000, "time_display": "9454 PS", "check_info": "Period violation on signal clk[50]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:46", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 38, "num": 38, "hier": "tb.fetch_unit.cache_unit[13].mmu.pipeline[4]", "time_fs": 9652000, "time_display": "9652 PS", "check_info": "Period violation on signal busy", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:46", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 39, "num": 39, "hier": "tb.mmu.branch_pred[7].cpu_top[0].branch_pred[20].cache_unit", "time_fs": 3693453, "time_display": "3693453 FS", "check_info": "Skew violation on signal mode[31]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 40, "num": 40, "hier": "tb.debug_unit[31].cache_unit.memory_ctrl.tlb", "time_fs": 9463669, "time_display": "9463669 FS", "check_info": "Setup time violation on signal rst_n[6]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:47", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 41, "num": 41, "hier": "tb.fetch_unit[21].mmu.branch_pred.interrupt_ctrl.debug_unit", "time_fs": 3813917, "time_display": "3813917 FS", "check_info": "Removal time violation on signal valid[19]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 42, "num": 42, "hier": "tb.fetch_unit.writeback_unit[19].pipeline[6].cpu_top[10].decode_unit.ddr_ctrl", "time_fs": 2978951, "time_display": "2978951 FS", "check_info": "Period violation on signal addr", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 43, "num": 43, "hier": "tb.cache_unit.pcie_if.fetch_unit.execute_unit.ddr_ctrl", "time_fs": 6754428, "time_display": "6754428 FS", "check_info": "Pulse width violation on signal intr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:47", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 44, "num": 44, "hier": "tb.decode_unit.cache_unit.cache_unit.pcie_if.cpu_top[18]", "time_fs": 2993739, "time_display": "2993739 FS", "check_info": "Removal time violation on signal intr[36]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 45, "num": 45, "hier": "tb.branch_pred.pcie_if.mmu[22].debug_unit", "time_fs": 6441000, "time_display": "6441 PS", "check_info": "Recovery time violation on signal mode[22]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:47", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 46, "num": 46, "hier": "tb.pipeline[29].branch_pred.memory_ctrl[13]", "time_fs": 2996531, "time_display": "2996531 FS", "check_info": "Period violation on signal mode", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 47, "num": 47, "hier": "tb.ddr_ctrl.pcie_if[22].pipeline[18]", "time_fs": 5938838, "time_display": "5938838 FS", "check_info": "Setup time violation on signal addr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:47", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 48, "num": 48, "hier": "tb.branch_pred.mmu.interrupt_ctrl.memory_ctrl[29]", "time_fs": 5389000, "time_display": "5389 PS", "check_info": "Removal time violation on signal ready", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:48", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 49, "num": 49, "hier": "tb.fetch_unit.branch_pred[11].execute_unit.interrupt_ctrl.pcie_if.pipeline[19]", "time_fs": 729587, "time_display": "729587 FS", "check_info": "Hold time violation on signal error[17]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 50, "num": 50, "hier": "tb.tlb[13].memory_ctrl.pipeline.mmu.decode_unit.memory_ctrl", "time_fs": 4348884, "time_display": "4348884 FS", "check_info": "Period violation on signal intr[13]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 51, "num": 51, "hier": "tb.writeback_unit.cpu_top.ddr_ctrl", "time_fs": 963000, "time_display": "963 PS", "check_info": "Period violation on signal rd_en[20]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 52, "num": 52, "hier": "tb.memory_ctrl[14].writeback_unit[1].memory_ctrl[20]", "time_fs": 2703663, "time_display": "2703663 FS", "check_info": "Skew violation on signal ready[2]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 53, "num": 53, "hier": "tb.tlb.interrupt_ctrl[6].memory_ctrl[1].memory_ctrl[5]", "time_fs": 4739000, "time_display": "4739 PS", "check_info": "Hold time violation on signal clk[50]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 54, "num": 54, "hier": "tb.decode_unit.fetch_unit.pipeline.tlb[16].decode_unit", "time_fs": 9397971, "time_display": "9397971 FS", "check_info": "Period violation on signal mode[33]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:48", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 55, "num": 55, "hier": "tb.debug_unit.pcie_if.memory_ctrl.cache_unit.execute_unit.pcie_if", "time_fs": 3245386, "time_display": "3245386 FS", "check_info": "Hold time violation on signal valid", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 56, "num": 56, "hier": "tb.cpu_top.tlb[28].fetch_unit[17]", "time_fs": 2231000, "time_display": "2231 PS", "check_info": "Period violation on signal clk[3]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 57, "num": 57, "hier": "tb.tlb[17].ddr_ctrl[2].debug_unit.ddr_ctrl.ddr_ctrl", "time_fs": 6875765, "time_display": "6875765 FS", "check_info": "Removal time violation on signal rd_en[61]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:48", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 58, "num": 58, "hier": "tb.mmu.ddr_ctrl.ddr_ctrl", "time_fs": 7256000, "time_display": "7256 PS", "check_info": "Setup time violation on signal rst_n", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:48", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 59, "num": 59, "hier": "tb.tlb[20].interrupt_ctrl[15].pcie_if.cache_unit.decode_unit.interrupt_ctrl", "time_fs": 5785000, "time_display": "5.785 NS", "check_info": "Setup time violation on signal intr", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:48", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 60, "num": 60, "hier": "tb.branch_pred[3].memory_ctrl.decode_unit[7].mmu", "time_fs": 7267576, "time_display": "7267576 FS", "check_info": "Hold time violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:49", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 61, "num": 61, "hier": "tb.tlb[12].debug_unit[1].interrupt_ctrl[12].ddr_ctrl", "time_fs": 6544000, "time_display": "6.544 NS", "check_info": "Setup time violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:49", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 62, "num": 62, "hier": "tb.writeback_unit.ddr_ctrl.decode_unit[17].memory_ctrl", "time_fs": 5445209, "time_display": "5445209 FS", "check_info": "Recovery time violation on signal data", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:49", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 63, "num": 63, "hier": "tb.memory_ctrl.execute_unit[19].debug_unit", "time_fs": 1272000, "time_display": "1.272 NS", "check_info": "Hold time violation on signal req", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 64, "num": 64, "hier": "tb.ddr_ctrl.interrupt_ctrl.fetch_unit.mmu[3]", "time_fs": 5883000, "time_display": "5883 PS", "check_info": "Removal time violation on signal idle", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:49", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 65, "num": 65, "hier": "tb.fetch_unit[6].pcie_if.execute_unit[9]", "time_fs": 9753164, "time_display": "9753164 FS", "check_info": "Period violation on signal ready", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:50", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 66, "num": 66, "hier": "tb.cpu_top.tlb.interrupt_ctrl.execute_unit", "time_fs": 8518000, "time_display": "8.518 NS", "check_info": "Recovery time violation on signal idle[17]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:50", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 67, "num": 67, "hier": "tb.tlb.writeback_unit.debug_unit.ddr_ctrl.fetch_unit", "time_fs": 4184974, "time_display": "4184974 FS", "check_info": "Hold time violation on signal grant", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 68, "num": 68, "hier": "tb.interrupt_ctrl[29].pcie_if.execute_unit.memory_ctrl.cache_unit", "time_fs": 7713454, "time_display": "7713454 FS", "check_info": "Hold time violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:50", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 69, "num": 69, "hier": "tb.fetch_unit.writeback_unit[26].memory_ctrl.mmu", "time_fs": 2808390, "time_display": "2808390 FS", "check_info": "Skew violation on signal addr[38]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 70, "num": 70, "hier": "tb.branch_pred.memory_ctrl.interrupt_ctrl", "time_fs": 2446951, "time_display": "2446951 FS", "check_info": "Removal time violation on signal grant[43]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 71, "num": 71, "hier": "tb.debug_unit.execute_unit.branch_pred.branch_pred.pipeline.decode_unit", "time_fs": 1094000, "time_display": "1094 PS", "check_info": "Period violation on signal busy", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 72, "num": 72, "hier": "tb.interrupt_ctrl[15].pipeline.decode_unit.tlb.mmu[15]", "time_fs": 4848000, "time_display": "4848 PS", "check_info": "Period violation on signal error[22]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 73, "num": 73, "hier": "tb.memory_ctrl[6].mmu[30].mmu.debug_unit[9].interrupt_ctrl", "time_fs": 5799539, "time_display": "5799539 FS", "check_info": "Pulse width violation on signal ack[23]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:50", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 74, "num": 74, "hier": "tb.cache_unit.decode_unit.ddr_ctrl.interrupt_ctrl.tlb", "time_fs": 4469000, "time_display": "4469 PS", "check_info": "Period violation on signal intr", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 75, "num": 75, "hier": "tb.cpu_top.fetch_unit[29].fetch_unit.decode_unit[1].decode_unit", "time_fs": 5847539, "time_display": "5847539 FS", "check_info": "Skew violation on signal grant", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:51", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 76, "num": 76, "hier": "tb.debug_unit.writeback_unit.tlb.mmu[17]", "time_fs": 6525000, "time_display": "6525 PS", "check_info": "Period violation on signal rst_n", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:51", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 77, "num": 77, "hier": "tb.pcie_if.debug_unit[27].execute_unit.branch_pred[15].execute_unit.cache_unit", "time_fs": 6201772, "time_display": "6201772 FS", "check_info": "Pulse width violation on signal error", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:51", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 78, "num": 78, "hier": "tb.cpu_top.mmu[8].memory_ctrl.branch_pred", "time_fs": 7414000, "time_display": "7.414 NS", "check_info": "Recovery time violation on signal data[46]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:51", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 79, "num": 79, "hier": "tb.tlb[30].debug_unit.cpu_top[7].fetch_unit.cpu_top[20].tlb[14]", "time_fs": 4748222, "time_display": "4748222 FS", "check_info": "Recovery time violation on signal mode", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 80, "num": 80, "hier": "tb.mmu.cache_unit.fetch_unit.ddr_ctrl[3].ddr_ctrl[9]", "time_fs": 3823482, "time_display": "3823482 FS", "check_info": "Skew violation on signal error[34]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 81, "num": 81, "hier": "tb.cpu_top.interrupt_ctrl.writeback_unit.pipeline[6]", "time_fs": 5912878, "time_display": "5912878 FS", "check_info": "Setup time violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:51", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 82, "num": 82, "hier": "tb.cpu_top.tlb[20].execute_unit", "time_fs": 7405000, "time_display": "7405 PS", "check_info": "Pulse width violation on signal ready", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:52", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 83, "num": 83, "hier": "tb.cache_unit.execute_unit.pipeline.cpu_top.cache_unit", "time_fs": 9559931, "time_display": "9559931 FS", "check_info": "Period violation on signal mode", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:52", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 84, "num": 84, "hier": "tb.ddr_ctrl.pipeline.fetch_unit.mmu.memory_ctrl[25].cache_unit", "time_fs": 4741110, "time_display": "4741110 FS", "check_info": "Recovery time violation on signal done[52]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 85, "num": 85, "hier": "tb.writeback_unit.interrupt_ctrl[19].ddr_ctrl", "time_fs": 431411, "time_display": "431411 FS", "check_info": "Hold time violation on signal error[2]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 86, "num": 86, "hier": "tb.pcie_if[24].mmu.debug_unit", "time_fs": 3951040, "time_display": "3951040 FS", "check_info": "Skew violation on signal mode", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 87, "num": 87, "hier": "tb.mmu.mmu.tlb", "time_fs": 189758, "time_display": "189758 FS", "check_info": "Removal time violation on signal addr[63]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 88, "num": 88, "hier": "tb.writeback_unit.cpu_top.execute_unit.decode_unit.decode_unit.branch_pred", "time_fs": 5220345, "time_display": "5220345 FS", "check_info": "Recovery time violation on signal ack[45]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:52", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 89, "num": 89, "hier": "tb.mmu.interrupt_ctrl.fetch_unit.pipeline[18].pcie_if", "time_fs": 3443000, "time_display": "3443 PS", "check_info": "Removal time violation on signal addr[13]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 90, "num": 90, "hier": "tb.decode_unit.cpu_top.interrupt_ctrl.pcie_if.mmu", "time_fs": 7591000, "time_display": "7591 PS", "check_info": "Recovery time violation on signal rd_en", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:52", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 91, "num": 91, "hier": "tb.ddr_ctrl[14].debug_unit[29].debug_unit.fetch_unit.decode_unit.memory_ctrl[25]", "time_fs": 1761414, "time_display": "1761414 FS", "check_info": "Hold time violation on signal rst_n[20]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 92, "num": 92, "hier": "tb.debug_unit[30].pcie_if.execute_unit", "time_fs": 4975616, "time_display": "4975616 FS", "check_info": "Skew violation on signal rst_n[24]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 93, "num": 93, "hier": "tb.tlb[26].cpu_top.branch_pred[26]", "time_fs": 7617000, "time_display": "7617 PS", "check_info": "Removal time violation on signal enable[60]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:53", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 94, "num": 94, "hier": "tb.branch_pred[20].cache_unit.cache_unit", "time_fs": 666000, "time_display": "666 PS", "check_info": "Pulse width violation on signal addr", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 95, "num": 95, "hier": "tb.decode_unit.ddr_ctrl[0].tlb.interrupt_ctrl.writeback_unit", "time_fs": 323000, "time_display": "323 PS", "check_info": "Setup time violation on signal ready", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 96, "num": 96, "hier": "tb.tlb[1].pipeline[16].pcie_if.decode_unit[24]", "time_fs": 9189267, "time_display": "9189267 FS", "check_info": "Period violation on signal valid[57]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:53", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 97, "num": 97, "hier": "tb.pipeline.pipeline.interrupt_ctrl.writeback_unit[25]", "time_fs": 4542541, "time_display": "4542541 FS", "check_info": "Setup time violation on signal rd_en[6]", "status": "confirmed", "confirmer": "系统自动", "result": "pass", "reason": "复位期间时序违例（<= 5.0ns），可以忽略", "confirmed_at": "2025-08-07 15:18:35", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 98, "num": 98, "hier": "tb.cpu_top.execute_unit.memory_ctrl.writeback_unit", "time_fs": 8360855, "time_display": "8360855 FS", "check_info": "Period violation on signal busy", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:53", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 99, "num": 99, "hier": "tb.writeback_unit.cache_unit.fetch_unit.tlb.debug_unit[4]", "time_fs": 9820218, "time_display": "9820218 FS", "check_info": "Period violation on signal ready[62]", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:53", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 100, "num": 100, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[25]", "time_fs": 8660022, "time_display": "8660022 FS", "check_info": "Period violation on signal clk", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:53", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 101, "num": 101, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[26]", "time_fs": 866002233, "time_display": "866002233 FS", "check_info": "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:54", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 102, "num": 102, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[27]", "time_fs": 866002234, "time_display": "866002234 FS", "check_info": "setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:54", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}, {"id": 103, "num": 103, "hier": "tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[28]", "time_fs": 866002234, "time_display": "866002234 FS", "check_info": "hold( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )", "status": "confirmed", "confirmer": "bbb", "result": "pass", "reason": "aaa", "confirmed_at": "2025-08-07 15:18:54", "corner": "npg_f1_ffg", "case": "page_test_027_test", "case_name": "page_test_027_test", "source": "gui"}]