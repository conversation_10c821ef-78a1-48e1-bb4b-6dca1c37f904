#!/usr/bin/env python3
"""
Excel解析器单元测试

测试ExcelTableParser类的所有解析功能
"""

import unittest
import sys
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock

# 添加路径以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from parser import ExcelTableParser
from models import HeaderInfo, FieldInfo, RegisterInfo, RegisterTableData, ParseError, ValidationError


class TestExcelTableParser(unittest.TestCase):
    """测试Excel表格解析器"""
    
    def setUp(self):
        """设置测试环境"""
        self.parser = ExcelTableParser()
    
    def test_parser_initialization(self):
        """测试解析器初始化"""
        self.assertIsInstance(self.parser, ExcelTableParser)
        self.assertIsNotNone(self.parser.EXPECTED_COLUMNS)
        self.assertIn('offset', self.parser.EXPECTED_COLUMNS)
        self.assertIn('reg_name', self.parser.EXPECTED_COLUMNS)
        self.assertIn('field_name', self.parser.EXPECTED_COLUMNS)
    
    @patch('parser.openpyxl.load_workbook')
    def test_file_loading_success(self, mock_load_workbook):
        """测试文件加载成功"""
        # 模拟工作簿
        mock_workbook = Mock()
        mock_worksheet = Mock()
        mock_workbook.active = mock_worksheet
        mock_load_workbook.return_value = mock_workbook
        
        # 模拟表头数据
        mock_worksheet.iter_rows.return_value = [
            [Mock(value="项目名称"), Mock(value="测试项目")],
            [Mock(value="子系统"), Mock(value="测试子系统")],
            [Mock(value="模块名称"), Mock(value="测试模块")],
            [Mock(value="BASE ADDR"), Mock(value="0x1000")],
            [Mock(value="Offset"), Mock(value="RegName"), Mock(value="FieldName"), 
             Mock(value="Bit Range"), Mock(value="RW")],
            [Mock(value="0x0000"), Mock(value="TEST_REG"), Mock(value="TEST_FIELD"),
             Mock(value="7:0"), Mock(value="RW")]
        ]
        
        # 测试解析
        with tempfile.NamedTemporaryFile(suffix='.xlsx') as temp_file:
            try:
                result = self.parser.parse_register_table(temp_file.name)
                self.assertIsInstance(result, RegisterTableData)
            except Exception as e:
                # 如果解析失败，至少验证文件加载被调用
                mock_load_workbook.assert_called_once_with(temp_file.name)
    
    @patch('parser.openpyxl.load_workbook')
    def test_file_loading_failure(self, mock_load_workbook):
        """测试文件加载失败"""
        mock_load_workbook.side_effect = Exception("文件不存在")
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx') as temp_file:
            with self.assertRaises(ParseError):
                self.parser.parse_register_table(temp_file.name)
    
    def test_column_mapping(self):
        """测试列映射功能"""
        # 测试标准列名
        test_headers = ["Offset", "RegName", "FieldName", "Bit Range", "RW"]
        mapping = self.parser._map_columns(test_headers)
        
        expected_mapping = {
            'offset': 0,
            'reg_name': 1,
            'field_name': 2,
            'bit_range': 3,
            'rw': 4
        }
        
        for key, expected_index in expected_mapping.items():
            self.assertEqual(mapping.get(key), expected_index)
    
    def test_column_mapping_case_insensitive(self):
        """测试列映射大小写不敏感"""
        test_headers = ["offset", "regname", "fieldname", "bit range", "rw"]
        mapping = self.parser._map_columns(test_headers)
        
        self.assertIsNotNone(mapping.get('offset'))
        self.assertIsNotNone(mapping.get('reg_name'))
        self.assertIsNotNone(mapping.get('field_name'))
    
    def test_column_mapping_missing_required(self):
        """测试缺少必需列的映射"""
        test_headers = ["Offset", "RegName"]  # 缺少必需列
        
        with self.assertRaises(ValidationError):
            self.parser._map_columns(test_headers)
    
    def test_header_extraction(self):
        """测试表头信息提取"""
        # 模拟表头行数据
        header_rows = [
            [Mock(value="项目名称"), Mock(value="测试项目")],
            [Mock(value="子系统"), Mock(value="测试子系统")],
            [Mock(value="模块名称"), Mock(value="测试模块")],
            [Mock(value="BASE ADDR"), Mock(value="0x1000")]
        ]
        
        header = self.parser._extract_header_info(header_rows)
        
        self.assertIsInstance(header, HeaderInfo)
        self.assertEqual(header.project_name, "测试项目")
        self.assertEqual(header.sub_system, "测试子系统")
        self.assertEqual(header.module_name, "测试模块")
        self.assertEqual(header.base_addr, "0x1000")
    
    def test_header_extraction_missing_data(self):
        """测试表头信息提取缺少数据"""
        # 不完整的表头数据
        header_rows = [
            [Mock(value="项目名称"), Mock(value="")],
            [Mock(value="子系统"), Mock(value="测试子系统")]
        ]
        
        with self.assertRaises(ValidationError):
            self.parser._extract_header_info(header_rows)
    
    def test_field_parsing(self):
        """测试字段解析"""
        # 模拟字段行数据
        field_row = [
            Mock(value="0x0000"),      # Offset
            Mock(value="TEST_REG"),    # RegName
            Mock(value="TEST_FIELD"),  # FieldName
            Mock(value="7:0"),         # Bit Range
            Mock(value="RW"),          # RW
            Mock(value="0xFF"),        # Reset Value
            Mock(value="测试字段")      # Description
        ]
        
        column_mapping = {
            'offset': 0, 'reg_name': 1, 'field_name': 2,
            'bit_range': 3, 'rw': 4, 'reset_value': 5, 'description': 6
        }
        
        field = self.parser._parse_field_row(field_row, column_mapping)
        
        self.assertIsInstance(field, FieldInfo)
        self.assertEqual(field.name, "TEST_FIELD")
        self.assertEqual(field.bit_range, "7:0")
        self.assertEqual(field.rw_attribute, "RW")
        self.assertEqual(field.reset_value, "0xFF")
        self.assertEqual(field.description, "测试字段")
    
    def test_field_parsing_reserved(self):
        """测试保留字段解析"""
        field_row = [
            Mock(value="0x0000"),
            Mock(value="TEST_REG"),
            Mock(value="Reserved"),
            Mock(value="15:8"),
            Mock(value="RO")
        ]
        
        column_mapping = {
            'offset': 0, 'reg_name': 1, 'field_name': 2,
            'bit_range': 3, 'rw': 4
        }
        
        field = self.parser._parse_field_row(field_row, column_mapping)
        
        self.assertTrue(field.is_reserved)
        self.assertEqual(field.name, "Reserved")
    
    def test_register_grouping(self):
        """测试寄存器分组"""
        # 模拟字段列表
        fields = [
            FieldInfo("FIELD_A", "31:24", "RW", "0x00", "字段A"),
            FieldInfo("FIELD_B", "23:16", "RO", "0xFF", "字段B"),
            FieldInfo("FIELD_C", "15:8", "RW", "0x55", "字段C"),
        ]
        
        # 模拟寄存器数据
        register_data = {
            "0x0000": {
                "name": "TEST_REG",
                "description": "测试寄存器",
                "fields": fields
            }
        }
        
        registers = self.parser._group_fields_into_registers(register_data)
        
        self.assertEqual(len(registers), 1)
        register = registers[0]
        self.assertEqual(register.offset, "0x0000")
        self.assertEqual(register.name, "TEST_REG")
        self.assertEqual(len(register.fields), 3)
    
    def test_data_validation(self):
        """测试数据验证"""
        # 有效数据
        valid_header = HeaderInfo("项目", "子系统", "模块", "0x1000")
        valid_registers = [
            RegisterInfo("0x0000", "REG1", "寄存器1", 32, [
                FieldInfo("FIELD1", "7:0", "RW", "0x00")
            ])
        ]
        
        # 验证应该通过
        self.assertTrue(self.parser._validate_parsed_data(valid_header, valid_registers))
        
        # 无效表头
        invalid_header = HeaderInfo("", "", "", "")
        self.assertFalse(self.parser._validate_parsed_data(invalid_header, valid_registers))
        
        # 空寄存器列表
        self.assertFalse(self.parser._validate_parsed_data(valid_header, []))
    
    def test_duplicate_register_detection(self):
        """测试重复寄存器检测"""
        registers = [
            RegisterInfo("0x0000", "REG1", "寄存器1", 32, []),
            RegisterInfo("0x0000", "REG1", "重复寄存器", 32, [])  # 重复
        ]
        
        with self.assertRaises(ValidationError):
            self.parser._validate_parsed_data(
                HeaderInfo("项目", "子系统", "模块", "0x1000"),
                registers
            )
    
    def test_offset_parsing(self):
        """测试偏移地址解析"""
        # 十六进制格式
        self.assertEqual(self.parser._parse_offset("0x1000"), "0x1000")
        self.assertEqual(self.parser._parse_offset("0X2000"), "0x2000")
        
        # 十进制格式
        self.assertEqual(self.parser._parse_offset("4096"), "0x1000")
        
        # 无效格式
        with self.assertRaises(ValueError):
            self.parser._parse_offset("invalid")
    
    def test_bit_range_validation(self):
        """测试位范围验证"""
        # 有效范围
        self.assertTrue(self.parser._validate_bit_range("31:24"))
        self.assertTrue(self.parser._validate_bit_range("15"))
        self.assertTrue(self.parser._validate_bit_range("7:0"))
        
        # 无效范围
        self.assertFalse(self.parser._validate_bit_range("32:24"))  # 超出32位
        self.assertFalse(self.parser._validate_bit_range("24:31"))  # 顺序错误
        self.assertFalse(self.parser._validate_bit_range("invalid"))
    
    def test_performance_with_large_data(self):
        """测试大数据量性能"""
        # 创建大量模拟数据
        large_register_data = {}
        
        for i in range(1000):  # 1000个寄存器
            offset = f"0x{i*4:04X}"
            fields = []
            
            for j in range(4):  # 每个寄存器4个字段
                field = FieldInfo(
                    name=f"FIELD_{i}_{j}",
                    bit_range=f"{j*8+7}:{j*8}",
                    rw_attribute="RW",
                    reset_value="0x00"
                )
                fields.append(field)
            
            large_register_data[offset] = {
                "name": f"REG_{i}",
                "description": f"寄存器{i}",
                "fields": fields
            }
        
        # 测试分组性能
        import time
        start_time = time.time()
        registers = self.parser._group_fields_into_registers(large_register_data)
        end_time = time.time()
        
        # 验证结果
        self.assertEqual(len(registers), 1000)
        
        # 性能要求：处理1000个寄存器应该在1秒内完成
        processing_time = end_time - start_time
        self.assertLess(processing_time, 1.0, f"处理时间过长: {processing_time:.2f}秒")


class TestParserErrorHandling(unittest.TestCase):
    """测试解析器错误处理"""
    
    def setUp(self):
        """设置测试环境"""
        self.parser = ExcelTableParser()
    
    def test_file_not_found_error(self):
        """测试文件不存在错误"""
        with self.assertRaises(ParseError) as context:
            self.parser.parse_register_table("nonexistent_file.xlsx")
        
        self.assertIn("文件不存在", str(context.exception))
    
    def test_invalid_file_format_error(self):
        """测试无效文件格式错误"""
        # 创建非Excel文件
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
            temp_file.write(b"This is not an Excel file")
            temp_file_path = temp_file.name
        
        try:
            with self.assertRaises(ParseError):
                self.parser.parse_register_table(temp_file_path)
        finally:
            os.unlink(temp_file_path)
    
    def test_empty_worksheet_error(self):
        """测试空工作表错误"""
        with patch('parser.openpyxl.load_workbook') as mock_load:
            mock_workbook = Mock()
            mock_worksheet = Mock()
            mock_worksheet.iter_rows.return_value = []  # 空工作表
            mock_workbook.active = mock_worksheet
            mock_load.return_value = mock_workbook
            
            with tempfile.NamedTemporaryFile(suffix='.xlsx') as temp_file:
                with self.assertRaises(ValidationError):
                    self.parser.parse_register_table(temp_file.name)
    
    def test_malformed_data_error(self):
        """测试格式错误的数据"""
        with patch('parser.openpyxl.load_workbook') as mock_load:
            mock_workbook = Mock()
            mock_worksheet = Mock()
            
            # 模拟格式错误的数据
            mock_worksheet.iter_rows.return_value = [
                [Mock(value="Invalid"), Mock(value="Header")],
                [Mock(value="Data"), Mock(value="Format")]
            ]
            
            mock_workbook.active = mock_worksheet
            mock_load.return_value = mock_workbook
            
            with tempfile.NamedTemporaryFile(suffix='.xlsx') as temp_file:
                with self.assertRaises((ParseError, ValidationError)):
                    self.parser.parse_register_table(temp_file.name)


if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromModule(sys.modules[__name__])
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print(f"\n✅ 所有测试通过！运行了 {result.testsRun} 个测试")
    else:
        print(f"\n❌ 测试失败！{len(result.failures)} 个失败，{len(result.errors)} 个错误")
        sys.exit(1)