"""
Test script for the integrated memory management system
"""

import time
import sys
import os

# Add the plugin directory to the path
sys.path.insert(0, os.path.dirname(__file__))

try:
    from integrated_memory_system import (
        IntegratedMemorySystem, MemorySystemConfig, OptimizationMode
    )
    from violation_data_streaming import ListDataSource
except ImportError:
    # Handle relative imports
    from .integrated_memory_system import (
        IntegratedMemorySystem, MemorySystemConfig, OptimizationMode
    )
    from .violation_data_streaming import ListDataSource


def create_test_violations(count: int):
    """Create test violation data"""
    violations = []
    for i in range(count):
        violations.append({
            'id': i,
            'NUM': i + 1,
            'Hier': f'test/path/violation_{i}',
            'Time': f'{i * 100}ps',
            'time_fs': i * 100000,
            'Check': f'setup_check_{i % 10}',
            'case_name': 'test_case',
            'corner': 'test_corner'
        })
    return violations


def test_memory_system():
    """Test the integrated memory system"""
    print("Testing Integrated Memory Management System")
    print("=" * 50)
    
    # Create configuration
    config = MemorySystemConfig(
        memory_warning_threshold=70.0,
        memory_critical_threshold=85.0,
        default_chunk_size=100,  # Small chunks for testing
        max_cached_chunks=5,
        optimization_mode=OptimizationMode.BALANCED,
        auto_optimization_enabled=True
    )
    
    # Create system
    memory_system = IntegratedMemorySystem(config)
    
    try:
        # Start the system
        print("1. Starting memory system...")
        memory_system.start_system()
        
        # Create test data
        print("2. Creating test violation data...")
        test_violations = create_test_violations(1000)
        
        # Create violation stream
        print("3. Creating violation stream...")
        stream = memory_system.create_violation_stream(
            "test_stream", 
            test_violations,
            chunk_size=100
        )
        
        print(f"   Stream created with {len(stream)} violations")
        
        # Test memory tracking
        print("4. Testing memory tracking...")
        start_time = time.time()
        
        # Process violations in batches
        batch_size = 100
        for i in range(0, len(test_violations), batch_size):
            batch_count = min(batch_size, len(test_violations) - i)
            processing_time = time.time() - start_time
            
            # Track processing
            result = memory_system.track_violation_processing(
                batch_count, processing_time
            )
            
            print(f"   Processed batch {i//batch_size + 1}: {batch_count} violations")
            
            # Access some data from stream
            for j in range(i, min(i + 10, len(test_violations))):
                violation = stream[j]
                assert violation['id'] == j
        
        # Test memory prediction
        print("5. Testing memory prediction...")
        prediction = memory_system.predict_memory_usage(5000)
        print(f"   Predicted memory for 5000 violations: {prediction['predicted_additional_mb']:.2f}MB")
        
        # Test thresholds
        print("6. Testing memory thresholds...")
        thresholds = memory_system.get_memory_thresholds(1000)
        print(f"   Memory limit for 1000 violations: {thresholds['memory_limit_mb']}MB")
        print(f"   Recommended batch size: {thresholds['batch_size']}")
        
        # Test optimization
        print("7. Testing forced optimization...")
        opt_result = memory_system.force_optimization()
        if 'optimization_result' in opt_result:
            result = opt_result['optimization_result']
            print(f"   Optimization completed: {result.success}")
            print(f"   Actions taken: {result.actions_taken}")
            print(f"   Memory freed: {result.memory_freed_mb:.2f}MB")
        
        # Test system statistics
        print("8. Getting system statistics...")
        stats = memory_system.get_system_stats()
        
        print(f"   System active: {stats['system']['active']}")
        print(f"   Total violations processed: {stats['system']['total_violations_processed']}")
        print(f"   Total streams created: {stats['system']['total_streams_created']}")
        print(f"   Peak memory usage: {stats['system']['peak_memory_usage_mb']:.2f}MB")
        print(f"   Current optimization mode: {stats['optimizer']['current_mode']}")
        
        # Test stream access patterns
        print("9. Testing stream access patterns...")
        
        # Sequential access
        start_time = time.time()
        for i in range(0, min(500, len(stream)), 10):
            violation = stream[i]
            assert violation['NUM'] == i + 1
        sequential_time = time.time() - start_time
        print(f"   Sequential access time: {sequential_time:.3f}s")
        
        # Random access
        import random
        start_time = time.time()
        for _ in range(50):
            i = random.randint(0, len(stream) - 1)
            violation = stream[i]
            assert violation['NUM'] == i + 1
        random_time = time.time() - start_time
        print(f"   Random access time: {random_time:.3f}s")
        
        # Test slice access
        start_time = time.time()
        slice_data = stream[100:200]
        assert len(slice_data) == 100
        slice_time = time.time() - start_time
        print(f"   Slice access time: {slice_time:.3f}s")
        
        # Test cache statistics
        stream_stats = stream.get_detailed_stats()
        cache_stats = stream_stats['cache']
        print(f"   Cache hit ratio: {cache_stats['hit_ratio']:.2f}")
        print(f"   Cache memory usage: {cache_stats['memory_usage_mb']:.2f}MB")
        
        print("\n✅ All tests passed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up
        print("\n10. Cleaning up...")
        memory_system.stop_system()
        print("    System stopped")


if __name__ == "__main__":
    # Only run test if PyQt5 is available
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QCoreApplication
        
        # Create minimal Qt application for testing
        app = QCoreApplication.instance()
        if app is None:
            app = QCoreApplication([])
        
        test_memory_system()
        
    except ImportError as e:
        print(f"Cannot run test: {e}")
        print("PyQt5 is required for the memory management system")
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()