"""
Data parsing modules for timing violation web display.

This package contains parsers for different data sources:
- Excel file parser for exported violation files
- Database reader for SQLite database access
"""

try:
    from .excel_parser import ExcelParser
    from .database_reader import DatabaseReader
except ImportError:
    from excel_parser import ExcelParser
    from database_reader import DatabaseReader

__all__ = ['ExcelParser', 'DatabaseReader']