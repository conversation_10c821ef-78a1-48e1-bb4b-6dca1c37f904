"""
Unit tests for Database reader.
"""

import unittest
import tempfile
import sqlite3
import os
from pathlib import Path

# Import the module to test
import sys
sys.path.append(str(Path(__file__).parent.parent))

from parsers.database_reader import DatabaseReader, DatabaseError


class TestDatabaseReader(unittest.TestCase):
    """Test cases for DatabaseReader class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.test_db_path = os.path.join(self.temp_dir, "test_timing_violations.db")
        self.create_test_database()
        self.reader = DatabaseReader(self.test_db_path)
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_database(self):
        """Create a test database with sample data."""
        conn = sqlite3.connect(self.test_db_path)
        cursor = conn.cursor()
        
        # Create timing_violations table
        cursor.execute('''
            CREATE TABLE timing_violations (
                id INTEGER PRIMARY KEY,
                num INTEGER,
                hier TEXT,
                time_ns REAL,
                check_info TEXT,
                corner TEXT,
                case_name TEXT
            )
        ''')
        
        # Create confirmation_records table
        cursor.execute('''
            CREATE TABLE confirmation_records (
                id INTEGER PRIMARY KEY,
                violation_id INTEGER,
                status TEXT,
                confirmer TEXT,
                result TEXT,
                reason TEXT,
                confirmed_at TEXT,
                FOREIGN KEY (violation_id) REFERENCES timing_violations (id)
            )
        ''')
        
        # Insert sample data
        cursor.execute('''
            INSERT INTO timing_violations (num, hier, time_ns, check_info, corner, case_name)
            VALUES (1, 'cpu/core/reg1', 1.5, 'setup check', 'ss', 'test1')
        ''')
        
        cursor.execute('''
            INSERT INTO timing_violations (num, hier, time_ns, check_info, corner, case_name)
            VALUES (2, 'cpu/core/reg2', 2.3, 'hold check', 'ff', 'test2')
        ''')
        
        cursor.execute('''
            INSERT INTO confirmation_records (violation_id, status, confirmer, result, reason, confirmed_at)
            VALUES (1, 'confirmed', 'john_doe', 'false_positive', 'Clock skew issue', '2024-01-15 10:30:00')
        ''')
        
        cursor.execute('''
            INSERT INTO confirmation_records (violation_id, status, confirmer, result, reason, confirmed_at)
            VALUES (2, 'confirmed', 'jane_smith', 'real_violation', 'Timing constraint violation', '2024-01-16 14:20:00')
        ''')
        
        conn.commit()
        conn.close()
    
    def test_connect_success(self):
        """Test successful database connection."""
        self.reader.connect()
        self.assertIsNotNone(self.reader.connection)
        self.reader.disconnect()
    
    def test_connect_nonexistent_database(self):
        """Test connection to non-existent database."""
        reader = DatabaseReader("/nonexistent/path/db.sqlite")
        
        with self.assertRaises(DatabaseError) as context:
            reader.connect()
        
        self.assertIn("Database file not found", str(context.exception))
    
    def test_context_manager(self):
        """Test using database reader as context manager."""
        with self.reader as db:
            self.assertIsNotNone(db.connection)
        
        # Connection should be closed after context
        self.assertIsNone(self.reader.connection)
    
    def test_get_confirmed_violations(self):
        """Test getting confirmed violations."""
        with self.reader:
            violations = self.reader.get_confirmed_violations()
        
        self.assertEqual(len(violations), 2)
        
        # Check first violation
        violation1 = violations[0]
        self.assertEqual(violation1['num'], 1)
        self.assertEqual(violation1['hier'], 'cpu/core/reg1')
        self.assertEqual(violation1['time_ns'], 1.5)
        self.assertEqual(violation1['corner'], 'ss')
        self.assertEqual(violation1['case'], 'test1')
        self.assertEqual(violation1['status'], 'confirmed')
        self.assertEqual(violation1['confirmer'], 'john_doe')
        self.assertEqual(violation1['source'], 'database')
    
    def test_get_corners_from_db(self):
        """Test getting unique corners from database."""
        with self.reader:
            corners = self.reader.get_corners_from_db()
        
        self.assertIn('ss', corners)
        self.assertIn('ff', corners)
        self.assertEqual(len(corners), 2)
    
    def test_get_cases_from_db(self):
        """Test getting cases from database."""
        with self.reader:
            # Get all cases
            all_cases = self.reader.get_cases_from_db()
            self.assertIn('test1', all_cases)
            self.assertIn('test2', all_cases)
            
            # Get cases for specific corner
            ss_cases = self.reader.get_cases_from_db('ss')
            self.assertIn('test1', ss_cases)
            self.assertNotIn('test2', ss_cases)
    
    def test_get_violation_with_confirmation(self):
        """Test getting specific violation with confirmation."""
        with self.reader:
            violation = self.reader.get_violation_with_confirmation(1)
        
        self.assertIsNotNone(violation)
        self.assertEqual(violation['num'], 1)
        self.assertEqual(violation['confirmer'], 'john_doe')
        self.assertEqual(violation['result'], 'false_positive')
    
    def test_get_violation_with_confirmation_not_found(self):
        """Test getting non-existent violation."""
        with self.reader:
            violation = self.reader.get_violation_with_confirmation(999)
        
        self.assertIsNone(violation)
    
    def test_test_connection(self):
        """Test connection testing method."""
        result = self.reader.test_connection()
        self.assertTrue(result)
    
    def test_test_connection_invalid_db(self):
        """Test connection testing with invalid database."""
        reader = DatabaseReader("/nonexistent/path/db.sqlite")
        result = reader.test_connection()
        self.assertFalse(result)
    
    def test_get_database_info(self):
        """Test getting database information."""
        info = self.reader.get_database_info()
        
        self.assertEqual(info['path'], self.test_db_path)
        self.assertTrue(info['exists'])
        self.assertIn('timing_violations', info['tables'])
        self.assertIn('confirmation_records', info['tables'])
        self.assertEqual(info['violation_count'], 2)
    
    def test_resolve_db_path_default(self):
        """Test database path resolution with defaults."""
        reader = DatabaseReader()
        # Should not raise an exception
        self.assertIsNotNone(reader.db_path)
    
    def test_single_table_fallback(self):
        """Test fallback to single table structure."""
        # Create a database with single table structure
        single_table_db = os.path.join(self.temp_dir, "single_table.db")
        conn = sqlite3.connect(single_table_db)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE timing_violations (
                id INTEGER PRIMARY KEY,
                num INTEGER,
                hier TEXT,
                time_ns REAL,
                check_info TEXT,
                corner TEXT,
                case_name TEXT,
                status TEXT,
                confirmer TEXT,
                result TEXT,
                reason TEXT,
                confirmed_at TEXT
            )
        ''')
        
        cursor.execute('''
            INSERT INTO timing_violations 
            (num, hier, time_ns, check_info, corner, case_name, status, confirmer, result, reason, confirmed_at)
            VALUES (1, 'test/path', 1.0, 'test check', 'ss', 'test1', 'confirmed', 'tester', 'ok', 'test reason', '2024-01-01 12:00:00')
        ''')
        
        conn.commit()
        conn.close()
        
        reader = DatabaseReader(single_table_db)
        with reader:
            violations = reader.get_confirmed_violations()
        
        self.assertEqual(len(violations), 1)
        self.assertEqual(violations[0]['status'], 'confirmed')


if __name__ == '__main__':
    unittest.main()