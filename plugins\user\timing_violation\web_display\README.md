# Timing Violation Web Display

This module provides a web-based interface for reviewing confirmed timing violation records. It allows project leaders to easily view and filter timing violations without manually opening multiple Excel files.

## Features

- **Data Source Flexibility**: Automatically loads data from Excel files or falls back to SQLite database
- **Web Interface**: Clean, responsive web interface for viewing violations
- **Filtering**: Filter violations by corner and case
- **Statistics**: Summary statistics and confirmation tracking
- **Performance Optimized**: Handles large datasets with pagination and efficient loading

## Directory Structure

```
web_display/
├── __init__.py                 # Package initialization
├── data_exporter.py           # Main data export coordinator
├── parsers/                   # Data parsing modules
│   ├── __init__.py
│   ├── excel_parser.py        # Excel file parser
│   └── database_reader.py     # SQLite database reader
├── utils/                     # Utility modules
│   ├── __init__.py
│   ├── file_utils.py          # File handling utilities
│   └── date_utils.py          # Date/time utilities
├── tests/                     # Unit tests
│   ├── __init__.py
│   ├── test_excel_parser.py
│   ├── test_database_reader.py
│   └── test_data_exporter.py
└── README.md                  # This file
```

## Usage

### Basic Usage

```python
from web_display.data_exporter import DataExporter

# Initialize exporter
exporter = DataExporter("VIOLATION_CHECK")

# Export all data to JSON files
success = exporter.export_all_data()

if success:
    print("Web data generated successfully")
else:
    print("Export failed")
```

### Command Line Usage

```bash
# Generate web data with default settings
python generate_web_data.py

# Specify custom VIOLATION_CHECK directory
python generate_web_data.py --violation-check-dir /path/to/VIOLATION_CHECK

# Enable verbose logging and validation
python generate_web_data.py --verbose --validate

# Show export information
python generate_web_data.py --info
```

## Data Sources

The system supports two data sources:

### 1. Excel Files (Primary)
- Automatically scans VIOLATION_CHECK directory for Excel files
- Supports both .xlsx and .xls formats
- Extracts corner and case information from file paths/names
- Expected columns: 序号, 层级路径, 时间(ns), 检查信息, 状态, 确认人, 确认结果, 确认理由, 确认时间

### 2. SQLite Database (Fallback)
- Uses timing_violations.db in VIOLATION_CHECK directory
- Supports multiple table structures
- Automatically adapts to available schema

## Output Structure

Generated files in `VIOLATION_CHECK/web_display/data/`:

```
data/
├── index.json                 # Metadata and summary
├── corners/                   # Corner-specific data
│   ├── corner1_cases.json
│   └── corner2_cases.json
├── violations/                # Violation data (paginated)
│   ├── corner1_case1.json
│   ├── corner1_case2_page1.json
│   └── corner1_case2_page2.json
└── statistics.json           # Detailed statistics
```

## Error Handling

The system includes comprehensive error handling for:

- **File Access Errors**: Permission denied, file not found, corrupted files
- **Data Format Errors**: Invalid Excel format, missing columns, malformed data
- **Database Errors**: Connection failures, schema mismatches, query errors
- **Export Errors**: Directory creation failures, JSON serialization errors

## Performance Considerations

- **Large Dataset Support**: Automatically splits large datasets into paginated files
- **Memory Efficient**: Processes data in chunks to avoid memory issues
- **Caching**: Intelligent caching of parsed data
- **Compression**: Optional gzip compression for JSON files

## Testing

Run unit tests:

```bash
# Run all tests
python -m unittest discover tests/

# Run specific test
python -m unittest tests.test_excel_parser

# Run with verbose output
python -m unittest discover tests/ -v
```

## Dependencies

- **openpyxl**: For Excel file parsing
- **sqlite3**: For database access (built-in)
- **pathlib**: For file path operations (built-in)
- **json**: For JSON serialization (built-in)

Install dependencies:

```bash
pip install -r requirements_web.txt
```

## Configuration

The system uses sensible defaults but can be configured:

- **VIOLATION_CHECK Directory**: Default is "VIOLATION_CHECK"
- **Database Path**: Default is "VIOLATION_CHECK/timing_violations.db"
- **Output Directory**: Default is "VIOLATION_CHECK/web_display"
- **Pagination Size**: Default is 1000 records per file

## Troubleshooting

### Common Issues

1. **No Excel files found**
   - Check VIOLATION_CHECK directory exists
   - Verify Excel files have .xlsx or .xls extensions
   - Check file permissions

2. **Database connection failed**
   - Verify timing_violations.db exists
   - Check database file permissions
   - Ensure database is not locked by another process

3. **Export failed**
   - Check write permissions for output directory
   - Verify sufficient disk space
   - Check log files for detailed error messages

### Logging

Enable verbose logging for troubleshooting:

```bash
python generate_web_data.py --verbose
```

Log files are written to `web_data_generation.log`.

## Future Enhancements

- Web server integration for real-time updates
- Advanced filtering and search capabilities
- Export to other formats (CSV, PDF)
- Integration with version control systems
- Automated report generation