<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timing Violation Display</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/scroller/2.2.0/css/scroller.bootstrap5.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/custom.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="header-section bg-primary text-white p-3 mb-4">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Timing Violation Display
                    </h1>
                    <p class="mb-0 mt-1">Review confirmed timing violation records across all corners and cases</p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4" id="statistics-section">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-icon text-primary mb-2">
                            <i class="fas fa-list-alt fa-2x"></i>
                        </div>
                        <h5 class="card-title">Total Violations</h5>
                        <h3 class="text-primary mb-0" id="total-violations">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-icon text-success mb-2">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                        <h5 class="card-title">Confirmed</h5>
                        <h3 class="text-success mb-0" id="confirmed-violations">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-icon text-warning mb-2">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                        <h5 class="card-title">Pending</h5>
                        <h3 class="text-warning mb-0" id="pending-violations">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-icon text-info mb-2">
                            <i class="fas fa-percentage fa-2x"></i>
                        </div>
                        <h5 class="card-title">Confirmation Rate</h5>
                        <h3 class="text-info mb-0" id="confirmation-rate">-</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Controls -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-filter me-2"></i>
                            Filter Controls
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="corner-filter" class="form-label">Corner Name</label>
                                <select class="form-select" id="corner-filter">
                                    <option value="all">All Corners</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="case-filter" class="form-label">Case Name</label>
                                <select class="form-select" id="case-filter">
                                    <option value="all">All Cases</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="status-filter" class="form-label">Status</label>
                                <select class="form-select" id="status-filter">
                                    <option value="all">All Status</option>
                                    <option value="confirmed">Confirmed</option>
                                    <option value="pending">Pending</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="button" class="btn btn-secondary me-2" id="clear-filters">
                                    <i class="fas fa-times me-1"></i>
                                    Clear Filters
                                </button>
                                <button type="button" class="btn btn-primary" id="refresh-data">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    Refresh Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table me-2"></i>
                            Violation Records
                        </h5>
                        <div class="table-info">
                            <span class="badge bg-secondary" id="filtered-count">0 records</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="violations-table" class="table table-striped table-hover" style="width:100%">
                                <thead class="table-dark">
                                    <tr>
                                        <th>NUM</th>
                                        <th>Hier</th>
                                        <th>Time (ns)</th>
                                        <th>Check Info</th>
                                        <th>Status</th>
                                        <th>Confirmer</th>
                                        <th>Result</th>
                                        <th>Reason</th>
                                        <th>Confirmed At</th>
                                        <th>Corner</th>
                                        <th>Case</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loading-overlay" class="loading-overlay">
            <div class="loading-content">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Loading violation data...</p>
            </div>
        </div>

        <!-- Error Modal -->
        <div class="modal fade" id="error-modal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Error
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p id="error-message">An error occurred while loading data.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" id="retry-button">Retry</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="text-muted mb-0">
                Timing Violation Display System | 
                Last Updated: <span id="last-updated">-</span>
            </p>
        </div>
    </footer>

    <!-- Scripts -->
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/scroller/2.2.0/js/dataTables.scroller.min.js"></script>
    <!-- Main Application JS -->
    <script src="js/app.js"></script>
</body>
</html>