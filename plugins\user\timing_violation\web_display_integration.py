"""
Web展示集成模块

提供GUI与Web展示功能的集成接口，简化用户操作。
"""

import os
import sys
import subprocess
import webbrowser
import time
import threading
from pathlib import Path
from typing import Optional, Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal


class WebDisplayIntegration(QObject):
    """Web展示集成管理器"""
    
    # 信号定义
    server_status_changed = pyqtSignal(bool, str)  # (is_running, message)
    data_update_status = pyqtSignal(str)  # status_message
    
    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
        self.web_server_process = None
        self.server_port = 8000
        self.server_url = f"http://localhost:{self.server_port}"
        
    def start_web_display(self, auto_open_browser=True) -> bool:
        """
        启动Web展示功能
        
        Args:
            auto_open_browser: 是否自动打开浏览器
            
        Returns:
            bool: 启动是否成功
        """
        try:
            # 1. 生成网页数据
            self.data_update_status.emit("正在生成网页数据...")
            if not self._generate_web_data():
                return False
            
            # 2. 启动Web服务器
            self.data_update_status.emit("正在启动Web服务器...")
            if not self._start_web_server():
                return False
            
            # 3. 打开浏览器
            if auto_open_browser:
                self.data_update_status.emit("正在打开浏览器...")
                self._open_browser()
            
            self.server_status_changed.emit(True, f"服务运行在 {self.server_url}")
            return True
            
        except Exception as e:
            self.server_status_changed.emit(False, f"启动失败: {str(e)}")
            return False
    
    def stop_web_display(self):
        """停止Web展示服务"""
        try:
            if self.web_server_process:
                self.web_server_process.terminate()
                self.web_server_process.wait()
                self.web_server_process = None
            
            self.server_status_changed.emit(False, "服务已停止")
            
        except Exception as e:
            self.server_status_changed.emit(False, f"停止失败: {str(e)}")
    
    def refresh_web_data(self):
        """刷新网页数据"""
        try:
            self.data_update_status.emit("正在刷新数据...")
            
            # 重新生成数据
            if self._generate_web_data():
                self.data_update_status.emit("数据刷新完成")
                
                # 如果服务器在运行，重新打开浏览器
                if self.is_server_running():
                    self._open_browser()
            else:
                self.data_update_status.emit("数据刷新失败")
                
        except Exception as e:
            self.data_update_status.emit(f"刷新失败: {str(e)}")
    
    def get_server_status(self) -> Dict[str, Any]:
        """获取服务器状态"""
        return {
            'running': self.is_server_running(),
            'url': self.server_url,
            'port': self.server_port
        }
    
    def is_server_running(self) -> bool:
        """检查服务器是否在运行"""
        return self.web_server_process is not None and self.web_server_process.poll() is None
    
    def _generate_web_data(self) -> bool:
        """生成网页数据"""
        try:
            # 获取当前违例数据
            violations_data = None
            if self.main_window and hasattr(self.main_window, 'get_all_violations'):
                violations_data = self.main_window.get_all_violations()
            
            # 使用数据导出器生成数据
            from .web_display.data_exporter import DataExporter
            
            exporter = DataExporter(
                violation_check_dir="VIOLATION_CHECK",
                enable_performance_monitoring=True
            )
            
            success = exporter.export_all_data(violations_data)
            return success
            
        except Exception as e:
            print(f"生成网页数据失败: {e}")
            return False
    
    def _start_web_server(self) -> bool:
        """启动Web服务器"""
        try:
            # 检查端口是否被占用
            if self._is_port_in_use(self.server_port):
                self.server_port = self._find_available_port()
                self.server_url = f"http://localhost:{self.server_port}"
            
            # 启动服务器
            server_script = Path("plugins/user/timing_violation/start_web_server.py")
            if server_script.exists():
                self.web_server_process = subprocess.Popen([
                    sys.executable, str(server_script),
                    "--port", str(self.server_port),
                    "--no-browser"
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                
                # 等待服务器启动
                time.sleep(2)
                
                if self.web_server_process.poll() is None:
                    return True
                else:
                    return False
            else:
                print(f"Web服务器脚本不存在: {server_script}")
                return False
                
        except Exception as e:
            print(f"启动Web服务器失败: {e}")
            return False
    
    def _open_browser(self):
        """打开浏览器"""
        try:
            # 优先打开独立测试页面
            test_url = f"{self.server_url}/standalone_test.html"
            webbrowser.open(test_url)
        except Exception as e:
            print(f"打开浏览器失败: {e}")
    
    def _is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                return s.connect_ex(('localhost', port)) == 0
        except:
            return False
    
    def _find_available_port(self, start_port: int = 8000) -> int:
        """查找可用端口"""
        for port in range(start_port, start_port + 100):
            if not self._is_port_in_use(port):
                return port
        return start_port  # 如果都被占用，返回原始端口


# 全局实例管理
_web_integration_instance = None


def get_web_display_integration(main_window=None) -> WebDisplayIntegration:
    """获取Web展示集成实例（单例模式）"""
    global _web_integration_instance
    
    if _web_integration_instance is None:
        _web_integration_instance = WebDisplayIntegration(main_window)
    
    return _web_integration_instance


def cleanup_web_display_integration():
    """清理Web展示集成实例"""
    global _web_integration_instance
    
    if _web_integration_instance:
        _web_integration_instance.stop_web_display()
        _web_integration_instance = None