# Requirements for timing violation web display feature

# Excel file parsing
openpyxl>=3.0.0

# Additional utilities (if needed)
# These are typically included with Python standard library
# but listed here for completeness

# For JSON handling (built-in)
# json

# For SQLite database access (built-in)
# sqlite3

# For file operations (built-in)
# pathlib
# shutil

# For date/time operations (built-in)
# datetime

# For logging (built-in)
# logging

# For regular expressions (built-in)
# re

# For testing (built-in)
# unittest

# Optional: For more advanced Excel features
# xlrd>=2.0.0  # For reading older .xls files
# xlsxwriter>=3.0.0  # For writing Excel files (if needed in future)

# Optional: For data compression
# gzip (built-in)

# Optional: For advanced date parsing
# python-dateutil>=2.8.0