#!/usr/bin/env python3
"""
Check database structure
"""

import sqlite3

def check_database_structure():
    """Check the structure of timing_violations table"""
    db_path = "VIOLATION_CHECK/timing_violations.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get table structure
        cursor.execute("PRAGMA table_info(timing_violations)")
        columns = cursor.fetchall()
        
        print("Columns in timing_violations table:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # Get sample data
        cursor.execute("SELECT * FROM timing_violations LIMIT 1")
        sample = cursor.fetchone()
        
        if sample:
            print("\nSample record:")
            for i, col in enumerate(columns):
                print(f"  {col[1]}: {sample[i]}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_database_structure()