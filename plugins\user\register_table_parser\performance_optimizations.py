#!/usr/bin/env python3
"""
性能优化模块

为大型寄存器表提供性能优化功能
"""

import time
import threading
from typing import List, Dict, Any, Optional, Callable
from functools import lru_cache, wraps
from PyQt5.QtCore import QObject, QThread, pyqtSignal, QTimer
from PyQt5.QtWidgets import QApplication

from models import RegisterInfo, RegisterTableData


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.enabled = True
    
    def measure_time(self, operation_name: str):
        """装饰器：测量函数执行时间"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                if not self.enabled:
                    return func(*args, **kwargs)
                
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.time()
                    duration = end_time - start_time
                    
                    if operation_name not in self.metrics:
                        self.metrics[operation_name] = []
                    
                    self.metrics[operation_name].append(duration)
            return wrapper
        return decorator
    
    def get_average_time(self, operation_name: str) -> float:
        """获取操作的平均执行时间"""
        if operation_name not in self.metrics:
            return 0.0
        
        times = self.metrics[operation_name]
        return sum(times) / len(times) if times else 0.0
    
    def get_total_time(self, operation_name: str) -> float:
        """获取操作的总执行时间"""
        if operation_name not in self.metrics:
            return 0.0
        
        return sum(self.metrics[operation_name])
    
    def reset_metrics(self):
        """重置性能指标"""
        self.metrics.clear()
    
    def print_report(self):
        """打印性能报告"""
        print("性能监控报告:")
        print("-" * 50)
        
        for operation, times in self.metrics.items():
            avg_time = sum(times) / len(times)
            total_time = sum(times)
            call_count = len(times)
            
            print(f"{operation}:")
            print(f"  调用次数: {call_count}")
            print(f"  平均时间: {avg_time:.4f}秒")
            print(f"  总时间: {total_time:.4f}秒")
            print()


class DataCache:
    """数据缓存管理器"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = {}
        self.access_order = []
    
    def get(self, key: str) -> Any:
        """获取缓存数据"""
        if key in self.cache:
            # 更新访问顺序
            self.access_order.remove(key)
            self.access_order.append(key)
            return self.cache[key]
        return None
    
    def set(self, key: str, value: Any):
        """设置缓存数据"""
        # 如果缓存已满，移除最久未访问的项
        if len(self.cache) >= self.max_size and key not in self.cache:
            oldest_key = self.access_order.pop(0)
            del self.cache[oldest_key]
        
        self.cache[key] = value
        
        if key in self.access_order:
            self.access_order.remove(key)
        self.access_order.append(key)
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_order.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)


class VirtualizedListManager:
    """虚拟化列表管理器
    
    用于处理大量寄存器数据的虚拟化显示
    """
    
    def __init__(self, item_height: int = 25, visible_count: int = 50):
        self.item_height = item_height
        self.visible_count = visible_count
        self.total_items = 0
        self.scroll_position = 0
        self.data_source = []
        self.cache = DataCache(max_size=200)
    
    def set_data_source(self, data: List[Any]):
        """设置数据源"""
        self.data_source = data
        self.total_items = len(data)
        self.cache.clear()
    
    def get_visible_range(self) -> tuple:
        """获取可见范围"""
        start_index = max(0, self.scroll_position // self.item_height)
        end_index = min(self.total_items, start_index + self.visible_count)
        return start_index, end_index
    
    def get_visible_items(self) -> List[Any]:
        """获取可见项目"""
        start_index, end_index = self.get_visible_range()
        
        # 使用缓存
        cache_key = f"range_{start_index}_{end_index}"
        cached_items = self.cache.get(cache_key)
        
        if cached_items is not None:
            return cached_items
        
        # 获取数据并缓存
        items = self.data_source[start_index:end_index]
        self.cache.set(cache_key, items)
        
        return items
    
    def update_scroll_position(self, position: int):
        """更新滚动位置"""
        self.scroll_position = position
    
    def get_total_height(self) -> int:
        """获取总高度"""
        return self.total_items * self.item_height


class LazyLoadingManager:
    """延迟加载管理器"""
    
    def __init__(self):
        self.loading_tasks = {}
        self.loaded_data = {}
        self.loading_callbacks = {}
    
    def load_data_async(self, key: str, loader_func: Callable, callback: Callable):
        """异步加载数据"""
        if key in self.loaded_data:
            # 数据已加载，直接回调
            callback(self.loaded_data[key])
            return
        
        if key in self.loading_tasks:
            # 正在加载，添加回调
            if key not in self.loading_callbacks:
                self.loading_callbacks[key] = []
            self.loading_callbacks[key].append(callback)
            return
        
        # 开始加载
        def load_worker():
            try:
                data = loader_func()
                self.loaded_data[key] = data
                
                # 执行主回调
                callback(data)
                
                # 执行其他等待的回调
                if key in self.loading_callbacks:
                    for cb in self.loading_callbacks[key]:
                        cb(data)
                    del self.loading_callbacks[key]
                
            except Exception as e:
                print(f"延迟加载失败 {key}: {e}")
            finally:
                if key in self.loading_tasks:
                    del self.loading_tasks[key]
        
        # 创建并启动线程
        thread = threading.Thread(target=load_worker)
        thread.daemon = True
        self.loading_tasks[key] = thread
        thread.start()
    
    def clear_cache(self):
        """清空缓存"""
        self.loaded_data.clear()


class BatchProcessor:
    """批处理器
    
    用于批量处理大量数据操作
    """
    
    def __init__(self, batch_size: int = 100, delay_ms: int = 10):
        self.batch_size = batch_size
        self.delay_ms = delay_ms
        self.pending_operations = []
        self.timer = None
    
    def add_operation(self, operation: Callable, *args, **kwargs):
        """添加操作到批处理队列"""
        self.pending_operations.append((operation, args, kwargs))
        
        # 如果达到批处理大小，立即处理
        if len(self.pending_operations) >= self.batch_size:
            self.process_batch()
        else:
            # 否则延迟处理
            self._schedule_processing()
    
    def _schedule_processing(self):
        """调度批处理"""
        if self.timer is not None:
            self.timer.stop()
        
        self.timer = QTimer()
        self.timer.timeout.connect(self.process_batch)
        self.timer.setSingleShot(True)
        self.timer.start(self.delay_ms)
    
    def process_batch(self):
        """处理批次"""
        if not self.pending_operations:
            return
        
        operations = self.pending_operations.copy()
        self.pending_operations.clear()
        
        if self.timer is not None:
            self.timer.stop()
            self.timer = None
        
        # 批量执行操作
        for operation, args, kwargs in operations:
            try:
                operation(*args, **kwargs)
            except Exception as e:
                print(f"批处理操作失败: {e}")
        
        # 处理事件循环以保持UI响应
        QApplication.processEvents()


class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.weak_references = {}
        self.cleanup_threshold = 100
        self.cleanup_counter = 0
    
    def optimize_register_data(self, registers: List[RegisterInfo]) -> List[RegisterInfo]:
        """优化寄存器数据内存使用"""
        optimized_registers = []
        
        for register in registers:
            # 优化字段数据
            optimized_fields = []
            for field in register.fields:
                # 只保留必要的字段信息
                if not field.is_reserved:
                    optimized_fields.append(field)
            
            # 创建优化的寄存器对象
            optimized_register = RegisterInfo(
                offset=register.offset,
                name=register.name,
                description=register.description if len(register.description) < 100 else register.description[:100] + "...",
                width=register.width,
                fields=optimized_fields
            )
            
            optimized_registers.append(optimized_register)
        
        return optimized_registers
    
    def cleanup_unused_data(self):
        """清理未使用的数据"""
        import gc
        
        # 强制垃圾回收
        collected = gc.collect()
        
        print(f"内存清理完成，回收了 {collected} 个对象")
    
    def monitor_memory_usage(self):
        """监控内存使用"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            
            print(f"当前内存使用: {memory_info.rss / 1024 / 1024:.2f} MB")
            
            return memory_info.rss
        except ImportError:
            print("psutil未安装，无法监控内存使用")
            return 0


class SearchOptimizer:
    """搜索优化器"""
    
    def __init__(self):
        self.search_index = {}
        self.last_search_term = ""
        self.last_search_results = []
    
    def build_search_index(self, registers: List[RegisterInfo]):
        """构建搜索索引"""
        self.search_index.clear()
        
        for i, register in enumerate(registers):
            # 索引寄存器名称
            name_lower = register.name.lower()
            if name_lower not in self.search_index:
                self.search_index[name_lower] = []
            self.search_index[name_lower].append(i)
            
            # 索引偏移地址
            offset_lower = register.offset.lower()
            if offset_lower not in self.search_index:
                self.search_index[offset_lower] = []
            self.search_index[offset_lower].append(i)
            
            # 索引十进制偏移
            try:
                offset_int = register.offset_int
                offset_dec = str(offset_int)
                if offset_dec not in self.search_index:
                    self.search_index[offset_dec] = []
                self.search_index[offset_dec].append(i)
            except:
                pass
    
    @lru_cache(maxsize=100)
    def search_registers(self, search_term: str, registers: tuple) -> List[int]:
        """搜索寄存器（带缓存）"""
        if not search_term:
            return list(range(len(registers)))
        
        search_term_lower = search_term.lower()
        results = set()
        
        # 精确匹配
        if search_term_lower in self.search_index:
            results.update(self.search_index[search_term_lower])
        
        # 模糊匹配
        for key, indices in self.search_index.items():
            if search_term_lower in key:
                results.update(indices)
        
        return sorted(list(results))
    
    def clear_search_cache(self):
        """清空搜索缓存"""
        self.search_registers.cache_clear()


class PerformanceOptimizedRegisterTable:
    """性能优化的寄存器表"""
    
    def __init__(self, table_data: RegisterTableData):
        self.original_data = table_data
        self.performance_monitor = PerformanceMonitor()
        self.memory_optimizer = MemoryOptimizer()
        self.search_optimizer = SearchOptimizer()
        self.virtualized_manager = VirtualizedListManager()
        self.lazy_loader = LazyLoadingManager()
        self.batch_processor = BatchProcessor()
        
        # 优化数据
        self._optimize_data()
    
    @PerformanceMonitor().measure_time("data_optimization")
    def _optimize_data(self):
        """优化数据"""
        # 内存优化
        optimized_registers = self.memory_optimizer.optimize_register_data(
            self.original_data.registers
        )
        
        # 构建搜索索引
        self.search_optimizer.build_search_index(optimized_registers)
        
        # 设置虚拟化数据源
        self.virtualized_manager.set_data_source(optimized_registers)
        
        print(f"数据优化完成: {len(optimized_registers)} 个寄存器")
    
    def get_visible_registers(self, scroll_position: int) -> List[RegisterInfo]:
        """获取可见寄存器"""
        self.virtualized_manager.update_scroll_position(scroll_position)
        return self.virtualized_manager.get_visible_items()
    
    def search_registers_optimized(self, search_term: str) -> List[RegisterInfo]:
        """优化的寄存器搜索"""
        # 将寄存器列表转换为元组以支持缓存
        registers_tuple = tuple(self.original_data.registers)
        
        indices = self.search_optimizer.search_registers(search_term, registers_tuple)
        return [self.original_data.registers[i] for i in indices]
    
    def batch_update_fields(self, updates: List[tuple]):
        """批量更新字段"""
        def process_update(register_name, field_name, value):
            # 这里实现具体的字段更新逻辑
            pass
        
        for register_name, field_name, value in updates:
            self.batch_processor.add_operation(process_update, register_name, field_name, value)
    
    def cleanup_resources(self):
        """清理资源"""
        self.search_optimizer.clear_search_cache()
        self.lazy_loader.clear_cache()
        self.memory_optimizer.cleanup_unused_data()
        self.performance_monitor.reset_metrics()
    
    def get_performance_report(self) -> str:
        """获取性能报告"""
        import io
        
        output = io.StringIO()
        
        # 重定向print输出
        import sys
        old_stdout = sys.stdout
        sys.stdout = output
        
        try:
            self.performance_monitor.print_report()
            self.memory_optimizer.monitor_memory_usage()
        finally:
            sys.stdout = old_stdout
        
        return output.getvalue()


# 全局性能监控实例
global_performance_monitor = PerformanceMonitor()


def optimize_for_large_datasets(func):
    """装饰器：为大数据集优化函数"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 在处理大数据集时禁用某些功能
        if hasattr(args[0], 'table_data') and args[0].table_data:
            register_count = len(args[0].table_data.registers)
            
            if register_count > 500:  # 大数据集阈值
                # 可以在这里添加特殊的优化逻辑
                print(f"检测到大数据集 ({register_count} 个寄存器)，启用性能优化")
        
        return func(*args, **kwargs)
    
    return wrapper


if __name__ == "__main__":
    # 性能测试示例
    print("性能优化模块测试")
    print("=" * 40)
    
    # 创建测试数据
    from models import HeaderInfo, FieldInfo, RegisterInfo, RegisterTableData
    
    header = HeaderInfo("测试项目", "测试子系统", "测试模块", "0x1000")
    
    # 创建大量寄存器用于性能测试
    registers = []
    for i in range(1000):
        fields = [
            FieldInfo(f"FIELD_{i}_{j}", f"{j*8+7}:{j*8}", "RW", "0x00")
            for j in range(4)
        ]
        register = RegisterInfo(f"0x{i*4:04X}", f"REG_{i}", f"寄存器{i}", 32, fields)
        registers.append(register)
    
    table_data = RegisterTableData(header, registers)
    
    # 测试性能优化
    optimized_table = PerformanceOptimizedRegisterTable(table_data)
    
    # 测试搜索性能
    start_time = time.time()
    results = optimized_table.search_registers_optimized("REG_100")
    search_time = time.time() - start_time
    
    print(f"搜索耗时: {search_time:.4f}秒")
    print(f"搜索结果: {len(results)} 个寄存器")
    
    # 输出性能报告
    print("\n性能报告:")
    print(optimized_table.get_performance_report())