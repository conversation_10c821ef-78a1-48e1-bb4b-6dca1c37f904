"""
Component Interaction Optimizer

Minimizes overhead between performance monitoring and violation processing components.
Optimizes data flow between parser, UI renderer, and memory manager.
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable, Tuple
from collections import deque
from dataclasses import dataclass
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThread, QMutex, QMutexLocker


@dataclass
class ComponentInteractionMetrics:
    """Metrics for component interactions"""
    component_a: str
    component_b: str
    interaction_type: str
    data_size_bytes: int
    processing_time_ms: float
    overhead_ms: float
    timestamp: float
    success: bool = True
    error_message: str = ""


class DataFlowOptimizer(QObject):
    """
    Optimizes data flow between components to minimize overhead and improve performance.
    """
    
    # Signals for monitoring
    bottleneck_detected = pyqtSignal(str, dict)  # Component name, bottleneck info
    optimization_applied = pyqtSignal(str, str)  # Optimization type, description
    
    def __init__(self):
        super().__init__()
        
        # Data flow monitoring
        self.interaction_metrics = deque(maxlen=1000)  # Keep last 1000 interactions
        self.component_performance = {}
        self.data_flow_cache = {}
        self.optimization_history = []
        
        # Optimization settings
        self.enable_data_caching = True
        self.enable_batch_processing = True
        self.enable_async_processing = True
        self.cache_size_limit_mb = 100
        
        # Performance thresholds
        self.bottleneck_threshold_ms = 100  # 100ms threshold for bottleneck detection
        self.overhead_threshold_percent = 20  # 20% overhead threshold
        
        # Thread safety
        self.metrics_mutex = QMutex()
        
        # Monitoring timer
        self.monitoring_timer = QTimer()
        self.monitoring_timer.timeout.connect(self._analyze_performance_patterns)
        self.monitoring_timer.start(5000)  # Analyze every 5 seconds
    
    def track_component_interaction(self, component_a: str, component_b: str,
                                  interaction_type: str, data_size_bytes: int,
                                  processing_time_ms: float, overhead_ms: float = 0,
                                  success: bool = True, error_message: str = ""):
        """
        Track interaction between components for optimization analysis.
        
        Args:
            component_a: Source component name
            component_b: Target component name
            interaction_type: Type of interaction (e.g., 'data_transfer', 'method_call')
            data_size_bytes: Size of data transferred
            processing_time_ms: Time taken for processing
            overhead_ms: Additional overhead time
            success: Whether interaction was successful
            error_message: Error message if failed
        """
        with QMutexLocker(self.metrics_mutex):
            metric = ComponentInteractionMetrics(
                component_a=component_a,
                component_b=component_b,
                interaction_type=interaction_type,
                data_size_bytes=data_size_bytes,
                processing_time_ms=processing_time_ms,
                overhead_ms=overhead_ms,
                timestamp=time.time(),
                success=success,
                error_message=error_message
            )
            
            self.interaction_metrics.append(metric)
            
            # Update component performance tracking
            self._update_component_performance(component_a, component_b, metric)
    
    def optimize_data_transfer(self, source_component: str, target_component: str,
                             data: Any, transfer_type: str = 'direct') -> Any:
        """
        Optimize data transfer between components.
        
        Args:
            source_component: Source component name
            target_component: Target component name
            data: Data to transfer
            transfer_type: Type of transfer optimization
            
        Returns:
            Any: Optimized data or transfer result
        """
        start_time = time.time()
        
        try:
            # Calculate data size
            data_size = self._calculate_data_size(data)
            
            # Apply optimization based on data size and components
            if transfer_type == 'cached' and self.enable_data_caching:
                optimized_data = self._apply_cached_transfer(
                    source_component, target_component, data, data_size
                )
            elif transfer_type == 'batched' and self.enable_batch_processing:
                optimized_data = self._apply_batched_transfer(
                    source_component, target_component, data, data_size
                )
            elif transfer_type == 'async' and self.enable_async_processing:
                optimized_data = self._apply_async_transfer(
                    source_component, target_component, data, data_size
                )
            else:
                # Direct transfer
                optimized_data = data
            
            # Track the interaction
            processing_time = (time.time() - start_time) * 1000
            self.track_component_interaction(
                source_component, target_component, f'data_transfer_{transfer_type}',
                data_size, processing_time, success=True
            )
            
            return optimized_data
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            self.track_component_interaction(
                source_component, target_component, f'data_transfer_{transfer_type}',
                0, processing_time, success=False, error_message=str(e)
            )
            # Return original data on error
            return data
    
    def create_optimized_data_pipeline(self, components: List[str], 
                                     pipeline_config: Dict) -> Callable:
        """
        Create an optimized data processing pipeline between multiple components.
        
        Args:
            components: List of component names in processing order
            pipeline_config: Configuration for pipeline optimization
            
        Returns:
            Callable: Optimized pipeline function
        """
        def optimized_pipeline(input_data: Any) -> Any:
            current_data = input_data
            pipeline_start_time = time.time()
            
            for i in range(len(components) - 1):
                source_comp = components[i]
                target_comp = components[i + 1]
                
                # Determine optimal transfer type for this stage
                transfer_type = self._determine_optimal_transfer_type(
                    source_comp, target_comp, current_data, pipeline_config
                )
                
                # Apply optimized transfer
                current_data = self.optimize_data_transfer(
                    source_comp, target_comp, current_data, transfer_type
                )
            
            # Track overall pipeline performance
            total_time = (time.time() - pipeline_start_time) * 1000
            self.track_component_interaction(
                components[0], components[-1], 'pipeline_execution',
                self._calculate_data_size(input_data), total_time
            )
            
            return current_data
        
        return optimized_pipeline
    
    def minimize_monitoring_overhead(self, monitoring_component: str,
                                   target_components: List[str]) -> Dict[str, Any]:
        """
        Minimize overhead from performance monitoring on target components.
        
        Args:
            monitoring_component: Name of monitoring component
            target_components: List of components being monitored
            
        Returns:
            Dict: Optimization settings applied
        """
        optimizations = {}
        
        for target_comp in target_components:
            # Analyze current monitoring overhead
            overhead_analysis = self._analyze_monitoring_overhead(
                monitoring_component, target_comp
            )
            
            if overhead_analysis['overhead_percent'] > self.overhead_threshold_percent:
                # Apply overhead reduction strategies
                if overhead_analysis['high_frequency_monitoring']:
                    # Reduce monitoring frequency
                    optimizations[f'{target_comp}_monitoring_interval'] = 'reduced'
                    
                if overhead_analysis['detailed_metrics']:
                    # Simplify metrics collection
                    optimizations[f'{target_comp}_metrics_detail'] = 'simplified'
                    
                if overhead_analysis['synchronous_monitoring']:
                    # Switch to asynchronous monitoring
                    optimizations[f'{target_comp}_monitoring_mode'] = 'async'
                
                self.optimization_applied.emit(
                    'monitoring_overhead_reduction',
                    f'Reduced monitoring overhead for {target_comp} by {overhead_analysis["overhead_percent"]:.1f}%'
                )
        
        return optimizations
    
    def optimize_memory_manager_interactions(self, memory_manager_name: str,
                                           interacting_components: List[str]) -> Dict:
        """
        Optimize interactions with memory manager to reduce overhead.
        
        Args:
            memory_manager_name: Name of memory manager component
            interacting_components: Components that interact with memory manager
            
        Returns:
            Dict: Applied optimizations
        """
        optimizations = {}
        
        for component in interacting_components:
            # Analyze memory-related interactions
            memory_interactions = self._get_component_interactions(
                component, memory_manager_name, 'memory_operation'
            )
            
            if len(memory_interactions) > 10:  # High frequency memory operations
                # Batch memory operations
                optimizations[f'{component}_memory_batching'] = True
                
                # Cache memory status to reduce queries
                optimizations[f'{component}_memory_status_caching'] = True
                
                # Use lazy memory cleanup
                optimizations[f'{component}_lazy_memory_cleanup'] = True
                
                self.optimization_applied.emit(
                    'memory_interaction_optimization',
                    f'Optimized memory interactions for {component}'
                )
        
        return optimizations
    
    def get_performance_bottlenecks(self) -> List[Dict]:
        """
        Identify performance bottlenecks in component interactions.
        
        Returns:
            List[Dict]: List of identified bottlenecks
        """
        bottlenecks = []
        
        with QMutexLocker(self.metrics_mutex):
            # Analyze recent interactions for bottlenecks
            recent_metrics = list(self.interaction_metrics)[-100:]  # Last 100 interactions
            
            # Group by component pairs
            component_pairs = {}
            for metric in recent_metrics:
                pair_key = f"{metric.component_a}->{metric.component_b}"
                if pair_key not in component_pairs:
                    component_pairs[pair_key] = []
                component_pairs[pair_key].append(metric)
            
            # Identify bottlenecks
            for pair_key, metrics in component_pairs.items():
                avg_processing_time = sum(m.processing_time_ms for m in metrics) / len(metrics)
                avg_overhead = sum(m.overhead_ms for m in metrics) / len(metrics)
                error_rate = sum(1 for m in metrics if not m.success) / len(metrics)
                
                if (avg_processing_time > self.bottleneck_threshold_ms or
                    avg_overhead > self.bottleneck_threshold_ms or
                    error_rate > 0.1):  # 10% error rate threshold
                    
                    bottleneck = {
                        'component_pair': pair_key,
                        'avg_processing_time_ms': avg_processing_time,
                        'avg_overhead_ms': avg_overhead,
                        'error_rate': error_rate,
                        'interaction_count': len(metrics),
                        'severity': self._calculate_bottleneck_severity(
                            avg_processing_time, avg_overhead, error_rate
                        )
                    }
                    bottlenecks.append(bottleneck)
        
        # Sort by severity
        bottlenecks.sort(key=lambda x: x['severity'], reverse=True)
        
        return bottlenecks
    
    def apply_automatic_optimizations(self) -> List[str]:
        """
        Apply automatic optimizations based on detected patterns.
        
        Returns:
            List[str]: List of applied optimizations
        """
        applied_optimizations = []
        
        # Get current bottlenecks
        bottlenecks = self.get_performance_bottlenecks()
        
        for bottleneck in bottlenecks[:3]:  # Handle top 3 bottlenecks
            pair_key = bottleneck['component_pair']
            components = pair_key.split('->')
            
            if len(components) == 2:
                source_comp, target_comp = components
                
                # Apply appropriate optimization
                if bottleneck['avg_overhead_ms'] > 50:
                    # High overhead - enable caching
                    self._enable_interaction_caching(source_comp, target_comp)
                    applied_optimizations.append(f'Enabled caching for {pair_key}')
                
                if bottleneck['avg_processing_time_ms'] > 200:
                    # Slow processing - enable async processing
                    self._enable_async_interaction(source_comp, target_comp)
                    applied_optimizations.append(f'Enabled async processing for {pair_key}')
                
                if bottleneck['error_rate'] > 0.2:
                    # High error rate - add retry mechanism
                    self._enable_retry_mechanism(source_comp, target_comp)
                    applied_optimizations.append(f'Enabled retry mechanism for {pair_key}')
        
        return applied_optimizations
    
    # Private helper methods
    
    def _update_component_performance(self, component_a: str, component_b: str,
                                    metric: ComponentInteractionMetrics):
        """Update component performance tracking"""
        pair_key = f"{component_a}->{component_b}"
        
        if pair_key not in self.component_performance:
            self.component_performance[pair_key] = {
                'total_interactions': 0,
                'total_processing_time': 0,
                'total_overhead': 0,
                'success_count': 0,
                'error_count': 0
            }
        
        perf = self.component_performance[pair_key]
        perf['total_interactions'] += 1
        perf['total_processing_time'] += metric.processing_time_ms
        perf['total_overhead'] += metric.overhead_ms
        
        if metric.success:
            perf['success_count'] += 1
        else:
            perf['error_count'] += 1
    
    def _calculate_data_size(self, data: Any) -> int:
        """Calculate approximate data size in bytes"""
        try:
            if isinstance(data, (list, tuple)):
                return len(data) * 100  # Rough estimate for violation records
            elif isinstance(data, dict):
                return len(str(data).encode('utf-8'))
            elif isinstance(data, str):
                return len(data.encode('utf-8'))
            else:
                return 64  # Default size estimate
        except:
            return 64
    
    def _apply_cached_transfer(self, source: str, target: str, data: Any, data_size: int) -> Any:
        """Apply cached data transfer optimization"""
        cache_key = f"{source}->{target}:{hash(str(data))}"
        
        if cache_key in self.data_flow_cache:
            return self.data_flow_cache[cache_key]
        
        # Cache the data if within size limits
        current_cache_size = sum(self._calculate_data_size(v) for v in self.data_flow_cache.values())
        if current_cache_size + data_size < self.cache_size_limit_mb * 1024 * 1024:
            self.data_flow_cache[cache_key] = data
        
        return data
    
    def _apply_batched_transfer(self, source: str, target: str, data: Any, data_size: int) -> Any:
        """Apply batched data transfer optimization"""
        # For now, return data as-is (batching would be implemented based on specific use case)
        return data
    
    def _apply_async_transfer(self, source: str, target: str, data: Any, data_size: int) -> Any:
        """Apply asynchronous data transfer optimization"""
        # For now, return data as-is (async transfer would require more complex implementation)
        return data
    
    def _determine_optimal_transfer_type(self, source: str, target: str, data: Any,
                                       config: Dict) -> str:
        """Determine optimal transfer type based on data and configuration"""
        data_size = self._calculate_data_size(data)
        
        # Use configuration preferences or defaults
        if data_size > 1024 * 1024:  # > 1MB
            return config.get('large_data_transfer', 'async')
        elif data_size > 10 * 1024:  # > 10KB
            return config.get('medium_data_transfer', 'batched')
        else:
            return config.get('small_data_transfer', 'cached')
    
    def _analyze_monitoring_overhead(self, monitor_comp: str, target_comp: str) -> Dict:
        """Analyze monitoring overhead for a component pair"""
        interactions = self._get_component_interactions(monitor_comp, target_comp, 'monitoring')
        
        if not interactions:
            return {'overhead_percent': 0}
        
        total_overhead = sum(m.overhead_ms for m in interactions)
        total_processing = sum(m.processing_time_ms for m in interactions)
        
        overhead_percent = (total_overhead / max(total_processing, 1)) * 100
        
        return {
            'overhead_percent': overhead_percent,
            'high_frequency_monitoring': len(interactions) > 50,
            'detailed_metrics': any(m.data_size_bytes > 1024 for m in interactions),
            'synchronous_monitoring': any(m.processing_time_ms > 10 for m in interactions)
        }
    
    def _get_component_interactions(self, comp_a: str, comp_b: str, 
                                  interaction_type: str = None) -> List[ComponentInteractionMetrics]:
        """Get interactions between specific components"""
        with QMutexLocker(self.metrics_mutex):
            interactions = []
            for metric in self.interaction_metrics:
                if (metric.component_a == comp_a and metric.component_b == comp_b):
                    if interaction_type is None or interaction_type in metric.interaction_type:
                        interactions.append(metric)
            return interactions
    
    def _calculate_bottleneck_severity(self, processing_time: float, overhead: float, 
                                     error_rate: float) -> float:
        """Calculate bottleneck severity score"""
        time_score = min(processing_time / 100, 10)  # Max 10 points for time
        overhead_score = min(overhead / 50, 5)  # Max 5 points for overhead
        error_score = error_rate * 10  # Max 10 points for error rate
        
        return time_score + overhead_score + error_score
    
    def _enable_interaction_caching(self, source: str, target: str):
        """Enable caching for specific component interaction"""
        cache_key = f"{source}->{target}_caching_enabled"
        self.optimization_history.append({
            'timestamp': time.time(),
            'optimization': 'interaction_caching',
            'components': [source, target],
            'key': cache_key
        })
    
    def _enable_async_interaction(self, source: str, target: str):
        """Enable async processing for specific component interaction"""
        async_key = f"{source}->{target}_async_enabled"
        self.optimization_history.append({
            'timestamp': time.time(),
            'optimization': 'async_interaction',
            'components': [source, target],
            'key': async_key
        })
    
    def _enable_retry_mechanism(self, source: str, target: str):
        """Enable retry mechanism for specific component interaction"""
        retry_key = f"{source}->{target}_retry_enabled"
        self.optimization_history.append({
            'timestamp': time.time(),
            'optimization': 'retry_mechanism',
            'components': [source, target],
            'key': retry_key
        })
    
    def _analyze_performance_patterns(self):
        """Analyze performance patterns and detect bottlenecks"""
        bottlenecks = self.get_performance_bottlenecks()
        
        for bottleneck in bottlenecks:
            if bottleneck['severity'] > 15:  # High severity threshold
                self.bottleneck_detected.emit(
                    bottleneck['component_pair'],
                    bottleneck
                )


class PerformanceProfiler(QObject):
    """
    Profiles component interactions to identify and eliminate bottlenecks.
    """
    
    profiling_completed = pyqtSignal(dict)  # Profiling results
    
    def __init__(self):
        super().__init__()
        self.profiling_active = False
        self.profiling_data = {}
        self.profiling_start_time = 0
    
    def start_profiling(self, components: List[str], duration_seconds: int = 60):
        """
        Start profiling component interactions.
        
        Args:
            components: List of components to profile
            duration_seconds: Duration of profiling session
        """
        self.profiling_active = True
        self.profiling_start_time = time.time()
        self.profiling_data = {
            'components': components,
            'interactions': [],
            'performance_metrics': {},
            'bottlenecks': []
        }
        
        # Stop profiling after specified duration
        QTimer.singleShot(duration_seconds * 1000, self.stop_profiling)
        
        print(f"Started profiling {len(components)} components for {duration_seconds} seconds")
    
    def record_interaction(self, source: str, target: str, operation: str,
                         start_time: float, end_time: float, data_size: int = 0):
        """Record a component interaction for profiling"""
        if not self.profiling_active:
            return
        
        interaction = {
            'source': source,
            'target': target,
            'operation': operation,
            'duration_ms': (end_time - start_time) * 1000,
            'data_size_bytes': data_size,
            'timestamp': start_time
        }
        
        self.profiling_data['interactions'].append(interaction)
    
    def stop_profiling(self):
        """Stop profiling and analyze results"""
        if not self.profiling_active:
            return
        
        self.profiling_active = False
        
        # Analyze profiling data
        analysis_results = self._analyze_profiling_data()
        
        # Emit results
        self.profiling_completed.emit(analysis_results)
        
        print(f"Profiling completed. Analyzed {len(self.profiling_data['interactions'])} interactions")
    
    def _analyze_profiling_data(self) -> Dict:
        """Analyze collected profiling data"""
        interactions = self.profiling_data['interactions']
        
        if not interactions:
            return {'error': 'No interactions recorded'}
        
        # Calculate performance metrics
        total_duration = sum(i['duration_ms'] for i in interactions)
        avg_duration = total_duration / len(interactions)
        
        # Find slowest interactions
        slowest_interactions = sorted(interactions, key=lambda x: x['duration_ms'], reverse=True)[:10]
        
        # Group by component pairs
        component_pairs = {}
        for interaction in interactions:
            pair_key = f"{interaction['source']}->{interaction['target']}"
            if pair_key not in component_pairs:
                component_pairs[pair_key] = []
            component_pairs[pair_key].append(interaction)
        
        # Identify bottlenecks
        bottlenecks = []
        for pair_key, pair_interactions in component_pairs.items():
            pair_avg_duration = sum(i['duration_ms'] for i in pair_interactions) / len(pair_interactions)
            if pair_avg_duration > avg_duration * 2:  # Significantly slower than average
                bottlenecks.append({
                    'component_pair': pair_key,
                    'avg_duration_ms': pair_avg_duration,
                    'interaction_count': len(pair_interactions),
                    'total_time_ms': sum(i['duration_ms'] for i in pair_interactions)
                })
        
        return {
            'total_interactions': len(interactions),
            'total_duration_ms': total_duration,
            'avg_duration_ms': avg_duration,
            'slowest_interactions': slowest_interactions,
            'bottlenecks': sorted(bottlenecks, key=lambda x: x['avg_duration_ms'], reverse=True),
            'component_pairs': len(component_pairs),
            'profiling_duration_seconds': time.time() - self.profiling_start_time
        }


class ComponentInteractionOptimizer(QObject):
    """
    Main optimizer that coordinates data flow optimization and performance profiling.
    """
    
    optimization_status_changed = pyqtSignal(dict)  # Status update
    
    def __init__(self):
        super().__init__()
        
        # Sub-components
        self.data_flow_optimizer = DataFlowOptimizer()
        self.performance_profiler = PerformanceProfiler()
        
        # Connect signals
        self.data_flow_optimizer.bottleneck_detected.connect(self._handle_bottleneck)
        self.data_flow_optimizer.optimization_applied.connect(self._handle_optimization)
        self.performance_profiler.profiling_completed.connect(self._handle_profiling_results)
        
        # Optimization state
        self.active_optimizations = {}
        self.optimization_effectiveness = {}
        
    def optimize_component_interactions(self, components: List[str], 
                                      optimization_config: Dict) -> Dict:
        """
        Optimize interactions between specified components.
        
        Args:
            components: List of component names to optimize
            optimization_config: Configuration for optimization
            
        Returns:
            Dict: Optimization results
        """
        results = {
            'optimizations_applied': [],
            'performance_improvements': {},
            'bottlenecks_resolved': [],
            'recommendations': []
        }
        
        # Start profiling if requested
        if optimization_config.get('enable_profiling', False):
            profiling_duration = optimization_config.get('profiling_duration', 30)
            self.performance_profiler.start_profiling(components, profiling_duration)
        
        # Apply data flow optimizations
        if optimization_config.get('optimize_data_flow', True):
            data_flow_results = self._optimize_data_flow(components, optimization_config)
            results['optimizations_applied'].extend(data_flow_results)
        
        # Minimize monitoring overhead
        if optimization_config.get('minimize_monitoring_overhead', True):
            monitoring_optimizations = self.data_flow_optimizer.minimize_monitoring_overhead(
                'performance_monitor', components
            )
            results['optimizations_applied'].append(f"Monitoring overhead optimizations: {monitoring_optimizations}")
        
        # Apply automatic optimizations
        if optimization_config.get('enable_automatic_optimizations', True):
            auto_optimizations = self.data_flow_optimizer.apply_automatic_optimizations()
            results['optimizations_applied'].extend(auto_optimizations)
        
        return results
    
    def track_interaction(self, source: str, target: str, operation: str,
                         processing_time_ms: float, data_size_bytes: int = 0,
                         overhead_ms: float = 0):
        """
        Track a component interaction for optimization analysis.
        
        Args:
            source: Source component name
            target: Target component name
            operation: Operation type
            processing_time_ms: Processing time in milliseconds
            data_size_bytes: Size of data transferred
            overhead_ms: Additional overhead time
        """
        # Track in data flow optimizer
        self.data_flow_optimizer.track_component_interaction(
            source, target, operation, data_size_bytes, processing_time_ms, overhead_ms
        )
        
        # Record in profiler if active
        if self.performance_profiler.profiling_active:
            start_time = time.time() - (processing_time_ms / 1000)
            end_time = time.time()
            self.performance_profiler.record_interaction(
                source, target, operation, start_time, end_time, data_size_bytes
            )
    
    def get_optimization_status(self) -> Dict:
        """Get current optimization status"""
        bottlenecks = self.data_flow_optimizer.get_performance_bottlenecks()
        
        return {
            'active_optimizations': len(self.active_optimizations),
            'detected_bottlenecks': len(bottlenecks),
            'profiling_active': self.performance_profiler.profiling_active,
            'optimization_effectiveness': self.optimization_effectiveness,
            'top_bottlenecks': bottlenecks[:3]  # Top 3 bottlenecks
        }
    
    # Private methods
    
    def _optimize_data_flow(self, components: List[str], config: Dict) -> List[str]:
        """Optimize data flow between components"""
        optimizations = []
        
        # Create optimized pipelines for common component chains
        common_chains = [
            ['parser', 'ui_renderer'],
            ['parser', 'memory_manager'],
            ['ui_renderer', 'memory_manager'],
            ['performance_monitor', 'parser'],
            ['performance_monitor', 'ui_renderer']
        ]
        
        for chain in common_chains:
            if all(comp in components for comp in chain):
                pipeline = self.data_flow_optimizer.create_optimized_data_pipeline(
                    chain, config
                )
                optimizations.append(f"Created optimized pipeline: {' -> '.join(chain)}")
        
        return optimizations
    
    def _handle_bottleneck(self, component_pair: str, bottleneck_info: Dict):
        """Handle detected bottleneck"""
        print(f"Bottleneck detected: {component_pair} - {bottleneck_info}")
        
        # Apply targeted optimization for this bottleneck
        components = component_pair.split('->')
        if len(components) == 2:
            source, target = components
            
            # Choose optimization strategy based on bottleneck type
            if bottleneck_info['avg_overhead_ms'] > 50:
                self.data_flow_optimizer._enable_interaction_caching(source, target)
            elif bottleneck_info['avg_processing_time_ms'] > 200:
                self.data_flow_optimizer._enable_async_interaction(source, target)
    
    def _handle_optimization(self, optimization_type: str, description: str):
        """Handle applied optimization"""
        self.active_optimizations[optimization_type] = {
            'description': description,
            'timestamp': time.time()
        }
        
        # Emit status update
        self.optimization_status_changed.emit(self.get_optimization_status())
    
    def _handle_profiling_results(self, results: Dict):
        """Handle profiling results"""
        print(f"Profiling results: {results}")
        
        # Update optimization effectiveness based on profiling results
        if 'bottlenecks' in results:
            for bottleneck in results['bottlenecks']:
                pair = bottleneck['component_pair']
                self.optimization_effectiveness[pair] = {
                    'avg_duration_ms': bottleneck['avg_duration_ms'],
                    'needs_optimization': bottleneck['avg_duration_ms'] > 100
                }