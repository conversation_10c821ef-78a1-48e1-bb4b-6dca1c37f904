#!/usr/bin/env python3
"""
测试Corner筛选功能
"""

import json
from pathlib import Path

def test_corner_filter():
    """测试Corner筛选数据"""
    print("🧪 测试Corner筛选功能")
    print("=" * 50)
    
    # 检查数据文件
    data_dir = Path("VIOLATION_CHECK/web_display/data")
    
    # 检查index.json
    index_file = data_dir / "index.json"
    if index_file.exists():
        with open(index_file, 'r', encoding='utf-8') as f:
            index_data = json.load(f)
        
        print("📋 Index数据:")
        print(f"  - Corners: {index_data.get('corners', [])}")
        print(f"  - Cases: {index_data.get('cases', [])}")
        print(f"  - Total violations: {index_data.get('metadata', {}).get('total_violations', 0)}")
    
    # 检查violations.json
    violations_file = data_dir / "violations.json"
    if violations_file.exists():
        with open(violations_file, 'r', encoding='utf-8') as f:
            violations_data = json.load(f)
        
        if 'violations' in violations_data:
            violations = violations_data['violations']
            print(f"\n📊 Violations数据: {len(violations)} 条记录")
            
            # 按corner统计
            corner_stats = {}
            for v in violations:
                corner = v.get('corner', 'unknown')
                if corner not in corner_stats:
                    corner_stats[corner] = 0
                corner_stats[corner] += 1
            
            print("📈 按Corner统计:")
            for corner, count in corner_stats.items():
                print(f"  - {corner}: {count} violations")
            
            # 按case统计
            case_stats = {}
            for v in violations:
                case = v.get('case', 'unknown')
                if case not in case_stats:
                    case_stats[case] = 0
                case_stats[case] += 1
            
            print("📈 按Case统计:")
            for case, count in case_stats.items():
                print(f"  - {case}: {count} violations")
            
            # 测试筛选逻辑
            print("\n🔍 测试筛选逻辑:")
            
            # 筛选npg_f1_ffg corner
            npg_f1_violations = [v for v in violations if v.get('corner') == 'npg_f1_ffg']
            print(f"  - npg_f1_ffg corner: {len(npg_f1_violations)} violations")
            
            # 筛选npg_f2_ffg corner
            npg_f2_violations = [v for v in violations if v.get('corner') == 'npg_f2_ffg']
            print(f"  - npg_f2_ffg corner: {len(npg_f2_violations)} violations")
            
            # 筛选page_test case
            page_test_violations = [v for v in violations if v.get('case') == 'page_test']
            print(f"  - page_test case: {len(page_test_violations)} violations")
            
            # 筛选page_test_027_test case
            page_test_027_violations = [v for v in violations if v.get('case') == 'page_test_027_test']
            print(f"  - page_test_027_test case: {len(page_test_027_violations)} violations")
            
            # 组合筛选
            npg_f1_page_test = [v for v in violations if v.get('corner') == 'npg_f1_ffg' and v.get('case') == 'page_test_027_test']
            print(f"  - npg_f1_ffg + page_test_027_test: {len(npg_f1_page_test)} violations")
            
            npg_f2_page_test = [v for v in violations if v.get('corner') == 'npg_f2_ffg' and v.get('case') == 'page_test']
            print(f"  - npg_f2_ffg + page_test: {len(npg_f2_page_test)} violations")
    
    print("\n" + "=" * 50)
    print("✅ Corner筛选测试完成")

if __name__ == "__main__":
    test_corner_filter()