"""
Violation-Aware Memory Management System

Provides intelligent memory monitoring, streaming, and optimization
specifically designed for timing violation data processing.
"""

import gc
import time
import threading
import psutil
from typing import Dict, List, Optional, Any, Callable
from collections import deque, OrderedDict
from dataclasses import dataclass
from PyQt5.QtCore import QObject, QTimer, pyqtSignal


@dataclass
class ViolationMemoryMetrics:
    """Memory metrics specific to violation processing"""
    violations_processed: int
    memory_per_violation_kb: float
    total_memory_mb: float
    peak_memory_mb: float
    memory_efficiency: float  # violations per MB
    processing_speed: float  # violations per second
    timestamp: float


@dataclass
class MemoryPressureLevel:
    """Memory pressure level definition"""
    level: str  # 'low', 'medium', 'high', 'critical'
    threshold_percent: float
    violation_batch_size: int
    gc_frequency: int
    streaming_enabled: bool
    lazy_loading_enabled: bool


class ViolationMemoryMonitor(QObject):
    """
    Violation-aware memory monitor that tracks memory usage
    per violation processed and provides violation-specific thresholds
    """
    
    # Signals
    memory_pressure_detected = pyqtSignal(str, dict)  # level, metrics
    violation_memory_stats = pyqtSignal(dict)  # memory statistics
    optimization_suggestion = pyqtSignal(str, dict)  # suggestion, context
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Memory tracking
        self._violation_count = 0
        self._memory_samples = deque(maxlen=100)
        self._processing_history = deque(maxlen=50)
        self._lock = threading.RLock()
        
        # Process monitoring
        self._process = psutil.Process()
        self._baseline_memory = self._get_current_memory_mb()
        
        # Pressure level definitions
        self._pressure_levels = {
            'low': MemoryPressureLevel('low', 60.0, 5000, 10000, False, False),
            'medium': MemoryPressureLevel('medium', 75.0, 2000, 5000, True, True),
            'high': MemoryPressureLevel('high', 85.0, 1000, 2000, True, True),
            'critical': MemoryPressureLevel('critical', 95.0, 500, 1000, True, True)
        }
        
        # Current state
        self._current_pressure_level = 'low'
        self._last_pressure_check = 0
        self._pressure_check_interval = 2.0  # seconds
        
        # Monitoring timer
        self._monitor_timer = QTimer(self)
        self._monitor_timer.timeout.connect(self._monitor_memory_usage)
        self._monitor_timer.start(1000)  # Check every second
        
        # Statistics
        self._stats = {
            'total_violations_processed': 0,
            'peak_memory_usage_mb': 0,
            'average_memory_per_violation_kb': 0,
            'memory_efficiency_score': 0,
            'pressure_events': 0,
            'optimizations_triggered': 0
        }
    
    def track_violation_processing(self, violation_count: int, 
                                 processing_time: float = None) -> ViolationMemoryMetrics:
        """
        Track memory usage for violation processing
        
        Args:
            violation_count: Number of violations processed
            processing_time: Time taken to process (optional)
            
        Returns:
            ViolationMemoryMetrics: Current memory metrics
        """
        with self._lock:
            current_memory = self._get_current_memory_mb()
            memory_delta = current_memory - self._baseline_memory
            
            # Calculate per-violation memory usage
            if violation_count > 0:
                memory_per_violation = (memory_delta * 1024) / violation_count  # KB per violation
            else:
                memory_per_violation = 0
            
            # Calculate processing speed
            processing_speed = 0
            if processing_time and processing_time > 0:
                processing_speed = violation_count / processing_time
            
            # Create metrics
            metrics = ViolationMemoryMetrics(
                violations_processed=violation_count,
                memory_per_violation_kb=memory_per_violation,
                total_memory_mb=current_memory,
                peak_memory_mb=max(current_memory, self._stats['peak_memory_usage_mb']),
                memory_efficiency=violation_count / max(memory_delta, 0.1),
                processing_speed=processing_speed,
                timestamp=time.time()
            )
            
            # Update tracking
            self._violation_count += violation_count
            self._memory_samples.append(metrics)
            
            # Update statistics
            self._update_statistics(metrics)
            
            # Check for memory pressure
            self._check_memory_pressure(metrics)
            
            return metrics
    
    def predict_memory_usage(self, violation_count: int) -> Dict[str, float]:
        """
        Predict memory usage based on violation count and complexity
        
        Args:
            violation_count: Number of violations to process
            
        Returns:
            Dict with predicted memory usage metrics
        """
        with self._lock:
            if not self._memory_samples:
                # Use default estimates if no history
                estimated_memory_per_violation = 1.0  # 1KB per violation
            else:
                # Calculate average from recent samples
                recent_samples = list(self._memory_samples)[-10:]
                avg_memory_per_violation = sum(s.memory_per_violation_kb for s in recent_samples) / len(recent_samples)
                estimated_memory_per_violation = max(avg_memory_per_violation, 0.5)
            
            # Predict total memory usage
            predicted_memory_mb = (violation_count * estimated_memory_per_violation) / 1024
            current_memory = self._get_current_memory_mb()
            total_predicted = current_memory + predicted_memory_mb
            
            # Calculate system memory percentage
            system_memory = psutil.virtual_memory()
            predicted_system_percent = (total_predicted * 1024 * 1024) / system_memory.total * 100
            
            return {
                'predicted_additional_mb': predicted_memory_mb,
                'predicted_total_mb': total_predicted,
                'predicted_system_percent': predicted_system_percent,
                'memory_per_violation_kb': estimated_memory_per_violation,
                'confidence_score': min(len(self._memory_samples) / 10.0, 1.0)
            }
    
    def get_violation_specific_thresholds(self, violation_count: int) -> Dict[str, Any]:
        """
        Get memory thresholds based on violation count
        
        Args:
            violation_count: Number of violations being processed
            
        Returns:
            Dict with violation-specific thresholds
        """
        # Base thresholds on violation count ranges
        if violation_count < 2000:
            return {
                'memory_limit_mb': 200,
                'batch_size': 1000,
                'gc_interval': 5000,
                'streaming_recommended': False,
                'pagination_recommended': False,
                'pressure_threshold': 70.0
            }
        elif violation_count < 20000:
            return {
                'memory_limit_mb': 500,
                'batch_size': 2000,
                'gc_interval': 10000,
                'streaming_recommended': True,
                'pagination_recommended': True,
                'pressure_threshold': 75.0
            }
        elif violation_count < 50000:
            return {
                'memory_limit_mb': 800,
                'batch_size': 5000,
                'gc_interval': 20000,
                'streaming_recommended': True,
                'pagination_recommended': True,
                'pressure_threshold': 80.0
            }
        else:
            return {
                'memory_limit_mb': 1000,
                'batch_size': 10000,
                'gc_interval': 50000,
                'streaming_recommended': True,
                'pagination_recommended': True,
                'pressure_threshold': 85.0
            }
    
    def _monitor_memory_usage(self):
        """Monitor memory usage and detect pressure"""
        try:
            current_time = time.time()
            if current_time - self._last_pressure_check < self._pressure_check_interval:
                return
            
            self._last_pressure_check = current_time
            
            # Get current memory usage
            current_memory = self._get_current_memory_mb()
            system_memory = psutil.virtual_memory()
            memory_percent = (current_memory * 1024 * 1024) / system_memory.total * 100
            
            # Determine pressure level
            new_pressure_level = self._determine_pressure_level(memory_percent)
            
            # Check if pressure level changed
            if new_pressure_level != self._current_pressure_level:
                self._current_pressure_level = new_pressure_level
                self._stats['pressure_events'] += 1
                
                # Emit pressure signal
                pressure_info = {
                    'level': new_pressure_level,
                    'memory_percent': memory_percent,
                    'memory_mb': current_memory,
                    'violation_count': self._violation_count,
                    'recommended_actions': self._get_pressure_recommendations(new_pressure_level)
                }
                
                self.memory_pressure_detected.emit(new_pressure_level, pressure_info)
            
            # Emit regular stats update
            stats = self._get_current_stats()
            self.violation_memory_stats.emit(stats)
            
        except Exception as e:
            print(f"Memory monitoring error: {e}")
    
    def _determine_pressure_level(self, memory_percent: float) -> str:
        """Determine memory pressure level based on usage percentage"""
        if memory_percent >= self._pressure_levels['critical'].threshold_percent:
            return 'critical'
        elif memory_percent >= self._pressure_levels['high'].threshold_percent:
            return 'high'
        elif memory_percent >= self._pressure_levels['medium'].threshold_percent:
            return 'medium'
        else:
            return 'low'
    
    def _get_pressure_recommendations(self, pressure_level: str) -> List[str]:
        """Get recommendations for handling memory pressure"""
        level_config = self._pressure_levels[pressure_level]
        recommendations = []
        
        if level_config.streaming_enabled:
            recommendations.append("Enable streaming mode for large datasets")
        
        if level_config.lazy_loading_enabled:
            recommendations.append("Use lazy loading for violation details")
        
        recommendations.append(f"Reduce batch size to {level_config.violation_batch_size}")
        recommendations.append(f"Increase GC frequency to every {level_config.gc_frequency} violations")
        
        if pressure_level in ['high', 'critical']:
            recommendations.append("Consider filtering data to reduce violation count")
            recommendations.append("Close other applications to free memory")
        
        return recommendations
    
    def _check_memory_pressure(self, metrics: ViolationMemoryMetrics):
        """Check for memory pressure and emit suggestions"""
        # Check if memory per violation is too high
        if metrics.memory_per_violation_kb > 2.0:  # More than 2KB per violation
            suggestion = f"High memory usage per violation ({metrics.memory_per_violation_kb:.1f}KB). Consider optimizing data structures."
            context = {
                'memory_per_violation': metrics.memory_per_violation_kb,
                'total_violations': metrics.violations_processed,
                'suggestion_type': 'optimization'
            }
            self.optimization_suggestion.emit(suggestion, context)
        
        # Check processing efficiency
        if metrics.memory_efficiency < 100:  # Less than 100 violations per MB
            suggestion = f"Low memory efficiency ({metrics.memory_efficiency:.1f} violations/MB). Consider using streaming mode."
            context = {
                'memory_efficiency': metrics.memory_efficiency,
                'suggestion_type': 'streaming'
            }
            self.optimization_suggestion.emit(suggestion, context)
    
    def _update_statistics(self, metrics: ViolationMemoryMetrics):
        """Update internal statistics"""
        self._stats['total_violations_processed'] += metrics.violations_processed
        self._stats['peak_memory_usage_mb'] = max(
            self._stats['peak_memory_usage_mb'], 
            metrics.total_memory_mb
        )
        
        # Update average memory per violation
        if self._stats['total_violations_processed'] > 0:
            total_memory_used = metrics.total_memory_mb - self._baseline_memory
            self._stats['average_memory_per_violation_kb'] = (
                (total_memory_used * 1024) / self._stats['total_violations_processed']
            )
        
        # Update efficiency score
        if metrics.memory_efficiency > 0:
            self._stats['memory_efficiency_score'] = (
                (self._stats['memory_efficiency_score'] + metrics.memory_efficiency) / 2
            )
    
    def _get_current_memory_mb(self) -> float:
        """Get current memory usage in MB"""
        try:
            return self._process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def _get_current_stats(self) -> Dict[str, Any]:
        """Get current memory statistics"""
        current_memory = self._get_current_memory_mb()
        system_memory = psutil.virtual_memory()
        
        return {
            'current_memory_mb': current_memory,
            'baseline_memory_mb': self._baseline_memory,
            'memory_delta_mb': current_memory - self._baseline_memory,
            'system_memory_percent': system_memory.percent,
            'pressure_level': self._current_pressure_level,
            'violations_processed': self._violation_count,
            'statistics': self._stats.copy(),
            'timestamp': time.time()
        }
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """Get comprehensive memory summary"""
        with self._lock:
            return self._get_current_stats()
    
    def reset_baseline(self):
        """Reset memory baseline for new processing session"""
        with self._lock:
            self._baseline_memory = self._get_current_memory_mb()
            self._violation_count = 0
            self._memory_samples.clear()
            self._processing_history.clear()
    
    def cleanup(self):
        """Clean up resources"""
        if self._monitor_timer:
            self._monitor_timer.stop()
        self._memory_samples.clear()
        self._processing_history.clear()


class ViolationDataStream:
    """
    Streaming data structure for large violation datasets
    Implements lazy loading and memory-efficient data access
    """
    
    def __init__(self, data_source: Any, chunk_size: int = 1000):
        self.data_source = data_source
        self.chunk_size = chunk_size
        self._cache = OrderedDict()
        self._cache_size_limit = 5  # Keep 5 chunks in memory
        self._total_size = None
        self._lock = threading.RLock()
    
    def __len__(self) -> int:
        """Get total number of violations"""
        if self._total_size is None:
            self._total_size = self._calculate_total_size()
        return self._total_size
    
    def __getitem__(self, index: int) -> Dict[str, Any]:
        """Get violation by index with lazy loading"""
        chunk_index = index // self.chunk_size
        item_index = index % self.chunk_size
        
        chunk = self._get_chunk(chunk_index)
        if chunk and item_index < len(chunk):
            return chunk[item_index]
        
        raise IndexError(f"Violation index {index} out of range")
    
    def get_chunk(self, chunk_index: int) -> List[Dict[str, Any]]:
        """Get a chunk of violations"""
        return self._get_chunk(chunk_index) or []
    
    def _get_chunk(self, chunk_index: int) -> Optional[List[Dict[str, Any]]]:
        """Get chunk with caching"""
        with self._lock:
            # Check cache first
            if chunk_index in self._cache:
                # Move to end (LRU)
                chunk = self._cache.pop(chunk_index)
                self._cache[chunk_index] = chunk
                return chunk
            
            # Load chunk from data source
            chunk = self._load_chunk(chunk_index)
            if chunk is not None:
                # Add to cache
                self._cache[chunk_index] = chunk
                
                # Evict old chunks if cache is full
                while len(self._cache) > self._cache_size_limit:
                    self._cache.popitem(last=False)
            
            return chunk
    
    def _load_chunk(self, chunk_index: int) -> Optional[List[Dict[str, Any]]]:
        """Load chunk from data source - to be implemented by subclasses"""
        # This is a placeholder - actual implementation depends on data source type
        start_index = chunk_index * self.chunk_size
        end_index = start_index + self.chunk_size
        
        if hasattr(self.data_source, '__getitem__'):
            try:
                return list(self.data_source[start_index:end_index])
            except (IndexError, TypeError):
                return None
        
        return None
    
    def _calculate_total_size(self) -> int:
        """Calculate total size - to be implemented by subclasses"""
        if hasattr(self.data_source, '__len__'):
            return len(self.data_source)
        return 0
    
    def prefetch_chunks(self, chunk_indices: List[int]):
        """Prefetch multiple chunks for better performance"""
        for chunk_index in chunk_indices:
            if chunk_index not in self._cache:
                self._get_chunk(chunk_index)
    
    def clear_cache(self):
        """Clear the chunk cache"""
        with self._lock:
            self._cache.clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            return {
                'cached_chunks': len(self._cache),
                'cache_limit': self._cache_size_limit,
                'chunk_size': self.chunk_size,
                'total_violations': len(self)
            }


class ViolationLRUCache:
    """
    LRU cache specifically designed for violation data
    with memory pressure-aware eviction
    """
    
    def __init__(self, max_size: int = 1000, memory_monitor: ViolationMemoryMonitor = None):
        self.max_size = max_size
        self.memory_monitor = memory_monitor
        self._cache = OrderedDict()
        self._access_count = {}
        self._memory_usage = 0
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        with self._lock:
            if key in self._cache:
                # Move to end (most recently used)
                value = self._cache.pop(key)
                self._cache[key] = value
                self._access_count[key] = self._access_count.get(key, 0) + 1
                return value
            return None
    
    def put(self, key: str, value: Any, size_bytes: int = None):
        """Put item in cache"""
        with self._lock:
            # Estimate size if not provided
            if size_bytes is None:
                size_bytes = self._estimate_size(value)
            
            # Check if we need to evict items
            self._evict_if_needed(size_bytes)
            
            # Add new item
            if key in self._cache:
                # Update existing item
                old_value = self._cache.pop(key)
                old_size = self._estimate_size(old_value)
                self._memory_usage -= old_size
            
            self._cache[key] = value
            self._memory_usage += size_bytes
            self._access_count[key] = self._access_count.get(key, 0) + 1
    
    def _evict_if_needed(self, new_item_size: int):
        """Evict items if needed based on size and memory pressure"""
        # Check memory pressure
        memory_pressure = False
        if self.memory_monitor:
            current_stats = self.memory_monitor.get_memory_summary()
            memory_pressure = current_stats.get('pressure_level', 'low') in ['high', 'critical']
        
        # Evict based on size limit or memory pressure
        while (len(self._cache) >= self.max_size or 
               memory_pressure and len(self._cache) > self.max_size // 2):
            
            if not self._cache:
                break
            
            # Remove least recently used item
            key, value = self._cache.popitem(last=False)
            size = self._estimate_size(value)
            self._memory_usage -= size
            
            if key in self._access_count:
                del self._access_count[key]
    
    def _estimate_size(self, value: Any) -> int:
        """Estimate memory size of value"""
        try:
            import sys
            return sys.getsizeof(value)
        except Exception:
            return 1024  # Default estimate
    
    def clear(self):
        """Clear the cache"""
        with self._lock:
            self._cache.clear()
            self._access_count.clear()
            self._memory_usage = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            total_access = sum(self._access_count.values())
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'memory_usage_bytes': self._memory_usage,
                'total_access_count': total_access,
                'hit_ratio': 0.0 if total_access == 0 else len(self._cache) / total_access
            }


class MemoryManager:
    """
    Main memory management class that coordinates all memory-related operations
    for violation data processing
    """
    
    def __init__(self):
        self.monitor = ViolationMemoryMonitor()
        self.cache = ViolationLRUCache(memory_monitor=self.monitor)
        self._streams = {}
        self._lock = threading.RLock()
        
        # Connect signals
        self.monitor.memory_pressure_detected.connect(self._handle_memory_pressure)
        self.monitor.optimization_suggestion.connect(self._handle_optimization_suggestion)
    
    def create_violation_stream(self, name: str, data_source: Any, 
                              chunk_size: int = 1000) -> ViolationDataStream:
        """Create a new violation data stream"""
        with self._lock:
            stream = ViolationDataStream(data_source, chunk_size)
            self._streams[name] = stream
            return stream
    
    def get_violation_stream(self, name: str) -> Optional[ViolationDataStream]:
        """Get existing violation stream"""
        with self._lock:
            return self._streams.get(name)
    
    def track_processing(self, violation_count: int, processing_time: float = None) -> ViolationMemoryMetrics:
        """Track violation processing"""
        return self.monitor.track_violation_processing(violation_count, processing_time)
    
    def predict_memory_usage(self, violation_count: int) -> Dict[str, float]:
        """Predict memory usage for violation count"""
        return self.monitor.predict_memory_usage(violation_count)
    
    def get_thresholds(self, violation_count: int) -> Dict[str, Any]:
        """Get violation-specific memory thresholds"""
        return self.monitor.get_violation_specific_thresholds(violation_count)
    
    def _handle_memory_pressure(self, level: str, info: Dict[str, Any]):
        """Handle memory pressure events"""
        print(f"Memory pressure detected: {level}")
        print(f"Recommendations: {info.get('recommended_actions', [])}")
        
        # Automatic optimizations based on pressure level
        if level in ['high', 'critical']:
            # Clear caches
            self.cache.clear()
            
            # Clear stream caches
            for stream in self._streams.values():
                stream.clear_cache()
            
            # Force garbage collection
            gc.collect()
    
    def _handle_optimization_suggestion(self, suggestion: str, context: Dict[str, Any]):
        """Handle optimization suggestions"""
        print(f"Optimization suggestion: {suggestion}")
        
        # Automatic optimizations based on suggestion type
        suggestion_type = context.get('suggestion_type', '')
        
        if suggestion_type == 'optimization':
            # Reduce cache sizes
            self.cache.max_size = max(self.cache.max_size // 2, 100)
        
        elif suggestion_type == 'streaming':
            # Reduce stream chunk sizes
            for stream in self._streams.values():
                stream.chunk_size = max(stream.chunk_size // 2, 100)
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """Get comprehensive memory summary"""
        monitor_stats = self.monitor.get_memory_summary()
        cache_stats = self.cache.get_stats()
        
        stream_stats = {}
        for name, stream in self._streams.items():
            stream_stats[name] = stream.get_cache_stats()
        
        return {
            'monitor': monitor_stats,
            'cache': cache_stats,
            'streams': stream_stats,
            'timestamp': time.time()
        }
    
    def cleanup(self):
        """Clean up all resources"""
        self.monitor.cleanup()
        self.cache.clear()
        
        with self._lock:
            for stream in self._streams.values():
                stream.clear_cache()
            self._streams.clear()