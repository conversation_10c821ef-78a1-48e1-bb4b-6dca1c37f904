#!/usr/bin/env python3
"""
Test script to verify the performance integration system works correctly.
"""

import sys
import os

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_performance_integration():
    """Test the performance integration system."""
    print("Testing Performance Integration System...")
    print("=" * 50)
    
    try:
        # Test importing the main integration system
        from performance_integration_system import PerformanceIntegrationSystem
        print("✓ PerformanceIntegrationSystem imported successfully")
        
        # Test creating an instance
        integration_system = PerformanceIntegrationSystem()
        print("✓ PerformanceIntegrationSystem instance created")
        
        # Test getting system status
        status = integration_system.get_system_status()
        print(f"✓ System status retrieved: {len(status)} status items")
        
        # Print component health
        print("\nComponent Health Status:")
        for component, health in status.get('component_health', {}).items():
            status_symbol = "✓" if health else "✗"
            print(f"  {status_symbol} {component}: {'Healthy' if health else 'Unavailable'}")
        
        print(f"\nIntegration Active: {status.get('integration_active', False)}")
        print(f"System Healthy: {status.get('is_healthy', False)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance Integration System test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_integration():
    """Test the main window integration adapter."""
    print("\nTesting Main Window Integration Adapter...")
    print("=" * 50)
    
    try:
        # Test importing the main window integration
        from main_window_integration import MainWindowIntegrationAdapter
        print("✓ MainWindowIntegrationAdapter imported successfully")
        
        # Test creating an instance (without actual main window)
        integration_adapter = MainWindowIntegrationAdapter(None)
        print("✓ MainWindowIntegrationAdapter instance created")
        
        # Test getting health status
        health_status = integration_adapter.get_system_health_status()
        print(f"✓ Health status retrieved: {len(health_status)} status items")
        
        print(f"Integration Enabled: {health_status.get('integration_enabled', False)}")
        print(f"Legacy Parsing: {health_status.get('use_legacy_parsing', True)}")
        print(f"Legacy Table: {health_status.get('use_legacy_table', True)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Main Window Integration Adapter test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all integration tests."""
    print("Performance Integration System Test Suite")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # Test performance integration system
    if test_performance_integration():
        success_count += 1
    
    # Test main window integration
    if test_main_window_integration():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✓ All integration tests passed successfully!")
        return True
    else:
        print("✗ Some integration tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)