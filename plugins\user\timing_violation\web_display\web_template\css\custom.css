/* Timing Violation Display - Custom Styles */

/* Global Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Styles */
.header-section {
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.header-section h1 {
    font-weight: 600;
}

/* Statistics Cards */
.stat-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.stat-icon {
    opacity: 0.8;
}

/* Filter Controls */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

/* Form Controls */
.form-select:focus,
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Table Styles */
#violations-table {
    font-size: 0.875rem;
}

#violations-table thead th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

#violations-table tbody tr {
    transition: background-color 0.15s ease-in-out;
}

#violations-table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Status-specific styling */
.status-confirmed {
    background-color: #d1e7dd;
    color: #0f5132;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.status-pending {
    background-color: #fff3cd;
    color: #664d03;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    font-size: 0.75rem;
    text-transform: uppercase;
}

/* Violation severity styling */
.violation-critical {
    border-left: 4px solid #dc3545;
}

.violation-warning {
    border-left: 4px solid #ffc107;
}

.violation-info {
    border-left: 4px solid #0dcaf0;
}

/* Time column styling */
.time-column {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
}

/* Hier column styling */
.hier-column {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.hier-column:hover {
    overflow: visible;
    white-space: normal;
    word-break: break-all;
}

/* Check info styling */
.check-info {
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.check-info:hover {
    overflow: visible;
    white-space: normal;
}

/* Reason column styling */
.reason-column {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.reason-column:hover {
    overflow: visible;
    white-space: normal;
}

/* Corner and Case badges */
.corner-badge {
    background-color: #6f42c1;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.case-badge {
    background-color: #20c997;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
}

.loading-content p {
    margin-top: 1rem;
    font-size: 1.1rem;
    color: #6c757d;
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-top: 1rem;
}

.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
}

.dataTables_wrapper .dataTables_length select {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
}

/* Pagination styling */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.375rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    background-color: #fff;
    color: #0d6efd;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

/* Scroller customization for virtual scrolling */
.dataTables_scrollBody {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

/* Table info badge */
.table-info .badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .header-section h1 {
        font-size: 1.5rem;
    }
    
    .stat-card .card-body {
        padding: 1rem;
    }
    
    .stat-card h3 {
        font-size: 1.5rem;
    }
    
    #violations-table {
        font-size: 0.75rem;
    }
    
    .hier-column,
    .check-info,
    .reason-column {
        max-width: 150px;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .header-section {
        padding: 1rem !important;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    #violations-table {
        font-size: 0.7rem;
    }
    
    .hier-column,
    .check-info,
    .reason-column {
        max-width: 100px;
    }
}

/* Print styles */
@media print {
    .header-section,
    .card-header,
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_paginate {
        display: none;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    #violations-table {
        font-size: 0.7rem;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators */
.btn:focus,
.form-select:focus,
.form-control:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .stat-card {
        border: 2px solid #000;
    }
    
    .status-confirmed {
        border: 1px solid #0f5132;
    }
    
    .status-pending {
        border: 1px solid #664d03;
    }
    
    .status-rejected {
        border: 1px solid #721c24;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .stat-card,
    #violations-table tbody tr {
        transition: none;
    }
    
    .stat-card:hover {
        transform: none;
    }
}

/* Performance optimizations */
.table-container {
    contain: layout style paint;
    will-change: scroll-position;
}

#violations-table {
    contain: layout style;
}

#violations-table tbody tr {
    contain: layout style paint;
}

/* Lazy loading optimization */
.lazy-load {
    content-visibility: auto;
    contain-intrinsic-size: 50px;
}

/* Virtual scrolling performance */
.dataTables_scrollBody {
    contain: strict;
    overflow: auto;
}

/* GPU acceleration for smooth scrolling */
.dataTables_scrollBody,
.loading-overlay {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Optimize filter controls */
.form-select,
.form-control {
    contain: layout style;
}

/* Memory-efficient animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* Efficient table row highlighting */
#violations-table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    contain: layout style paint;
}