"""
Integrated Memory Management System

Coordinates all memory management components for violation data processing.
Provides a unified interface for memory monitoring, streaming, and optimization.
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from PyQt5.QtCore import QObject, pyqtSignal

try:
    from .memory_manager import (
        ViolationMemoryMonitor, ViolationDataStream, ViolationLRUCache, MemoryManager
    )
    from .violation_data_streaming import (
        ViolationDataSource, ListDataSource, FileDataSource, ViolationStreamManager
    )
    from .automatic_memory_optimizer import (
        AutomaticMemoryOptimizer, OptimizationMode, GarbageCollectionScheduler
    )
except ImportError:
    from memory_manager import (
        ViolationMemoryMonitor, ViolationDataStream, ViolationLRUCache, MemoryManager
    )
    from violation_data_streaming import (
        ViolationDataSource, ListDataSource, FileDataSource, ViolationStreamManager
    )
    from automatic_memory_optimizer import (
        AutomaticMemoryOptimizer, OptimizationMode, GarbageCollectionScheduler
    )


@dataclass
class MemorySystemConfig:
    """Configuration for the integrated memory system"""
    # Memory monitoring
    memory_warning_threshold: float = 75.0
    memory_critical_threshold: float = 90.0
    monitoring_interval_seconds: float = 2.0
    
    # Streaming configuration
    default_chunk_size: int = 1000
    max_cached_chunks: int = 10
    max_cache_memory_mb: float = 100.0
    
    # Optimization configuration
    optimization_mode: OptimizationMode = OptimizationMode.BALANCED
    auto_optimization_enabled: bool = True
    gc_violations_threshold: int = 10000
    gc_memory_threshold_mb: float = 50.0
    
    # Emergency settings
    emergency_memory_threshold: float = 95.0
    emergency_chunk_size: int = 100
    emergency_cache_size: int = 2


class IntegratedMemorySystem(QObject):
    """
    Main integrated memory management system that coordinates all components
    """
    
    # Signals
    memory_status_changed = pyqtSignal(str, dict)  # status, info
    optimization_performed = pyqtSignal(dict)  # optimization result
    emergency_mode_activated = pyqtSignal(dict)  # emergency info
    performance_warning = pyqtSignal(str, dict)  # warning, context
    
    def __init__(self, config: MemorySystemConfig = None, parent=None):
        super().__init__(parent)
        
        self.config = config or MemorySystemConfig()
        self._lock = threading.RLock()
        
        # Initialize components
        self._initialize_components()
        
        # Connect signals
        self._connect_signals()
        
        # System state
        self._system_active = False
        self._emergency_mode = False
        self._current_violation_count = 0
        
        # Statistics
        self._stats = {
            'system_start_time': time.time(),
            'total_violations_processed': 0,
            'total_memory_optimizations': 0,
            'total_streams_created': 0,
            'peak_memory_usage_mb': 0.0,
            'emergency_activations': 0
        }
    
    def _initialize_components(self):
        """Initialize all memory management components"""
        # Memory monitor
        self.memory_monitor = ViolationMemoryMonitor(self)
        
        # Stream manager
        self.stream_manager = ViolationStreamManager(
            max_total_memory_mb=self.config.max_cache_memory_mb
        )
        
        # Automatic optimizer
        self.optimizer = AutomaticMemoryOptimizer(self)
        self.optimizer.set_optimization_mode(self.config.optimization_mode)
        self.optimizer.enable_optimization(self.config.auto_optimization_enabled)
        
        # Main memory manager (legacy compatibility)
        self.memory_manager = MemoryManager()
    
    def _connect_signals(self):
        """Connect signals between components"""
        # Memory monitor signals
        self.memory_monitor.memory_pressure_detected.connect(
            self._handle_memory_pressure
        )
        self.memory_monitor.optimization_suggestion.connect(
            self._handle_optimization_suggestion
        )
        
        # Optimizer signals
        self.optimizer.optimization_performed.connect(
            self._handle_optimization_performed
        )
        self.optimizer.emergency_mode_activated.connect(
            self._handle_emergency_mode
        )
        self.optimizer.mode_changed.connect(
            self._handle_mode_changed
        )
    
    def start_system(self):
        """Start the integrated memory system"""
        with self._lock:
            if self._system_active:
                return
            
            self._system_active = True
            print("Integrated memory management system started")
            
            # Reset baseline memory
            self.memory_monitor.reset_baseline()
            
            # Emit status change
            self.memory_status_changed.emit("started", {
                'timestamp': time.time(),
                'config': self._get_config_dict()
            })
    
    def stop_system(self):
        """Stop the integrated memory system"""
        with self._lock:
            if not self._system_active:
                return
            
            self._system_active = False
            
            # Cleanup components
            self.memory_monitor.cleanup()
            self.stream_manager.cleanup()
            self.optimizer.cleanup()
            self.memory_manager.cleanup()
            
            print("Integrated memory management system stopped")
            
            # Emit status change
            self.memory_status_changed.emit("stopped", {
                'timestamp': time.time(),
                'final_stats': self.get_system_stats()
            })
    
    def create_violation_stream(self, name: str, data_source: Any, 
                              chunk_size: int = None, priority: int = 1) -> ViolationDataStream:
        """
        Create a new violation data stream with integrated memory management
        
        Args:
            name: Stream name
            data_source: Data source (list, file path, or ViolationDataSource)
            chunk_size: Chunk size (uses config default if None)
            priority: Stream priority for memory management
            
        Returns:
            ViolationDataStream: The created stream
        """
        if not self._system_active:
            raise RuntimeError("Memory system not started")
        
        # Determine chunk size based on current mode
        if chunk_size is None:
            if self._emergency_mode:
                chunk_size = self.config.emergency_chunk_size
            else:
                chunk_size = self.config.default_chunk_size
        
        # Create appropriate data source
        if isinstance(data_source, list):
            source = ListDataSource(data_source)
        elif isinstance(data_source, str):
            # Assume it's a file path - would need parser function
            source = FileDataSource(data_source, self._default_parser)
        elif isinstance(data_source, ViolationDataSource):
            source = data_source
        else:
            raise ValueError(f"Unsupported data source type: {type(data_source)}")
        
        # Create stream through manager
        stream = self.stream_manager.create_stream(name, source, chunk_size, priority)
        
        # Update statistics
        self._stats['total_streams_created'] += 1
        
        return stream
    
    def get_violation_stream(self, name: str) -> Optional[ViolationDataStream]:
        """Get existing violation stream"""
        return self.stream_manager.get_stream(name)
    
    def remove_violation_stream(self, name: str):
        """Remove violation stream and free resources"""
        self.stream_manager.remove_stream(name)
    
    def track_violation_processing(self, violation_count: int, 
                                 processing_time: float = None) -> Dict[str, Any]:
        """
        Track violation processing across all components
        
        Args:
            violation_count: Number of violations processed
            processing_time: Time taken to process
            
        Returns:
            Dict with tracking results
        """
        if not self._system_active:
            return {}
        
        with self._lock:
            # Update counters
            self._current_violation_count += violation_count
            self._stats['total_violations_processed'] += violation_count
            
            # Track in memory monitor
            metrics = self.memory_monitor.track_violation_processing(
                violation_count, processing_time
            )
            
            # Track in optimizer for GC scheduling
            self.optimizer.track_violation_processing(violation_count)
            
            # Update peak memory usage
            current_memory = metrics.total_memory_mb
            if current_memory > self._stats['peak_memory_usage_mb']:
                self._stats['peak_memory_usage_mb'] = current_memory
            
            return {
                'metrics': metrics,
                'total_processed': self._stats['total_violations_processed'],
                'system_status': 'emergency' if self._emergency_mode else 'normal'
            }
    
    def predict_memory_usage(self, violation_count: int) -> Dict[str, Any]:
        """Predict memory usage for processing violation count"""
        base_prediction = self.memory_monitor.predict_memory_usage(violation_count)
        
        # Add system-level context
        system_memory = self.optimizer._get_memory_usage_percent()
        
        # Determine if emergency mode would be triggered
        predicted_memory_percent = base_prediction.get('predicted_system_percent', 0)
        would_trigger_emergency = predicted_memory_percent > self.config.emergency_memory_threshold
        
        return {
            **base_prediction,
            'current_system_memory_percent': system_memory,
            'would_trigger_emergency': would_trigger_emergency,
            'recommended_chunk_size': (
                self.config.emergency_chunk_size if would_trigger_emergency 
                else self.config.default_chunk_size
            ),
            'recommended_mode': (
                OptimizationMode.EMERGENCY.value if would_trigger_emergency
                else self.optimizer._current_mode.value
            )
        }
    
    def get_memory_thresholds(self, violation_count: int) -> Dict[str, Any]:
        """Get memory thresholds for violation count"""
        base_thresholds = self.memory_monitor.get_violation_specific_thresholds(violation_count)
        
        # Add system-level thresholds
        return {
            **base_thresholds,
            'system_warning_threshold': self.config.memory_warning_threshold,
            'system_critical_threshold': self.config.memory_critical_threshold,
            'emergency_threshold': self.config.emergency_memory_threshold,
            'current_optimization_mode': self.optimizer._current_mode.value
        }
    
    def force_optimization(self) -> Dict[str, Any]:
        """Force immediate memory optimization"""
        if not self._system_active:
            return {'error': 'system_not_active'}
        
        result = self.optimizer.force_optimization()
        self._stats['total_memory_optimizations'] += 1
        
        return {
            'optimization_result': result,
            'system_stats': self.get_system_stats()
        }
    
    def set_optimization_mode(self, mode: OptimizationMode):
        """Set system optimization mode"""
        self.optimizer.set_optimization_mode(mode)
        self.config.optimization_mode = mode
    
    def enable_emergency_mode(self, force: bool = False):
        """Enable emergency memory conservation mode"""
        if self._emergency_mode and not force:
            return
        
        with self._lock:
            self._emergency_mode = True
            self._stats['emergency_activations'] += 1
            
            # Switch to emergency optimization mode
            self.optimizer.set_optimization_mode(OptimizationMode.EMERGENCY)
            
            # Reduce chunk sizes for all streams
            for stream_name in list(self.stream_manager._streams.keys()):
                stream = self.stream_manager.get_stream(stream_name)
                if stream:
                    stream.chunk_size = self.config.emergency_chunk_size
                    stream.optimize_for_memory_pressure()
            
            # Force optimization
            self.force_optimization()
            
            print("Emergency memory mode enabled")
    
    def disable_emergency_mode(self):
        """Disable emergency memory conservation mode"""
        if not self._emergency_mode:
            return
        
        with self._lock:
            self._emergency_mode = False
            
            # Reset to configured optimization mode
            self.optimizer.set_optimization_mode(self.config.optimization_mode)
            self.optimizer.reset_emergency_mode()
            
            print("Emergency memory mode disabled")
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive system statistics"""
        with self._lock:
            # Get component stats
            memory_stats = self.memory_monitor.get_memory_summary()
            stream_stats = self.stream_manager.get_global_stats()
            optimizer_stats = self.optimizer.get_optimization_stats()
            
            # Calculate uptime
            uptime_seconds = time.time() - self._stats['system_start_time']
            
            return {
                'system': {
                    'active': self._system_active,
                    'emergency_mode': self._emergency_mode,
                    'uptime_seconds': uptime_seconds,
                    'current_violation_count': self._current_violation_count,
                    **self._stats
                },
                'memory_monitor': memory_stats,
                'stream_manager': stream_stats,
                'optimizer': optimizer_stats,
                'config': self._get_config_dict(),
                'timestamp': time.time()
            }
    
    def _get_config_dict(self) -> Dict[str, Any]:
        """Get configuration as dictionary"""
        return {
            'memory_warning_threshold': self.config.memory_warning_threshold,
            'memory_critical_threshold': self.config.memory_critical_threshold,
            'monitoring_interval_seconds': self.config.monitoring_interval_seconds,
            'default_chunk_size': self.config.default_chunk_size,
            'max_cached_chunks': self.config.max_cached_chunks,
            'max_cache_memory_mb': self.config.max_cache_memory_mb,
            'optimization_mode': self.config.optimization_mode.value,
            'auto_optimization_enabled': self.config.auto_optimization_enabled,
            'emergency_memory_threshold': self.config.emergency_memory_threshold
        }
    
    def _handle_memory_pressure(self, level: str, info: Dict[str, Any]):
        """Handle memory pressure events"""
        print(f"Memory pressure detected: {level}")
        
        # Emit system signal
        self.memory_status_changed.emit(f"pressure_{level}", info)
        
        # Take automatic actions based on pressure level
        if level in ['high', 'critical']:
            # Force optimization
            self.force_optimization()
            
            # If critical, consider emergency mode
            if level == 'critical' and not self._emergency_mode:
                memory_percent = info.get('memory_percent', 0)
                if memory_percent > self.config.emergency_memory_threshold:
                    self.enable_emergency_mode()
    
    def _handle_optimization_suggestion(self, suggestion: str, context: Dict[str, Any]):
        """Handle optimization suggestions"""
        self.performance_warning.emit(suggestion, context)
    
    def _handle_optimization_performed(self, result):
        """Handle optimization performed events"""
        self._stats['total_memory_optimizations'] += 1
        
        # Convert result to dict for signal
        result_dict = {
            'actions_taken': result.actions_taken,
            'memory_freed_mb': result.memory_freed_mb,
            'time_taken_ms': result.time_taken_ms,
            'success': result.success,
            'new_mode': result.new_mode.value
        }
        
        self.optimization_performed.emit(result_dict)
    
    def _handle_emergency_mode(self, emergency_info: Dict[str, Any]):
        """Handle emergency mode activation"""
        self.enable_emergency_mode(force=True)
        self.emergency_mode_activated.emit(emergency_info)
    
    def _handle_mode_changed(self, old_mode: str, new_mode: str):
        """Handle optimization mode changes"""
        print(f"Optimization mode changed: {old_mode} -> {new_mode}")
        
        # Update config
        try:
            self.config.optimization_mode = OptimizationMode(new_mode)
        except ValueError:
            pass  # Invalid mode value
    
    def _default_parser(self, content: str) -> List[Dict[str, Any]]:
        """Default parser for file-based data sources"""
        # This is a placeholder - actual implementation would depend on file format
        lines = content.strip().split('\n')
        violations = []
        
        # Simple parsing assuming each violation is on a separate line
        for i, line in enumerate(lines):
            if line.strip():
                violations.append({
                    'id': i,
                    'content': line.strip(),
                    'timestamp': time.time()
                })
        
        return violations
    
    def cleanup(self):
        """Clean up all system resources"""
        self.stop_system()


# Global instance management
_global_memory_system = None
_system_lock = threading.Lock()


def get_integrated_memory_system(config: MemorySystemConfig = None) -> IntegratedMemorySystem:
    """Get global integrated memory system instance"""
    global _global_memory_system
    
    with _system_lock:
        if _global_memory_system is None:
            _global_memory_system = IntegratedMemorySystem(config)
        return _global_memory_system


def cleanup_integrated_memory_system():
    """Clean up global memory system"""
    global _global_memory_system
    
    with _system_lock:
        if _global_memory_system:
            _global_memory_system.cleanup()
            _global_memory_system = None