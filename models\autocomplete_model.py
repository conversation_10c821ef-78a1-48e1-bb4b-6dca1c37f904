"""
智能自动补全数据模型
负责管理补全历史数据、频率统计和数据持久化
"""
import os
import json
import threading
from datetime import datetime
from typing import Dict, List, Set, Optional, Any
from collections import defaultdict, Counter
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, pyqtSlot


class AutoCompleteModel(QObject):
    """自动补全数据模型"""
    
    # 信号定义
    data_loaded = pyqtSignal()  # 数据加载完成
    data_updated = pyqtSignal(str, str)  # 数据更新 (field_type, value)
    
    # 支持的参数类型
    FIELD_TYPES = {
        'base': 'BASE参数',
        'block': 'BLOCK路径',
        'case': '用例名',
        'bq_server': '提交服务器',
        'rundir': '工作目录',
        'cfg_def': 'CFG定义',
        'simarg': '仿真参数',
        'wdd': '波形时间',
        'other_options': '其他选项',
        'tag': '回归TAG',
        'nt': '不回归TAG',
        'dashboard': 'Dashboard TAG',
        'dump_level': 'Dump层级'  # 添加dump层级字段类型
    }
    
    def __init__(self, data_file="autocomplete_data.json", max_items_per_field=100):
        """
        初始化自动补全数据模型
        
        Args:
            data_file (str): 数据文件路径
            max_items_per_field (int): 每个字段最大保存项目数
        """
        super().__init__()
        
        self.data_file = data_file
        self.max_items_per_field = max_items_per_field
        self._lock = threading.RLock()
        
        # 数据结构：{field_type: {value: {'count': int, 'last_used': str}}}
        self._data: Dict[str, Dict[str, Dict[str, Any]]] = defaultdict(dict)
        
        # 缓存最近的建议结果
        self._cache: Dict[str, List[str]] = {}
        self._cache_timeout = 300  # 缓存5分钟
        self._cache_timestamps: Dict[str, float] = {}
        
        # 异步加载标志
        self._loading = False
        self._loaded = False
        
        # 定时保存
        self._save_timer = QTimer()
        self._save_timer.timeout.connect(self._save_data)
        self._save_timer.setSingleShot(True)

        # 销毁标志
        self._destroyed = False
        
        # 启动时异步加载数据
        self._async_load_data()
    
    def _async_load_data(self):
        """异步加载数据"""
        if self._loading or self._loaded:
            return
            
        self._loading = True
        
        def load_worker():
            try:
                self._load_data_sync()
                self._loaded = True
                self.data_loaded.emit()
            except Exception as e:
                print(f"异步加载自动补全数据失败: {e}")
            finally:
                self._loading = False
        
        thread = threading.Thread(target=load_worker, daemon=True)
        thread.start()
    
    def _load_data_sync(self):
        """同步加载数据"""
        with self._lock:
            try:
                if os.path.exists(self.data_file):
                    with open(self.data_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 验证和清理数据
                    for field_type, values in data.items():
                        if field_type in self.FIELD_TYPES:
                            # 限制每个字段的项目数量
                            if len(values) > self.max_items_per_field:
                                # 按使用频率和最近使用时间排序，保留最有价值的项目
                                sorted_items = sorted(
                                    values.items(),
                                    key=lambda x: (x[1].get('count', 0), x[1].get('last_used', '')),
                                    reverse=True
                                )
                                values = dict(sorted_items[:self.max_items_per_field])
                            
                            self._data[field_type] = values
                            
            except Exception as e:
                print(f"加载自动补全数据失败: {e}")
                # 初始化空数据
                self._data = defaultdict(dict)
    
    def add_value(self, field_type: str, value: str) -> bool:
        """
        添加或更新参数值
        
        Args:
            field_type (str): 参数类型
            value (str): 参数值
            
        Returns:
            bool: 是否成功添加
        """
        if not value or not value.strip():
            return False
            
        if field_type not in self.FIELD_TYPES:
            return False
        
        value = value.strip()
        
        with self._lock:
            # 更新数据
            if value in self._data[field_type]:
                # 增加使用次数
                self._data[field_type][value]['count'] += 1
            else:
                # 新增项目
                self._data[field_type][value] = {'count': 1}
            
            # 更新最后使用时间
            self._data[field_type][value]['last_used'] = datetime.now().isoformat()
            
            # 清理缓存
            self._clear_cache_for_field(field_type)
            
            # 延迟保存
            self._schedule_save()
            
            # 发送更新信号
            self.data_updated.emit(field_type, value)
            
            return True
    
    def get_suggestions(self, field_type: str, prefix: str = "", limit: int = 10) -> List[str]:
        """
        获取建议列表
        
        Args:
            field_type (str): 参数类型
            prefix (str): 输入前缀
            limit (int): 返回数量限制
            
        Returns:
            List[str]: 建议列表
        """
        if field_type not in self.FIELD_TYPES:
            return []
        
        # 检查缓存
        cache_key = f"{field_type}:{prefix}:{limit}"
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]
        
        with self._lock:
            suggestions = []
            field_data = self._data.get(field_type, {})
            
            if not prefix:
                # 无前缀时返回最常用的项目
                sorted_items = sorted(
                    field_data.items(),
                    key=lambda x: (x[1].get('count', 0), x[1].get('last_used', '')),
                    reverse=True
                )
                suggestions = [item[0] for item in sorted_items[:limit]]
            else:
                # 有前缀时进行匹配
                prefix_lower = prefix.lower()
                matches = []
                
                for value, data in field_data.items():
                    value_lower = value.lower()
                    
                    # 计算匹配分数
                    score = 0
                    if value_lower.startswith(prefix_lower):
                        score = 100  # 前缀匹配最高分
                    elif prefix_lower in value_lower:
                        score = 50   # 包含匹配中等分
                    else:
                        continue
                    
                    # 加上使用频率权重
                    score += min(data.get('count', 0) * 2, 20)
                    
                    matches.append((value, score))
                
                # 按分数排序
                matches.sort(key=lambda x: x[1], reverse=True)
                suggestions = [match[0] for match in matches[:limit]]
            
            # 缓存结果
            self._cache[cache_key] = suggestions
            self._cache_timestamps[cache_key] = datetime.now().timestamp()
            
            return suggestions

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache:
            return False

        timestamp = self._cache_timestamps.get(cache_key, 0)
        return (datetime.now().timestamp() - timestamp) < self._cache_timeout

    def _clear_cache_for_field(self, field_type: str):
        """清理指定字段的缓存"""
        keys_to_remove = [key for key in self._cache.keys() if key.startswith(f"{field_type}:")]
        for key in keys_to_remove:
            self._cache.pop(key, None)
            self._cache_timestamps.pop(key, None)

    def _schedule_save(self):
        """延迟保存数据"""
        if self._destroyed:
            return
        # 使用QTimer.singleShot确保在主线程中执行
        QTimer.singleShot(0, self._schedule_save_impl)

    @pyqtSlot()
    def _schedule_save_impl(self):
        """在主线程中执行的延迟保存实现"""
        if self._destroyed:
            return
        self._save_timer.stop()
        self._save_timer.start(2000)  # 2秒后保存

    def _save_data(self):
        """保存数据到文件"""
        with self._lock:
            try:
                # 创建目录
                os.makedirs(os.path.dirname(self.data_file) if os.path.dirname(self.data_file) else '.', exist_ok=True)

                # 保存数据
                with open(self.data_file, 'w', encoding='utf-8') as f:
                    json.dump(dict(self._data), f, indent=2, ensure_ascii=False)

            except Exception as e:
                print(f"保存自动补全数据失败: {e}")

    def clear_field_data(self, field_type: str) -> bool:
        """
        清除指定字段的所有数据

        Args:
            field_type (str): 参数类型

        Returns:
            bool: 是否成功清除
        """
        if field_type not in self.FIELD_TYPES:
            return False

        with self._lock:
            self._data[field_type].clear()
            self._clear_cache_for_field(field_type)
            self._schedule_save()
            return True

    def clear_all_data(self):
        """清除所有数据"""
        with self._lock:
            self._data.clear()
            self._cache.clear()
            self._cache_timestamps.clear()
            self._schedule_save()

    def get_field_stats(self, field_type: str) -> Dict[str, Any]:
        """
        获取字段统计信息

        Args:
            field_type (str): 参数类型

        Returns:
            Dict[str, Any]: 统计信息
        """
        if field_type not in self.FIELD_TYPES:
            return {}

        with self._lock:
            field_data = self._data.get(field_type, {})

            if not field_data:
                return {
                    'total_items': 0,
                    'total_usage': 0,
                    'most_used': None,
                    'last_updated': None
                }

            total_usage = sum(item.get('count', 0) for item in field_data.values())
            most_used = max(field_data.items(), key=lambda x: x[1].get('count', 0))
            last_updated = max(item.get('last_used', '') for item in field_data.values())

            return {
                'total_items': len(field_data),
                'total_usage': total_usage,
                'most_used': most_used[0] if most_used else None,
                'most_used_count': most_used[1].get('count', 0) if most_used else 0,
                'last_updated': last_updated
            }

    def export_data(self, file_path: str) -> bool:
        """
        导出数据到文件

        Args:
            file_path (str): 导出文件路径

        Returns:
            bool: 是否成功导出
        """
        try:
            with self._lock:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(dict(self._data), f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出自动补全数据失败: {e}")
            return False

    def import_data(self, file_path: str, merge: bool = True) -> bool:
        """
        从文件导入数据

        Args:
            file_path (str): 导入文件路径
            merge (bool): 是否合并现有数据

        Returns:
            bool: 是否成功导入
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)

            with self._lock:
                if not merge:
                    self._data.clear()

                # 合并数据
                for field_type, values in import_data.items():
                    if field_type in self.FIELD_TYPES:
                        if field_type not in self._data:
                            self._data[field_type] = {}

                        for value, data in values.items():
                            if value in self._data[field_type]:
                                # 合并使用次数
                                self._data[field_type][value]['count'] += data.get('count', 1)
                                # 使用最新的时间
                                if data.get('last_used', '') > self._data[field_type][value].get('last_used', ''):
                                    self._data[field_type][value]['last_used'] = data.get('last_used', '')
                            else:
                                self._data[field_type][value] = data

                # 清理缓存
                self._cache.clear()
                self._cache_timestamps.clear()

                # 保存数据
                self._schedule_save()

            return True
        except Exception as e:
            print(f"导入自动补全数据失败: {e}")
            return False

    def is_loaded(self) -> bool:
        """检查数据是否已加载"""
        return self._loaded

    def force_save(self):
        """强制立即保存数据"""
        if self._destroyed:
            return
        # 使用QTimer.singleShot确保在主线程中执行
        QTimer.singleShot(0, self._force_save_impl)

    @pyqtSlot()
    def _force_save_impl(self):
        """在主线程中执行的强制保存实现"""
        if self._destroyed:
            return
        self._save_timer.stop()
        self._save_data()

    def cleanup(self):
        """显式清理资源"""
        self._destroyed = True
        if hasattr(self, '_save_timer') and self._save_timer:
            try:
                self._save_timer.stop()
                self._save_timer.deleteLater()
                self._save_timer = None
            except RuntimeError:
                # QTimer已经被Qt删除，忽略错误
                pass

    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            self.cleanup()
        except:
            # 忽略析构时的所有错误
            pass
