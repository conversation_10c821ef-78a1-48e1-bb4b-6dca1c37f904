#!/usr/bin/env python3
"""
测试运行器

运行所有测试并生成详细报告
"""

import unittest
import sys
import os
import time
import traceback
from io import StringIO

# 添加路径以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入所有测试模块
from tests.test_models import *
from tests.test_parser import *
from tests.test_utils import *
from tests.test_integration import *


class DetailedTestResult(unittest.TextTestResult):
    """详细的测试结果类"""
    
    def __init__(self, stream, descriptions, verbosity):
        super().__init__(stream, descriptions, verbosity)
        self.test_results = []
        self.start_time = None
        self.end_time = None
    
    def startTest(self, test):
        super().startTest(test)
        self.start_time = time.time()
    
    def stopTest(self, test):
        super().stopTest(test)
        self.end_time = time.time()
        
        # 记录测试结果
        test_info = {
            'name': str(test),
            'duration': self.end_time - self.start_time,
            'status': 'PASS',
            'error': None
        }
        
        # 检查是否有错误或失败
        for failure in self.failures:
            if failure[0] == test:
                test_info['status'] = 'FAIL'
                test_info['error'] = failure[1]
                break
        
        for error in self.errors:
            if error[0] == test:
                test_info['status'] = 'ERROR'
                test_info['error'] = error[1]
                break
        
        self.test_results.append(test_info)
    
    def generate_report(self):
        """生成详细报告"""
        report = StringIO()
        
        # 总体统计
        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results if t['status'] == 'PASS'])
        failed_tests = len([t for t in self.test_results if t['status'] == 'FAIL'])
        error_tests = len([t for t in self.test_results if t['status'] == 'ERROR'])
        
        total_time = sum(t['duration'] for t in self.test_results)
        
        report.write("=" * 80 + "\n")
        report.write("寄存器表格解析器 - 测试报告\n")
        report.write("=" * 80 + "\n")
        report.write(f"总测试数: {total_tests}\n")
        report.write(f"通过: {passed_tests}\n")
        report.write(f"失败: {failed_tests}\n")
        report.write(f"错误: {error_tests}\n")
        report.write(f"总耗时: {total_time:.3f}秒\n")
        report.write(f"成功率: {(passed_tests/total_tests*100):.1f}%\n")
        report.write("\n")
        
        # 按模块分组统计
        modules = {}
        for test in self.test_results:
            module_name = test['name'].split('.')[0]
            if module_name not in modules:
                modules[module_name] = {'total': 0, 'passed': 0, 'failed': 0, 'errors': 0}
            
            modules[module_name]['total'] += 1
            if test['status'] == 'PASS':
                modules[module_name]['passed'] += 1
            elif test['status'] == 'FAIL':
                modules[module_name]['failed'] += 1
            else:
                modules[module_name]['errors'] += 1
        
        report.write("按模块统计:\n")
        report.write("-" * 40 + "\n")
        for module, stats in modules.items():
            success_rate = (stats['passed'] / stats['total'] * 100) if stats['total'] > 0 else 0
            report.write(f"{module}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)\n")
        report.write("\n")
        
        # 性能统计
        report.write("性能统计:\n")
        report.write("-" * 40 + "\n")
        
        # 最慢的测试
        slowest_tests = sorted(self.test_results, key=lambda x: x['duration'], reverse=True)[:5]
        report.write("最慢的5个测试:\n")
        for test in slowest_tests:
            report.write(f"  {test['name']}: {test['duration']:.3f}秒\n")
        report.write("\n")
        
        # 失败和错误详情
        if failed_tests > 0 or error_tests > 0:
            report.write("失败和错误详情:\n")
            report.write("-" * 40 + "\n")
            
            for test in self.test_results:
                if test['status'] in ['FAIL', 'ERROR']:
                    report.write(f"\n{test['status']}: {test['name']}\n")
                    report.write(f"耗时: {test['duration']:.3f}秒\n")
                    if test['error']:
                        report.write(f"错误信息:\n{test['error']}\n")
                    report.write("-" * 40 + "\n")
        
        return report.getvalue()


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.test_modules = [
            'tests.test_models',
            'tests.test_parser', 
            'tests.test_utils',
            'tests.test_integration'
        ]
    
    def discover_tests(self):
        """发现所有测试"""
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        for module_name in self.test_modules:
            try:
                # 动态导入模块
                module = __import__(module_name, fromlist=[''])
                
                # 加载测试
                module_suite = loader.loadTestsFromModule(module)
                suite.addTest(module_suite)
                
                print(f"✅ 加载测试模块: {module_name}")
                
            except ImportError as e:
                print(f"❌ 无法加载测试模块 {module_name}: {e}")
            except Exception as e:
                print(f"❌ 加载测试模块 {module_name} 时出错: {e}")
        
        return suite
    
    def run_tests(self, verbosity=2):
        """运行所有测试"""
        print("🧪 开始运行寄存器表格解析器测试套件")
        print("=" * 60)
        
        # 发现测试
        suite = self.discover_tests()
        
        if suite.countTestCases() == 0:
            print("❌ 没有发现任何测试")
            return False
        
        print(f"📋 发现 {suite.countTestCases()} 个测试")
        print()
        
        # 运行测试
        stream = StringIO()
        runner = unittest.TextTestRunner(
            stream=stream,
            verbosity=verbosity,
            resultclass=DetailedTestResult
        )
        
        start_time = time.time()
        result = runner.run(suite)
        end_time = time.time()
        
        # 输出结果到控制台
        print(stream.getvalue())
        
        # 生成详细报告
        if hasattr(result, 'generate_report'):
            report = result.generate_report()
            print(report)
            
            # 保存报告到文件
            report_file = os.path.join(os.path.dirname(__file__), 'test_report.txt')
            try:
                with open(report_file, 'w', encoding='utf-8') as f:
                    f.write(report)
                print(f"📄 详细报告已保存到: {report_file}")
            except Exception as e:
                print(f"⚠️ 无法保存报告文件: {e}")
        
        # 返回测试结果
        return result.wasSuccessful()
    
    def run_specific_test(self, test_pattern):
        """运行特定的测试"""
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromName(test_pattern)
        
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
    
    def run_performance_tests(self):
        """运行性能测试"""
        print("🚀 运行性能测试")
        print("-" * 40)
        
        # 这里可以添加专门的性能测试
        performance_tests = [
            'tests.test_utils.TestUtilsPerformance',
            'tests.test_integration.TestIntegrationWorkflows.test_performance_with_large_dataset'
        ]
        
        for test_name in performance_tests:
            try:
                success = self.run_specific_test(test_name)
                status = "✅ 通过" if success else "❌ 失败"
                print(f"{status} {test_name}")
            except Exception as e:
                print(f"❌ 错误 {test_name}: {e}")
    
    def check_test_coverage(self):
        """检查测试覆盖率"""
        print("📊 检查测试覆盖率")
        print("-" * 40)
        
        # 这里可以集成coverage.py来检查代码覆盖率
        # 由于这是一个简化版本，我们只做基本检查
        
        modules_to_test = [
            'models.py',
            'parser.py', 
            'utils.py',
            'widgets.py',
            'main_window.py'
        ]
        
        test_modules = [
            'test_models.py',
            'test_parser.py',
            'test_utils.py',
            'test_integration.py'
        ]
        
        print(f"主要模块数: {len(modules_to_test)}")
        print(f"测试模块数: {len(test_modules)}")
        print(f"测试覆盖率: {len(test_modules)/len(modules_to_test)*100:.1f}%")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='寄存器表格解析器测试运行器')
    parser.add_argument('--verbose', '-v', action='count', default=1,
                       help='增加输出详细程度')
    parser.add_argument('--performance', '-p', action='store_true',
                       help='运行性能测试')
    parser.add_argument('--coverage', '-c', action='store_true',
                       help='检查测试覆盖率')
    parser.add_argument('--test', '-t', type=str,
                       help='运行特定测试')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    try:
        if args.test:
            # 运行特定测试
            success = runner.run_specific_test(args.test)
        elif args.performance:
            # 运行性能测试
            runner.run_performance_tests()
            success = True
        elif args.coverage:
            # 检查测试覆盖率
            runner.check_test_coverage()
            success = True
        else:
            # 运行所有测试
            success = runner.run_tests(args.verbose)
        
        # 退出码
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行器出错: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()