"""
配置面板视图组件
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QFormLayout, QLineEdit, QPushButton, QCheckBox,
    QLabel, QComboBox, QFileDialog, QTextEdit, QToolTip, QSpinBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QRegExp, QTimer, pyqtSlot, QEvent
from PyQt5.QtGui import QRegExpValidator, QIcon
import time
import sys
import os
from utils.common_widgets import LabeledInput, FileSelector
from utils.multi_select_combobox import MultiSelectComboBox
from utils.autocomplete_widget import AutoCompleteLineEdit
from models.autocomplete_model import AutoCompleteModel
from models.config_template_model import RundirPlaceholderHandler
from utils.history_parser import HistoryParser

class ConfigPanel(QWidget):
    """配置面板，用于管理运行参数配置"""

    # 定义信号
    config_changed = pyqtSignal(dict)
    execute_command_requested = pyqtSignal(str, str)  # mode, case_name
    terminal_execute_requested = pyqtSignal(str, str)  # mode, case_name
    apply_history_requested = pyqtSignal(int)
    re_run_history_requested = pyqtSignal()
    select_fsdb_file_requested = pyqtSignal()
    clear_fsdb_file_requested = pyqtSignal()
    select_regr_file_requested = pyqtSignal()
    clear_regr_file_requested = pyqtSignal()
    get_seed_requested = pyqtSignal()
    parse_regr_command_requested = pyqtSignal()
    template_applied = pyqtSignal(dict)  # 应用配置模板信号

    def __init__(self, parent=None):
        """初始化配置面板"""
        super().__init__(parent)

        # 初始化自动补全系统
        self._init_autocomplete_system()

        self.init_ui()

        # 添加配置变更标志和上次更新时间
        self.config_changed_flag = False
        self.last_preview_update = time.time()
        self.last_user_input = time.time()
        self.input_delay = 1.0  # 用户输入后延迟更新的时间（秒）

        # 添加实时预览计时器
        self.preview_timer = QTimer()
        self.preview_timer.timeout.connect(self.check_preview_update)
        self.preview_timer.start(2000)  # 每2000ms检查一次是否需要更新预览，进一步减少CPU使用

        # 异步加载历史数据
        self._load_history_data_async()

        # 标记对象是否正在销毁
        self._is_destroying = False

        # 初始化配置模板模型
        from models.config_template_model import ConfigTemplateModel
        self.template_model = ConfigTemplateModel()
        self.current_templates = []

        # 连接模板模型信号
        self.template_model.templates_changed.connect(self.update_template_combo)

        # 加载配置模板
        self.template_model.load_templates()

    def _init_autocomplete_system(self):
        """初始化自动补全系统"""
        # 创建自动补全数据模型
        self.autocomplete_model = AutoCompleteModel()

        # 创建历史解析器
        self.history_parser = HistoryParser()

        # 连接数据加载完成信号
        self.autocomplete_model.data_loaded.connect(self._on_autocomplete_data_loaded)

    def _load_history_data_async(self):
        """异步加载历史数据到自动补全系统"""
        def load_worker():
            try:
                # 解析各种数据源
                history_file = "runsim_command_history.json"
                config_file = "runsim_config.json"
                regression_dirs = [".", "dv", "test"]  # 可以根据实际情况调整搜索目录

                # 解析所有数据源
                parsed_data = self.history_parser.parse_all_sources(
                    history_file=history_file if os.path.exists(history_file) else None,
                    config_file=config_file if os.path.exists(config_file) else None,
                    regression_dirs=regression_dirs
                )

                # 添加常见的dump层级选项
                if 'dump_level' not in parsed_data:
                    parsed_data['dump_level'] = set()
                
                # 添加常见的dump层级值
                common_dump_levels = [
                    "tb_top",                             # 顶层testbench
                    "tb_top.chip_top.dut.u_digital_top",                # dut.u_digital_top层级
                    "tb_top.chip_top.dut.u_digital_top.u_sys_apcpu",    # APCPU_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_ap",       # AP_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_ap_ai",    # AI_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_ap_dpu",   # DPU_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_ap_gpu",   # GPU_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_ap_mm",    # MM_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_cp_aud",   # AUD_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_ap_vdec",  # VDEC_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_dbg",      # DBG_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_pcie",     # PCIE_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_lp",       # LP_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_lp.u_pmu_top",   # PMU_TOP
                    "tb_top.chip_top.dut.u_digital_top.u_sys_pub",      # PUB_SYS
                    "tb_top.chip_top.dut.u_digital_top.u_sys_aon"       # AON_SYS
                ]
                
                for level in common_dump_levels:
                    parsed_data['dump_level'].add(level)

                # 使用QTimer.singleShot在主线程中添加数据
                self._parsed_data = parsed_data
                QTimer.singleShot(0, self._add_parsed_data_to_model)

            except Exception as e:
                print(f"加载历史数据失败: {e}")

        import threading
        thread = threading.Thread(target=load_worker, daemon=True)
        thread.start()

    @pyqtSlot()
    def _add_parsed_data_to_model(self):
        """在主线程中将解析的数据添加到自动补全模型"""
        try:
            if hasattr(self, '_parsed_data'):
                for field_type, values in self._parsed_data.items():
                    for value in values:
                        self.autocomplete_model.add_value(field_type, value)
                delattr(self, '_parsed_data')  # 清理临时数据
        except Exception as e:
            print(f"添加解析数据到模型失败: {e}")

    def _on_autocomplete_data_loaded(self):
        """自动补全数据加载完成回调"""
        print("自动补全数据加载完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        layout.setSpacing(2)
        layout.setContentsMargins(2, 2, 2, 2)

        # 添加历史命令区域
        layout.addLayout(self.create_history_layout())

        # 添加配置模板区域
        layout.addLayout(self.create_template_layout())

        # 添加基础参数组
        layout.addWidget(self.create_basic_group())

        # 添加波形配置组
        layout.addWidget(self.create_wave_group())

        # 添加回归测试组
        layout.addWidget(self.create_regr_group())

        # 添加后仿配置组
        layout.addWidget(self.create_post_group())

        # 添加工具按钮区域
        layout.addLayout(self.create_tool_btn_layout())

        self.setLayout(layout)

    def create_history_layout(self):
        """创建历史命令区域"""
        history_layout = QHBoxLayout()
        history_layout.setSpacing(2)

        history_label = QLabel("历史命令:")
        history_label.setMinimumWidth(70)

        self.history_combo = QComboBox()
        self.history_combo.setEditable(True)
        self.history_combo.currentIndexChanged.connect(self.apply_history_requested.emit)

        # 添加tooltip功能
        self.history_combo.setToolTipDuration(-1)  # 工具提示一直显示直到鼠标移开
        self.history_combo.installEventFilter(self)  # 安装事件过滤器以处理tooltip

        history_btn = QPushButton("↺")
        history_btn.setToolTip("重新执行该命令")
        history_btn.setFixedWidth(30)
        history_btn.clicked.connect(self.re_run_history_requested.emit)

        history_layout.addWidget(history_label)
        history_layout.addWidget(self.history_combo)
        history_layout.addWidget(history_btn)

        return history_layout

    def create_template_layout(self):
        """创建配置模板区域"""
        template_layout = QHBoxLayout()
        template_layout.setSpacing(5)

        template_label = QLabel("配置模板:")
        template_label.setMinimumWidth(70)

        self.template_combo = QComboBox()
        self.template_combo.setEditable(False)
        self.template_combo.addItem("选择配置模板...")
        self.template_combo.currentIndexChanged.connect(self.on_template_selected)

        # 添加tooltip功能
        self.template_combo.setToolTipDuration(-1)
        self.template_combo.installEventFilter(self)

        # 应用模板按钮
        apply_template_btn = QPushButton("应用")
        apply_template_btn.setToolTip("应用选中的配置模板")
        apply_template_btn.setFixedWidth(50)
        apply_template_btn.clicked.connect(self.apply_selected_template)

        # 管理模板按钮
        manage_template_btn = QPushButton("管理")
        manage_template_btn.setToolTip("管理配置模板")
        manage_template_btn.setFixedWidth(50)
        manage_template_btn.clicked.connect(self.open_template_manager)

        template_layout.addWidget(template_label)
        template_layout.addWidget(self.template_combo)
        template_layout.addWidget(apply_template_btn)
        template_layout.addWidget(manage_template_btn)

        return template_layout

    def create_basic_group(self):
        """创建基础参数组"""
        basic_group = QGroupBox("基础参数")
        basic_layout = QFormLayout()
        basic_layout.setSpacing(2)
        basic_layout.setContentsMargins(2, 6, 2, 2)

        # 使用自动补全输入框替换原有输入框
        self.base_input = AutoCompleteLineEdit(
            field_type='base',
            model=self.autocomplete_model,
            placeholder="输入BASE参数（可选）"
        )
        self.block_input = AutoCompleteLineEdit(
            field_type='block',
            model=self.autocomplete_model,
            placeholder="输入BLOCK参数（必填）"
        )
        self.case_input = AutoCompleteLineEdit(
            field_type='case',
            model=self.autocomplete_model,
            placeholder="输入用例名"
        )

        # 连接输入变更事件
        self.base_input.textChanged.connect(self.on_input_changed)
        self.block_input.textChanged.connect(self.on_input_changed)
        self.case_input.textChanged.connect(self.on_input_changed)

        # 连接值选择事件（用于记录使用历史）
        self.base_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('base', v))
        self.block_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('block', v))
        self.case_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('case', v))

        seed_layout = QHBoxLayout()
        seed_layout.setSpacing(2)

        self.seed_input = QLineEdit()
        self.seed_input.setPlaceholderText("输入仿真种子号（可选）")
        self.seed_input.setValidator(QRegExpValidator(QRegExp("[0-9]+")))
        self.seed_input.textChanged.connect(self.on_input_changed)
        # 添加验证逻辑，当输入无效时显示警告
        self.seed_input.textChanged.connect(self.validate_seed_input)

        self.get_seed_btn = QPushButton("获取种子号")
        self.get_seed_btn.clicked.connect(self.get_seed_requested.emit)
        self.get_seed_btn.setIcon(QIcon.fromTheme("edit-find", QIcon()))

        seed_layout.addWidget(self.seed_input)
        seed_layout.addWidget(self.get_seed_btn)

        rundir_other_layout = QHBoxLayout()
        rundir_other_layout.setSpacing(8)

        self.rundir_input = AutoCompleteLineEdit(
            field_type='rundir',
            model=self.autocomplete_model,
            placeholder="输入工作目录（可选）"
        )
        self.rundir_input.textChanged.connect(self.on_input_changed)
        self.rundir_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('rundir', v))

        self.bq_input = AutoCompleteLineEdit(
            field_type='bq_server',
            model=self.autocomplete_model,
            placeholder="输入服务器名称"
        )
        self.bq_input.textChanged.connect(self.on_input_changed)
        self.bq_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('bq_server', v))

        self.other_options_input = AutoCompleteLineEdit(
            field_type='other_options',
            model=self.autocomplete_model,
            placeholder="输入其他runsim选项（可选）"
        )
        self.other_options_input.textChanged.connect(self.on_input_changed)
        self.other_options_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('other_options', v))

        rundir_form = QFormLayout()
        rundir_form.setSpacing(2)
        rundir_form.addRow("工作目录（-rundir）:", self.rundir_input)

        bq_form = QFormLayout()
        bq_form.setSpacing(2)
        bq_form.addRow("提交服务器（-bq）:", self.bq_input)

        other_options_form = QFormLayout()
        other_options_form.setSpacing(2)
        other_options_form.addRow("其他选项:", self.other_options_input)

        rundir_other_layout.addLayout(rundir_form)
        rundir_other_layout.addLayout(bq_form)
        rundir_other_layout.addLayout(other_options_form)

        basic_layout.addRow("BASE:", self.base_input)
        basic_layout.addRow("BLOCK:", self.block_input)
        basic_layout.addRow("CASE:", self.case_input)
        basic_layout.addRow("种子号（-seed）:", seed_layout)
        basic_layout.addRow(rundir_other_layout)

        basic_group.setLayout(basic_layout)
        return basic_group

    def create_wave_group(self):
        """创建波形配置组，包含FSDB, VWDB, SVA, COV等选项"""
        wave_group = QGroupBox("波形配置")
        wave_layout = QVBoxLayout()
        wave_layout.setSpacing(2)
        wave_layout.setContentsMargins(2, 6, 2, 2)

        fsdb_layout = QVBoxLayout()
        fsdb_layout.setSpacing(2)

        fsdb_check_layout = QHBoxLayout()
        fsdb_check_layout.setSpacing(2)

        self.fsdb_check = QCheckBox("Dump FSDB波形（-fsdb）")
        self.vwdb_check = QCheckBox("Dump VWDB波形（-vwdb）")
        self.cl_check = QCheckBox("Clean INCA_libs（-cl）")
        self.fsdb_check.stateChanged.connect(self.toggle_fsdb_input)
        self.vwdb_check.stateChanged.connect(self.toggle_fsdb_input)

        # 连接复选框变更事件
        self.fsdb_check.stateChanged.connect(self.on_checkbox_changed)
        self.vwdb_check.stateChanged.connect(self.on_checkbox_changed)
        self.cl_check.stateChanged.connect(self.on_checkbox_changed)
        
        # 添加dump层级输入框
        dump_level_label = QLabel("dump层级：")
        self.dump_level_input = AutoCompleteLineEdit(
            field_type='dump_level',
            model=self.autocomplete_model,
            placeholder="输入dump层级（与TCL文件互斥）"
        )
        self.dump_level_input.textChanged.connect(self.on_dump_level_changed)
        self.dump_level_input.textChanged.connect(self.on_input_changed)
        self.dump_level_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('dump_level', v))
        
        fsdb_check_layout.addWidget(self.fsdb_check)
        fsdb_check_layout.addWidget(self.vwdb_check)
        fsdb_check_layout.addWidget(dump_level_label)
        fsdb_check_layout.addWidget(self.dump_level_input)
        fsdb_check_layout.addWidget(self.cl_check)
        fsdb_layout.addLayout(fsdb_check_layout)

        fsdb_file_layout = QHBoxLayout()
        fsdb_file_layout.setSpacing(2)

        self.fsdb_btn = QPushButton("选择TCL文件")
        self.fsdb_btn.setIcon(QIcon.fromTheme("document-open", QIcon()))
        self.fsdb_btn.clicked.connect(self.select_fsdb_file_requested.emit)

        self.fsdb_label = QLabel("未选择TCL文件")
        self.fsdb_label.setStyleSheet("color: #888; font-style: italic;")

        self.fsdb_clear_btn = QPushButton("清除")
        self.fsdb_clear_btn.clicked.connect(self.clear_fsdb_file_requested.emit)
        self.fsdb_btn.setEnabled(False)
        self.fsdb_clear_btn.setEnabled(False)

        fsdb_file_layout.addWidget(self.fsdb_btn)
        fsdb_file_layout.addWidget(self.fsdb_label)
        fsdb_file_layout.addWidget(self.fsdb_clear_btn)
        fsdb_layout.addLayout(fsdb_file_layout)
        wave_layout.addLayout(fsdb_layout)

        check_layout = QHBoxLayout()
        check_layout.setSpacing(2)

        self.sva_check = QCheckBox("Dump SVA断言（-dump_sva）")
        self.sim_only_check = QCheckBox("仅仿真（-R）")
        self.compile_only_check = QCheckBox("仅编译（-C）")
        self.cov_check = QCheckBox("收集覆盖率（-cov）")
        self.upf_check = QCheckBox("UPF仿真（-upf）")

        # 连接复选框变更事件
        self.sva_check.stateChanged.connect(self.on_checkbox_changed)
        self.sim_only_check.stateChanged.connect(self.on_checkbox_changed)
        self.compile_only_check.stateChanged.connect(self.on_checkbox_changed)
        self.cov_check.stateChanged.connect(self.on_checkbox_changed)
        self.upf_check.stateChanged.connect(self.on_checkbox_changed)

        check_layout.addWidget(self.sva_check)
        check_layout.addWidget(self.sim_only_check)
        check_layout.addWidget(self.compile_only_check)
        check_layout.addWidget(self.cov_check)
        check_layout.addWidget(self.upf_check)
        wave_layout.addLayout(check_layout)

        self.sim_only_check.stateChanged.connect(self.handle_mode_change)
        self.compile_only_check.stateChanged.connect(self.handle_mode_change)

        # 使用多选下拉框替换原有的dump_mem输入框
        self.dump_mem_input = MultiSelectComboBox()
        self.dump_mem_input.selectionChanged.connect(self.on_dump_mem_changed)

        self.wdd_input = AutoCompleteLineEdit(
            field_type='wdd',
            model=self.autocomplete_model,
            placeholder="输入时间（如：1ns）"
        )
        self.wdd_input.textChanged.connect(self.on_input_changed)
        self.wdd_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('wdd', v))

        self.simarg_input = AutoCompleteLineEdit(
            field_type='simarg',
            model=self.autocomplete_model,
            placeholder="输入仿真参数（可选）"
        )
        self.simarg_input.textChanged.connect(self.on_input_changed)
        self.simarg_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('simarg', v))

        self.cfg_def_input = AutoCompleteLineEdit(
            field_type='cfg_def',
            model=self.autocomplete_model,
            placeholder="输入配置定义（可选）"
        )
        self.cfg_def_input.textChanged.connect(self.on_input_changed)
        self.cfg_def_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('cfg_def', v))

        dump_row_layout = QHBoxLayout()
        dump_mem_form = QFormLayout()
        dump_mem_form.setSpacing(3)
        dump_mem_form.addRow("Dump Memory（-dump_mem）:", self.dump_mem_input)
        wdd_form = QFormLayout()
        wdd_form.setSpacing(3)
        wdd_form.addRow("波形Dump起始时间（-wdd）:", self.wdd_input)
        dump_row_layout.addLayout(dump_mem_form)
        dump_row_layout.addLayout(wdd_form)
        wave_layout.addLayout(dump_row_layout)

        param_row_layout = QHBoxLayout()
        simarg_form = QFormLayout()
        simarg_form.setSpacing(3)
        simarg_form.addRow("仿真参数（-simarg）:", self.simarg_input)
        cfg_def_form = QFormLayout()
        cfg_def_form.setSpacing(3)
        cfg_def_form.addRow("配置定义（-cfg_def）:", self.cfg_def_input)
        param_row_layout.addLayout(simarg_form)
        param_row_layout.addLayout(cfg_def_form)
        wave_layout.addLayout(param_row_layout)

        wave_group.setLayout(wave_layout)
        return wave_group

    def create_post_group(self):
        """创建后仿配置组，包含后仿参数下拉列表"""
        post_group = QGroupBox("后仿配置")
        post_layout = QHBoxLayout()
        post_layout.setSpacing(2)
        post_layout.setContentsMargins(2, 2, 2, 2)

        post_layout.addWidget(QLabel("后仿（-post）:"))
        self.post_input = QComboBox()
        self.post_input.setEditable(True)  # 允许用户输入自定义值

        # 添加常见的SDF选项
        post_options = [
            "",  # 空选项
            "sdf=fake",
            "sdf=pg_fake",
            "sdf=npg_f1_ssg",
            "sdf=npg_f2_ssg",
            "sdf=npg_f3_ssg",
            "sdf=npg_f4_ssg",
            "sdf=npg_f5_ssg",
            "sdf=npg_f6_ssg",
            "sdf=npg_f7_ssg",
            "sdf=npg_f1_ffg",
            "sdf=npg_f2_ffg",
            "sdf=npg_f3_ffg",
            "sdf=npg_f4_ffg",
            "sdf=npg_f5_ffg",
            "sdf=npg_f6_ffg",
            "sdf=npg_f7_ffg",
            "sdf=npg_f1_tt",
            "sdf=npg_f2_tt"
        ]
        self.post_input.addItems(post_options)

        # 设置占位符文本
        self.post_input.setCurrentText("")
        self.post_input.lineEdit().setPlaceholderText("选择或输入sdf=CORNER_NAME")

        # 连接信号
        self.post_input.currentTextChanged.connect(self.on_input_changed)
        self.post_input.editTextChanged.connect(self.on_input_changed)

        post_layout.addWidget(self.post_input)

        post_group.setLayout(post_layout)
        return post_group

    def create_regr_group(self):
        """创建回归测试配置组，包含回归列表文件选择功能和指令解析功能"""
        regr_group = QGroupBox("回归测试")
        regr_layout = QVBoxLayout()
        regr_layout.setSpacing(2)
        regr_layout.setContentsMargins(2, 6, 2, 2)

        # 回归文件选择区域
        file_layout = QHBoxLayout()
        file_layout.setSpacing(2)

        self.regr_btn = QPushButton("选择回归列表")
        self.regr_btn.setIcon(QIcon.fromTheme("document-open", QIcon()))
        self.regr_btn.clicked.connect(self.select_regr_file_requested.emit)

        self.regr_label = QLabel("未选择回归文件")
        self.regr_label.setStyleSheet("color: #888; font-style: italic;")

        self.clear_regr_btn = QPushButton("清除")
        self.clear_regr_btn.clicked.connect(self.clear_regr_file_requested.emit)
        self.clear_regr_btn.setEnabled(False)

        file_layout.addWidget(self.regr_btn)
        file_layout.addWidget(self.regr_label)
        file_layout.addWidget(self.clear_regr_btn)

        # 添加-fm复选框
        options_layout = QHBoxLayout()
        options_layout.setSpacing(2)

        # 添加-fm复选框
        self.fm_check = QCheckBox("回归FAIL用例(-fm)")
        self.fm_check.setToolTip("只回归之前失败的用例")
        self.fm_check.stateChanged.connect(self.on_checkbox_changed)
        options_layout.addWidget(self.fm_check)

        # 添加-regr_work输入框
        regr_work_layout = QHBoxLayout()
        regr_work_layout.setSpacing(2)
        regr_work_label = QLabel("回归路径(-regr_work):")
        self.regr_work_input = AutoCompleteLineEdit(
            field_type='regr_work',
            model=self.autocomplete_model,
            placeholder="输入回归路径（可选）"
        )
        self.regr_work_input.textChanged.connect(self.on_input_changed)
        self.regr_work_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('regr_work', v))
        regr_work_layout.addWidget(regr_work_label)
        regr_work_layout.addWidget(self.regr_work_input)
        options_layout.addLayout(regr_work_layout)

        # 添加-tag输入框
        tag_layout = QHBoxLayout()
        tag_layout.setSpacing(2)
        tag_label = QLabel("回归TAG(-tag):")
        self.tag_input = AutoCompleteLineEdit(
            field_type='tag',
            model=self.autocomplete_model,
            placeholder="输入回归TAG名称"
        )
        self.tag_input.textChanged.connect(self.on_input_changed)
        self.tag_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('tag', v))
        tag_layout.addWidget(tag_label)
        tag_layout.addWidget(self.tag_input)
        options_layout.addLayout(tag_layout)
        
        # 添加-nt输入框
        nt_layout = QHBoxLayout()
        nt_layout.setSpacing(2)
        nt_label = QLabel("回归nt(-nt):")
        self.nt_input = AutoCompleteLineEdit(
            field_type='nt',
            model=self.autocomplete_model,
            placeholder="输入不回归TAG名称"
        )
        self.nt_input.textChanged.connect(self.on_input_changed)
        self.nt_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('nt', v))
        nt_layout.addWidget(nt_label)
        nt_layout.addWidget(self.nt_input)
        options_layout.addLayout(nt_layout)
        
        # 添加-dashboard输入框
        dashboard_layout = QHBoxLayout()
        dashboard_layout.setSpacing(2)
        dashboard_label = QLabel("提交dashboard(-m):")
        self.dashboard_input = AutoCompleteLineEdit(
            field_type='dashboard',
            model=self.autocomplete_model,
            placeholder="输入DE TAG"
        )
        self.dashboard_input.textChanged.connect(self.on_input_changed)
        self.dashboard_input.value_selected.connect(lambda v: self._on_autocomplete_value_selected('dashboard', v))
        dashboard_layout.addWidget(dashboard_label)
        dashboard_layout.addWidget(self.dashboard_input)
        options_layout.addLayout(dashboard_layout)
        
        # 添加解析回归指令按钮
        parse_layout = QHBoxLayout()
        parse_layout.setSpacing(2)

        self.parse_regr_btn = QPushButton("解析回归指令")
        self.parse_regr_btn.setIcon(QIcon.fromTheme("document-edit", QIcon()))
        self.parse_regr_btn.clicked.connect(self.parse_regr_command_requested.emit)
        parse_layout.addWidget(self.parse_regr_btn)

        regr_layout.addLayout(file_layout)
        regr_layout.addLayout(options_layout)
        regr_layout.addLayout(parse_layout)

        regr_group.setLayout(regr_layout)
        return regr_group

    def create_batch_run_group(self):
        """创建 BATCH RUN 控件组"""
        batch_run_group = QGroupBox("BATCH RUN 模式")
        batch_run_layout = QHBoxLayout()  # 使用水平布局，将所有控件放在同一行
        batch_run_layout.setSpacing(15)  # 增加控件间距
        batch_run_layout.setContentsMargins(2, 2, 2, 2)

        # 启用 BATCH RUN 模式复选框
        self.batch_run_check = QCheckBox("启用BATCH RUN模式")
        self.batch_run_check.setToolTip(
            "<b>BATCH RUN模式</b><br/>"
            "在BATCH RUN模式下，会自动检查基础用例目录是否存在，<br/>"
            "并根据需要跳过编译步骤，直接执行仿真。<br/>"
            "若要强制更新编译基板，请勾选'强制更新编译基板'选项。<br/>"
            "可设置执行次数以使用不同随机种子多次运行同一用例。"
        )
        self.batch_run_check.stateChanged.connect(self.on_batch_run_mode_changed)
        self.batch_run_check.stateChanged.connect(self.on_checkbox_changed)

        # 强制更新编译基板复选框
        self.force_update_check = QCheckBox("强制更新编译基板")
        self.force_update_check.setEnabled(False)  # 默认禁用
        self.force_update_check.setToolTip(
            "强制重新编译基础用例，即使编译结果已存在。<br/>"
            "仅在启用BATCH RUN模式时可用。"
        )
        self.force_update_check.stateChanged.connect(self.on_checkbox_changed)
        self.force_update_check.stateChanged.connect(self.on_force_update_changed)

        # 执行次数标签和输入框
        self.execution_count_label = QLabel("执行次数:")
        self.execution_count_label.setEnabled(False)  # 默认禁用

        self.execution_count_spin = QSpinBox()
        self.execution_count_spin.setRange(1, 100)  # 允许1-100次执行
        self.execution_count_spin.setValue(1)  # 默认值为1
        self.execution_count_spin.setEnabled(False)  # 默认禁用
        self.execution_count_spin.setFixedWidth(60)  # 设置固定宽度
        self.execution_count_spin.setToolTip(
            "设置用例执行次数，每次执行将使用不同的随机种子号。<br/>"
            "每次执行将在独立的工作目录中进行，格式为：{用例名}_{种子号}<br/>"
            "仅在启用BATCH RUN模式时可用。"
        )
        self.execution_count_spin.valueChanged.connect(self.on_checkbox_changed)

        # 将所有控件添加到同一行
        batch_run_layout.addWidget(self.batch_run_check)
        batch_run_layout.addWidget(self.force_update_check)
        batch_run_layout.addWidget(self.execution_count_label)
        batch_run_layout.addWidget(self.execution_count_spin)
        batch_run_layout.addStretch()  # 添加弹簧，使控件靠左对齐

        batch_run_group.setLayout(batch_run_layout)
        return batch_run_group

    def on_batch_run_mode_changed(self, state):
        """处理 BATCH RUN 模式变化"""
        enabled = state == Qt.Checked
        self.force_update_check.setEnabled(enabled)

        # 启用/禁用执行次数相关控件
        self.execution_count_label.setEnabled(enabled)
        self.execution_count_spin.setEnabled(enabled)

        # 如果禁用 BATCH RUN 模式，同时取消强制更新选项
        if not enabled:
            self.force_update_check.setChecked(False)
            # 清理"其他选项"中的-R参数
            self._clean_r_option_from_other_options()

    def _clean_r_option_from_other_options(self):
        """清理"其他选项"中的-R参数"""
        current_options = self.other_options_input.text().strip()
        if not current_options:
            return

        # 分割选项
        parts = current_options.split()
        new_parts = []

        i = 0
        while i < len(parts):
            part = parts[i]
            if part == "-R":
                # 跳过 -R 选项及其参数
                if i + 1 < len(parts) and not parts[i+1].startswith("-"):
                    i += 2  # 跳过 -R 和其路径参数
                else:
                    i += 1  # 只跳过 -R
            else:
                new_parts.append(part)
                i += 1

        # 更新"其他选项"输入框
        cleaned_options = " ".join(new_parts).strip()
        self.other_options_input.setText(cleaned_options)

    def on_force_update_changed(self, state):
        """处理强制更新编译基板选项变化"""
        # 当强制更新选项变化时，重新生成命令预览
        # 这会触发命令重新生成，确保编译和仿真命令的正确性
        self.on_checkbox_changed(state)

    def create_tool_btn_layout(self):
        """创建工具按钮区域"""
        tool_btn_layout = QVBoxLayout()

        # 添加 BATCH RUN 控件区域
        batch_run_group = self.create_batch_run_group()
        tool_btn_layout.addWidget(batch_run_group)

        # 添加命令预览
        preview_group = QGroupBox("命令预览")
        preview_layout = QVBoxLayout()

        self.preview_edit = QTextEdit()
        self.preview_edit.setReadOnly(True)
        self.preview_edit.setFixedHeight(60)
        self.preview_edit.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 9pt;
                background-color: #2d2d2d;
                color: #e6e6e6;
                border: 1px solid #1a1a1a;
                border-radius: 3px;
                padding: 5px;
            }
        """)

        preview_layout.addWidget(self.preview_edit)
        preview_group.setLayout(preview_layout)
        tool_btn_layout.addWidget(preview_group)

        # 按钮布局
        btn_layout = QHBoxLayout()

        # 使用原有的按钮样式定义
        main_btn_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover { background-color: #45a049; }
            QPushButton:pressed { background-color: #3d8b40; }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }
        """

        self.run_btn = QPushButton("执行仿真和编译")
        self.run_btn.setStyleSheet(main_btn_style)
        self.run_btn.setIcon(QIcon.fromTheme("media-playbook-start", QIcon()))
        self.run_btn.clicked.connect(self.on_execute_button_clicked)

        btn_layout.addWidget(self.run_btn)

        # 添加"发射到终端执行"按钮
        terminal_btn_style = """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover { background-color: #1976D2; }
            QPushButton:pressed { background-color: #1565C0; }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }
        """

        self.terminal_btn = QPushButton("发射到终端执行")
        self.terminal_btn.setStyleSheet(terminal_btn_style)
        self.terminal_btn.setIcon(QIcon.fromTheme("utilities-terminal", QIcon()))
        self.terminal_btn.clicked.connect(self.on_terminal_execute_button_clicked)

        # 在Linux环境下启用，其他环境下禁用
        import sys
        if sys.platform != 'linux':
            self.terminal_btn.setEnabled(False)
            self.terminal_btn.setToolTip("此功能仅在Linux环境下可用")
        else:
            self.terminal_btn.setToolTip("将命令发送到选定的终端标签页执行")

        btn_layout.addWidget(self.terminal_btn)

        tool_btn_layout.addLayout(btn_layout)
        return tool_btn_layout

    def on_execute_button_clicked(self):
        """处理执行按钮点击事件，根据复选框状态确定执行模式"""
        # 根据复选框状态确定执行模式
        mode = "normal"
        if hasattr(self, 'sim_only_check') and self.sim_only_check.isChecked():
            mode = "R"
        elif hasattr(self, 'compile_only_check') and self.compile_only_check.isChecked():
            mode = "C"

        # 获取当前用例名称
        case_name = self.case_input.text() if hasattr(self, 'case_input') else ""

        # 发出执行命令信号
        self.execute_command_requested.emit(mode, case_name)

    def on_terminal_execute_button_clicked(self):
        """处理发射到终端执行按钮点击事件"""
        # 根据复选框状态确定执行模式
        mode = "normal"
        if hasattr(self, 'sim_only_check') and self.sim_only_check.isChecked():
            mode = "R"
        elif hasattr(self, 'compile_only_check') and self.compile_only_check.isChecked():
            mode = "C"

        # 获取当前用例名称
        case_name = self.case_input.text() if hasattr(self, 'case_input') else ""

        # 发出终端执行命令信号
        self.terminal_execute_requested.emit(mode, case_name)

    def _update_fsdb_button_state_silently(self):
        """静默更新fsdb按钮状态，不触发信号"""
        enabled = self.fsdb_check.isChecked() or self.vwdb_check.isChecked()

        # 只在状态真正发生变化时才更新按钮状态
        if self.fsdb_btn.isEnabled() != enabled:
            self.fsdb_btn.setEnabled(enabled)

    def toggle_fsdb_input(self, state=None):
        """启用/禁用 FSDB tcl 文件选择按钮，当勾选 FSDB 或 VWDB 时启用"""
        # 如果正在更新配置，忽略此事件，避免循环触发
        if hasattr(self, '_updating_config') and self._updating_config:
            return

        # 当 FSDB 或 VWDB 任一被选中时，启用 TCL 文件选择
        checkbox_enabled = self.fsdb_check.isChecked() or self.vwdb_check.isChecked()
        
        # 检查dump层级是否有输入，如果有则禁用TCL文件选择
        dump_level_has_input = hasattr(self, 'dump_level_input') and self.dump_level_input.text().strip()
        
        # 只有在复选框选中且没有dump层级输入时才启用TCL文件选择
        enabled = checkbox_enabled and not dump_level_has_input

        # 只在状态真正发生变化时才更新按钮状态
        if self.fsdb_btn.isEnabled() != enabled:
            self.fsdb_btn.setEnabled(enabled)
            if dump_level_has_input:
                self.fsdb_btn.setToolTip("dump层级已设置，TCL文件选择已禁用")
            else:
                self.fsdb_btn.setToolTip("选择TCL文件")

        # 只在禁用且当前有文件选择时才发出清除信号
        if not checkbox_enabled and self.fsdb_clear_btn.isEnabled():
            self.clear_fsdb_file_requested.emit()

    def handle_mode_change(self, state):
        """处理仅仿真和仅编译复选框的互斥，只允许单选"""
        sender = self.sender()
        if state == Qt.Checked:
            if sender == self.sim_only_check:
                self.compile_only_check.setChecked(False)
            else:
                self.sim_only_check.setChecked(False)

    def check_preview_update(self):
        """检查是否需要更新预览"""
        try:
            # 如果正在更新配置，跳过预览更新
            if hasattr(self, '_updating_config') and self._updating_config:
                return

            current_time = time.time()

            # 如果配置没有变化，或者距离上次用户输入时间不足延迟时间，则不更新
            # 在测试模式下，我们忽略延迟，立即更新预览
            is_test_mode = 'unittest' in sys.modules
            if not is_test_mode and not self.config_changed_flag and (current_time - self.last_user_input < self.input_delay):
                return

            # 更新预览
            self.update_preview()

            # 重置配置变更标志
            self.config_changed_flag = False
            self.last_preview_update = current_time

        except KeyboardInterrupt:
            # 用户中断，优雅地处理
            print("用户中断了预览更新检查")
            # 停止预览计时器
            if hasattr(self, 'preview_timer'):
                self.preview_timer.stop()

        except Exception as e:
            print(f"检查预览更新时出错: {str(e)}")

    def update_preview(self):
        """更新命令预览"""
        try:
            # 如果正在更新配置，跳过预览更新，避免循环触发
            if hasattr(self, '_updating_config') and self._updating_config:
                return

            # 如果正在更新预览，跳过，避免重复更新
            if hasattr(self, '_updating_preview') and self._updating_preview:
                return

            # 发出信号，请求生成命令预览
            config = self.get_current_config()
            self.config_changed.emit(config)

            # 注意：实际的命令生成将由控制器处理，这里只是触发信号

            # 在测试模式下，我们需要处理事件循环，确保预览更新
            if 'unittest' in sys.modules:
                from PyQt5.QtCore import QCoreApplication
                QCoreApplication.processEvents()

        except KeyboardInterrupt:
            # 用户中断，优雅地处理
            print("用户中断了预览更新")
            # 停止预览计时器
            if hasattr(self, 'preview_timer'):
                self.preview_timer.stop()

        except Exception as e:
            if hasattr(self, 'preview_edit'):
                self.preview_edit.setText(f"预览生成失败: {str(e)}")
                self.preview_edit.setStyleSheet("""
                    QTextEdit {
                        font-family: 'Consolas', 'Courier New', monospace;
                        font-size: 9pt;
                        background-color: #2d2d2d;
                        color: #ff7043;
                    }
                """)

    def on_input_changed(self):
        """处理输入变更事件"""
        # 如果正在更新配置，忽略此事件，避免循环触发
        if hasattr(self, '_updating_config') and self._updating_config:
            return

        # 标记配置已变更
        self.config_changed_flag = True
        # 记录最后用户输入时间
        self.last_user_input = time.time()

    def on_checkbox_changed(self, state):
        """处理复选框变更事件"""
        # 如果正在更新配置，忽略此事件，避免循环触发
        if hasattr(self, '_updating_config') and self._updating_config:
            return

        # 标记配置已变更
        self.config_changed_flag = True
        # 记录最后用户输入时间
        self.last_user_input = time.time()

    def on_dump_mem_changed(self, selected_options):
        """
        处理dump_mem选择变化

        Args:
            selected_options (list): 选中的选项列表
        """
        # 标记配置已变更
        self.config_changed_flag = True
        # 记录最后用户输入时间
        self.last_user_input = time.time()

    def on_dump_level_changed(self):
        """处理dump层级输入变更事件"""
        # 如果正在更新配置，忽略此事件，避免循环触发
        if hasattr(self, '_updating_config') and self._updating_config:
            return

        # 检查dump层级和TCL文件的互斥关系
        dump_level = self.dump_level_input.text().strip()
        if dump_level:
            # 如果输入了dump层级，清除TCL文件选择并禁用TCL文件按钮
            if hasattr(self, 'fsdb_label') and self.fsdb_label.text() != "未选择TCL文件":
                self.clear_fsdb_file_requested.emit()
            # 更新按钮状态提示
            self.fsdb_btn.setToolTip("dump层级已设置，TCL文件选择已禁用")
            self.fsdb_btn.setEnabled(False)
        else:
            # 如果清空了dump层级，恢复TCL文件选择功能
            enabled = self.fsdb_check.isChecked() or self.vwdb_check.isChecked()
            self.fsdb_btn.setEnabled(enabled)
            self.fsdb_btn.setToolTip("选择TCL文件")

        # 标记配置已变更
        self.config_changed_flag = True
        # 记录最后用户输入时间
        self.last_user_input = time.time()

    def validate_seed_input(self, text):
        """验证种子号输入，确保是数字"""
        if text and not text.isdigit():
            # 使用父窗口显示警告
            from PyQt5.QtWidgets import QMessageBox
            parent = self.window()
            QMessageBox.warning(parent, "输入格式错误", "种子号应为数字")

    def set_preview_text(self, text):
        """设置预览文本"""
        # 防止无限递归
        if hasattr(self, '_updating_preview') and self._updating_preview:
            return

        # 检查文本是否真正发生变化，避免不必要的更新
        if hasattr(self, 'preview_edit'):
            current_text = self.preview_edit.toPlainText()
            if current_text == text:
                return  # 文本相同，不需要更新

        self._updating_preview = True
        try:
            # 确保文本不为空
            if text is None:
                text = ""

            # 直接设置文本，不进行任何处理
            self.preview_edit.blockSignals(True)  # 阻止信号传递
            self.preview_edit.setText(text)
            self.preview_edit.blockSignals(False)  # 恢复信号传递

            # 确保文本可见
            self.preview_edit.ensureCursorVisible()

            # 根据命令长度调整颜色（只在文本变化时才调整）
            if len(text) > 200:
                new_style = """
                    QTextEdit {
                        font-family: 'Consolas', 'Courier New', monospace;
                        font-size: 9pt;
                        background-color: #2d2d2d;
                        color: #ffab91;
                    }
                """
            else:
                new_style = """
                    QTextEdit {
                        font-family: 'Consolas', 'Courier New', monospace;
                        font-size: 9pt;
                        background-color: #2d2d2d;
                        color: #e6e6e6;
                    }
                """

            # 只在样式真正需要变化时才设置
            current_style = self.preview_edit.styleSheet()
            if current_style != new_style:
                self.preview_edit.setStyleSheet(new_style)

            # 减少强制UI更新的频率，只在必要时更新
            # from PyQt5.QtCore import QCoreApplication
            # QCoreApplication.processEvents()
        finally:
            self._updating_preview = False

    def get_current_config(self):
        """获取当前配置"""
        return {
            "base": self.base_input.text(),
            "block": self.block_input.text(),
            "rundir": self.rundir_input.text(),
            "bq_server": self.bq_input.text(),
            "other_options": self.other_options_input.text(),
            "fsdb_checked": self.fsdb_check.isChecked(),
            "vwdb_checked": self.vwdb_check.isChecked(),
            "cl_checked": self.cl_check.isChecked(),
            "sva_checked": self.sva_check.isChecked(),
            "cov_checked": self.cov_check.isChecked(),
            "upf_checked": self.upf_check.isChecked(),
            "sim_only": self.sim_only_check.isChecked(),
            "compile_only": self.compile_only_check.isChecked(),
            "dump_mem": self.dump_mem_input.get_dump_mem_value(),
            "dump_level": self.dump_level_input.text() if hasattr(self, 'dump_level_input') else "",
            "wdd": self.wdd_input.text(),
            "seed": self.seed_input.text(),
            "simarg": self.simarg_input.text(),
            "cfg_def": self.cfg_def_input.text(),
            "post": self.post_input.currentText(),
            "fm_checked": self.fm_check.isChecked(),
            "regr_work": self.regr_work_input.text(),
            "tag": self.tag_input.text(),
            "nt": self.nt_input.text(),
            "dashboard": self.dashboard_input.text(),
            # 为了向后兼容，同时提供旧的字段名
            "fsdb": self.fsdb_check.isChecked(),
            "vwdb": self.vwdb_check.isChecked(),
            "cl": self.cl_check.isChecked(),
            "dump_sva": self.sva_check.isChecked(),
            "cov": self.cov_check.isChecked(),
            "upf": self.upf_check.isChecked(),
        }

    def update_config(self, config, force_update=False):
        """
        更新配置，支持增量更新以避免不必要的重复操作

        Args:
            config (dict): 配置数据
            force_update (bool): 是否强制更新所有字段，默认为False（增量更新）
        """
        # 防止在更新过程中触发信号，避免循环更新
        if hasattr(self, '_updating_config') and self._updating_config and not force_update:
            return

        self._updating_config = True

        # 暂停定时器，防止更新过程中触发预览更新
        timer_was_active = False
        if hasattr(self, 'preview_timer') and self.preview_timer.isActive():
            timer_was_active = True
            self.preview_timer.stop()

        try:
            # 阻止所有输入控件的信号传递，避免更新过程中触发配置变更信号
            widgets_to_block = [
                self.base_input, self.block_input, self.case_input, self.rundir_input,
                self.bq_input, self.other_options_input, self.wdd_input, self.seed_input,
                self.simarg_input, self.cfg_def_input, self.tag_input, self.nt_input,
                self.dashboard_input, self.post_input, self.fsdb_check, self.vwdb_check,
                self.cl_check, self.sva_check, self.cov_check, self.upf_check,
                self.sim_only_check, self.compile_only_check, self.fm_check,
                # 添加回归相关按钮，防止它们在更新过程中触发信号
                self.regr_btn, self.clear_regr_btn
            ]

            # 阻止所有控件的信号
            for widget in widgets_to_block:
                if widget:
                    widget.blockSignals(True)

            # 获取当前配置以进行比较（仅在增量更新模式下）
            current_config = {}
            if not force_update:
                current_config = self.get_current_config()

            # 文本输入框的增量更新
            text_fields = [
                ('base', self.base_input),
                ('block', self.block_input),
                ('rundir', self.rundir_input),
                ('bq_server', self.bq_input),
                ('other_options', self.other_options_input),
                ('wdd', self.wdd_input),
                ('seed', self.seed_input),
                ('simarg', self.simarg_input),
                ('cfg_def', self.cfg_def_input),
                ('regr_work', self.regr_work_input),
                ('tag', self.tag_input),
                ('nt', self.nt_input),
                ('dashboard', self.dashboard_input),
                ('dump_level', self.dump_level_input if hasattr(self, 'dump_level_input') else None)
            ]

            for field_name, widget in text_fields:
                if widget is None:  # 跳过不存在的控件
                    continue
                new_value = config.get(field_name, "")
                # 只在值真正发生变化时才更新
                if force_update or current_config.get(field_name, "") != new_value:
                    if hasattr(widget, 'set_text_programmatically'):
                        widget.set_text_programmatically(new_value)
                    else:
                        widget.setText(new_value)

            # 复选框的增量更新
            checkbox_fields = [
                ('fsdb_checked', self.fsdb_check),
                ('vwdb_checked', self.vwdb_check),
                ('cl_checked', self.cl_check),
                ('sva_checked', self.sva_check),
                ('cov_checked', self.cov_check),
                ('upf_checked', self.upf_check),
                ('sim_only', self.sim_only_check),
                ('compile_only', self.compile_only_check),
                ('fm_checked', self.fm_check),
                # 为了向后兼容，同时支持旧的字段名
                ('fsdb', self.fsdb_check),
                ('vwdb', self.vwdb_check),
                ('cl', self.cl_check),
                ('dump_sva', self.sva_check),
                ('cov', self.cov_check),
                ('upf', self.upf_check),
            ]

            for field_name, widget in checkbox_fields:
                new_value = config.get(field_name, False)
                # 只在值真正发生变化时才更新
                if force_update or current_config.get(field_name, False) != new_value:
                    widget.setChecked(new_value)

            # 下拉框的增量更新
            post_value = config.get("post", "")
            if force_update or current_config.get("post", "") != post_value:
                self.post_input.setCurrentText(post_value)

            # 处理dump_mem的设置（特殊处理）
            dump_mem_value = config.get("dump_mem", "")
            current_dump_mem = current_config.get("dump_mem", "")
            if force_update or current_dump_mem != dump_mem_value:
                if dump_mem_value:
                    # 将字符串分割为选项列表
                    options = [opt.strip() for opt in dump_mem_value.split() if opt.strip()]
                    self.dump_mem_input.set_selected_options(options)
                else:
                    self.dump_mem_input.clear_selection()

            # 处理fsdb文件相关的UI状态（特殊处理）
            fsdb_file = config.get("fsdb_file", "")
            if fsdb_file:
                # 有fsdb文件时，更新标签和启用清除按钮
                import os
                self.fsdb_label.setText(os.path.basename(fsdb_file))
                self.fsdb_clear_btn.setEnabled(True)
            else:
                # 没有fsdb文件时，重置标签和禁用清除按钮
                self.fsdb_label.setText("未选择TCL文件")
                self.fsdb_clear_btn.setEnabled(False)

            # 处理回归文件相关的UI状态（特殊处理）
            regr_file = config.get("regr_file", "")
            if regr_file:
                # 有回归文件时，更新标签和启用清除按钮
                import os
                self.regr_label.setText(os.path.basename(regr_file))
                self.clear_regr_btn.setEnabled(True)
            else:
                # 没有回归文件时，重置标签和禁用清除按钮
                self.regr_label.setText("未选择回归文件")
                self.clear_regr_btn.setEnabled(False)

        finally:
            # 恢复所有控件的信号传递
            for widget in widgets_to_block:
                if widget:
                    widget.blockSignals(False)

            # 在恢复信号后，手动更新fsdb按钮状态，避免信号触发时的闪烁
            self._update_fsdb_button_state_silently()

            # 重启定时器（如果之前是活跃的）
            if timer_was_active and hasattr(self, 'preview_timer'):
                self.preview_timer.start(2000)

            self._updating_config = False

    def update_history_combo(self, history):
        """更新历史命令下拉框"""
        self.history_combo.clear()
        for record in history:
            if isinstance(record, dict) and 'command' in record:
                command = record['command']
                # 添加命令到下拉框
                self.history_combo.addItem(command)
                # 设置完整命令作为工具提示数据
                self.history_combo.setItemData(
                    self.history_combo.count() - 1,
                    command,
                    Qt.ToolTipRole
                )

    def eventFilter(self, obj, event):
        """事件过滤器，处理历史命令下拉框的tooltip显示"""
        try:
            # 检查对象是否正在销毁
            if getattr(self, '_is_destroying', False):
                return False

            # 检查对象是否仍然有效
            if obj == self.history_combo and hasattr(self, 'history_combo'):
                if event.type() == QEvent.ToolTip:
                    # 获取当前选中的项目索引
                    index = self.history_combo.currentIndex()
                    if index >= 0 and index < self.history_combo.count():
                        # 获取完整命令文本
                        full_command = self.history_combo.itemData(index, Qt.ToolTipRole)
                        if full_command:
                            # 显示完整命令作为tooltip，支持多行显示
                            QToolTip.showText(event.globalPos(), full_command)
                            return True
                elif event.type() == QEvent.Enter:
                    # 当鼠标进入下拉框时，设置当前项的tooltip
                    index = self.history_combo.currentIndex()
                    if index >= 0 and index < self.history_combo.count():
                        full_command = self.history_combo.itemData(index, Qt.ToolTipRole)
                        if full_command:
                            self.history_combo.setToolTip(full_command)
                        else:
                            self.history_combo.setToolTip("")
        except (RuntimeError, AttributeError):
            # 对象已被销毁或属性不存在，忽略事件
            pass

        try:
            return super().eventFilter(obj, event)
        except (RuntimeError, AttributeError):
            # 父类方法调用失败，返回False
            return False

    def closeEvent(self, event):
        """处理关闭事件，清理资源"""
        try:
            self._is_destroying = True

            # 停止定时器
            if hasattr(self, 'preview_timer') and self.preview_timer:
                self.preview_timer.stop()
                self.preview_timer = None

            # 移除事件过滤器
            if hasattr(self, 'history_combo') and self.history_combo:
                self.history_combo.removeEventFilter(self)

        except (RuntimeError, AttributeError):
            # 忽略清理过程中的错误
            pass

        # 调用父类的关闭事件
        try:
            super().closeEvent(event)
        except (RuntimeError, AttributeError):
            pass

    def _on_autocomplete_value_selected(self, field_type: str, value: str):
        """处理自动补全值选择事件"""
        # 记录使用历史
        self.autocomplete_model.add_value(field_type, value)

        # 触发配置变更
        self.on_input_changed()

    def save_current_values_to_autocomplete(self):
        """将当前输入的值保存到自动补全历史"""
        # 获取当前配置
        config = self.get_current_config()

        # 保存各个字段的值
        field_mappings = {
            'base': config.get('base', ''),
            'block': config.get('block', ''),
            'bq_server': config.get('bq_server', ''),
            'rundir': config.get('rundir', ''),
            'cfg_def': config.get('cfg_def', ''),
            'simarg': config.get('simarg', ''),
            'wdd': config.get('wdd', ''),
            'other_options': config.get('other_options', ''),
            'tag': config.get('tag', ''),
            'nt': config.get('nt', ''),
            'dashboard': config.get('dashboard', '')
        }

        for field_type, value in field_mappings.items():
            if value and value.strip():
                self.autocomplete_model.add_value(field_type, value.strip())

    def get_autocomplete_stats(self):
        """获取自动补全统计信息"""
        stats = {}
        for field_type in self.autocomplete_model.FIELD_TYPES.keys():
            stats[field_type] = self.autocomplete_model.get_field_stats(field_type)
        return stats

    def clear_autocomplete_history(self, field_type: str = None):
        """清除自动补全历史"""
        if field_type:
            self.autocomplete_model.clear_field_data(field_type)
        else:
            self.autocomplete_model.clear_all_data()

    def export_autocomplete_data(self, file_path: str):
        """导出自动补全数据"""
        return self.autocomplete_model.export_data(file_path)

    def import_autocomplete_data(self, file_path: str, merge: bool = True):
        """导入自动补全数据"""
        return self.autocomplete_model.import_data(file_path, merge)

    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'autocomplete_model') and self.autocomplete_model:
            self.autocomplete_model.cleanup()

    def update_template_combo(self, templates):
        """更新配置模板下拉框"""
        self.current_templates = templates

        # 保存当前选中项
        current_text = self.template_combo.currentText()

        # 清空并重新填充
        self.template_combo.clear()
        self.template_combo.addItem("选择配置模板...")

        for template in templates:
            display_text = template['name']
            if template.get('description'):
                display_text += f" - {template['description'][:30]}..."

            self.template_combo.addItem(display_text)

            # 设置工具提示
            tooltip = f"名称: {template['name']}\n"
            if template.get('description'):
                tooltip += f"描述: {template['description']}\n"
            tooltip += f"创建时间: {template.get('created_at', '未知')}\n"
            tooltip += f"使用次数: {template.get('usage_count', 0)}"

            self.template_combo.setItemData(
                self.template_combo.count() - 1,
                tooltip,
                Qt.ToolTipRole
            )

            # 设置模板数据
            self.template_combo.setItemData(
                self.template_combo.count() - 1,
                template,
                Qt.UserRole
            )

        # 尝试恢复选中项
        index = self.template_combo.findText(current_text)
        if index >= 0:
            self.template_combo.setCurrentIndex(index)

    def on_template_selected(self, index):
        """处理模板选择"""
        if index <= 0:  # 第一项是提示文本
            return

        template = self.template_combo.itemData(index, Qt.UserRole)
        if template:
            # 这里不直接应用，只是选中，需要点击应用按钮才应用
            pass

    def apply_selected_template(self):
        """应用选中的配置模板"""
        index = self.template_combo.currentIndex()
        if index <= 0:
            return

        template = self.template_combo.itemData(index, Qt.UserRole)
        if template and 'config' in template:
            # 应用模板配置
            self.apply_template_config(template['config'])

            # 增加使用次数
            self.template_model.increment_usage_count(template['id'])

            # 发射信号
            self.template_applied.emit(template['config'])

            # 显示成功消息
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "成功",
                f"已应用配置模板: {template['name']}"
            )

    def apply_template_config(self, config):
        """应用模板配置到界面"""
        # 暂时禁用信号，避免频繁触发配置变更
        self._updating_config = True

        try:
            # 应用基础参数
            # 注意：跳过BASE和BLOCK字段，因为它们应该根据当前选择的case自动填充
            # if 'base' in config:
            #     self.base_input.setText(config['base'])
            # if 'block' in config:
            #     self.block_input.setText(config['block'])
            if 'rundir' in config:
                rundir_value = config['rundir']
                # 如果rundir包含{case_name}占位符，替换为当前用例名
                if '{case_name}' in rundir_value:
                    current_case_name = self.case_input.text().strip()
                    # 过滤掉多选提示
                    if current_case_name and not current_case_name.startswith("已选择"):
                        rundir_value = rundir_value.replace('{case_name}', current_case_name)
                self.rundir_input.setText(rundir_value)
            if 'bq_server' in config:
                self.bq_input.setText(config['bq_server'])
            if 'other_options' in config:
                self.other_options_input.setText(config['other_options'])

            # 应用复选框选项
            if 'fsdb' in config:
                self.fsdb_check.setChecked(config['fsdb'])
            if 'vwdb' in config:
                self.vwdb_check.setChecked(config['vwdb'])
            if 'cl' in config:
                self.cl_check.setChecked(config['cl'])
            if 'dump_sva' in config:
                self.sva_check.setChecked(config['dump_sva'])
            if 'cov' in config:
                self.cov_check.setChecked(config['cov'])
            if 'upf' in config:
                self.upf_check.setChecked(config['upf'])
            if 'sim_only' in config:
                self.sim_only_check.setChecked(config['sim_only'])
            if 'compile_only' in config:
                self.compile_only_check.setChecked(config['compile_only'])

            # 应用其他参数
            if 'dump_mem' in config:
                dump_mem_value = config['dump_mem']
                if dump_mem_value:
                    # 将字符串分割为选项列表
                    options = [opt.strip() for opt in dump_mem_value.split() if opt.strip()]
                    self.dump_mem_input.set_selected_options(options)
                else:
                    self.dump_mem_input.clear_selection()
            if 'wdd' in config:
                self.wdd_input.setText(config['wdd'])
            if 'seed' in config:
                self.seed_input.setText(config['seed'])
            if 'simarg' in config:
                self.simarg_input.setText(config['simarg'])
            if 'cfg_def' in config:
                self.cfg_def_input.setText(config['cfg_def'])
            if 'post' in config:
                self.post_input.setCurrentText(config['post'])
            if 'fm_checked' in config:
                self.fm_check.setChecked(config['fm_checked'])
            if 'regr_work' in config:
                self.regr_work_input.setText(config['regr_work'])
            if 'tag' in config:
                self.tag_input.setText(config['tag'])
            if 'nt' in config:
                self.nt_input.setText(config['nt'])
            if 'dashboard' in config:
                self.dashboard_input.setText(config['dashboard'])

        finally:
            self._updating_config = False

        # 触发配置变更
        self.config_changed.emit(self.get_current_config())

    def open_template_manager(self):
        """打开配置模板管理器"""
        from views.config_template_dialog import ConfigTemplateDialog

        # 获取当前配置
        current_config = self.get_current_config()

        # 创建并显示对话框
        dialog = ConfigTemplateDialog(self.template_model, current_config, self)
        dialog.template_applied.connect(self.apply_template_config)

        dialog.exec_()


