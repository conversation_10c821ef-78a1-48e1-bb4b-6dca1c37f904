# User Guide - Timing Violation Web Display

## Table of Contents

1. [Overview](#overview)
2. [System Requirements](#system-requirements)
3. [Installation](#installation)
4. [Quick Start](#quick-start)
5. [Data Sources](#data-sources)
6. [Using the Web Interface](#using-the-web-interface)
7. [Advanced Features](#advanced-features)
8. [Troubleshooting](#troubleshooting)
9. [Performance Optimization](#performance-optimization)
10. [FAQ](#faq)

## Overview

The Timing Violation Web Display system provides a centralized web interface for reviewing confirmed timing violation records. Instead of manually opening multiple Excel files, project leaders can use this tool to:

- View all timing violations in a single interface
- Filter violations by corner and case
- Track confirmation status and statistics
- Export data for further analysis

### Key Features

- **Unified View**: All violations from multiple sources in one place
- **Smart Filtering**: Filter by corner, case, and confirmation status
- **Real-time Statistics**: Live updates of confirmation rates and counts
- **Performance Optimized**: Handles large datasets (10,000+ records) efficiently
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **No Server Required**: Static files that work directly in web browsers

## System Requirements

### Minimum Requirements

- **Operating System**: Windows 10+, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Python**: Version 3.8 or higher
- **Memory**: 4GB RAM (8GB recommended for large datasets)
- **Storage**: 1GB free space for data and web files
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, or Edge 90+

### Python Dependencies

```
openpyxl>=3.0.0      # Excel file processing
pathlib              # File path operations (built-in)
sqlite3              # Database access (built-in)
json                 # JSON processing (built-in)
logging              # Logging support (built-in)
```

## Installation

### Step 1: Install Python Dependencies

```bash
# Navigate to the web_display directory
cd plugins/user/timing_violation/web_display

# Install required packages
pip install openpyxl>=3.0.0

# Or install from requirements file (if available)
pip install -r requirements_web.txt
```

### Step 2: Verify Installation

```bash
# Test the installation
python -c "import openpyxl; print('Dependencies installed successfully')"
```

### Step 3: Check Directory Structure

Ensure your directory structure looks like this:

```
VIOLATION_CHECK/
├── timing_violations.db          # SQLite database (optional)
├── corner1_case1.xlsx           # Excel files (optional)
├── corner1_case2.xlsx
└── ...

plugins/user/timing_violation/
├── web_display/                 # Main module
├── generate_web_data.py         # Launcher script
└── ...
```

## Quick Start

### Method 1: Using the Launcher Script (Recommended)

```bash
# Navigate to the timing_violation directory
cd plugins/user/timing_violation

# Generate web data with default settings
python generate_web_data.py

# The web interface will be created at:
# VIOLATION_CHECK/web_display/index.html
```

### Method 2: Using Python API

```python
from web_display.data_exporter import DataExporter

# Initialize the exporter
exporter = DataExporter("VIOLATION_CHECK")

# Export all data
success = exporter.export_all_data()

if success:
    print("Web interface ready at VIOLATION_CHECK/web_display/index.html")
else:
    print("Export failed - check the logs")
```

### Step 3: Open the Web Interface

1. Navigate to `VIOLATION_CHECK/web_display/`
2. Double-click `index.html` to open in your default browser
3. Or use a local web server for better performance:

```bash
# Using Python's built-in server
cd VIOLATION_CHECK/web_display
python -m http.server 8000

# Then open http://localhost:8000 in your browser
```

## Data Sources

The system supports two data sources and automatically chooses the best available option:

### Primary Source: Excel Files

**Location**: `VIOLATION_CHECK/` directory and subdirectories

**Supported Formats**: `.xlsx` and `.xls` files

**Expected Structure**:
- Column 1: 序号 (NUM) - Violation number
- Column 2: 层级路径 (Hier) - Hierarchy path  
- Column 3: 时间(ns) (Time) - Time in nanoseconds
- Column 4: 检查信息 (Check Info) - Check information
- Column 5: 状态 (Status) - Confirmation status
- Column 6: 确认人 (Confirmer) - Person who confirmed
- Column 7: 确认结果 (Result) - Confirmation result
- Column 8: 确认理由 (Reason) - Confirmation reason
- Column 9: 确认时间 (Confirmed At) - Confirmation timestamp

**File Naming**: The system extracts corner and case information from file paths and names.

### Fallback Source: SQLite Database

**Location**: `VIOLATION_CHECK/timing_violations.db`

**Usage**: Automatically used when Excel files are not available or as supplementary data.

**Schema**: Supports multiple table structures and adapts automatically.

## Using the Web Interface

### Main Interface Components

#### 1. Statistics Dashboard

Located at the top of the page, showing:
- **Total Violations**: Overall count of violations
- **Confirmed**: Number of confirmed violations
- **Pending**: Number of pending violations  
- **Confirmation Rate**: Percentage of confirmed violations

#### 2. Filter Controls

**Corner Filter**: 
- Select "All Corners" to view all violations
- Choose specific corner to filter violations

**Case Filter**:
- Automatically updates based on selected corner
- Select "All Cases" or specific case name

**Status Filter**:
- "All Status": Show all violations
- "Confirmed": Show only confirmed violations
- "Pending": Show only pending violations

#### 3. Data Table

**Features**:
- Sortable columns (click column headers)
- Search functionality (search box)
- Pagination controls
- Responsive design for mobile devices

**Columns**:
- NUM: Violation number
- Hier: Hierarchy path (truncated with tooltip)
- Time (ns): Timing value with 3 decimal precision
- Check Info: Check information (truncated with tooltip)
- Status: Color-coded status badges
- Confirmer: Person who confirmed
- Result: Confirmation result
- Reason: Confirmation reason (truncated with tooltip)
- Confirmed At: Timestamp in local format
- Corner: Corner badge
- Case: Case badge

### Basic Operations

#### Filtering Data

1. **Filter by Corner**:
   - Select corner from dropdown
   - Case filter automatically updates
   - Table refreshes with filtered data

2. **Filter by Case**:
   - Select case from dropdown (after selecting corner)
   - Table shows violations for selected corner/case

3. **Filter by Status**:
   - Choose "Confirmed" to see only confirmed violations
   - Choose "Pending" to see unconfirmed violations

#### Searching

- Use the search box to find specific violations
- Search works across all visible columns
- Search is case-insensitive

#### Sorting

- Click any column header to sort
- Click again to reverse sort order
- Multiple column sorting available (hold Shift)

#### Clearing Filters

- Click "Clear Filters" button to reset all filters
- Click "Refresh Data" to reload data from source

### Advanced Features

#### Performance Mode

For large datasets (10,000+ records):
- Virtual scrolling automatically enabled
- Data loaded in chunks for smooth performance
- Memory optimization runs automatically

#### Responsive Design

- **Desktop**: Full table with all columns
- **Tablet**: Horizontal scrolling for table
- **Mobile**: Stacked card layout for better readability

#### Keyboard Navigation

- **Tab**: Navigate between filter controls
- **Enter**: Apply filter changes
- **Escape**: Clear current search
- **Arrow Keys**: Navigate table cells

## Performance Optimization

### For Large Datasets

#### Automatic Optimizations

1. **Data Pagination**: Large datasets split into 1000-record chunks
2. **Virtual Scrolling**: Only visible rows rendered
3. **Intelligent Caching**: Frequently accessed data cached
4. **Memory Cleanup**: Automatic cleanup of unused data

#### Manual Optimizations

1. **Use Specific Filters**: Filter by corner/case to reduce data load
2. **Close Unused Tabs**: Free up browser memory
3. **Use Modern Browser**: Chrome/Firefox perform better than older browsers
4. **Local Web Server**: Use `python -m http.server` for better performance

### Performance Monitoring

The system includes built-in performance monitoring:

```javascript
// Check memory usage (in browser console)
console.log(window.violationApp.getMemoryStats());

// Monitor filter performance
// Filter change times are logged to console
```

### Recommended Limits

- **Optimal**: Up to 5,000 violations (instant response)
- **Good**: 5,000-25,000 violations (< 2 second response)
- **Acceptable**: 25,000-50,000 violations (< 5 second response)
- **Maximum**: 50,000+ violations (may require patience)

## Troubleshooting

### Common Issues

#### 1. "No violation data found"

**Symptoms**: Empty table, no statistics shown

**Causes**:
- No Excel files in VIOLATION_CHECK directory
- Database file missing or corrupted
- Incorrect file permissions

**Solutions**:
```bash
# Check if files exist
ls -la VIOLATION_CHECK/

# Check file permissions
chmod 644 VIOLATION_CHECK/*.xlsx
chmod 644 VIOLATION_CHECK/timing_violations.db

# Regenerate data
python generate_web_data.py --verbose
```

#### 2. "Failed to load data" error

**Symptoms**: Error modal appears when opening web interface

**Causes**:
- Corrupted JSON data files
- Browser security restrictions
- Network connectivity issues

**Solutions**:
```bash
# Regenerate web data
python generate_web_data.py --force

# Check generated files
ls -la VIOLATION_CHECK/web_display/data/

# Use local web server
cd VIOLATION_CHECK/web_display
python -m http.server 8000
```

#### 3. Slow performance

**Symptoms**: Long loading times, unresponsive interface

**Causes**:
- Very large dataset
- Insufficient browser memory
- Multiple browser tabs open

**Solutions**:
- Close other browser tabs
- Use corner/case filters to reduce data
- Restart browser
- Use Chrome or Firefox for better performance

#### 4. Excel parsing errors

**Symptoms**: "Excel parsing failed" in logs

**Causes**:
- Unsupported Excel format
- Missing required columns
- Corrupted Excel files

**Solutions**:
```bash
# Check Excel file format
python -c "
from openpyxl import load_workbook
wb = load_workbook('VIOLATION_CHECK/your_file.xlsx')
print('File is valid')
"

# Validate column structure
python generate_web_data.py --validate
```

#### 5. Database connection errors

**Symptoms**: "Database connection failed" in logs

**Causes**:
- Database file locked by another process
- Corrupted database file
- Insufficient permissions

**Solutions**:
```bash
# Check database file
sqlite3 VIOLATION_CHECK/timing_violations.db ".tables"

# Check file permissions
chmod 644 VIOLATION_CHECK/timing_violations.db

# Test database connection
python -c "
import sqlite3
conn = sqlite3.connect('VIOLATION_CHECK/timing_violations.db')
print('Database connection successful')
conn.close()
"
```

### Debug Mode

Enable debug mode for detailed troubleshooting:

```bash
# Enable verbose logging
python generate_web_data.py --verbose --debug

# Check log file
cat web_data_generation.log

# Enable browser debug mode
# Open browser console (F12) and check for JavaScript errors
```

### Log Files

Check these log files for detailed error information:

- `web_data_generation.log`: Data export logs
- Browser console: JavaScript errors and performance info
- System logs: File permission and access errors

### Getting Help

If you continue to experience issues:

1. **Check the FAQ section** below
2. **Review the log files** for specific error messages
3. **Test with sample data** to isolate the issue
4. **Contact support** with log files and system information

## FAQ

### General Questions

**Q: Do I need a web server to use this tool?**
A: No, the web interface works directly in browsers. However, a local web server provides better performance for large datasets.

**Q: Can multiple users access the same data?**
A: Yes, copy the generated `web_display` folder to a shared location or web server.

**Q: How often should I regenerate the web data?**
A: Regenerate whenever new Excel files are added or violation confirmations are updated.

**Q: Does this tool modify my original data?**
A: No, it only reads data and creates separate web files. Original Excel files and database remain unchanged.

### Data Questions

**Q: What Excel formats are supported?**
A: Both `.xlsx` (Excel 2007+) and `.xls` (Excel 97-2003) formats are supported.

**Q: Can I use custom column names?**
A: The system supports both Chinese and English column headers. See the API documentation for the complete list.

**Q: What happens if some Excel files are corrupted?**
A: The system logs errors and continues processing other files. Check the logs for specific issues.

**Q: Can I exclude certain files from processing?**
A: Currently, all Excel files in the VIOLATION_CHECK directory are processed. Move files you want to exclude to a different location.

### Performance Questions

**Q: How many violations can the system handle?**
A: The system is optimized for up to 50,000 violations. Larger datasets may require additional optimization.

**Q: Why is the initial load slow?**
A: First-time data processing can be slow for large datasets. Subsequent loads use cached data and are much faster.

**Q: Can I improve performance?**
A: Yes, use specific filters, close unused browser tabs, and consider using a local web server for large datasets.

### Technical Questions

**Q: Can I customize the web interface?**
A: Yes, modify the CSS and JavaScript files in the `web_template` directory before generating the web data.

**Q: Is there an API for programmatic access?**
A: Yes, see the API documentation for details on the Python API. A REST API is planned for future releases.

**Q: Can I integrate this with other tools?**
A: Yes, the JSON data files can be consumed by other applications. See the API documentation for data structure details.

**Q: How do I backup my data?**
A: Backup the entire VIOLATION_CHECK directory, including Excel files, database, and generated web files.

### Troubleshooting Questions

**Q: The web page shows "Loading..." forever**
A: Check browser console for JavaScript errors. Try regenerating the data or using a different browser.

**Q: Filters don't work properly**
A: Clear browser cache and regenerate web data. Check that corner/case information is correctly extracted from file names.

**Q: Statistics don't match my expectations**
A: Verify that all Excel files are being processed correctly. Check the logs for parsing errors or skipped files.

**Q: Can I recover if I accidentally delete web files?**
A: Yes, simply run the data generation script again to recreate all web files from the original data sources.

## Support and Updates

### Getting Support

For technical support:
1. Check this user guide and troubleshooting section
2. Review log files for specific error messages
3. Test with minimal data to isolate issues
4. Contact your system administrator with detailed error information

### Updates and Maintenance

- **Regular Updates**: Regenerate web data when new violations are added
- **Performance Monitoring**: Monitor browser performance for large datasets
- **Backup Strategy**: Regular backups of VIOLATION_CHECK directory
- **Browser Updates**: Keep browsers updated for best performance and security

### Version Information

Check the version information in generated files:
- Look for `api_version` in `data/index.json`
- Check browser console for system information
- Review `README.md` files for component versions

---

*This user guide covers the essential aspects of using the Timing Violation Web Display system. For technical details and API information, refer to the API Documentation.*