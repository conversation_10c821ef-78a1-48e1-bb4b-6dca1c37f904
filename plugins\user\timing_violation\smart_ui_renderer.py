"""
Smart UI Rendering System for Timing Violation Plugin

Provides intelligent rendering mode selection based on violation count optimization.
Implements adaptive UI rendering strategies for different dataset sizes.
"""

import time
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from PyQt5.QtWidgets import (
    QWidget, Q<PERSON><PERSON>Layout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QAbstractItemView, QHeaderView, QScrollArea, QFrame, QLabel, QPushButton,
    QTableView, QStyledItemDelegate, QApplication
)
from PyQt5.QtCore import Qt, QObject, pyqtSignal, QTimer, QAbstractTableModel, QModelIndex, QVariant, QRect, QSize
from PyQt5.QtGui import QColor, QFont, QPainter, QFontMetrics


class RenderingMode(Enum):
    """Rendering mode enumeration"""
    STANDARD_TABLE = "standard_table"
    HIGH_PERFORMANCE_TABLE = "high_performance_table"
    VIRTUAL_TABLE = "virtual_table"
    PAGINATED_TABLE = "paginated_table"
    PAGINATED_VIRTUAL_TABLE = "paginated_virtual_table"


class RenderingConfiguration:
    """Configuration for rendering modes"""
    
    def __init__(self, mode: RenderingMode, violation_count: int, system_capabilities: Dict):
        self.mode = mode
        self.violation_count = violation_count
        self.system_capabilities = system_capabilities
        
        # Configure based on mode and violation count
        self._configure_parameters()
    
    def _configure_parameters(self):
        """Configure rendering parameters based on mode and violation count"""
        if self.mode == RenderingMode.STANDARD_TABLE:
            self.page_size = None
            self.virtual_buffer_size = None
            self.lazy_loading_enabled = False
            self.caching_strategy = "none"
            self.update_frequency_ms = 16  # 60 FPS
            self.max_visible_rows = self.violation_count
            
        elif self.mode == RenderingMode.HIGH_PERFORMANCE_TABLE:
            self.page_size = min(500, self.violation_count)
            self.virtual_buffer_size = None
            self.lazy_loading_enabled = False
            self.caching_strategy = "basic"
            self.update_frequency_ms = 33  # 30 FPS
            self.max_visible_rows = self.page_size
            
        elif self.mode == RenderingMode.VIRTUAL_TABLE:
            self.page_size = None
            self.virtual_buffer_size = min(200, self.violation_count)
            self.lazy_loading_enabled = True
            self.caching_strategy = "intelligent"
            self.update_frequency_ms = 16  # 60 FPS
            self.max_visible_rows = self.virtual_buffer_size
            
        elif self.mode == RenderingMode.PAGINATED_TABLE:
            self.page_size = self._calculate_optimal_page_size()
            self.virtual_buffer_size = None
            self.lazy_loading_enabled = True
            self.caching_strategy = "page_based"
            self.update_frequency_ms = 33  # 30 FPS
            self.max_visible_rows = self.page_size
            
        elif self.mode == RenderingMode.PAGINATED_VIRTUAL_TABLE:
            self.page_size = self._calculate_optimal_page_size()
            self.virtual_buffer_size = min(100, self.page_size)
            self.lazy_loading_enabled = True
            self.caching_strategy = "hybrid"
            self.update_frequency_ms = 50  # 20 FPS
            self.max_visible_rows = self.virtual_buffer_size
    
    def _calculate_optimal_page_size(self) -> int:
        """Calculate optimal page size based on violation count and system capabilities"""
        if self.violation_count > 50000:
            return 50
        elif self.violation_count > 20000:
            return 100
        elif self.violation_count > 5000:
            return 200
        else:
            return 500


class VirtualizedTableModel(QAbstractTableModel):
    """
    Virtualized table model for efficient handling of medium datasets (2K-20K violations).
    Implements intelligent row caching and optimized cell rendering.
    """
    
    def __init__(self, violations_data: List[Dict], parent=None):
        super().__init__(parent)
        self._violations_data = violations_data
        self._headers = ["NUM", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "操作"]
        
        # Intelligent row caching based on violation data size
        self._cache_size = min(500, max(100, len(violations_data) // 20))  # 5% of data or 100-500 rows
        self._row_cache = {}
        self._cache_access_order = []
        
        # Pre-computed formatting for performance
        self._status_colors = {
            'confirmed': QColor(144, 238, 144),  # Light green
            'ignored': QColor(255, 182, 193),    # Light red
            'pending': QColor(255, 255, 224)     # Light yellow
        }
        
        self._status_text_map = {
            'pending': '待确认',
            'confirmed': '已确认', 
            'ignored': '已忽略'
        }
        
        self._result_text_map = {
            'pass': '通过',
            'issue': '有问题',
            '': ''
        }
    
    def rowCount(self, parent=QModelIndex()):
        return len(self._violations_data)
    
    def columnCount(self, parent=QModelIndex()):
        return len(self._headers)
    
    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self._headers[section]
        return QVariant()
    
    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or index.row() >= len(self._violations_data):
            return QVariant()
        
        row = index.row()
        column = index.column()
        
        # Use cached data if available
        cache_key = (row, column, role)
        if cache_key in self._row_cache:
            self._update_cache_access(cache_key)
            return self._row_cache[cache_key]
        
        # Get violation data
        violation = self._violations_data[row]
        result = self._get_cell_data(violation, column, role)
        
        # Cache the result
        self._cache_cell_data(cache_key, result)
        
        return result
    
    def _get_cell_data(self, violation: Dict, column: int, role: int):
        """Get cell data with optimized formatting for violation-specific data"""
        if role == Qt.DisplayRole:
            if column == 0:  # NUM
                return str(violation.get('num', ''))
            elif column == 1:  # 层级路径
                return violation.get('hier', '')
            elif column == 2:  # 时间(ns) - optimized time formatting
                time_fs = violation.get('time_fs', 0)
                if time_fs:
                    time_ns = time_fs / 1000000
                    return f"{time_ns:.3f}"
                return "0.000"
            elif column == 3:  # 检查信息
                return violation.get('check_info', '')
            elif column == 4:  # 状态 - optimized status colors
                status = violation.get('status', 'pending')
                return self._status_text_map.get(status, status)
            elif column == 5:  # 确认人
                return violation.get('confirmer', '')
            elif column == 6:  # 确认结果
                result = violation.get('result', '')
                return self._result_text_map.get(result, result)
            elif column == 7:  # 操作
                status = violation.get('status', 'pending')
                return "确认" if status == 'pending' else "编辑"
                
        elif role == Qt.BackgroundRole and column == 4:  # 状态列背景色
            status = violation.get('status', 'pending')
            return self._status_colors.get(status, self._status_colors['pending'])
            
        elif role == Qt.ForegroundRole:
            status = violation.get('status', 'pending')
            if status in ['confirmed', 'ignored']:
                return QColor(128, 128, 128)  # Gray for completed items
            return QColor(0, 0, 0)  # Black for pending items
            
        elif role == Qt.TextAlignmentRole:
            if column in [0, 2, 4, 5, 6]:  # NUM, 时间, 状态, 确认人, 确认结果
                return Qt.AlignCenter
            return Qt.AlignLeft | Qt.AlignVCenter
            
        elif role == Qt.ToolTipRole:
            if column == 1:  # 层级路径
                return violation.get('hier', '')
            elif column == 3:  # 检查信息
                return violation.get('check_info', '')
        
        return QVariant()
    
    def _cache_cell_data(self, cache_key, data):
        """Cache cell data with LRU eviction"""
        if len(self._row_cache) >= self._cache_size:
            # Remove least recently used item
            oldest_key = self._cache_access_order.pop(0)
            self._row_cache.pop(oldest_key, None)
        
        self._row_cache[cache_key] = data
        self._cache_access_order.append(cache_key)
    
    def _update_cache_access(self, cache_key):
        """Update cache access order for LRU"""
        if cache_key in self._cache_access_order:
            self._cache_access_order.remove(cache_key)
            self._cache_access_order.append(cache_key)
    
    def get_violation_at_row(self, row: int) -> Optional[Dict]:
        """Get violation data at specific row"""
        if 0 <= row < len(self._violations_data):
            return self._violations_data[row]
        return None
    
    def clear_cache(self):
        """Clear the row cache"""
        self._row_cache.clear()
        self._cache_access_order.clear()


class ViolationActionDelegate(QStyledItemDelegate):
    """Custom delegate for handling action buttons in virtualized table"""
    
    action_clicked = pyqtSignal(int, str, str)  # row, violation_id, action_type
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._button_rects = {}  # Cache button rectangles
    
    def paint(self, painter, option, index):
        if index.column() == 7:  # Action column
            # Get violation data
            model = index.model()
            violation = model.get_violation_at_row(index.row())
            if not violation:
                return
            
            status = violation.get('status', 'pending')
            action_text = "确认" if status == 'pending' else "编辑"
            
            # Set up button appearance
            button_rect = QRect(option.rect.x() + 5, option.rect.y() + 5, 
                              option.rect.width() - 10, option.rect.height() - 10)
            
            # Cache button rectangle for click detection
            self._button_rects[index.row()] = button_rect
            
            # Draw button
            painter.save()
            
            if status == 'pending':
                painter.fillRect(button_rect, QColor(74, 158, 255))  # Blue
                painter.setPen(QColor(255, 255, 255))  # White text
            else:
                painter.fillRect(button_rect, QColor(240, 240, 240))  # Light gray
                painter.setPen(QColor(128, 128, 128))  # Gray text
            
            # Draw button text
            painter.drawText(button_rect, Qt.AlignCenter, action_text)
            painter.restore()
        else:
            super().paint(painter, option, index)
    
    def editorEvent(self, event, model, option, index):
        if index.column() == 7 and event.type() == event.MouseButtonPress:
            # Check if click is within button bounds
            button_rect = self._button_rects.get(index.row())
            if button_rect and button_rect.contains(event.pos()):
                violation = model.get_violation_at_row(index.row())
                if violation:
                    violation_id = violation.get('id', '')
                    status = violation.get('status', 'pending')
                    self.action_clicked.emit(index.row(), violation_id, status)
                return True
        
        return super().editorEvent(event, model, option, index)


class VirtualizedTableView(QTableView):
    """
    Virtualized table view for medium datasets (2K-20K violations).
    Implements virtual scrolling with intelligent row caching and optimized cell rendering.
    """
    
    # Signals
    cell_double_clicked = pyqtSignal(int, int)  # row, column
    action_button_clicked = pyqtSignal(str, str)  # violation_id, action_type
    
    def __init__(self, violations_data: List[Dict], parent=None):
        super().__init__(parent)
        
        # Create virtualized model
        self.model = VirtualizedTableModel(violations_data, self)
        self.setModel(self.model)
        
        # Set up custom delegate for action buttons
        self.action_delegate = ViolationActionDelegate(self)
        self.action_delegate.action_clicked.connect(self._handle_action_clicked)
        self.setItemDelegateForColumn(7, self.action_delegate)  # Action column
        
        # Configure table properties for optimal performance
        self._configure_table()
        
        # Set up column widths
        self._setup_columns()
        
        # Performance optimization settings
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        self.setSortingEnabled(False)  # Disable sorting for better performance
        
        # Enable virtual scrolling optimizations
        self.setVerticalScrollMode(QAbstractItemView.ScrollPerPixel)
        self.setHorizontalScrollMode(QAbstractItemView.ScrollPerPixel)
        
        # Connect double-click signal
        self.doubleClicked.connect(self._handle_double_click)
    
    def _configure_table(self):
        """Configure table for optimal virtual scrolling performance"""
        # Set uniform row heights for better virtual scrolling
        self.verticalHeader().setDefaultSectionSize(35)
        self.verticalHeader().setVisible(False)
        
        # Optimize header
        horizontal_header = self.horizontalHeader()
        horizontal_header.setStretchLastSection(False)
        horizontal_header.setSectionResizeMode(QHeaderView.Fixed)
        
        # Performance optimizations
        self.setShowGrid(True)
        self.setGridStyle(Qt.SolidLine)
        self.setWordWrap(False)  # Disable word wrap for better performance
        
        # Optimize viewport updates
        self.setUpdatesEnabled(True)
        self.viewport().setUpdatesEnabled(True)
    
    def _setup_columns(self):
        """Set up column widths optimized for violation data"""
        column_widths = [60, 300, 100, 200, 80, 100, 80, 100]
        
        for i, width in enumerate(column_widths):
            self.setColumnWidth(i, width)
    
    def _handle_double_click(self, index):
        """Handle double-click events"""
        if index.isValid():
            self.cell_double_clicked.emit(index.row(), index.column())
    
    def _handle_action_clicked(self, row, violation_id, action_type):
        """Handle action button clicks"""
        self.action_button_clicked.emit(violation_id, action_type)
    
    def update_data(self, violations_data: List[Dict]):
        """Update table data efficiently"""
        # Clear model cache for fresh data
        if hasattr(self.model, 'clear_cache'):
            self.model.clear_cache()
        
        # Create new model with updated data
        old_model = self.model
        self.model = VirtualizedTableModel(violations_data, self)
        self.setModel(self.model)
        
        # Update delegate
        self.action_delegate = ViolationActionDelegate(self)
        self.action_delegate.action_clicked.connect(self._handle_action_clicked)
        self.setItemDelegateForColumn(7, self.action_delegate)
        
        # Clean up old model
        if old_model:
            old_model.deleteLater()
        
        # Refresh column widths
        self._setup_columns()
    
    def scrollTo(self, index, hint=QAbstractItemView.EnsureVisible):
        """Optimized scrolling for virtual table"""
        if index.isValid():
            super().scrollTo(index, hint)
    
    def sizeHint(self):
        """Provide size hint for optimal layout"""
        return QSize(1020, 600)  # Sum of column widths + margins
    
    def get_performance_metrics(self) -> Dict:
        """Get performance metrics for the virtualized table"""
        return {
            'total_rows': self.model.rowCount(),
            'cache_size': len(self.model._row_cache) if hasattr(self.model, '_row_cache') else 0,
            'cache_hit_ratio': self._calculate_cache_hit_ratio(),
            'visible_rows': self._get_visible_row_count(),
            'rendering_mode': 'virtualized_table'
        }
    
    def _calculate_cache_hit_ratio(self) -> float:
        """Calculate cache hit ratio for performance monitoring"""
        # This would require tracking cache hits/misses
        # For now, return estimated ratio based on cache size
        if hasattr(self.model, '_row_cache'):
            cache_size = len(self.model._row_cache)
            total_rows = self.model.rowCount()
            return min(1.0, cache_size / max(1, total_rows * 0.1))  # Estimate
        return 0.0
    
    def _get_visible_row_count(self) -> int:
        """Get number of currently visible rows"""
        if self.model.rowCount() == 0:
            return 0
        
        viewport_height = self.viewport().height()
        row_height = self.verticalHeader().defaultSectionSize()
        return min(self.model.rowCount(), viewport_height // row_height + 2)  # +2 for partial rows


class SmartUIRenderer(QObject):
    """
    Smart UI Renderer that selects rendering mode based on violation count
    and integrates with existing HighPerformanceTableView and standard table rendering.
    """
    
    # Signals
    rendering_mode_changed = pyqtSignal(str, dict)  # mode, config
    performance_metrics_updated = pyqtSignal(dict)
    optimization_applied = pyqtSignal(str, str)  # optimization_type, description
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        
        # Violation count thresholds for mode selection
        self.thresholds = {
            'small_dataset': 2000,      # < 2K violations
            'medium_dataset': 20000,    # 2K-20K violations  
            'large_dataset': 50000,     # 20K-50K violations
            'very_large_dataset': 100000 # > 50K violations
        }
        
        # Current rendering state
        self.current_mode = None
        self.current_config = None
        self.current_violation_count = 0
        self.current_table_widget = None
        
        # Performance monitoring
        self.performance_metrics = {
            'render_time': 0,
            'memory_usage': 0,
            'ui_response_time': 0,
            'scroll_performance': 0,
            'update_frequency': 0
        }
        
        # Mode switching history for optimization
        self.mode_history = []
        self.performance_history = []
        
        # Timer for performance monitoring
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self._monitor_performance)
        self.performance_timer.start(2000)  # Monitor every 2 seconds
    
    def select_rendering_mode(self, violation_count: int, system_capabilities: Dict = None) -> RenderingMode:
        """
        Select optimal rendering mode based on violation count and system capabilities.
        
        Args:
            violation_count: Number of violations to render
            system_capabilities: System capability information
            
        Returns:
            RenderingMode: Selected rendering mode
        """
        if system_capabilities is None:
            system_capabilities = self._get_default_system_capabilities()
        
        # Store current violation count
        self.current_violation_count = violation_count
        
        # Select mode based on violation count thresholds
        if violation_count < self.thresholds['small_dataset']:
            # < 2K violations: Use standard table for best user experience
            selected_mode = RenderingMode.STANDARD_TABLE
            
        elif violation_count < self.thresholds['medium_dataset']:
            # 2K-20K violations: Use virtual table for medium datasets
            if system_capabilities.get('can_handle_large_datasets', False):
                selected_mode = RenderingMode.VIRTUAL_TABLE
            else:
                selected_mode = RenderingMode.HIGH_PERFORMANCE_TABLE
                
        elif violation_count < self.thresholds['large_dataset']:
            # 20K-50K violations: Use paginated virtual table
            selected_mode = RenderingMode.PAGINATED_VIRTUAL_TABLE
            
        else:
            # > 50K violations: Use paginated table with minimal memory footprint
            selected_mode = RenderingMode.PAGINATED_TABLE
        
        # Apply system capability adjustments
        selected_mode = self._adjust_mode_for_system(selected_mode, system_capabilities)
        
        # Record mode selection
        self._record_mode_selection(selected_mode, violation_count, system_capabilities)
        
        return selected_mode
    
    def _adjust_mode_for_system(self, mode: RenderingMode, system_capabilities: Dict) -> RenderingMode:
        """Adjust rendering mode based on system capabilities"""
        available_memory_gb = system_capabilities.get('available_memory_gb', 4)
        cpu_cores = system_capabilities.get('cpu_cores', 4)
        performance_tier = system_capabilities.get('performance_tier', 'medium')
        
        # If system has limited resources, prefer more conservative modes
        if available_memory_gb < 2 or cpu_cores < 4 or performance_tier == 'low':
            if mode == RenderingMode.VIRTUAL_TABLE:
                return RenderingMode.PAGINATED_TABLE
            elif mode == RenderingMode.STANDARD_TABLE and self.current_violation_count > 1000:
                return RenderingMode.HIGH_PERFORMANCE_TABLE
        
        # If system has excellent resources, can use more advanced modes
        elif available_memory_gb > 8 and cpu_cores >= 8 and performance_tier == 'high':
            if mode == RenderingMode.PAGINATED_TABLE and self.current_violation_count < 30000:
                return RenderingMode.VIRTUAL_TABLE
        
        return mode
    
    def create_optimized_table(self, mode: RenderingMode, violations_data: List[Dict], 
                             parent_widget: QWidget = None) -> QWidget:
        """
        Create optimized table widget based on selected rendering mode.
        
        Args:
            mode: Selected rendering mode
            violations_data: Violation data to display
            parent_widget: Parent widget
            
        Returns:
            QWidget: Created table widget
        """
        if parent_widget is None:
            parent_widget = self.parent_widget
        
        # Create configuration for the mode
        system_capabilities = self._get_system_capabilities()
        config = RenderingConfiguration(mode, len(violations_data), system_capabilities)
        
        # Store current configuration
        self.current_mode = mode
        self.current_config = config
        
        # Create table widget based on mode
        if mode == RenderingMode.STANDARD_TABLE:
            table_widget = self._create_standard_table(violations_data, config, parent_widget)
            
        elif mode == RenderingMode.HIGH_PERFORMANCE_TABLE:
            table_widget = self._create_high_performance_table(violations_data, config, parent_widget)
            
        elif mode == RenderingMode.VIRTUAL_TABLE:
            table_widget = self._create_virtual_table(violations_data, config, parent_widget)
            
        elif mode == RenderingMode.PAGINATED_TABLE:
            table_widget = self._create_paginated_table(violations_data, config, parent_widget)
            
        elif mode == RenderingMode.PAGINATED_VIRTUAL_TABLE:
            table_widget = self._create_paginated_virtual_table(violations_data, config, parent_widget)
            
        else:
            # Fallback to standard table
            table_widget = self._create_standard_table(violations_data, config, parent_widget)
        
        # Store reference to current table
        self.current_table_widget = table_widget
        
        # Emit signal about mode change
        self.rendering_mode_changed.emit(mode.value, config.__dict__)
        
        return table_widget
    
    def switch_rendering_mode(self, new_mode: RenderingMode, violations_data: List[Dict]) -> bool:
        """
        Switch to a different rendering mode dynamically.
        
        Args:
            new_mode: New rendering mode to switch to
            violations_data: Current violation data
            
        Returns:
            bool: True if switch was successful
        """
        try:
            if new_mode == self.current_mode:
                return True  # Already in the requested mode
            
            # Record the switch
            self._record_mode_switch(self.current_mode, new_mode)
            
            # Create new table widget
            new_table = self.create_optimized_table(new_mode, violations_data)
            
            # Replace current table if parent widget is available
            if self.parent_widget and self.current_table_widget:
                # Find the layout containing the current table
                layout = self.current_table_widget.parent().layout()
                if layout:
                    # Replace the widget
                    layout.replaceWidget(self.current_table_widget, new_table)
                    self.current_table_widget.deleteLater()
                    self.current_table_widget = new_table
            
            # Emit optimization applied signal
            self.optimization_applied.emit(
                "rendering_mode_switch",
                f"Switched from {self.current_mode.value if self.current_mode else 'none'} to {new_mode.value}"
            )
            
            return True
            
        except Exception as e:
            print(f"Failed to switch rendering mode: {e}")
            return False
    
    def handle_violation_count_change(self, new_violation_count: int, violations_data: List[Dict]) -> bool:
        """
        Handle changes in violation count and automatically switch modes if needed.
        
        Args:
            new_violation_count: New violation count
            violations_data: Updated violation data
            
        Returns:
            bool: True if mode was switched
        """
        # Determine optimal mode for new violation count
        system_capabilities = self._get_system_capabilities()
        optimal_mode = self.select_rendering_mode(new_violation_count, system_capabilities)
        
        # Check if mode switch is needed
        if optimal_mode != self.current_mode:
            print(f"Violation count changed to {new_violation_count}, switching from {self.current_mode} to {optimal_mode}")
            return self.switch_rendering_mode(optimal_mode, violations_data)
        
        return False
    
    def _create_standard_table(self, violations_data: List[Dict], config: RenderingConfiguration, 
                             parent: QWidget) -> QWidget:
        """Create standard table widget"""
        from .main_window import ViolationTableModel
        
        # Create standard QTableWidget
        table = QTableWidget(parent)
        
        # Configure table
        headers = ["NUM", "层级路径", "时间(ns)", "检查信息", "状态", "确认人", "确认结果", "操作"]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        table.setRowCount(len(violations_data))
        
        # Set column widths
        column_widths = [60, 300, 100, 200, 80, 100, 80, 100]
        for i, width in enumerate(column_widths):
            table.setColumnWidth(i, width)
        
        # Populate table
        for row, violation in enumerate(violations_data):
            self._populate_table_row(table, row, violation)
        
        # Configure table properties
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setSelectionMode(QAbstractItemView.SingleSelection)
        table.setSortingEnabled(True)
        
        return table
    
    def _create_high_performance_table(self, violations_data: List[Dict], 
                                     config: RenderingConfiguration, parent: QWidget) -> QWidget:
        """Create high performance table widget"""
        # Import the existing HighPerformanceTableView
        from .main_window import HighPerformanceTableView
        
        # Create high performance table
        table = HighPerformanceTableView(parent)
        
        # Update with violation data
        table.update_data(violations_data)
        
        return table
    
    def _create_virtual_table(self, violations_data: List[Dict], config: RenderingConfiguration, 
                            parent: QWidget) -> QWidget:
        """Create virtual table widget for medium datasets (2K-20K violations)"""
        # Create container widget
        container = QWidget(parent)
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Create info header showing virtualization status
        info_widget = QWidget()
        info_widget.setFixedHeight(30)
        info_widget.setStyleSheet("""
            QWidget {
                background-color: #e8f4fd;
                border-bottom: 1px solid #b3d9ff;
            }
            QLabel {
                color: #0066cc;
                font-weight: bold;
                padding: 5px;
            }
        """)
        
        info_layout = QHBoxLayout(info_widget)
        info_layout.setContentsMargins(10, 0, 10, 0)
        
        violation_count = len(violations_data)
        info_label = QLabel(f"虚拟化表格模式 - {violation_count:,} 条违例记录 (智能缓存已启用)")
        info_layout.addWidget(info_label)
        info_layout.addStretch()
        
        # Add performance indicator
        perf_label = QLabel("性能: 优化")
        perf_label.setStyleSheet("color: #009900; font-weight: bold;")
        info_layout.addWidget(perf_label)
        
        layout.addWidget(info_widget)
        
        # Create virtualized table
        virtual_table = VirtualizedTableView(violations_data, container)
        
        # Connect signals to parent if available
        if hasattr(parent, 'handle_cell_double_click'):
            virtual_table.cell_double_clicked.connect(parent.handle_cell_double_click)
        if hasattr(parent, 'handle_action_button_click'):
            virtual_table.action_button_clicked.connect(parent.handle_action_button_click)
        
        layout.addWidget(virtual_table)
        
        # Store reference to virtual table for performance monitoring
        container.virtual_table = virtual_table
        
        return container
    
    def _create_paginated_table(self, violations_data: List[Dict], config: RenderingConfiguration, 
                              parent: QWidget) -> QWidget:
        """Create enhanced paginated table widget for large datasets (>20K violations)"""
        # Create enhanced paginated table with lazy loading and background prefetching
        enhanced_table = EnhancedPaginatedTableView(violations_data, config, parent)
        
        # Connect signals to parent if available
        if hasattr(parent, 'handle_cell_double_click'):
            enhanced_table.cell_double_clicked.connect(parent.handle_cell_double_click)
        if hasattr(parent, 'handle_action_button_click'):
            enhanced_table.action_button_clicked.connect(parent.handle_action_button_click)
        
        return enhanced_table
    
    def _create_paginated_virtual_table(self, violations_data: List[Dict], 
                                      config: RenderingConfiguration, parent: QWidget) -> QWidget:
        """Create paginated virtual table widget"""
        # This combines pagination with virtualization
        # For now, use paginated table
        return self._create_paginated_table(violations_data, config, parent)
    
    def _populate_table_row(self, table: QTableWidget, row: int, violation: Dict):
        """Populate a single table row with violation data"""
        # NUM
        table.setItem(row, 0, QTableWidgetItem(str(violation.get('num', ''))))
        
        # 层级路径
        table.setItem(row, 1, QTableWidgetItem(violation.get('hier', '')))
        
        # 时间(ns)
        time_fs = violation.get('time_fs', 0)
        time_ns = time_fs / 1000000 if time_fs else 0
        table.setItem(row, 2, QTableWidgetItem(f"{time_ns:.3f}"))
        
        # 检查信息
        table.setItem(row, 3, QTableWidgetItem(violation.get('check_info', '')))
        
        # 状态
        status = violation.get('status', 'pending')
        status_text = {'pending': '待确认', 'confirmed': '已确认', 'ignored': '已忽略'}.get(status, status)
        status_item = QTableWidgetItem(status_text)
        
        # Set status background color
        if status == 'confirmed':
            status_item.setBackground(QColor(144, 238, 144))  # Light green
        elif status == 'ignored':
            status_item.setBackground(QColor(255, 182, 193))  # Light red
        else:
            status_item.setBackground(QColor(255, 255, 224))  # Light yellow
            
        table.setItem(row, 4, status_item)
        
        # 确认人
        table.setItem(row, 5, QTableWidgetItem(violation.get('confirmer', '')))
        
        # 确认结果
        result = violation.get('result', '')
        result_text = {'pass': '通过', 'issue': '有问题', '': ''}.get(result, result)
        table.setItem(row, 6, QTableWidgetItem(result_text))
        
        # 操作 - Create button
        action_text = "确认" if status == 'pending' else "编辑"
        button = QPushButton(action_text)
        
        if status == 'pending':
            button.setStyleSheet("""
                QPushButton {
                    background-color: #4a9eff;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #3d8ced;
                }
            """)
        else:
            button.setStyleSheet("""
                QPushButton {
                    color: #808080;
                    background-color: #f0f0f0;
                    border: 1px solid #c0c0c0;
                    border-radius: 4px;
                    padding: 4px 8px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)
        
        table.setCellWidget(row, 7, button)
    
    def _get_system_capabilities(self) -> Dict:
        """Get current system capabilities"""
        try:
            import psutil
            memory_info = psutil.virtual_memory()
            cpu_count = psutil.cpu_count()
            
            available_memory_gb = memory_info.available / (1024 ** 3)
            
            # Determine performance tier
            if available_memory_gb > 8 and cpu_count >= 8:
                performance_tier = 'high'
            elif available_memory_gb > 4 and cpu_count >= 4:
                performance_tier = 'medium'
            else:
                performance_tier = 'low'
            
            return {
                'cpu_cores': cpu_count,
                'available_memory_gb': available_memory_gb,
                'performance_tier': performance_tier,
                'can_handle_large_datasets': available_memory_gb > 4 and cpu_count >= 4
            }
        except ImportError:
            return self._get_default_system_capabilities()
    
    def _get_default_system_capabilities(self) -> Dict:
        """Get default system capabilities when psutil is not available"""
        return {
            'cpu_cores': 4,
            'available_memory_gb': 4,
            'performance_tier': 'medium',
            'can_handle_large_datasets': True
        }
    
    def _record_mode_selection(self, mode: RenderingMode, violation_count: int, 
                             system_capabilities: Dict):
        """Record mode selection for optimization learning"""
        selection_record = {
            'timestamp': time.time(),
            'mode': mode.value,
            'violation_count': violation_count,
            'system_capabilities': system_capabilities.copy(),
            'selection_reason': self._get_selection_reason(mode, violation_count)
        }
        
        self.mode_history.append(selection_record)
        
        # Keep only last 100 records
        if len(self.mode_history) > 100:
            self.mode_history = self.mode_history[-100:]
    
    def _record_mode_switch(self, old_mode: RenderingMode, new_mode: RenderingMode):
        """Record mode switch for performance analysis"""
        switch_record = {
            'timestamp': time.time(),
            'old_mode': old_mode.value if old_mode else None,
            'new_mode': new_mode.value,
            'violation_count': self.current_violation_count,
            'reason': 'automatic_optimization'
        }
        
        print(f"Mode switch recorded: {old_mode} -> {new_mode} for {self.current_violation_count} violations")
    
    def _get_selection_reason(self, mode: RenderingMode, violation_count: int) -> str:
        """Get human-readable reason for mode selection"""
        if mode == RenderingMode.STANDARD_TABLE:
            return f"Small dataset ({violation_count} violations) - optimal user experience"
        elif mode == RenderingMode.HIGH_PERFORMANCE_TABLE:
            return f"Medium dataset ({violation_count} violations) - balanced performance"
        elif mode == RenderingMode.VIRTUAL_TABLE:
            return f"Medium dataset ({violation_count} violations) - virtual scrolling optimization"
        elif mode == RenderingMode.PAGINATED_TABLE:
            return f"Large dataset ({violation_count} violations) - pagination for memory efficiency"
        elif mode == RenderingMode.PAGINATED_VIRTUAL_TABLE:
            return f"Very large dataset ({violation_count} violations) - hybrid optimization"
        else:
            return f"Unknown mode for {violation_count} violations"
    
    def _monitor_performance(self):
        """Monitor current rendering performance"""
        if not self.current_table_widget:
            return
        
        try:
            # Measure basic performance metrics
            start_time = time.time()
            
            # Simulate a UI operation to measure response time
            if hasattr(self.current_table_widget, 'update'):
                self.current_table_widget.update()
            
            response_time = time.time() - start_time
            
            # Update performance metrics
            self.performance_metrics.update({
                'ui_response_time': response_time,
                'current_mode': self.current_mode.value if self.current_mode else 'none',
                'violation_count': self.current_violation_count,
                'timestamp': time.time()
            })
            
            # Emit performance metrics
            self.performance_metrics_updated.emit(self.performance_metrics.copy())
            
            # Check if performance optimization is needed
            self._check_performance_optimization()
            
        except Exception as e:
            print(f"Performance monitoring error: {e}")
    
    def _check_performance_optimization(self):
        """Check if performance optimization is needed and apply if necessary"""
        if not self.current_mode or not self.current_table_widget:
            return
        
        response_time = self.performance_metrics.get('ui_response_time', 0)
        
        # If UI response time is too slow, consider switching to a more efficient mode
        if response_time > 0.1:  # 100ms threshold
            if self.current_mode == RenderingMode.STANDARD_TABLE and self.current_violation_count > 1000:
                # Switch to high performance mode
                self.optimization_applied.emit(
                    "performance_degradation",
                    f"UI response time {response_time:.3f}s exceeded threshold, switching to high performance mode"
                )
            elif self.current_mode == RenderingMode.VIRTUAL_TABLE and self.current_violation_count > 10000:
                # Switch to paginated mode
                self.optimization_applied.emit(
                    "performance_degradation", 
                    f"UI response time {response_time:.3f}s exceeded threshold, switching to paginated mode"
                )
    
    def get_performance_report(self) -> Dict:
        """Get comprehensive performance report"""
        return {
            'current_mode': self.current_mode.value if self.current_mode else 'none',
            'current_config': self.current_config.__dict__ if self.current_config else {},
            'violation_count': self.current_violation_count,
            'performance_metrics': self.performance_metrics.copy(),
            'mode_history': self.mode_history[-10:],  # Last 10 mode selections
            'optimization_suggestions': self._generate_optimization_suggestions()
        }
    
    def _generate_optimization_suggestions(self) -> List[str]:
        """Generate optimization suggestions based on current performance"""
        suggestions = []
        
        if self.current_violation_count > 50000:
            suggestions.append("Consider using data filtering to reduce the number of displayed violations")
            
        if self.performance_metrics.get('ui_response_time', 0) > 0.05:
            suggestions.append("UI response time is high, consider switching to a more efficient rendering mode")
            
        if self.current_mode == RenderingMode.STANDARD_TABLE and self.current_violation_count > 5000:
            suggestions.append("Standard table mode may not be optimal for this dataset size")
            
        return suggestions