"""
时序违例插件配置对话框

提供用户配置界面，用于管理性能配置文件和违例处理设置。
"""

import json
from typing import Dict, List, Optional, Any
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QCheckBox, QTextEdit, QTabWidget, QWidget, QGroupBox, QSlider,
    QListWidget, QListWidgetItem, QSplitter, QMessageBox, QProgressBar,
    QTableWidget, QTableWidgetItem, QHeaderView, QScrollArea, QFrame,
    QButtonGroup, QRadioButton, QDialogButtonBox, QFileDialog
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon, QPalette

try:
    from .configuration_manager import ConfigurationManager, PerformanceProfile
except ImportError:
    from configuration_manager import ConfigurationManager, PerformanceProfile


class PerformanceProfileEditor(QWidget):
    """性能配置文件编辑器"""
    
    profile_changed = pyqtSignal(str, dict)  # 配置文件名称, 配置数据
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_profile = None
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 配置文件基本信息
        info_group = QGroupBox("配置文件信息")
        info_layout = QFormLayout(info_group)
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("配置文件名称")
        info_layout.addRow("名称:", self.name_edit)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(60)
        self.description_edit.setPlaceholderText("配置文件描述")
        info_layout.addRow("描述:", self.description_edit)
        
        # 违例数量范围
        range_layout = QHBoxLayout()
        self.min_violations_spin = QSpinBox()
        self.min_violations_spin.setRange(0, 1000000)
        self.min_violations_spin.setValue(0)
        range_layout.addWidget(QLabel("最小:"))
        range_layout.addWidget(self.min_violations_spin)
        
        range_layout.addWidget(QLabel("到"))
        
        self.max_violations_spin = QSpinBox()
        self.max_violations_spin.setRange(1, 1000000)
        self.max_violations_spin.setValue(10000)
        self.max_violations_spin.setSpecialValueText("无限制")
        range_layout.addWidget(QLabel("最大:"))
        range_layout.addWidget(self.max_violations_spin)
        
        info_layout.addRow("违例数量范围:", range_layout)
        
        layout.addWidget(info_group)
        
        # 配置选项卡
        self.tab_widget = QTabWidget()
        
        # 解析器配置选项卡
        self.parser_tab = self.create_parser_config_tab()
        self.tab_widget.addTab(self.parser_tab, "解析器配置")
        
        # 显示配置选项卡
        self.display_tab = self.create_display_config_tab()
        self.tab_widget.addTab(self.display_tab, "显示配置")
        
        # 内存配置选项卡
        self.memory_tab = self.create_memory_config_tab()
        self.tab_widget.addTab(self.memory_tab, "内存配置")
        
        # UI配置选项卡
        self.ui_tab = self.create_ui_config_tab()
        self.tab_widget.addTab(self.ui_tab, "界面配置")
        
        # 优化配置选项卡
        self.optimization_tab = self.create_optimization_config_tab()
        self.tab_widget.addTab(self.optimization_tab, "优化配置")
        
        layout.addWidget(self.tab_widget)
        
        # 连接信号
        self.connect_signals()    
    
def create_parser_config_tab(self) -> QWidget:
        """创建解析器配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 解析器类型
        parser_group = QGroupBox("解析器设置")
        parser_layout = QFormLayout(parser_group)
        
        self.parser_type_combo = QComboBox()
        self.parser_type_combo.addItems([
            "standard_async", "high_performance_async", "high_performance_streaming",
            "streaming_with_chunking", "memory_efficient_streaming", "adaptive"
        ])
        parser_layout.addRow("解析器类型:", self.parser_type_combo)
        
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(100, 50000)
        self.batch_size_spin.setValue(2000)
        self.batch_size_spin.setSuffix(" 条记录")
        parser_layout.addRow("批处理大小:", self.batch_size_spin)
        
        self.use_streaming_check = QCheckBox("启用流式处理")
        parser_layout.addRow("", self.use_streaming_check)
        
        self.progress_interval_spin = QSpinBox()
        self.progress_interval_spin.setRange(100, 100000)
        self.progress_interval_spin.setValue(5000)
        self.progress_interval_spin.setSuffix(" 条记录")
        parser_layout.addRow("进度更新间隔:", self.progress_interval_spin)
        
        self.gc_interval_spin = QSpinBox()
        self.gc_interval_spin.setRange(1000, 200000)
        self.gc_interval_spin.setValue(15000)
        self.gc_interval_spin.setSuffix(" 条记录")
        parser_layout.addRow("垃圾回收间隔:", self.gc_interval_spin)
        
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(30, 3600)
        self.timeout_spin.setValue(90)
        self.timeout_spin.setSuffix(" 秒")
        parser_layout.addRow("超时时间:", self.timeout_spin)
        
        layout.addWidget(parser_group)
        
        # 高级解析器设置
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QFormLayout(advanced_group)
        
        self.chunk_size_spin = QSpinBox()
        self.chunk_size_spin.setRange(1000, 100000)
        self.chunk_size_spin.setValue(10000)
        self.chunk_size_spin.setSuffix(" 条记录")
        advanced_layout.addRow("数据块大小:", self.chunk_size_spin)
        
        layout.addWidget(advanced_group)
        layout.addStretch()
        
        return widget
    
    def create_display_config_tab(self) -> QWidget:
        """创建显示配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 显示模式
        display_group = QGroupBox("显示设置")
        display_layout = QFormLayout(display_group)
        
        self.display_mode_combo = QComboBox()
        self.display_mode_combo.addItems([
            "standard_table", "high_performance_table", "virtual_table_with_lazy_loading",
            "paginated_virtual_table", "simple_table", "adaptive"
        ])
        display_layout.addRow("显示模式:", self.display_mode_combo)
        
        self.use_pagination_check = QCheckBox("启用分页")
        display_layout.addRow("", self.use_pagination_check)
        
        self.page_size_spin = QSpinBox()
        self.page_size_spin.setRange(10, 1000)
        self.page_size_spin.setValue(150)
        self.page_size_spin.setSuffix(" 条记录")
        display_layout.addRow("每页记录数:", self.page_size_spin)
        
        self.virtual_scrolling_check = QCheckBox("启用虚拟滚动")
        display_layout.addRow("", self.virtual_scrolling_check)
        
        self.lazy_loading_check = QCheckBox("启用延迟加载")
        display_layout.addRow("", self.lazy_loading_check)
        
        self.row_height_spin = QSpinBox()
        self.row_height_spin.setRange(20, 60)
        self.row_height_spin.setValue(35)
        self.row_height_spin.setSuffix(" 像素")
        display_layout.addRow("行高:", self.row_height_spin)
        
        self.column_auto_resize_check = QCheckBox("自动调整列宽")
        display_layout.addRow("", self.column_auto_resize_check)
        
        layout.addWidget(display_group)
        layout.addStretch()
        
        return widget
    
    def create_memory_config_tab(self) -> QWidget:
        """创建内存配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 内存限制
        memory_group = QGroupBox("内存管理")
        memory_layout = QFormLayout(memory_group)
        
        self.memory_limit_spin = QSpinBox()
        self.memory_limit_spin.setRange(50, 2000)
        self.memory_limit_spin.setValue(400)
        self.memory_limit_spin.setSuffix(" MB")
        memory_layout.addRow("内存限制:", self.memory_limit_spin)
        
        self.enable_memory_monitoring_check = QCheckBox("启用内存监控")
        memory_layout.addRow("", self.enable_memory_monitoring_check)
        
        self.aggressive_gc_check = QCheckBox("激进垃圾回收")
        memory_layout.addRow("", self.aggressive_gc_check)
        
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(100, 50000)
        self.cache_size_spin.setValue(3000)
        self.cache_size_spin.setSuffix(" 条记录")
        memory_layout.addRow("缓存大小:", self.cache_size_spin)
        
        self.preload_data_check = QCheckBox("预加载数据")
        memory_layout.addRow("", self.preload_data_check)
        
        layout.addWidget(memory_group)
        layout.addStretch()
        
        return widget
    
    def create_ui_config_tab(self) -> QWidget:
        """创建UI配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # UI响应设置
        ui_group = QGroupBox("界面响应")
        ui_layout = QFormLayout(ui_group)
        
        self.ui_update_interval_spin = QSpinBox()
        self.ui_update_interval_spin.setRange(50, 2000)
        self.ui_update_interval_spin.setValue(300)
        self.ui_update_interval_spin.setSuffix(" 毫秒")
        ui_layout.addRow("界面更新间隔:", self.ui_update_interval_spin)
        
        self.show_progress_bar_check = QCheckBox("显示进度条")
        ui_layout.addRow("", self.show_progress_bar_check)
        
        self.enable_animations_check = QCheckBox("启用动画效果")
        ui_layout.addRow("", self.enable_animations_check)
        
        self.responsive_ui_check = QCheckBox("响应式界面")
        ui_layout.addRow("", self.responsive_ui_check)
        
        self.background_processing_check = QCheckBox("后台处理")
        ui_layout.addRow("", self.background_processing_check)
        
        layout.addWidget(ui_group)
        layout.addStretch()
        
        return widget
    
    def create_optimization_config_tab(self) -> QWidget:
        """创建优化配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 优化策略
        optimization_group = QGroupBox("优化策略")
        optimization_layout = QFormLayout(optimization_group)
        
        self.priority_combo = QComboBox()
        self.priority_combo.addItems([
            "user_experience", "balanced", "memory_efficiency", "speed", "stability"
        ])
        optimization_layout.addRow("优化优先级:", self.priority_combo)
        
        self.enable_caching_check = QCheckBox("启用缓存")
        optimization_layout.addRow("", self.enable_caching_check)
        
        self.prefetch_data_check = QCheckBox("预取数据")
        optimization_layout.addRow("", self.prefetch_data_check)
        
        self.optimize_for_combo = QComboBox()
        self.optimize_for_combo.addItems([
            "responsiveness", "balanced", "memory", "speed", "stability"
        ])
        optimization_layout.addRow("优化目标:", self.optimize_for_combo)
        
        self.fallback_enabled_check = QCheckBox("启用回退机制")
        optimization_layout.addRow("", self.fallback_enabled_check)
        
        layout.addWidget(optimization_group)
        layout.addStretch()
        
        return widget    

    def connect_signals(self):
        """连接信号"""
        # 当任何配置改变时发出信号
        controls = [
            self.name_edit, self.description_edit, self.min_violations_spin, self.max_violations_spin,
            self.parser_type_combo, self.batch_size_spin, self.use_streaming_check,
            self.progress_interval_spin, self.gc_interval_spin, self.timeout_spin, self.chunk_size_spin,
            self.display_mode_combo, self.use_pagination_check, self.page_size_spin,
            self.virtual_scrolling_check, self.lazy_loading_check, self.row_height_spin,
            self.column_auto_resize_check, self.memory_limit_spin, self.enable_memory_monitoring_check,
            self.aggressive_gc_check, self.cache_size_spin, self.preload_data_check,
            self.ui_update_interval_spin, self.show_progress_bar_check, self.enable_animations_check,
            self.responsive_ui_check, self.background_processing_check, self.priority_combo,
            self.enable_caching_check, self.prefetch_data_check, self.optimize_for_combo,
            self.fallback_enabled_check
        ]
        
        for control in controls:
            if hasattr(control, 'textChanged'):
                control.textChanged.connect(self.on_config_changed)
            elif hasattr(control, 'valueChanged'):
                control.valueChanged.connect(self.on_config_changed)
            elif hasattr(control, 'toggled'):
                control.toggled.connect(self.on_config_changed)
            elif hasattr(control, 'currentTextChanged'):
                control.currentTextChanged.connect(self.on_config_changed)
    
    def on_config_changed(self):
        """配置改变时的处理"""
        if self.current_profile:
            config_data = self.get_config_data()
            self.profile_changed.emit(self.current_profile, config_data)
    
    def load_profile(self, profile: PerformanceProfile):
        """加载配置文件到编辑器"""
        self.current_profile = profile.name
        
        # 基本信息
        self.name_edit.setText(profile.name)
        self.description_edit.setPlainText(profile.description)
        
        # 违例数量范围
        min_violations, max_violations = profile.violation_count_range
        self.min_violations_spin.setValue(min_violations)
        if max_violations == float('inf'):
            self.max_violations_spin.setValue(1000000)  # 使用最大值表示无限制
        else:
            self.max_violations_spin.setValue(int(max_violations))
        
        # 解析器配置
        parser_config = profile.parser_config
        self.parser_type_combo.setCurrentText(parser_config.get('parser_type', 'standard_async'))
        self.batch_size_spin.setValue(parser_config.get('batch_size', 2000))
        self.use_streaming_check.setChecked(parser_config.get('use_streaming', False))
        self.progress_interval_spin.setValue(parser_config.get('progress_interval', 5000))
        self.gc_interval_spin.setValue(parser_config.get('gc_interval', 15000))
        self.timeout_spin.setValue(parser_config.get('timeout_seconds', 90))
        self.chunk_size_spin.setValue(parser_config.get('chunk_size', 10000))
        
        # 显示配置
        display_config = profile.display_config
        self.display_mode_combo.setCurrentText(display_config.get('display_mode', 'standard_table'))
        self.use_pagination_check.setChecked(display_config.get('use_pagination', False))
        self.page_size_spin.setValue(display_config.get('page_size', 150))
        self.virtual_scrolling_check.setChecked(display_config.get('virtual_scrolling', False))
        self.lazy_loading_check.setChecked(display_config.get('lazy_loading', False))
        self.row_height_spin.setValue(display_config.get('row_height', 35))
        self.column_auto_resize_check.setChecked(display_config.get('column_auto_resize', True))
        
        # 内存配置
        memory_config = profile.memory_config
        self.memory_limit_spin.setValue(memory_config.get('memory_limit_mb', 400))
        self.enable_memory_monitoring_check.setChecked(memory_config.get('enable_memory_monitoring', True))
        self.aggressive_gc_check.setChecked(memory_config.get('aggressive_gc', False))
        self.cache_size_spin.setValue(memory_config.get('cache_size', 3000))
        self.preload_data_check.setChecked(memory_config.get('preload_data', False))
        
        # UI配置
        ui_config = profile.ui_config
        self.ui_update_interval_spin.setValue(ui_config.get('ui_update_interval', 300))
        self.show_progress_bar_check.setChecked(ui_config.get('show_progress_bar', True))
        self.enable_animations_check.setChecked(ui_config.get('enable_animations', True))
        self.responsive_ui_check.setChecked(ui_config.get('responsive_ui', True))
        self.background_processing_check.setChecked(ui_config.get('background_processing', False))
        
        # 优化配置
        optimization_config = profile.optimization_config
        self.priority_combo.setCurrentText(optimization_config.get('priority', 'balanced'))
        self.enable_caching_check.setChecked(optimization_config.get('enable_caching', True))
        self.prefetch_data_check.setChecked(optimization_config.get('prefetch_data', False))
        self.optimize_for_combo.setCurrentText(optimization_config.get('optimize_for', 'balanced'))
        self.fallback_enabled_check.setChecked(optimization_config.get('fallback_enabled', True))
    
    def get_config_data(self) -> Dict[str, Any]:
        """获取当前配置数据"""
        return {
            'name': self.name_edit.text(),
            'description': self.description_edit.toPlainText(),
            'violation_count_range': (
                self.min_violations_spin.value(),
                float('inf') if self.max_violations_spin.value() >= 1000000 else self.max_violations_spin.value()
            ),
            'parser_config': {
                'parser_type': self.parser_type_combo.currentText(),
                'batch_size': self.batch_size_spin.value(),
                'use_streaming': self.use_streaming_check.isChecked(),
                'progress_interval': self.progress_interval_spin.value(),
                'gc_interval': self.gc_interval_spin.value(),
                'timeout_seconds': self.timeout_spin.value(),
                'chunk_size': self.chunk_size_spin.value()
            },
            'display_config': {
                'display_mode': self.display_mode_combo.currentText(),
                'use_pagination': self.use_pagination_check.isChecked(),
                'page_size': self.page_size_spin.value(),
                'virtual_scrolling': self.virtual_scrolling_check.isChecked(),
                'lazy_loading': self.lazy_loading_check.isChecked(),
                'row_height': self.row_height_spin.value(),
                'column_auto_resize': self.column_auto_resize_check.isChecked()
            },
            'memory_config': {
                'memory_limit_mb': self.memory_limit_spin.value(),
                'enable_memory_monitoring': self.enable_memory_monitoring_check.isChecked(),
                'aggressive_gc': self.aggressive_gc_check.isChecked(),
                'cache_size': self.cache_size_spin.value(),
                'preload_data': self.preload_data_check.isChecked()
            },
            'ui_config': {
                'ui_update_interval': self.ui_update_interval_spin.value(),
                'show_progress_bar': self.show_progress_bar_check.isChecked(),
                'enable_animations': self.enable_animations_check.isChecked(),
                'responsive_ui': self.responsive_ui_check.isChecked(),
                'background_processing': self.background_processing_check.isChecked()
            },
            'optimization_config': {
                'priority': self.priority_combo.currentText(),
                'enable_caching': self.enable_caching_check.isChecked(),
                'prefetch_data': self.prefetch_data_check.isChecked(),
                'optimize_for': self.optimize_for_combo.currentText(),
                'fallback_enabled': self.fallback_enabled_check.isChecked()
            }
        }

cl
ass ViolationThresholdEditor(QWidget):
    """违例数量阈值编辑器"""
    
    thresholds_changed = pyqtSignal(dict)  # 阈值配置
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 阈值设置
        threshold_group = QGroupBox("违例数量阈值设置")
        threshold_layout = QFormLayout(threshold_group)
        
        # 小数据集阈值
        self.small_dataset_spin = QSpinBox()
        self.small_dataset_spin.setRange(100, 10000)
        self.small_dataset_spin.setValue(2000)
        self.small_dataset_spin.setSuffix(" 个违例")
        threshold_layout.addRow("小数据集上限:", self.small_dataset_spin)
        
        # 中等数据集阈值
        self.medium_dataset_spin = QSpinBox()
        self.medium_dataset_spin.setRange(1000, 50000)
        self.medium_dataset_spin.setValue(20000)
        self.medium_dataset_spin.setSuffix(" 个违例")
        threshold_layout.addRow("中等数据集上限:", self.medium_dataset_spin)
        
        # 大数据集阈值
        self.large_dataset_spin = QSpinBox()
        self.large_dataset_spin.setRange(10000, 200000)
        self.large_dataset_spin.setValue(50000)
        self.large_dataset_spin.setSuffix(" 个违例")
        threshold_layout.addRow("大数据集上限:", self.large_dataset_spin)
        
        # 内存警告阈值
        self.memory_warning_spin = QSpinBox()
        self.memory_warning_spin.setRange(1000, 100000)
        self.memory_warning_spin.setValue(10000)
        self.memory_warning_spin.setSuffix(" 个违例")
        threshold_layout.addRow("内存警告阈值:", self.memory_warning_spin)
        
        # 性能严重阈值
        self.performance_critical_spin = QSpinBox()
        self.performance_critical_spin.setRange(5000, 200000)
        self.performance_critical_spin.setValue(25000)
        self.performance_critical_spin.setSuffix(" 个违例")
        threshold_layout.addRow("性能严重阈值:", self.performance_critical_spin)
        
        # UI响应性限制
        self.ui_responsiveness_spin = QSpinBox()
        self.ui_responsiveness_spin.setRange(1000, 50000)
        self.ui_responsiveness_spin.setValue(5000)
        self.ui_responsiveness_spin.setSuffix(" 个违例")
        threshold_layout.addRow("UI响应性限制:", self.ui_responsiveness_spin)
        
        layout.addWidget(threshold_group)
        
        # 阈值说明
        info_group = QGroupBox("阈值说明")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(120)
        info_text.setPlainText(
            "• 小数据集: 使用标准处理模式，优先用户体验\n"
            "• 中等数据集: 使用高性能模式，启用分页和虚拟滚动\n"
            "• 大数据集: 使用流式处理，优化内存使用\n"
            "• 超大数据集: 使用分块处理，最大化稳定性\n"
            "• 内存警告: 当违例数量超过此值时显示内存使用警告\n"
            "• 性能严重: 当违例数量超过此值时可能出现严重性能问题\n"
            "• UI响应性: 当违例数量超过此值时UI响应可能变慢"
        )
        info_layout.addWidget(info_text)
        
        layout.addWidget(info_group)
        
        # 连接信号
        self.connect_signals()
        
        layout.addStretch()
    
    def connect_signals(self):
        """连接信号"""
        controls = [
            self.small_dataset_spin, self.medium_dataset_spin, self.large_dataset_spin,
            self.memory_warning_spin, self.performance_critical_spin, self.ui_responsiveness_spin
        ]
        
        for control in controls:
            control.valueChanged.connect(self.on_thresholds_changed)
    
    def on_thresholds_changed(self):
        """阈值改变时的处理"""
        thresholds = self.get_thresholds()
        self.thresholds_changed.emit(thresholds)
    
    def get_thresholds(self) -> Dict[str, int]:
        """获取当前阈值配置"""
        return {
            'small_dataset': self.small_dataset_spin.value(),
            'medium_dataset': self.medium_dataset_spin.value(),
            'large_dataset': self.large_dataset_spin.value(),
            'memory_warning': self.memory_warning_spin.value(),
            'performance_critical': self.performance_critical_spin.value(),
            'ui_responsiveness_limit': self.ui_responsiveness_spin.value()
        }
    
    def set_thresholds(self, thresholds: Dict[str, int]):
        """设置阈值配置"""
        self.small_dataset_spin.setValue(thresholds.get('small_dataset', 2000))
        self.medium_dataset_spin.setValue(thresholds.get('medium_dataset', 20000))
        self.large_dataset_spin.setValue(thresholds.get('large_dataset', 50000))
        self.memory_warning_spin.setValue(thresholds.get('memory_warning', 10000))
        self.performance_critical_spin.setValue(thresholds.get('performance_critical', 25000))
        self.ui_responsiveness_spin.setValue(thresholds.get('ui_responsiveness_limit', 5000))
class C
onfigurationDialog(QDialog):
    """配置对话框主窗口"""
    
    def __init__(self, config_manager: ConfigurationManager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.current_profile_name = None
        self.unsaved_changes = False
        
        self.setWindowTitle("时序违例插件配置")
        self.setModal(True)
        self.resize(900, 700)
        
        self.init_ui()
        self.load_initial_data()
        self.connect_signals()
    
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        
        # 左侧配置文件列表
        left_panel = self.create_profile_list_panel()
        layout.addWidget(left_panel, 1)
        
        # 右侧配置编辑区域
        right_panel = self.create_config_panel()
        layout.addWidget(right_panel, 3)
    
    def create_profile_list_panel(self) -> QWidget:
        """创建配置文件列表面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 标题
        title_label = QLabel("性能配置文件")
        title_label.setFont(QFont("", 12, QFont.Bold))
        layout.addWidget(title_label)
        
        # 配置文件列表
        self.profile_list = QListWidget()
        self.profile_list.setMaximumWidth(250)
        layout.addWidget(self.profile_list)
        
        # 按钮组
        button_layout = QVBoxLayout()
        
        self.new_profile_btn = QPushButton("新建配置")
        self.new_profile_btn.clicked.connect(self.create_new_profile)
        button_layout.addWidget(self.new_profile_btn)
        
        self.copy_profile_btn = QPushButton("复制配置")
        self.copy_profile_btn.clicked.connect(self.copy_current_profile)
        button_layout.addWidget(self.copy_profile_btn)
        
        self.delete_profile_btn = QPushButton("删除配置")
        self.delete_profile_btn.clicked.connect(self.delete_current_profile)
        button_layout.addWidget(self.delete_profile_btn)
        
        button_layout.addStretch()
        
        self.import_btn = QPushButton("导入配置")
        self.import_btn.clicked.connect(self.import_profiles)
        button_layout.addWidget(self.import_btn)
        
        self.export_btn = QPushButton("导出配置")
        self.export_btn.clicked.connect(self.export_profiles)
        button_layout.addWidget(self.export_btn)
        
        layout.addLayout(button_layout)
        
        return panel
    
    def create_config_panel(self) -> QWidget:
        """创建配置编辑面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 配置选项卡
        self.config_tabs = QTabWidget()
        
        # 配置文件编辑选项卡
        self.profile_editor = PerformanceProfileEditor()
        self.config_tabs.addTab(self.profile_editor, "配置文件编辑")
        
        # 违例阈值设置选项卡
        self.threshold_editor = ViolationThresholdEditor()
        self.config_tabs.addTab(self.threshold_editor, "违例阈值设置")
        
        # 使用统计选项卡
        self.stats_tab = self.create_statistics_tab()
        self.config_tabs.addTab(self.stats_tab, "使用统计")
        
        layout.addWidget(self.config_tabs)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.reset_btn = QPushButton("重置默认")
        self.reset_btn.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        
        self.apply_btn = QPushButton("应用")
        self.apply_btn.clicked.connect(self.apply_changes)
        button_layout.addWidget(self.apply_btn)
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept_changes)
        button_layout.addWidget(self.ok_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        return panel
    
    def create_statistics_tab(self) -> QWidget:
        """创建使用统计选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 统计信息表格
        self.stats_table = QTableWidget()
        self.stats_table.setColumnCount(4)
        self.stats_table.setHorizontalHeaderLabels(["配置文件", "使用次数", "最后使用", "性能评分"])
        
        header = self.stats_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.stats_table)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新统计")
        refresh_btn.clicked.connect(self.refresh_statistics)
        layout.addWidget(refresh_btn)
        
        return widget 
   
    def connect_signals(self):
        """连接信号"""
        # 配置文件列表选择
        self.profile_list.currentItemChanged.connect(self.on_profile_selected)
        
        # 配置编辑器变化
        self.profile_editor.profile_changed.connect(self.on_profile_changed)
        
        # 阈值编辑器变化
        self.threshold_editor.thresholds_changed.connect(self.on_thresholds_changed)
        
        # 配置管理器信号
        self.config_manager.profile_changed.connect(self.on_manager_profile_changed)
        self.config_manager.profile_created.connect(self.refresh_profile_list)
        self.config_manager.profile_deleted.connect(self.refresh_profile_list)
    
    def load_initial_data(self):
        """加载初始数据"""
        self.refresh_profile_list()
        self.refresh_statistics()
        
        # 加载阈值设置
        thresholds = self.config_manager.violation_thresholds
        self.threshold_editor.set_thresholds(thresholds)
        
        # 选择当前配置文件
        current_profile = self.config_manager.current_profile_name
        if current_profile:
            self.select_profile(current_profile)
    
    def refresh_profile_list(self):
        """刷新配置文件列表"""
        self.profile_list.clear()
        
        profiles = self.config_manager.get_all_profiles()
        current_profile = self.config_manager.current_profile_name
        
        for name, profile in profiles.items():
            item = QListWidgetItem(f"{profile.name}")
            item.setData(Qt.UserRole, name)
            
            # 标记当前配置文件
            if name == current_profile:
                item.setText(f"● {profile.name}")
                font = item.font()
                font.setBold(True)
                item.setFont(font)
            
            # 添加描述作为工具提示
            item.setToolTip(f"{profile.description}\n违例范围: {profile.violation_count_range[0]:,} - {profile.violation_count_range[1]:,}")
            
            self.profile_list.addItem(item)
    
    def refresh_statistics(self):
        """刷新使用统计"""
        stats = self.config_manager.get_usage_statistics()
        profile_usage = stats.get('profile_usage', {})
        
        self.stats_table.setRowCount(len(profile_usage))
        
        for row, (name, usage_info) in enumerate(profile_usage.items()):
            # 配置文件名称
            self.stats_table.setItem(row, 0, QTableWidgetItem(name))
            
            # 使用次数
            usage_count = usage_info.get('usage_count', 0)
            self.stats_table.setItem(row, 1, QTableWidgetItem(str(usage_count)))
            
            # 最后使用时间
            last_used = usage_info.get('last_used_time', 0)
            if last_used > 0:
                import datetime
                last_used_str = datetime.datetime.fromtimestamp(last_used).strftime('%Y-%m-%d %H:%M')
            else:
                last_used_str = "从未使用"
            self.stats_table.setItem(row, 2, QTableWidgetItem(last_used_str))
            
            # 性能评分
            score = usage_info.get('performance_score', 0.0)
            self.stats_table.setItem(row, 3, QTableWidgetItem(f"{score:.1f}"))
    
    def select_profile(self, profile_name: str):
        """选择配置文件"""
        for i in range(self.profile_list.count()):
            item = self.profile_list.item(i)
            if item.data(Qt.UserRole) == profile_name:
                self.profile_list.setCurrentItem(item)
                break
    
    def on_profile_selected(self, current_item, previous_item):
        """配置文件选择改变"""
        if current_item:
            profile_name = current_item.data(Qt.UserRole)
            profile = self.config_manager.profiles.get(profile_name)
            
            if profile:
                self.current_profile_name = profile_name
                self.profile_editor.load_profile(profile)
                self.unsaved_changes = False
    
    def on_profile_changed(self, profile_name: str, config_data: Dict):
        """配置文件内容改变"""
        self.unsaved_changes = True
        self.apply_btn.setEnabled(True)
    
    def on_thresholds_changed(self, thresholds: Dict):
        """阈值改变"""
        self.config_manager.violation_thresholds.update(thresholds)
    
    def on_manager_profile_changed(self, profile_name: str):
        """配置管理器配置文件改变"""
        self.refresh_profile_list()
    
    def create_new_profile(self):
        """创建新配置文件"""
        from PyQt5.QtWidgets import QInputDialog
        
        name, ok = QInputDialog.getText(self, "新建配置文件", "请输入配置文件名称:")
        if ok and name.strip():
            name = name.strip()
            
            if name in self.config_manager.profiles:
                QMessageBox.warning(self, "警告", f"配置文件 '{name}' 已存在！")
                return
            
            # 基于当前配置创建新配置
            if self.current_profile_name:
                current_profile = self.config_manager.profiles[self.current_profile_name]
                success = self.config_manager.create_custom_profile(
                    name=name,
                    description=f"基于 {current_profile.name} 的自定义配置",
                    violation_count_range=current_profile.violation_count_range,
                    parser_config=current_profile.parser_config.copy(),
                    display_config=current_profile.display_config.copy(),
                    memory_config=current_profile.memory_config.copy(),
                    ui_config=current_profile.ui_config.copy(),
                    optimization_config=current_profile.optimization_config.copy()
                )
            else:
                # 创建默认配置
                success = self.config_manager.create_custom_profile(
                    name=name,
                    description="自定义配置文件",
                    violation_count_range=(0, float('inf')),
                    parser_config={'parser_type': 'standard_async', 'batch_size': 2000},
                    display_config={'display_mode': 'standard_table', 'page_size': 150},
                    memory_config={'memory_limit_mb': 400},
                    ui_config={'ui_update_interval': 300},
                    optimization_config={'priority': 'balanced'}
                )
            
            if success:
                self.refresh_profile_list()
                self.select_profile(name)
    
    def copy_current_profile(self):
        """复制当前配置文件"""
        if not self.current_profile_name:
            QMessageBox.warning(self, "警告", "请先选择要复制的配置文件！")
            return
        
        from PyQt5.QtWidgets import QInputDialog
        
        original_name = self.current_profile_name
        new_name, ok = QInputDialog.getText(self, "复制配置文件", 
                                          f"请输入新配置文件名称:", 
                                          text=f"{original_name}_副本")
        
        if ok and new_name.strip():
            new_name = new_name.strip()
            
            if new_name in self.config_manager.profiles:
                QMessageBox.warning(self, "警告", f"配置文件 '{new_name}' 已存在！")
                return
            
            original_profile = self.config_manager.profiles[original_name]
            success = self.config_manager.create_custom_profile(
                name=new_name,
                description=f"复制自 {original_profile.name}",
                violation_count_range=original_profile.violation_count_range,
                parser_config=original_profile.parser_config.copy(),
                display_config=original_profile.display_config.copy(),
                memory_config=original_profile.memory_config.copy(),
                ui_config=original_profile.ui_config.copy(),
                optimization_config=original_profile.optimization_config.copy()
            )
            
            if success:
                self.refresh_profile_list()
                self.select_profile(new_name)
    
    def delete_current_profile(self):
        """删除当前配置文件"""
        if not self.current_profile_name:
            QMessageBox.warning(self, "警告", "请先选择要删除的配置文件！")
            return
        
        reply = QMessageBox.question(self, "确认删除", 
                                   f"确定要删除配置文件 '{self.current_profile_name}' 吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            success = self.config_manager.delete_profile(self.current_profile_name)
            if success:
                self.current_profile_name = None
                self.refresh_profile_list()
    
    def import_profiles(self):
        """导入配置文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入配置文件", "", "JSON文件 (*.json);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                imported_count = 0
                for name, profile_data in data.items():
                    if name not in self.config_manager.profiles:
                        profile = PerformanceProfile(**profile_data)
                        self.config_manager.profiles[name] = profile
                        imported_count += 1
                
                if imported_count > 0:
                    self.config_manager._save_profiles()
                    self.refresh_profile_list()
                    QMessageBox.information(self, "导入成功", f"成功导入 {imported_count} 个配置文件！")
                else:
                    QMessageBox.information(self, "导入完成", "没有新的配置文件需要导入。")
                    
            except Exception as e:
                QMessageBox.critical(self, "导入失败", f"导入配置文件失败：{str(e)}")
    
    def export_profiles(self):
        """导出配置文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出配置文件", "performance_profiles.json", "JSON文件 (*.json);;所有文件 (*)"
        )
        
        if file_path:
            try:
                from dataclasses import asdict
                
                data = {}
                for name, profile in self.config_manager.profiles.items():
                    data[name] = asdict(profile)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                QMessageBox.information(self, "导出成功", f"配置文件已导出到：{file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出配置文件失败：{str(e)}")
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        reply = QMessageBox.question(self, "确认重置", 
                                   "确定要重置所有配置为默认值吗？这将删除所有自定义配置！",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            # 清除所有配置文件
            self.config_manager.profiles.clear()
            
            # 重新创建默认配置文件
            self.config_manager._create_default_profiles()
            self.config_manager._save_profiles()
            
            # 重置阈值
            self.config_manager.violation_thresholds = {
                'small_dataset': 2000,
                'medium_dataset': 20000,
                'large_dataset': 50000,
                'very_large_dataset': 100000,
                'memory_warning': 10000,
                'performance_critical': 25000,
                'ui_responsiveness_limit': 5000
            }
            
            # 刷新界面
            self.refresh_profile_list()
            self.threshold_editor.set_thresholds(self.config_manager.violation_thresholds)
            
            QMessageBox.information(self, "重置完成", "所有配置已重置为默认值！")
    
    def apply_changes(self):
        """应用更改"""
        if self.unsaved_changes and self.current_profile_name:
            config_data = self.profile_editor.get_config_data()
            
            # 更新配置文件
            profile = self.config_manager.profiles[self.current_profile_name]
            profile.name = config_data['name']
            profile.description = config_data['description']
            profile.violation_count_range = config_data['violation_count_range']
            profile.parser_config = config_data['parser_config']
            profile.display_config = config_data['display_config']
            profile.memory_config = config_data['memory_config']
            profile.ui_config = config_data['ui_config']
            profile.optimization_config = config_data['optimization_config']
            
            # 保存配置
            self.config_manager._save_profiles()
            
            self.unsaved_changes = False
            self.apply_btn.setEnabled(False)
            
            # 刷新列表
            self.refresh_profile_list()
            
            QMessageBox.information(self, "应用成功", "配置更改已应用！")
    
    def accept_changes(self):
        """接受更改并关闭对话框"""
        if self.unsaved_changes:
            self.apply_changes()
        
        self.accept()
    
    def closeEvent(self, event):
        """关闭事件处理"""
        if self.unsaved_changes:
            reply = QMessageBox.question(self, "未保存的更改", 
                                       "有未保存的更改，是否保存？",
                                       QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel)
            
            if reply == QMessageBox.Save:
                self.apply_changes()
                event.accept()
            elif reply == QMessageBox.Discard:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()