"""
Automatic Memory Optimization System

Implements automatic garbage collection scheduling, memory-efficient data structures,
and automatic fallback to memory-efficient modes when pressure is detected.
"""

import gc
import time
import threading
import psutil
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass
from enum import Enum
from collections import deque
from PyQt5.QtCore import QObject, QTimer, pyqtSignal


class OptimizationMode(Enum):
    """Memory optimization modes"""
    PERFORMANCE = "performance"  # Prioritize speed
    BALANCED = "balanced"       # Balance speed and memory
    MEMORY_EFFICIENT = "memory_efficient"  # Prioritize memory efficiency
    EMERGENCY = "emergency"     # Emergency memory conservation


@dataclass
class OptimizationAction:
    """Represents a memory optimization action"""
    action_type: str
    description: str
    priority: int  # Lower number = higher priority
    target_memory_reduction_mb: float
    estimated_performance_impact: float  # 0.0 to 1.0
    callback: Callable[[], bool]  # Returns True if successful


@dataclass
class MemoryOptimizationResult:
    """Result of memory optimization"""
    actions_taken: List[str]
    memory_freed_mb: float
    time_taken_ms: float
    success: bool
    new_mode: OptimizationMode


class ViolationDataStructureOptimizer:
    """
    Optimizes violation data structures for memory efficiency
    """
    
    def __init__(self):
        self._optimized_structures = {}
        self._optimization_callbacks = []
        self._lock = threading.RLock()
    
    def register_structure(self, name: str, structure: Any, 
                          optimizer_func: Callable[[Any], Any]):
        """Register a data structure for optimization"""
        with self._lock:
            self._optimized_structures[name] = {
                'structure': structure,
                'optimizer': optimizer_func,
                'original_size': self._estimate_size(structure),
                'optimized': False
            }
    
    def optimize_structure(self, name: str) -> bool:
        """Optimize a specific data structure"""
        with self._lock:
            if name not in self._optimized_structures:
                return False
            
            info = self._optimized_structures[name]
            if info['optimized']:
                return True  # Already optimized
            
            try:
                original_structure = info['structure']
                optimized_structure = info['optimizer'](original_structure)
                
                # Update the structure
                info['structure'] = optimized_structure
                info['optimized_size'] = self._estimate_size(optimized_structure)
                info['optimized'] = True
                
                return True
                
            except Exception as e:
                print(f"Error optimizing structure {name}: {e}")
                return False
    
    def optimize_all_structures(self) -> Dict[str, bool]:
        """Optimize all registered structures"""
        results = {}
        with self._lock:
            for name in self._optimized_structures:
                results[name] = self.optimize_structure(name)
        return results
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get optimization statistics"""
        with self._lock:
            stats = {
                'total_structures': len(self._optimized_structures),
                'optimized_count': 0,
                'total_memory_saved': 0,
                'structures': {}
            }
            
            for name, info in self._optimized_structures.items():
                if info['optimized']:
                    stats['optimized_count'] += 1
                    memory_saved = info['original_size'] - info.get('optimized_size', info['original_size'])
                    stats['total_memory_saved'] += memory_saved
                
                stats['structures'][name] = {
                    'optimized': info['optimized'],
                    'original_size': info['original_size'],
                    'current_size': info.get('optimized_size', info['original_size']),
                    'memory_saved': memory_saved if info['optimized'] else 0
                }
            
            return stats
    
    def _estimate_size(self, obj: Any) -> int:
        """Estimate memory size of object"""
        try:
            import sys
            return sys.getsizeof(obj)
        except Exception:
            return 0


class GarbageCollectionScheduler:
    """
    Intelligent garbage collection scheduler based on violation processing
    """
    
    def __init__(self):
        self._gc_thresholds = {
            'violations_processed': 10000,  # GC after processing N violations
            'memory_increase_mb': 50,       # GC after memory increases by N MB
            'time_interval_seconds': 30     # GC at least every N seconds
        }
        
        self._last_gc_time = time.time()
        self._last_gc_memory = self._get_current_memory_mb()
        self._violations_since_gc = 0
        self._gc_stats = {
            'total_collections': 0,
            'total_objects_collected': 0,
            'total_time_spent': 0.0,
            'memory_freed_mb': 0.0
        }
        self._lock = threading.RLock()
    
    def should_collect_garbage(self, violations_processed: int = 0) -> bool:
        """Determine if garbage collection should be triggered"""
        with self._lock:
            current_time = time.time()
            current_memory = self._get_current_memory_mb()
            
            self._violations_since_gc += violations_processed
            
            # Check violation count threshold
            if self._violations_since_gc >= self._gc_thresholds['violations_processed']:
                return True
            
            # Check memory increase threshold
            memory_increase = current_memory - self._last_gc_memory
            if memory_increase >= self._gc_thresholds['memory_increase_mb']:
                return True
            
            # Check time interval threshold
            time_since_gc = current_time - self._last_gc_time
            if time_since_gc >= self._gc_thresholds['time_interval_seconds']:
                return True
            
            return False
    
    def collect_garbage(self, force: bool = False) -> Dict[str, Any]:
        """Perform garbage collection with statistics"""
        with self._lock:
            if not force and not self.should_collect_garbage():
                return {'collected': False, 'reason': 'not_needed'}
            
            start_time = time.perf_counter()
            memory_before = self._get_current_memory_mb()
            
            # Perform garbage collection
            collected_objects = gc.collect()
            
            # Update statistics
            end_time = time.perf_counter()
            memory_after = self._get_current_memory_mb()
            gc_time = end_time - start_time
            memory_freed = max(0, memory_before - memory_after)
            
            self._gc_stats['total_collections'] += 1
            self._gc_stats['total_objects_collected'] += collected_objects
            self._gc_stats['total_time_spent'] += gc_time
            self._gc_stats['memory_freed_mb'] += memory_freed
            
            # Reset counters
            self._last_gc_time = time.time()
            self._last_gc_memory = memory_after
            self._violations_since_gc = 0
            
            return {
                'collected': True,
                'objects_collected': collected_objects,
                'memory_freed_mb': memory_freed,
                'time_taken_ms': gc_time * 1000,
                'memory_before_mb': memory_before,
                'memory_after_mb': memory_after
            }
    
    def set_thresholds(self, violations: int = None, memory_mb: float = None, 
                      time_seconds: float = None):
        """Update GC thresholds"""
        with self._lock:
            if violations is not None:
                self._gc_thresholds['violations_processed'] = violations
            if memory_mb is not None:
                self._gc_thresholds['memory_increase_mb'] = memory_mb
            if time_seconds is not None:
                self._gc_thresholds['time_interval_seconds'] = time_seconds
    
    def get_gc_stats(self) -> Dict[str, Any]:
        """Get garbage collection statistics"""
        with self._lock:
            return {
                **self._gc_stats,
                'thresholds': self._gc_thresholds.copy(),
                'violations_since_gc': self._violations_since_gc,
                'time_since_gc': time.time() - self._last_gc_time,
                'memory_since_gc': self._get_current_memory_mb() - self._last_gc_memory
            }
    
    def _get_current_memory_mb(self) -> float:
        """Get current memory usage in MB"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0


class AutomaticMemoryOptimizer(QObject):
    """
    Main automatic memory optimizer that coordinates all optimization strategies
    """
    
    # Signals
    optimization_performed = pyqtSignal(object)  # MemoryOptimizationResult
    mode_changed = pyqtSignal(str, str)  # old_mode, new_mode
    emergency_mode_activated = pyqtSignal(dict)  # emergency info
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Components
        self.gc_scheduler = GarbageCollectionScheduler()
        self.structure_optimizer = ViolationDataStructureOptimizer()
        
        # Current state
        self._current_mode = OptimizationMode.BALANCED
        self._optimization_enabled = True
        self._emergency_mode_active = False
        
        # Memory monitoring
        self._memory_thresholds = {
            OptimizationMode.PERFORMANCE: 85.0,      # Switch at 85% memory
            OptimizationMode.BALANCED: 75.0,         # Switch at 75% memory
            OptimizationMode.MEMORY_EFFICIENT: 90.0, # Switch at 90% memory
            OptimizationMode.EMERGENCY: 95.0         # Emergency at 95% memory
        }
        
        # Optimization actions registry
        self._optimization_actions = []
        self._register_default_actions()
        
        # Monitoring timer
        self._monitor_timer = QTimer(self)
        self._monitor_timer.timeout.connect(self._monitor_and_optimize)
        self._monitor_timer.start(5000)  # Check every 5 seconds
        
        # Statistics
        self._stats = {
            'optimizations_performed': 0,
            'mode_switches': 0,
            'emergency_activations': 0,
            'total_memory_freed_mb': 0.0,
            'total_optimization_time_ms': 0.0
        }
        
        self._lock = threading.RLock()
    
    def _register_default_actions(self):
        """Register default optimization actions"""
        # Garbage collection action
        self._optimization_actions.append(OptimizationAction(
            action_type="garbage_collection",
            description="Perform garbage collection",
            priority=1,
            target_memory_reduction_mb=20.0,
            estimated_performance_impact=0.1,
            callback=self._perform_garbage_collection
        ))
        
        # Structure optimization action
        self._optimization_actions.append(OptimizationAction(
            action_type="optimize_structures",
            description="Optimize data structures",
            priority=2,
            target_memory_reduction_mb=50.0,
            estimated_performance_impact=0.2,
            callback=self._optimize_data_structures
        ))
        
        # Cache clearing action
        self._optimization_actions.append(OptimizationAction(
            action_type="clear_caches",
            description="Clear violation data caches",
            priority=3,
            target_memory_reduction_mb=100.0,
            estimated_performance_impact=0.5,
            callback=self._clear_violation_caches
        ))
    
    def register_optimization_action(self, action: OptimizationAction):
        """Register a custom optimization action"""
        with self._lock:
            self._optimization_actions.append(action)
            # Sort by priority
            self._optimization_actions.sort(key=lambda x: x.priority)
    
    def set_optimization_mode(self, mode: OptimizationMode, force: bool = False):
        """Set optimization mode"""
        with self._lock:
            if mode == self._current_mode and not force:
                return
            
            old_mode = self._current_mode.value
            self._current_mode = mode
            self._stats['mode_switches'] += 1
            
            # Apply mode-specific settings
            self._apply_mode_settings(mode)
            
            # Emit signal
            self.mode_changed.emit(old_mode, mode.value)
            
            print(f"Memory optimization mode changed: {old_mode} -> {mode.value}")
    
    def _apply_mode_settings(self, mode: OptimizationMode):
        """Apply settings for specific optimization mode"""
        if mode == OptimizationMode.PERFORMANCE:
            # Optimize for performance
            self.gc_scheduler.set_thresholds(
                violations=20000,
                memory_mb=100,
                time_seconds=60
            )
        
        elif mode == OptimizationMode.BALANCED:
            # Balanced settings
            self.gc_scheduler.set_thresholds(
                violations=10000,
                memory_mb=50,
                time_seconds=30
            )
        
        elif mode == OptimizationMode.MEMORY_EFFICIENT:
            # Optimize for memory
            self.gc_scheduler.set_thresholds(
                violations=5000,
                memory_mb=25,
                time_seconds=15
            )
        
        elif mode == OptimizationMode.EMERGENCY:
            # Emergency mode
            self.gc_scheduler.set_thresholds(
                violations=1000,
                memory_mb=10,
                time_seconds=5
            )
            self._emergency_mode_active = True
    
    def _monitor_and_optimize(self):
        """Monitor memory usage and perform optimizations"""
        if not self._optimization_enabled:
            return
        
        try:
            # Get current memory usage
            memory_percent = self._get_memory_usage_percent()
            
            # Determine if mode change is needed
            new_mode = self._determine_optimal_mode(memory_percent)
            if new_mode != self._current_mode:
                self.set_optimization_mode(new_mode)
            
            # Check if optimization is needed
            if self._should_optimize(memory_percent):
                self._perform_optimization()
            
            # Check for emergency conditions
            if memory_percent > 95.0 and not self._emergency_mode_active:
                self._activate_emergency_mode(memory_percent)
        
        except Exception as e:
            print(f"Memory monitoring error: {e}")
    
    def _determine_optimal_mode(self, memory_percent: float) -> OptimizationMode:
        """Determine optimal optimization mode based on memory usage"""
        if memory_percent >= 95.0:
            return OptimizationMode.EMERGENCY
        elif memory_percent >= 85.0:
            return OptimizationMode.MEMORY_EFFICIENT
        elif memory_percent >= 75.0:
            return OptimizationMode.BALANCED
        else:
            return OptimizationMode.PERFORMANCE
    
    def _should_optimize(self, memory_percent: float) -> bool:
        """Determine if optimization should be performed"""
        threshold = self._memory_thresholds.get(self._current_mode, 80.0)
        return memory_percent > threshold
    
    def _perform_optimization(self) -> MemoryOptimizationResult:
        """Perform memory optimization"""
        start_time = time.perf_counter()
        memory_before = self._get_current_memory_mb()
        actions_taken = []
        
        with self._lock:
            # Sort actions by priority and estimated effectiveness
            sorted_actions = sorted(
                self._optimization_actions,
                key=lambda x: (x.priority, -x.target_memory_reduction_mb)
            )
            
            # Perform optimization actions
            for action in sorted_actions:
                try:
                    if action.callback():
                        actions_taken.append(action.description)
                        
                        # Check if we've freed enough memory
                        current_memory = self._get_current_memory_mb()
                        if memory_before - current_memory >= 50:  # 50MB freed
                            break
                
                except Exception as e:
                    print(f"Optimization action failed: {action.description} - {e}")
            
            # Calculate results
            end_time = time.perf_counter()
            memory_after = self._get_current_memory_mb()
            memory_freed = max(0, memory_before - memory_after)
            time_taken = (end_time - start_time) * 1000
            
            # Update statistics
            self._stats['optimizations_performed'] += 1
            self._stats['total_memory_freed_mb'] += memory_freed
            self._stats['total_optimization_time_ms'] += time_taken
            
            # Create result
            result = MemoryOptimizationResult(
                actions_taken=actions_taken,
                memory_freed_mb=memory_freed,
                time_taken_ms=time_taken,
                success=len(actions_taken) > 0,
                new_mode=self._current_mode
            )
            
            # Emit signal
            self.optimization_performed.emit(result)
            
            return result
    
    def _activate_emergency_mode(self, memory_percent: float):
        """Activate emergency memory conservation mode"""
        self._emergency_mode_active = True
        self._stats['emergency_activations'] += 1
        
        emergency_info = {
            'memory_percent': memory_percent,
            'timestamp': time.time(),
            'actions_taken': []
        }
        
        # Perform aggressive optimizations
        try:
            # Force garbage collection
            gc_result = self.gc_scheduler.collect_garbage(force=True)
            if gc_result['collected']:
                emergency_info['actions_taken'].append(f"GC freed {gc_result['memory_freed_mb']:.1f}MB")
            
            # Clear all caches
            if self._clear_violation_caches():
                emergency_info['actions_taken'].append("Cleared violation caches")
            
            # Optimize all structures
            if self._optimize_data_structures():
                emergency_info['actions_taken'].append("Optimized data structures")
            
        except Exception as e:
            emergency_info['error'] = str(e)
        
        # Emit emergency signal
        self.emergency_mode_activated.emit(emergency_info)
        
        print(f"Emergency memory mode activated at {memory_percent:.1f}% memory usage")
    
    def _perform_garbage_collection(self) -> bool:
        """Perform garbage collection optimization action"""
        try:
            result = self.gc_scheduler.collect_garbage(force=True)
            return result['collected'] and result['memory_freed_mb'] > 0
        except Exception as e:
            print(f"Garbage collection failed: {e}")
            return False
    
    def _optimize_data_structures(self) -> bool:
        """Optimize data structures optimization action"""
        try:
            results = self.structure_optimizer.optimize_all_structures()
            return any(results.values())
        except Exception as e:
            print(f"Structure optimization failed: {e}")
            return False
    
    def _clear_violation_caches(self) -> bool:
        """Clear violation data caches optimization action"""
        try:
            # This would be implemented to clear specific violation caches
            # For now, it's a placeholder that always returns True
            return True
        except Exception as e:
            print(f"Cache clearing failed: {e}")
            return False
    
    def track_violation_processing(self, violation_count: int):
        """Track violation processing for GC scheduling"""
        if self.gc_scheduler.should_collect_garbage(violation_count):
            self.gc_scheduler.collect_garbage()
    
    def _get_memory_usage_percent(self) -> float:
        """Get current memory usage percentage"""
        try:
            return psutil.virtual_memory().percent
        except Exception:
            return 0.0
    
    def _get_current_memory_mb(self) -> float:
        """Get current process memory usage in MB"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get comprehensive optimization statistics"""
        with self._lock:
            gc_stats = self.gc_scheduler.get_gc_stats()
            structure_stats = self.structure_optimizer.get_optimization_stats()
            
            return {
                'current_mode': self._current_mode.value,
                'emergency_mode_active': self._emergency_mode_active,
                'memory_usage_percent': self._get_memory_usage_percent(),
                'process_memory_mb': self._get_current_memory_mb(),
                'optimization_stats': self._stats.copy(),
                'gc_stats': gc_stats,
                'structure_stats': structure_stats,
                'registered_actions': len(self._optimization_actions),
                'timestamp': time.time()
            }
    
    def enable_optimization(self, enabled: bool = True):
        """Enable or disable automatic optimization"""
        self._optimization_enabled = enabled
        if enabled:
            self._monitor_timer.start(5000)
        else:
            self._monitor_timer.stop()
    
    def force_optimization(self) -> MemoryOptimizationResult:
        """Force immediate optimization"""
        return self._perform_optimization()
    
    def reset_emergency_mode(self):
        """Reset emergency mode"""
        self._emergency_mode_active = False
        if self._current_mode == OptimizationMode.EMERGENCY:
            # Switch back to balanced mode
            self.set_optimization_mode(OptimizationMode.BALANCED)
    
    def cleanup(self):
        """Clean up resources"""
        self._monitor_timer.stop()
        self._optimization_enabled = False
        
        # Perform final cleanup
        try:
            self.gc_scheduler.collect_garbage(force=True)
        except Exception:
            pass