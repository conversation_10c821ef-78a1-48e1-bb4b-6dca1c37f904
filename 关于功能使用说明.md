# RunSim 控制台 - 关于功能使用说明

## 功能概述

已成功为 RunSim GUI 主界面添加了关于按钮和关于对话框功能，用户可以通过菜单栏访问详细的软件信息。

## 访问方式

1. **菜单栏访问**：
   - 启动 RunSim 控制台
   - 点击菜单栏中的"帮助"菜单
   - 选择"关于"选项

2. **快捷访问**：
   - 关于对话框会在新窗口中打开
   - 支持多标签页浏览不同信息

## 关于对话框内容

### 📋 基本信息标签页
- **软件名称**：RunSim 控制台
- **版本信息**：2.0.0 (Build 20240805)
- **开发者**：何佳东
- **邮箱**：<EMAIL>
- **公司**：紫光展锐 (UNISOC)
- **最后更新**：2024年8月5日
- **软件描述**：详细介绍软件功能和特性

### 🔧 工具集成标签页
包含以下EDA工具和流程软件信息：

#### 仿真器
- Cadence Incisive (irun) - 高性能混合信号仿真器
- Synopsys VCS - 业界领先的SystemVerilog仿真器
- Mentor ModelSim/QuestaSim - 多语言仿真平台

#### 综合工具
- Synopsys Design Compiler - 逻辑综合工具
- Cadence Genus - 下一代综合平台

#### 时序分析
- Synopsys PrimeTime - 静态时序分析工具
- Cadence Tempus - 时序签核平台

#### 布局布线
- Cadence Innovus - 数字后端设计平台
- Synopsys IC Compiler II - 布局布线工具

#### 验证工具
- Cadence JasperGold - 形式化验证平台
- Synopsys VC Formal - 形式化验证工具
- UVM/OVM 验证方法学支持

#### 调试工具
- Cadence SimVision - 波形查看器
- Synopsys Verdi - 调试和分析平台

#### 覆盖率分析
- Cadence IMC - 覆盖率管理平台
- Synopsys VCS Coverage - 覆盖率分析工具

#### 回归测试
- LSF/SGE - 集群作业管理系统
- 大规模并行回归测试支持

#### 版本控制
- Git - 分布式版本控制系统
- Perforce - 企业级版本控制

#### 项目管理
- 多项目并行开发支持
- 配置模板和环境管理
- 自动化流程脚本生成

### 📝 更新日志标签页
- **版本历史**：详细的版本更新记录
- **功能改进**：每个版本的新增功能
- **技术优化**：性能和稳定性改进
- **未来规划**：后续版本计划

### 💻 系统信息标签页
- **操作系统**：当前运行的操作系统信息
- **架构**：系统架构信息
- **Python版本**：Python运行环境信息
- **PyQt5版本**：GUI框架版本信息

### 📦 依赖信息标签页
- **主要依赖库**：PyQt5、Python标准库等
- **可选依赖库**：psutil、openpyxl、matplotlib等
- **插件系统**：插件架构说明
- **特性列表**：软件主要特性介绍

### 📄 许可证标签页
- **MIT许可证**：完整的许可证条款
- **第三方库许可证**：依赖库的许可证信息
- **免责声明**：使用条款和免责声明

## 技术实现

### 文件结构
```
views/
├── main_window.py      # 主窗口（已修改）
└── about_dialog.py     # 关于对话框（新增）
```

### 主要修改
1. **main_window.py**：
   - 在菜单栏添加"帮助"菜单
   - 添加"关于"菜单项
   - 实现 `show_about_dialog()` 方法

2. **about_dialog.py**：
   - 创建完整的关于对话框类
   - 实现多标签页界面
   - 包含详细的软件和工具信息

### 样式特性
- 现代化的UI设计
- 响应式布局
- 统一的字体和颜色方案
- 图标和表情符号增强视觉效果
- 滚动区域支持大量内容

## 使用建议

1. **信息查看**：用户可以通过不同标签页快速找到所需信息
2. **版本确认**：通过基本信息标签页确认当前软件版本
3. **工具了解**：通过工具集成标签页了解支持的EDA工具
4. **问题反馈**：通过联系信息进行问题反馈和建议

## 后续扩展

可以根据需要进一步扩展关于对话框的功能：
- 添加在线帮助链接
- 集成用户手册
- 添加快捷键说明
- 包含视频教程链接
- 添加社区论坛链接

---

**开发者**：何佳东  
**邮箱**：<EMAIL>  
**更新日期**：2024年8月5日
