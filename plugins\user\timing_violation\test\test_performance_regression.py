"""
Performance Regression Testing

Automated performance tests that run with each code change to detect
performance regressions in violation processing speed and memory usage.
"""

import unittest
import tempfile
import os
import time
import json
import psutil
from typing import Dict, List, Any, Optional
from datetime import datetime
from unittest.mock import Mock, patch

try:
    from .performance_optimizer import PerformanceOptimizer
    from .parser import <PERSON><PERSON><PERSON>og<PERSON>ars<PERSON>, HighPerformanceVioLogParser, HighPerformanceAsyncParser
    from .models import ViolationDataModel
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.insert(0, os.path.dirname(__file__))
    from performance_optimizer import PerformanceOptimizer
    from parser import VioLogParser, HighPerformanceVioLogParser, HighPerformanceAsyncParser
    from models import ViolationDataModel


class PerformanceRegressionTests(unittest.TestCase):
    """Performance regression test suite"""
    
    BASELINE_FILE = "performance_baseline.json"
    REGRESSION_THRESHOLD = 0.20  # 20% performance degradation threshold
    MEMORY_REGRESSION_THRESHOLD = 0.30  # 30% memory usage increase threshold
    
    def setUp(self):
        """Set up test environment"""
        self.performance_optimizer = PerformanceOptimizer()
        self.temp_dir = tempfile.mkdtemp()
        self.baseline_metrics = self._load_baseline_metrics()
        
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def _load_baseline_metrics(self) -> Dict[str, Any]:
        """Load baseline performance metrics from file"""
        baseline_path = os.path.join(os.path.dirname(__file__), self.BASELINE_FILE)
        
        if os.path.exists(baseline_path):
            try:
                with open(baseline_path, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                pass
        
        # Return default baseline if file doesn't exist or is corrupted
        return self._get_default_baseline()
    
    def _get_default_baseline(self) -> Dict[str, Any]:
        """Get default baseline metrics for initial runs"""
        return {
            "version": "1.0.0",
            "created": datetime.now().isoformat(),
            "benchmarks": {
                "small_dataset_parse": {
                    "violation_count": 1000,
                    "execution_time": 1.0,
                    "memory_usage_mb": 50.0,
                    "violations_per_second": 1000.0
                },
                "medium_dataset_parse": {
                    "violation_count": 10000,
                    "execution_time": 5.0,
                    "memory_usage_mb": 200.0,
                    "violations_per_second": 2000.0
                },
                "large_dataset_parse": {
                    "violation_count": 50000,
                    "execution_time": 15.0,
                    "memory_usage_mb": 800.0,
                    "violations_per_second": 3333.0
                },
                "batch_confirmation": {
                    "violation_count": 5000,
                    "execution_time": 2.0,
                    "memory_usage_mb": 100.0,
                    "violations_per_second": 2500.0
                }
            }
        }
    
    def _save_baseline_metrics(self, metrics: Dict[str, Any]) -> None:
        """Save baseline metrics to file"""
        baseline_path = os.path.join(os.path.dirname(__file__), self.BASELINE_FILE)
        
        try:
            with open(baseline_path, 'w') as f:
                json.dump(metrics, f, indent=2)
        except IOError as e:
            print(f"Warning: Could not save baseline metrics: {e}")
    
    def _generate_test_violation_file(self, violation_count: int, file_path: str) -> None:
        """Generate a test violation file with specified violation count"""
        # Ensure the file is named vio_summary.log as required by the parser
        if not file_path.endswith('vio_summary.log'):
            file_path = os.path.join(os.path.dirname(file_path), 'vio_summary.log')
        
        with open(file_path, 'w', encoding='utf-8') as f:
            # Write header in the format expected by the parser
            f.write("# Timing Violation Summary Report\n")
            f.write("# Generated for regression testing\n")
            f.write("\n")
            
            # Generate violations in the format expected by the parser
            # Format: key : value pairs separated by ---- lines
            for i in range(violation_count):
                time_fs = (10500 + (i % 50) * 100) * 1000  # Convert ns to fs
                
                # Write violation fields in key : value format
                f.write(f"NUM : {i+1:06d}\n")
                f.write(f"Hier : /design/module_{i%100}/submodule_{i%10}/signal_{i}\n")
                f.write(f"Time : {time_fs} FS\n")
                f.write(f"Check : Setup violation on signal_{i}\n")
                f.write("----\n")  # Violation separator
        
        return file_path
    
    def _measure_performance(self, operation_name: str, operation_func, *args, **kwargs) -> Dict[str, Any]:
        """Measure performance metrics for an operation"""
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        start_time = time.time()
        result = operation_func(*args, **kwargs)
        end_time = time.time()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        execution_time = end_time - start_time
        
        # Calculate violations per second if result contains violation count
        violations_per_second = 0
        if hasattr(result, '__len__'):
            violations_per_second = len(result) / execution_time if execution_time > 0 else 0
        
        return {
            'operation': operation_name,
            'execution_time': execution_time,
            'memory_usage_mb': final_memory - initial_memory,
            'violations_per_second': violations_per_second,
            'result': result
        }
    
    def _check_regression(self, benchmark_name: str, current_metrics: Dict[str, Any]) -> None:
        """Check for performance regression against baseline"""
        if benchmark_name not in self.baseline_metrics.get('benchmarks', {}):
            # No baseline exists, this becomes the new baseline
            print(f"No baseline found for {benchmark_name}, establishing new baseline")
            return
        
        baseline = self.baseline_metrics['benchmarks'][benchmark_name]
        
        # Check execution time regression
        time_regression = (current_metrics['execution_time'] - baseline['execution_time']) / baseline['execution_time']
        self.assertLess(time_regression, self.REGRESSION_THRESHOLD,
            f"Performance regression detected in {benchmark_name}: "
            f"execution time increased by {time_regression:.1%} "
            f"(current: {current_metrics['execution_time']:.2f}s, "
            f"baseline: {baseline['execution_time']:.2f}s)")
        
        # Check memory usage regression
        memory_regression = (current_metrics['memory_usage_mb'] - baseline['memory_usage_mb']) / baseline['memory_usage_mb']
        self.assertLess(memory_regression, self.MEMORY_REGRESSION_THRESHOLD,
            f"Memory regression detected in {benchmark_name}: "
            f"memory usage increased by {memory_regression:.1%} "
            f"(current: {current_metrics['memory_usage_mb']:.1f}MB, "
            f"baseline: {baseline['memory_usage_mb']:.1f}MB)")
        
        # Check throughput regression (violations per second should not decrease significantly)
        if baseline['violations_per_second'] > 0:
            throughput_regression = (baseline['violations_per_second'] - current_metrics['violations_per_second']) / baseline['violations_per_second']
            self.assertLess(throughput_regression, self.REGRESSION_THRESHOLD,
                f"Throughput regression detected in {benchmark_name}: "
                f"violations per second decreased by {throughput_regression:.1%} "
                f"(current: {current_metrics['violations_per_second']:.0f}, "
                f"baseline: {baseline['violations_per_second']:.0f})")
    
    def test_small_dataset_parsing_regression(self):
        """Test for regression in small dataset parsing performance"""
        violation_count = 1000
        test_file = os.path.join(self.temp_dir, "vio_summary.log")
        self._generate_test_violation_file(violation_count, test_file)
        
        parser = VioLogParser()
        metrics = self._measure_performance(
            "small_dataset_parse",
            parser.parse_log_file,
            test_file
        )
        
        # Add violation count for throughput calculation
        metrics['violations_per_second'] = violation_count / metrics['execution_time']
        
        self._check_regression("small_dataset_parse", metrics)
    
    def test_medium_dataset_parsing_regression(self):
        """Test for regression in medium dataset parsing performance"""
        violation_count = 10000
        test_file = os.path.join(self.temp_dir, "vio_summary.log")
        self._generate_test_violation_file(violation_count, test_file)
        
        parser = HighPerformanceVioLogParser()
        metrics = self._measure_performance(
            "medium_dataset_parse",
            parser.parse_log_file_streaming,  # Use the correct method name
            test_file
        )
        
        metrics['violations_per_second'] = violation_count / metrics['execution_time']
        
        self._check_regression("medium_dataset_parse", metrics)
    
    def test_large_dataset_parsing_regression(self):
        """Test for regression in large dataset parsing performance"""
        violation_count = 50000
        test_file = os.path.join(self.temp_dir, "vio_summary.log")
        self._generate_test_violation_file(violation_count, test_file)
        
        # HighPerformanceAsyncParser requires file_path parameter
        parser = HighPerformanceAsyncParser(test_file)
        
        # For async parser, we need to handle it differently
        def run_async_parser():
            parser.start()
            parser.wait()  # Wait for completion
            return parser.violations if hasattr(parser, 'violations') else []
        
        metrics = self._measure_performance(
            "large_dataset_parse",
            run_async_parser
        )
        
        metrics['violations_per_second'] = violation_count / metrics['execution_time']
        
        self._check_regression("large_dataset_parse", metrics)
    
    def test_batch_confirmation_regression(self):
        """Test for regression in batch confirmation performance"""
        violation_count = 5000
        
        # Create mock violations for batch confirmation
        violations = []
        for i in range(violation_count):
            violation = Mock()
            violation.id = i
            violation.status = "unconfirmed"
            violations.append(violation)
        
        # Mock batch confirmation operation
        def mock_batch_confirm(violation_list):
            # Simulate batch confirmation processing
            time.sleep(len(violation_list) * 0.0004)  # 0.4ms per violation
            for v in violation_list:
                v.status = "confirmed"
            return violation_list
        
        metrics = self._measure_performance(
            "batch_confirmation",
            mock_batch_confirm,
            violations
        )
        
        metrics['violations_per_second'] = violation_count / metrics['execution_time']
        
        self._check_regression("batch_confirmation", metrics)
    
    def test_memory_usage_regression(self):
        """Test for memory usage regression across different dataset sizes"""
        test_cases = [
            (1000, "small"),
            (10000, "medium"),
            (50000, "large")
        ]
        
        for violation_count, size_category in test_cases:
            with self.subTest(size=size_category, count=violation_count):
                test_file = os.path.join(self.temp_dir, "vio_summary.log")
                self._generate_test_violation_file(violation_count, test_file)
                
                # Select appropriate parser and method
                if size_category == "small":
                    parser = VioLogParser()
                    parse_method = parser.parse_log_file
                elif size_category == "medium":
                    parser = HighPerformanceVioLogParser()
                    parse_method = parser.parse_log_file_streaming  # Use correct method
                else:  # large
                    parser = HighPerformanceAsyncParser(test_file)  # Pass file_path
                    def parse_method():
                        parser.start()
                        parser.wait()
                        return parser.violations if hasattr(parser, 'violations') else []
                
                # Measure memory usage
                process = psutil.Process()
                initial_memory = process.memory_info().rss / 1024 / 1024
                
                if size_category == "large":
                    result = parse_method()
                else:
                    result = parse_method(test_file)
                
                peak_memory = process.memory_info().rss / 1024 / 1024
                memory_usage = peak_memory - initial_memory
                
                # Check against memory usage requirements
                if size_category == "small":
                    self.assertLess(memory_usage, 100,
                        f"Small dataset memory usage {memory_usage:.1f}MB exceeds 100MB limit")
                elif size_category == "medium":
                    self.assertLess(memory_usage, 500,
                        f"Medium dataset memory usage {memory_usage:.1f}MB exceeds 500MB limit")
                else:  # large
                    self.assertLess(memory_usage, 1024,
                        f"Large dataset memory usage {memory_usage:.1f}MB exceeds 1GB limit")
    
    def test_violation_processing_speed_regression(self):
        """Test for regression in violation processing speed benchmarks"""
        speed_benchmarks = [
            (1000, 500),    # 1000 violations should process at >500 violations/sec
            (10000, 1000),  # 10000 violations should process at >1000 violations/sec
            (50000, 2000),  # 50000 violations should process at >2000 violations/sec
        ]
        
        for violation_count, min_speed in speed_benchmarks:
            with self.subTest(violation_count=violation_count, min_speed=min_speed):
                test_file = os.path.join(self.temp_dir, "vio_summary.log")
                self._generate_test_violation_file(violation_count, test_file)
                
                # Select appropriate parser and method
                if violation_count <= 2000:
                    parser = VioLogParser()
                    parse_method = parser.parse_log_file
                elif violation_count <= 20000:
                    parser = HighPerformanceVioLogParser()
                    parse_method = parser.parse_log_file_streaming  # Use correct method
                else:
                    parser = HighPerformanceAsyncParser(test_file)  # Pass file_path
                    def parse_method():
                        parser.start()
                        parser.wait()
                        return parser.violations if hasattr(parser, 'violations') else []
                
                # Measure processing speed
                start_time = time.time()
                if violation_count > 20000:
                    result = parse_method()  # Async case - no file parameter
                else:
                    result = parse_method(test_file)  # Sync case - with file parameter
                end_time = time.time()
                
                processing_speed = violation_count / (end_time - start_time)
                
                self.assertGreater(processing_speed, min_speed,
                    f"Processing speed {processing_speed:.0f} violations/sec is below "
                    f"minimum threshold of {min_speed} violations/sec for {violation_count} violations")
    
    def test_ui_responsiveness_regression(self):
        """Test for regression in UI responsiveness during violation processing"""
        # This test simulates UI operations during violation processing
        violation_count = 10000
        test_file = os.path.join(self.temp_dir, "vio_summary.log")
        self._generate_test_violation_file(violation_count, test_file)
        
        # Mock UI operations
        ui_response_times = []
        
        def mock_ui_operation():
            """Simulate a UI operation like scrolling or filtering"""
            start = time.time()
            time.sleep(0.001)  # Simulate minimal UI work
            end = time.time()
            return end - start
        
        # Start parsing in background (simulated)
        parser = HighPerformanceVioLogParser()
        start_parse = time.time()
        
        # Simulate UI operations during parsing
        for i in range(10):
            response_time = mock_ui_operation()
            ui_response_times.append(response_time)
            time.sleep(0.1)  # Wait between UI operations
        
        # Complete parsing
        result = parser.parse_log_file_streaming(test_file)  # Use correct method
        end_parse = time.time()
        
        # Check UI responsiveness (should be < 100ms per requirement 2.3)
        max_response_time = max(ui_response_times) * 1000  # Convert to ms
        avg_response_time = sum(ui_response_times) / len(ui_response_times) * 1000
        
        self.assertLess(max_response_time, 100,
            f"UI response time {max_response_time:.1f}ms exceeds 100ms threshold")
        self.assertLess(avg_response_time, 50,
            f"Average UI response time {avg_response_time:.1f}ms exceeds 50ms target")
    
    def update_baseline_metrics(self) -> None:
        """Update baseline metrics with current performance measurements"""
        print("Updating performance baseline metrics...")
        
        new_baseline = {
            "version": "1.0.0",
            "updated": datetime.now().isoformat(),
            "benchmarks": {}
        }
        
        # Measure current performance for all benchmarks
        benchmarks = [
            (1000, "small_dataset_parse", VioLogParser, "parse_log_file"),
            (10000, "medium_dataset_parse", HighPerformanceVioLogParser, "parse_log_file"),
            (50000, "large_dataset_parse", HighPerformanceAsyncParser, "parse_log_file_streaming"),
        ]
        
        for violation_count, benchmark_name, parser_class, method_name in benchmarks:
            test_file = os.path.join(self.temp_dir, f"baseline_{benchmark_name}.log")
            self._generate_test_violation_file(violation_count, test_file)
            
            parser = parser_class()
            parse_method = getattr(parser, method_name)
            
            metrics = self._measure_performance(benchmark_name, parse_method, test_file)
            
            new_baseline["benchmarks"][benchmark_name] = {
                "violation_count": violation_count,
                "execution_time": metrics['execution_time'],
                "memory_usage_mb": metrics['memory_usage_mb'],
                "violations_per_second": violation_count / metrics['execution_time']
            }
        
        # Add batch confirmation benchmark
        violations = [Mock() for _ in range(5000)]
        def mock_batch_confirm(violation_list):
            time.sleep(len(violation_list) * 0.0004)
            return violation_list
        
        batch_metrics = self._measure_performance("batch_confirmation", mock_batch_confirm, violations)
        new_baseline["benchmarks"]["batch_confirmation"] = {
            "violation_count": 5000,
            "execution_time": batch_metrics['execution_time'],
            "memory_usage_mb": batch_metrics['memory_usage_mb'],
            "violations_per_second": 5000 / batch_metrics['execution_time']
        }
        
        self._save_baseline_metrics(new_baseline)
        print("Baseline metrics updated successfully")


if __name__ == '__main__':
    # Check if we should update baseline metrics
    import sys
    if '--update-baseline' in sys.argv:
        test_instance = PerformanceRegressionTests()
        test_instance.setUp()
        test_instance.update_baseline_metrics()
        test_instance.tearDown()
        print("Baseline metrics updated. Run tests normally to check for regressions.")
    else:
        unittest.main(verbosity=2)