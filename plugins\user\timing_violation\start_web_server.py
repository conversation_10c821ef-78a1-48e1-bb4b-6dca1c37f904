#!/usr/bin/env python3
"""
启动本地Web服务器用于测试时序违例网页展示

这个脚本启动一个简单的HTTP服务器来避免CORS问题，
让网页能够正常加载JSON数据文件。
"""

import os
import sys
import http.server
import socketserver
import webbrowser
from pathlib import Path
import argparse


class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()


def start_web_server(port=8000, directory=None, open_browser=True):
    """
    启动Web服务器
    
    Args:
        port: 端口号
        directory: 服务目录
        open_browser: 是否自动打开浏览器
    """
    if directory is None:
        directory = Path("VIOLATION_CHECK/web_display").resolve()
    else:
        directory = Path(directory).resolve()
    
    if not directory.exists():
        print(f"错误: 目录不存在: {directory}")
        print("请先运行数据生成脚本: python generate_optimized_web_data.py")
        return False
    
    # 切换到目标目录
    os.chdir(directory)
    
    try:
        with socketserver.TCPServer(("", port), CORSHTTPRequestHandler) as httpd:
            print(f"启动Web服务器...")
            print(f"服务目录: {directory}")
            print(f"访问地址: http://localhost:{port}")
            print(f"测试页面: http://localhost:{port}/test_data_loading.html")
            print(f"简单测试: http://localhost:{port}/simple_test.html")
            print("按 Ctrl+C 停止服务器")
            
            if open_browser:
                webbrowser.open(f'http://localhost:{port}')
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n服务器已停止")
        return True
    except OSError as e:
        if e.errno == 10048:  # Windows: Address already in use
            print(f"错误: 端口 {port} 已被占用，请尝试其他端口")
            print(f"例如: python {sys.argv[0]} --port {port + 1}")
        else:
            print(f"错误: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="启动时序违例网页展示的本地Web服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python start_web_server.py
  python start_web_server.py --port 8080
  python start_web_server.py --directory ./VIOLATION_CHECK/web_display
  python start_web_server.py --no-browser
        """
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        default=8000,
        help='Web服务器端口号 (默认: 8000)'
    )
    
    parser.add_argument(
        '--directory', '-d',
        type=str,
        help='Web服务目录 (默认: VIOLATION_CHECK/web_display)'
    )
    
    parser.add_argument(
        '--no-browser',
        action='store_true',
        help='不自动打开浏览器'
    )
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("时序违例网页展示 - 本地Web服务器")
    print("=" * 60)
    
    success = start_web_server(
        port=args.port,
        directory=args.directory,
        open_browser=not args.no_browser
    )
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()