# 寄存器表格解析器插件

## 概述

寄存器表格解析器插件是一个用于解析Excel格式寄存器规格表的工具，提供可视化显示和交互式字段编辑功能。

## 功能特性

### 已实现功能（任务1）

- **插件结构**: 完整的插件目录结构和主插件类
- **数据模型**: 完整的数据模型定义，包括表头信息、字段信息、寄存器信息等
- **Excel解析器**: 支持Excel文件解析，包括格式验证和数据提取
- **错误处理**: 全面的错误处理和验证机制
- **基础GUI**: 主窗口框架和文件加载功能

### 待实现功能（后续任务）

- 寄存器列表显示和搜索功能
- 字段编辑器和实时值计算
- 数字格式转换系统
- 完整的用户界面集成

## 文件结构

```
plugins/user/register_table_parser/
├── __init__.py                 # 包初始化文件
├── models.py                   # 数据模型定义
├── parser.py                   # Excel解析器
├── main_window.py              # 主窗口GUI
├── utils.py                    # 工具函数
├── test_parser.py              # 测试脚本
└── README.md                   # 说明文档
```

## 数据模型

### HeaderInfo
表头信息，包含：
- `project_name`: 项目名称
- `sub_system`: 子系统名称
- `module_name`: 模块名称
- `base_addr`: 基地址

### FieldInfo
字段信息，包含：
- `name`: 字段名称
- `bit_range`: 位范围（如"31:24"）
- `rw_attribute`: 读写属性（RW/RO/WO）
- `reset_value`: 复位值
- `description`: 描述

### RegisterInfo
寄存器信息，包含：
- `offset`: 偏移地址
- `name`: 寄存器名称
- `description`: 描述
- `width`: 位宽（默认32位）
- `fields`: 字段列表

### RegisterTableData
完整的寄存器表格数据，包含：
- `header`: 表头信息
- `registers`: 寄存器列表

## Excel文件格式要求

### 表头格式
前4行应包含表头信息：
- 项目名称
- 子系统名称
- 模块名称
- 基地址

### 数据格式
第5行或之后为列标题行，必需的列包括：
- **Offset**: 偏移地址
- **RegName**: 寄存器名称
- **FieldName**: 字段名称
- **Bit Range**: 位范围
- **RW**: 读写属性

可选的列：
- **Reset Value**: 复位值
- **Description**: 描述

### 数据行格式
- 每个寄存器可以有多个字段
- 保留字段（Reserved）会被自动跳过
- 支持十六进制（0x前缀）和十进制地址格式
- 位范围支持"31:24"或"15"格式

## 使用方法

### 插件加载
插件会自动加载到主窗口的工具菜单中。

### 文件解析
1. 点击"寄存器表格解析器"菜单项打开插件窗口
2. 点击"加载Excel文件"按钮选择文件
3. 等待解析完成，查看表头信息和寄存器列表

### 错误处理
- 文件格式错误会显示详细的错误信息
- 解析过程中的警告会记录到日志
- 支持数据验证和完整性检查

## 工具函数

### NumberFormatConverter
数字格式转换器，支持：
- 二进制、十进制、十六进制格式转换
- 字符串解析和格式检测
- 位宽指定和补零

### BitRangeParser
位范围解析工具，支持：
- 位范围字符串解析
- 位宽计算
- 格式验证

### FieldValueCalculator
字段值计算工具，支持：
- 从寄存器值提取字段值
- 将字段值插入寄存器值
- 字段值验证

## 测试

运行测试脚本验证功能：

```bash
python plugins/user/register_table_parser/test_parser.py
```

测试包括：
- 数据模型功能测试
- 工具函数测试
- 解析器基本功能测试

## 依赖项

- **PyQt5**: GUI框架
- **openpyxl**: Excel文件处理（.xlsx格式）
- **xlrd**: Excel文件处理（.xls格式）
- **Python 3.6+**: 基础运行环境

### 依赖安装

运行依赖安装脚本：
```bash
python plugins/user/register_table_parser/install_dependencies.py
```

或手动安装：
```bash
pip install openpyxl xlrd
```

## 错误处理

### ParseError
解析错误异常，包含：
- 错误消息
- 行号和列信息（可选）
- 格式化的错误消息

### ValidationError
验证错误异常，包含：
- 错误消息
- 字段名和寄存器名（可选）
- 格式化的错误消息

## 开发说明

### 扩展功能
后续任务将实现：
1. 寄存器列表GUI组件
2. 搜索和过滤功能
3. 字段编辑器
4. 实时值计算
5. 数字格式转换界面

### 代码规范
- 使用类型提示
- 完整的文档字符串
- 异常处理和日志记录
- 单元测试覆盖

## 版本历史

### v1.0.0
- 初始版本
- 完成任务1：插件结构和Excel解析器
- 实现核心数据模型和解析功能
- 添加基础GUI框架和错误处理