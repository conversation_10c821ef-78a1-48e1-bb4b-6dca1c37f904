"""
Unit tests for Data exporter.
"""

import unittest
import tempfile
import os
import json
from pathlib import Path
from unittest.mock import Mock, patch

# Import the module to test
import sys
sys.path.append(str(Path(__file__).parent.parent))

from data_exporter import DataExporter, DataExportError


class TestDataExporter(unittest.TestCase):
    """Test cases for DataExporter class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.violation_check_dir = os.path.join(self.temp_dir, "VIOLATION_CHECK")
        os.makedirs(self.violation_check_dir)
        
        self.exporter = DataExporter(self.violation_check_dir)
        
        # Create sample violation data
        self.sample_violations = [
            {
                'num': 1,
                'hier': 'cpu/core/reg1',
                'time_ns': 1.5,
                'check_info': 'setup check',
                'status': 'confirmed',
                'confirmer': 'john_doe',
                'result': 'false_positive',
                'reason': 'Clock skew issue',
                'confirmed_at': '2024-01-15 10:30:00',
                'corner': 'ss',
                'case': 'test1',
                'source': 'excel'
            },
            {
                'num': 2,
                'hier': 'cpu/core/reg2',
                'time_ns': 2.3,
                'check_info': 'hold check',
                'status': 'confirmed',
                'confirmer': 'jane_smith',
                'result': 'real_violation',
                'reason': 'Timing constraint violation',
                'confirmed_at': '2024-01-16 14:20:00',
                'corner': 'ff',
                'case': 'test2',
                'source': 'database'
            }
        ]
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initialization(self):
        """Test exporter initialization."""
        self.assertEqual(str(self.exporter.violation_check_dir), self.violation_check_dir)
        self.assertTrue(str(self.exporter.web_display_dir).endswith("web_display"))
        self.assertTrue(str(self.exporter.data_dir).endswith("data"))
    
    @patch.object(DataExporter, 'load_violation_data')
    def test_export_all_data_success(self, mock_load_data):
        """Test successful data export."""
        # Mock the data loading
        self.exporter.violations_data = self.sample_violations
        self.exporter.corners = {'ss', 'ff'}
        self.exporter.cases = {'test1', 'test2'}
        
        result = self.exporter.export_all_data()
        
        self.assertTrue(result)
        mock_load_data.assert_called_once()
        
        # Check that output directories were created
        self.assertTrue(self.exporter.data_dir.exists())
        self.assertTrue((self.exporter.data_dir / "corners").exists())
        self.assertTrue((self.exporter.data_dir / "violations").exists())
    
    @patch.object(DataExporter, 'load_violation_data')
    def test_export_all_data_failure(self, mock_load_data):
        """Test data export failure."""
        mock_load_data.side_effect = Exception("Test error")
        
        result = self.exporter.export_all_data()
        
        self.assertFalse(result)
    
    def test_calculate_summary_statistics(self):
        """Test summary statistics calculation."""
        self.exporter.violations_data = self.sample_violations
        
        summary = self.exporter.calculate_summary_statistics()
        
        self.assertEqual(summary['total_violations'], 2)
        self.assertEqual(summary['confirmed_violations'], 2)
        self.assertEqual(summary['pending_violations'], 0)
        self.assertEqual(summary['confirmation_rate'], 100.0)
        self.assertIsNotNone(summary['latest_confirmation'])
    
    def test_get_statistics_by_corner(self):
        """Test statistics by corner calculation."""
        self.exporter.violations_data = self.sample_violations
        self.exporter.corners = {'ss', 'ff'}
        
        stats = self.exporter.get_statistics_by_corner()
        
        self.assertEqual(stats['ss']['total'], 1)
        self.assertEqual(stats['ss']['confirmed'], 1)
        self.assertEqual(stats['ff']['total'], 1)
        self.assertEqual(stats['ff']['confirmed'], 1)
    
    def test_get_statistics_by_case(self):
        """Test statistics by case calculation."""
        self.exporter.violations_data = self.sample_violations
        self.exporter.cases = {'test1', 'test2'}
        
        stats = self.exporter.get_statistics_by_case()
        
        self.assertEqual(stats['test1']['total'], 1)
        self.assertEqual(stats['test1']['confirmed'], 1)
        self.assertEqual(stats['test2']['total'], 1)
        self.assertEqual(stats['test2']['confirmed'], 1)
    
    def test_get_statistics_by_confirmer(self):
        """Test statistics by confirmer calculation."""
        self.exporter.violations_data = self.sample_violations
        
        stats = self.exporter.get_statistics_by_confirmer()
        
        self.assertEqual(stats['john_doe'], 1)
        self.assertEqual(stats['jane_smith'], 1)
    
    def test_get_confirmation_timeline(self):
        """Test confirmation timeline generation."""
        self.exporter.violations_data = self.sample_violations
        
        timeline = self.exporter.get_confirmation_timeline()
        
        self.assertEqual(len(timeline), 2)
        self.assertEqual(timeline[0]['date'], '2024-01-15')
        self.assertEqual(timeline[0]['confirmations'], 1)
        self.assertEqual(timeline[1]['date'], '2024-01-16')
        self.assertEqual(timeline[1]['confirmations'], 1)
    
    def test_get_data_sources(self):
        """Test data sources identification."""
        self.exporter.violations_data = self.sample_violations
        
        sources = self.exporter.get_data_sources()
        
        self.assertIn('excel', sources)
        self.assertIn('database', sources)
        self.assertEqual(len(sources), 2)
    
    def test_get_export_info(self):
        """Test export information retrieval."""
        info = self.exporter.get_export_info()
        
        self.assertEqual(info['violation_check_dir'], self.violation_check_dir)
        self.assertIn('web_display_dir', info)
        self.assertIn('data_dir', info)
        self.assertEqual(info['violations_loaded'], 0)  # No data loaded yet
        self.assertEqual(info['corners_found'], 0)
        self.assertEqual(info['cases_found'], 0)
    
    def test_validate_data_integrity(self):
        """Test data integrity validation."""
        # Test with valid data
        self.exporter.violations_data = self.sample_violations
        
        validation = self.exporter.validate_data_integrity()
        
        self.assertTrue(validation['valid'])
        self.assertEqual(validation['summary']['total_violations'], 2)
        
        # Test with invalid data
        invalid_violations = [
            {
                'num': None,  # Missing required field
                'hier': '',   # Empty required field
                'corner': 'ss',
                'case': 'test1'
            }
        ]
        
        self.exporter.violations_data = invalid_violations
        validation = self.exporter.validate_data_integrity()
        
        self.assertFalse(validation['valid'])
        self.assertGreater(len(validation['warnings']), 0)
    
    @patch.object(DataExporter, 'load_from_excel_files')
    @patch.object(DataExporter, 'load_from_database')
    def test_load_violation_data_excel_priority(self, mock_db_load, mock_excel_load):
        """Test that Excel files are tried first."""
        mock_excel_load.return_value = self.sample_violations
        mock_db_load.return_value = []
        
        self.exporter.load_violation_data()
        
        mock_excel_load.assert_called_once()
        mock_db_load.assert_not_called()  # Should not be called if Excel succeeds
        
        self.assertEqual(len(self.exporter.violations_data), 2)
        self.assertEqual(len(self.exporter.corners), 2)
        self.assertEqual(len(self.exporter.cases), 2)
    
    @patch.object(DataExporter, 'load_from_excel_files')
    @patch.object(DataExporter, 'load_from_database')
    def test_load_violation_data_database_fallback(self, mock_db_load, mock_excel_load):
        """Test fallback to database when Excel fails."""
        mock_excel_load.return_value = []  # No Excel data
        mock_db_load.return_value = self.sample_violations
        
        self.exporter.load_violation_data()
        
        mock_excel_load.assert_called_once()
        mock_db_load.assert_called_once()  # Should be called as fallback
        
        self.assertEqual(len(self.exporter.violations_data), 2)
    
    def test_export_index_json(self):
        """Test index JSON export."""
        self.exporter.violations_data = self.sample_violations
        self.exporter.corners = {'ss', 'ff'}
        self.exporter.cases = {'test1', 'test2'}
        
        # Ensure data directory exists
        self.exporter.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.exporter.export_index_json()
        
        index_file = self.exporter.data_dir / "index.json"
        self.assertTrue(index_file.exists())
        
        with open(index_file, 'r') as f:
            index_data = json.load(f)
        
        self.assertEqual(index_data['metadata']['total_violations'], 2)
        self.assertEqual(index_data['metadata']['total_corners'], 2)
        self.assertEqual(index_data['metadata']['total_cases'], 2)
        self.assertIn('ss', index_data['corners'])
        self.assertIn('ff', index_data['corners'])
        self.assertIn('test1', index_data['cases'])
        self.assertIn('test2', index_data['cases'])


if __name__ == '__main__':
    unittest.main()