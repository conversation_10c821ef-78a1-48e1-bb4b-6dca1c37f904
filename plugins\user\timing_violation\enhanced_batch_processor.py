"""
增强批处理器

为大型违例数据集提供优化的批处理功能，支持自适应批大小和内存感知处理。
"""

import os
import gc
import time
import psutil
from typing import List, Dict, Optional, Callable, Any, Tuple
from PyQt5.QtCore import QObject, pyqtSignal


class MemoryAwareBatchProcessor(QObject):
    """内存感知批处理器"""
    
    # 信号定义
    batch_processed = pyqtSignal(int, int, dict)  # 已处理批次, 总批次, 批次统计
    memory_pressure_detected = pyqtSignal(float, str)  # 内存使用率, 压力等级
    batch_size_adjusted = pyqtSignal(int, int, str)  # 旧批大小, 新批大小, 调整原因
    
    def __init__(self):
        super().__init__()
        
        # 内存监控配置
        self.memory_thresholds = {
            'warning': 0.75,    # 75% 内存使用率警告
            'critical': 0.85,   # 85% 内存使用率严重
            'emergency': 0.95   # 95% 内存使用率紧急
        }
        
        # 批处理配置
        self.batch_config = {
            'initial_batch_size': 1000,
            'min_batch_size': 100,
            'max_batch_size': 10000,
            'adjustment_factor': 0.8,  # 批大小调整因子
            'memory_check_interval': 10,  # 每10个批次检查一次内存
            'gc_interval': 5  # 每5个批次执行一次垃圾回收
        }
        
        # 性能统计
        self.processing_stats = {
            'total_batches': 0,
            'total_items': 0,
            'total_time': 0,
            'memory_adjustments': 0,
            'gc_collections': 0,
            'average_batch_time': 0,
            'peak_memory_usage': 0
        }
        
        # 当前状态
        self.current_batch_size = self.batch_config['initial_batch_size']
        self.last_memory_check = 0
        self.performance_history = []
    
    def process_violations_in_batches(self, violations: List[Dict], 
                                    processor_func: Callable[[List[Dict]], List[Dict]],
                                    progress_callback: Optional[Callable[[int, str], None]] = None) -> List[Dict]:
        """批量处理违例数据
        
        Args:
            violations: 违例数据列表
            processor_func: 处理函数，接收违例列表并返回处理后的列表
            progress_callback: 进度回调函数
            
        Returns:
            List[Dict]: 处理后的违例数据
        """
        start_time = time.time()
        total_violations = len(violations)
        processed_violations = []
        
        if total_violations == 0:
            return []
        
        # 根据违例数量调整初始批大小
        self.current_batch_size = self._calculate_initial_batch_size(total_violations)
        
        # 计算总批次数
        total_batches = (total_violations + self.current_batch_size - 1) // self.current_batch_size
        
        if progress_callback:
            progress_callback(0, f"开始批处理 {total_violations:,} 条违例，批大小: {self.current_batch_size}")
        
        try:
            for batch_index in range(total_batches):
                batch_start_time = time.time()
                
                # 计算当前批次的范围
                start_idx = batch_index * self.current_batch_size
                end_idx = min(start_idx + self.current_batch_size, total_violations)
                current_batch = violations[start_idx:end_idx]
                
                # 内存检查和批大小调整
                if batch_index % self.batch_config['memory_check_interval'] == 0:
                    self._check_memory_and_adjust_batch_size(batch_index, total_batches)
                
                # 处理当前批次
                try:
                    processed_batch = processor_func(current_batch)
                    processed_violations.extend(processed_batch)
                except Exception as e:
                    print(f"批次 {batch_index + 1} 处理失败: {str(e)}")
                    # 尝试减小批大小重新处理
                    if self.current_batch_size > self.batch_config['min_batch_size']:
                        self._reduce_batch_size("processing_error")
                        # 重新计算批次范围
                        smaller_batches = self._split_batch_for_retry(current_batch)
                        for small_batch in smaller_batches:
                            try:
                                processed_small_batch = processor_func(small_batch)
                                processed_violations.extend(processed_small_batch)
                            except Exception as retry_error:
                                print(f"小批次重试也失败: {str(retry_error)}")
                                # 跳过这个批次，继续处理下一个
                                continue
                    else:
                        # 批大小已经最小，跳过这个批次
                        print(f"跳过失败的批次 {batch_index + 1}")
                        continue
                
                # 记录批次性能
                batch_time = time.time() - batch_start_time
                self._record_batch_performance(batch_index, len(current_batch), batch_time)
                
                # 定期垃圾回收
                if batch_index % self.batch_config['gc_interval'] == 0:
                    self._perform_garbage_collection()
                
                # 更新进度
                progress = int((batch_index + 1) / total_batches * 100)
                if progress_callback:
                    progress_callback(progress, 
                        f"已处理 {batch_index + 1}/{total_batches} 批次，"
                        f"当前批大小: {self.current_batch_size}，"
                        f"平均速度: {len(current_batch) / batch_time:.0f} 违例/秒")
                
                # 发出批次完成信号
                batch_stats = {
                    'batch_size': len(current_batch),
                    'processing_time': batch_time,
                    'violations_per_second': len(current_batch) / batch_time if batch_time > 0 else 0
                }
                self.batch_processed.emit(batch_index + 1, total_batches, batch_stats)
        
        except Exception as e:
            print(f"批处理过程中发生错误: {str(e)}")
            raise
        
        # 更新总体统计
        total_time = time.time() - start_time
        self.processing_stats.update({
            'total_batches': total_batches,
            'total_items': len(processed_violations),
            'total_time': total_time,
            'average_batch_time': total_time / total_batches if total_batches > 0 else 0
        })
        
        if progress_callback:
            progress_callback(100, 
                f"批处理完成！处理了 {len(processed_violations):,} 条违例，"
                f"耗时 {total_time:.2f}秒，"
                f"平均速度: {len(processed_violations) / total_time:.0f} 违例/秒")
        
        return processed_violations
    
    def _calculate_initial_batch_size(self, total_violations: int) -> int:
        """根据违例总数计算初始批大小
        
        Args:
            total_violations: 违例总数
            
        Returns:
            int: 初始批大小
        """
        # 获取系统内存信息
        memory_info = psutil.virtual_memory()
        available_memory_gb = memory_info.available / (1024 ** 3)
        
        # 基于违例数量的批大小策略
        if total_violations < 2000:
            base_batch_size = 500
        elif total_violations < 10000:
            base_batch_size = 1000
        elif total_violations < 50000:
            base_batch_size = 2000
        else:
            base_batch_size = 5000
        
        # 根据可用内存调整
        if available_memory_gb < 2:
            base_batch_size = int(base_batch_size * 0.5)
        elif available_memory_gb > 8:
            base_batch_size = int(base_batch_size * 1.5)
        
        # 确保在合理范围内
        return max(self.batch_config['min_batch_size'], 
                  min(base_batch_size, self.batch_config['max_batch_size']))
    
    def _check_memory_and_adjust_batch_size(self, current_batch: int, total_batches: int):
        """检查内存使用并调整批大小
        
        Args:
            current_batch: 当前批次索引
            total_batches: 总批次数
        """
        try:
            memory_info = psutil.virtual_memory()
            memory_usage_percent = memory_info.percent / 100.0
            
            # 更新峰值内存使用
            if memory_usage_percent > self.processing_stats['peak_memory_usage']:
                self.processing_stats['peak_memory_usage'] = memory_usage_percent
            
            # 检查内存压力等级
            pressure_level = self._get_memory_pressure_level(memory_usage_percent)
            
            if pressure_level != 'normal':
                self.memory_pressure_detected.emit(memory_usage_percent, pressure_level)
            
            # 根据内存压力调整批大小
            if pressure_level == 'emergency':
                # 紧急情况：大幅减小批大小
                new_batch_size = max(self.batch_config['min_batch_size'], 
                                   int(self.current_batch_size * 0.3))
                self._adjust_batch_size(new_batch_size, "memory_emergency")
                # 强制垃圾回收
                self._perform_garbage_collection()
                
            elif pressure_level == 'critical':
                # 严重情况：减小批大小
                new_batch_size = max(self.batch_config['min_batch_size'], 
                                   int(self.current_batch_size * 0.5))
                self._adjust_batch_size(new_batch_size, "memory_critical")
                
            elif pressure_level == 'warning':
                # 警告情况：适度减小批大小
                new_batch_size = max(self.batch_config['min_batch_size'], 
                                   int(self.current_batch_size * 0.8))
                self._adjust_batch_size(new_batch_size, "memory_warning")
                
            elif pressure_level == 'normal' and memory_usage_percent < 0.5:
                # 内存充足：可以适度增加批大小
                if self.current_batch_size < self.batch_config['max_batch_size']:
                    new_batch_size = min(self.batch_config['max_batch_size'], 
                                       int(self.current_batch_size * 1.2))
                    self._adjust_batch_size(new_batch_size, "memory_sufficient")
        
        except Exception as e:
            print(f"内存检查失败: {str(e)}")
    
    def _get_memory_pressure_level(self, memory_usage_percent: float) -> str:
        """获取内存压力等级
        
        Args:
            memory_usage_percent: 内存使用百分比 (0.0-1.0)
            
        Returns:
            str: 压力等级
        """
        if memory_usage_percent >= self.memory_thresholds['emergency']:
            return 'emergency'
        elif memory_usage_percent >= self.memory_thresholds['critical']:
            return 'critical'
        elif memory_usage_percent >= self.memory_thresholds['warning']:
            return 'warning'
        else:
            return 'normal'
    
    def _adjust_batch_size(self, new_batch_size: int, reason: str):
        """调整批大小
        
        Args:
            new_batch_size: 新的批大小
            reason: 调整原因
        """
        if new_batch_size != self.current_batch_size:
            old_batch_size = self.current_batch_size
            self.current_batch_size = new_batch_size
            self.processing_stats['memory_adjustments'] += 1
            
            print(f"批大小调整: {old_batch_size} -> {new_batch_size} (原因: {reason})")
            self.batch_size_adjusted.emit(old_batch_size, new_batch_size, reason)
    
    def _reduce_batch_size(self, reason: str):
        """减小批大小
        
        Args:
            reason: 减小原因
        """
        new_batch_size = max(self.batch_config['min_batch_size'], 
                           int(self.current_batch_size * self.batch_config['adjustment_factor']))
        self._adjust_batch_size(new_batch_size, reason)
    
    def _split_batch_for_retry(self, failed_batch: List[Dict]) -> List[List[Dict]]:
        """将失败的批次分割为更小的批次进行重试
        
        Args:
            failed_batch: 失败的批次数据
            
        Returns:
            List[List[Dict]]: 分割后的小批次列表
        """
        small_batch_size = max(self.batch_config['min_batch_size'], len(failed_batch) // 4)
        small_batches = []
        
        for i in range(0, len(failed_batch), small_batch_size):
            small_batch = failed_batch[i:i + small_batch_size]
            small_batches.append(small_batch)
        
        return small_batches
    
    def _perform_garbage_collection(self):
        """执行垃圾回收"""
        try:
            gc.collect()
            self.processing_stats['gc_collections'] += 1
        except Exception as e:
            print(f"垃圾回收失败: {str(e)}")
    
    def _record_batch_performance(self, batch_index: int, batch_size: int, processing_time: float):
        """记录批次性能
        
        Args:
            batch_index: 批次索引
            batch_size: 批次大小
            processing_time: 处理时间
        """
        performance_record = {
            'batch_index': batch_index,
            'batch_size': batch_size,
            'processing_time': processing_time,
            'violations_per_second': batch_size / processing_time if processing_time > 0 else 0,
            'timestamp': time.time()
        }
        
        self.performance_history.append(performance_record)
        
        # 保持历史记录在合理大小
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-500:]
    
    def get_processing_statistics(self) -> Dict:
        """获取处理统计信息
        
        Returns:
            Dict: 统计信息
        """
        stats = self.processing_stats.copy()
        
        # 计算额外的统计信息
        if self.performance_history:
            recent_performance = self.performance_history[-10:]  # 最近10个批次
            avg_recent_speed = sum(p['violations_per_second'] for p in recent_performance) / len(recent_performance)
            stats['recent_average_speed'] = avg_recent_speed
            
            all_speeds = [p['violations_per_second'] for p in self.performance_history]
            stats['min_speed'] = min(all_speeds) if all_speeds else 0
            stats['max_speed'] = max(all_speeds) if all_speeds else 0
            stats['speed_variance'] = max(all_speeds) - min(all_speeds) if all_speeds else 0
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.processing_stats = {
            'total_batches': 0,
            'total_items': 0,
            'total_time': 0,
            'memory_adjustments': 0,
            'gc_collections': 0,
            'average_batch_time': 0,
            'peak_memory_usage': 0
        }
        self.performance_history.clear()
        self.current_batch_size = self.batch_config['initial_batch_size']


class ProgressiveBatchSizeOptimizer(QObject):
    """渐进式批大小优化器"""
    
    def __init__(self):
        super().__init__()
        
        # 优化配置
        self.optimization_config = {
            'learning_window': 20,  # 学习窗口大小（批次数）
            'performance_threshold': 0.1,  # 性能改善阈值
            'adjustment_step': 0.2,  # 调整步长
            'stability_requirement': 5,  # 稳定性要求（连续稳定批次数）
            'max_optimization_attempts': 10  # 最大优化尝试次数
        }
        
        # 优化状态
        self.optimization_history = []
        self.current_optimal_size = None
        self.stability_counter = 0
        self.optimization_attempts = 0
    
    def optimize_batch_size(self, performance_history: List[Dict], 
                          current_batch_size: int, system_constraints: Dict) -> int:
        """优化批大小
        
        Args:
            performance_history: 性能历史记录
            current_batch_size: 当前批大小
            system_constraints: 系统约束条件
            
        Returns:
            int: 优化后的批大小
        """
        if len(performance_history) < self.optimization_config['learning_window']:
            return current_batch_size
        
        # 分析最近的性能趋势
        recent_performance = performance_history[-self.optimization_config['learning_window']:]
        performance_trend = self._analyze_performance_trend(recent_performance)
        
        # 根据趋势调整批大小
        if performance_trend['trend'] == 'improving':
            # 性能在改善，可以尝试增加批大小
            if performance_trend['stability'] >= self.optimization_config['stability_requirement']:
                new_batch_size = int(current_batch_size * (1 + self.optimization_config['adjustment_step']))
            else:
                new_batch_size = current_batch_size
        elif performance_trend['trend'] == 'degrading':
            # 性能在下降，需要减小批大小
            new_batch_size = int(current_batch_size * (1 - self.optimization_config['adjustment_step']))
        else:
            # 性能稳定，保持当前批大小
            new_batch_size = current_batch_size
        
        # 应用系统约束
        new_batch_size = self._apply_system_constraints(new_batch_size, system_constraints)
        
        # 记录优化历史
        self._record_optimization(current_batch_size, new_batch_size, performance_trend)
        
        return new_batch_size
    
    def _analyze_performance_trend(self, performance_data: List[Dict]) -> Dict:
        """分析性能趋势
        
        Args:
            performance_data: 性能数据
            
        Returns:
            Dict: 趋势分析结果
        """
        if len(performance_data) < 3:
            return {'trend': 'stable', 'stability': 0, 'confidence': 0.0}
        
        # 计算性能指标的变化趋势
        speeds = [p['violations_per_second'] for p in performance_data]
        times = [p['processing_time'] for p in performance_data]
        
        # 计算速度趋势
        speed_trend = self._calculate_trend(speeds)
        time_trend = self._calculate_trend(times)
        
        # 计算稳定性
        speed_variance = max(speeds) - min(speeds) if speeds else 0
        speed_avg = sum(speeds) / len(speeds) if speeds else 0
        stability_score = 1 - (speed_variance / speed_avg) if speed_avg > 0 else 0
        
        # 确定总体趋势
        if speed_trend > 0.05:  # 速度提升超过5%
            trend = 'improving'
        elif speed_trend < -0.05:  # 速度下降超过5%
            trend = 'degrading'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'speed_trend': speed_trend,
            'time_trend': time_trend,
            'stability': stability_score * 10,  # 转换为0-10分
            'confidence': min(1.0, len(performance_data) / self.optimization_config['learning_window'])
        }
    
    def _calculate_trend(self, values: List[float]) -> float:
        """计算数值序列的趋势
        
        Args:
            values: 数值列表
            
        Returns:
            float: 趋势值（正数表示上升，负数表示下降）
        """
        if len(values) < 2:
            return 0.0
        
        # 简单线性趋势计算
        n = len(values)
        x_sum = sum(range(n))
        y_sum = sum(values)
        xy_sum = sum(i * values[i] for i in range(n))
        x2_sum = sum(i * i for i in range(n))
        
        if n * x2_sum - x_sum * x_sum == 0:
            return 0.0
        
        slope = (n * xy_sum - x_sum * y_sum) / (n * x2_sum - x_sum * x_sum)
        
        # 归一化趋势值
        avg_value = y_sum / n if n > 0 else 0
        normalized_slope = slope / avg_value if avg_value > 0 else 0
        
        return normalized_slope
    
    def _apply_system_constraints(self, proposed_batch_size: int, constraints: Dict) -> int:
        """应用系统约束条件
        
        Args:
            proposed_batch_size: 建议的批大小
            constraints: 约束条件
            
        Returns:
            int: 应用约束后的批大小
        """
        min_size = constraints.get('min_batch_size', 100)
        max_size = constraints.get('max_batch_size', 10000)
        memory_limit = constraints.get('memory_limit_mb', 500)
        
        # 应用大小约束
        constrained_size = max(min_size, min(proposed_batch_size, max_size))
        
        # 应用内存约束（假设每个违例需要1KB内存）
        max_size_by_memory = int(memory_limit * 1024 * 0.8)  # 使用80%的内存限制
        constrained_size = min(constrained_size, max_size_by_memory)
        
        return constrained_size
    
    def _record_optimization(self, old_size: int, new_size: int, performance_trend: Dict):
        """记录优化历史
        
        Args:
            old_size: 旧的批大小
            new_size: 新的批大小
            performance_trend: 性能趋势
        """
        optimization_record = {
            'timestamp': time.time(),
            'old_batch_size': old_size,
            'new_batch_size': new_size,
            'performance_trend': performance_trend,
            'optimization_attempt': self.optimization_attempts
        }
        
        self.optimization_history.append(optimization_record)
        self.optimization_attempts += 1
        
        # 保持历史记录在合理大小
        if len(self.optimization_history) > 100:
            self.optimization_history = self.optimization_history[-50:]
    
    def get_optimization_summary(self) -> Dict:
        """获取优化摘要
        
        Returns:
            Dict: 优化摘要
        """
        if not self.optimization_history:
            return {'total_optimizations': 0}
        
        # 计算优化效果
        size_changes = [r['new_batch_size'] - r['old_batch_size'] for r in self.optimization_history]
        total_size_change = sum(size_changes)
        avg_size_change = total_size_change / len(size_changes) if size_changes else 0
        
        # 统计优化方向
        increases = sum(1 for change in size_changes if change > 0)
        decreases = sum(1 for change in size_changes if change < 0)
        
        return {
            'total_optimizations': len(self.optimization_history),
            'total_size_change': total_size_change,
            'average_size_change': avg_size_change,
            'size_increases': increases,
            'size_decreases': decreases,
            'optimization_attempts': self.optimization_attempts,
            'current_optimal_size': self.current_optimal_size
        }