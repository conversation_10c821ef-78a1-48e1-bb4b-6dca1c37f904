"""
时序违例插件性能优化器

提供性能监控、分析和优化建议功能。
"""

import os
import time
import psutil
from typing import Dict, List, Optional, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QTimer


class PerformanceOptimizer(QObject):
    """性能优化器"""
    
    # 信号定义
    performance_warning = pyqtSignal(str, str)  # 警告类型, 警告信息
    optimization_suggestion = pyqtSignal(str)   # 优化建议
    
    def __init__(self):
        super().__init__()
        
        # 性能阈值配置
        self.thresholds = {
            'load_time_warning': 3.0,      # 加载时间警告阈值（秒）
            'load_time_critical': 10.0,    # 加载时间严重阈值（秒）
            'memory_warning': 300,         # 内存使用警告阈值（MB）
            'memory_critical': 500,        # 内存使用严重阈值（MB）
            'record_count_high': 5000,     # 高记录数阈值
            'record_count_critical': 15000, # 严重记录数阈值
            'ui_response_warning': 0.1,    # UI响应时间警告阈值（秒）
        }
        
        # 性能统计
        self.stats = {
            'total_load_time': 0,
            'total_records_processed': 0,
            'peak_memory_usage': 0,
            'ui_freeze_count': 0,
            'optimization_applied': []
        }
        
        # 性能监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._monitor_performance)
        self.monitor_timer.start(5000)  # 每5秒监控一次
        
    def analyze_file_performance(self, file_path: str) -> Dict:
        """分析文件性能特征
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 性能分析结果
        """
        try:
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)
            
            # 使用增强的违例数量估算
            estimated_violations = self.estimate_violation_count_with_sampling(file_path)
            
            # 评估系统能力
            system_capabilities = self.assess_system_capabilities()
            
            # 基于违例数量确定性能阈值
            violation_thresholds = self._get_violation_count_thresholds()
            
            # 预测加载时间（基于违例数量）
            predicted_load_time = self._predict_load_time_by_violations(estimated_violations, system_capabilities)
            
            # 推荐解析策略（基于违例数量）
            recommended_strategy = self._recommend_strategy_by_violations(estimated_violations, system_capabilities)
            
            # 推荐显示模式
            recommended_display = self._recommend_display_mode_by_violations(estimated_violations)
            
            # 内存需求预测
            memory_requirements = self._predict_memory_requirements(estimated_violations)
            
            # 性能等级评估
            performance_level = self._assess_performance_level_by_violations(estimated_violations, system_capabilities)
            
            return {
                'file_path': file_path,
                'file_size_mb': file_size_mb,
                'estimated_violations': estimated_violations,
                'violation_thresholds': violation_thresholds,
                'system_capabilities': system_capabilities,
                'predicted_load_time': predicted_load_time,
                'memory_requirements_mb': memory_requirements,
                'recommended_strategy': recommended_strategy,
                'recommended_display': recommended_display,
                'performance_level': performance_level,
                'optimization_suggestions': self._generate_optimization_suggestions(
                    estimated_violations, system_capabilities, performance_level
                )
            }
            
        except Exception as e:
            print(f"文件性能分析失败: {str(e)}")
            return {}
    
    def assess_system_capabilities(self) -> Dict:
        """评估系统能力
        
        Returns:
            Dict: 系统能力信息
        """
        try:
            # 获取系统信息
            memory_info = psutil.virtual_memory()
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # 获取当前进程信息
            process = psutil.Process()
            process_memory = process.memory_info()
            
            # 计算可用资源
            available_memory_gb = memory_info.available / (1024 ** 3)
            total_memory_gb = memory_info.total / (1024 ** 3)
            memory_usage_percent = memory_info.percent
            
            # 评估系统性能等级
            performance_tier = self._assess_system_performance_tier(
                cpu_count, cpu_freq, available_memory_gb
            )
            
            return {
                'cpu_cores': cpu_count,
                'cpu_frequency_mhz': cpu_freq.current if cpu_freq else 0,
                'total_memory_gb': total_memory_gb,
                'available_memory_gb': available_memory_gb,
                'memory_usage_percent': memory_usage_percent,
                'process_memory_mb': process_memory.rss / (1024 ** 2),
                'performance_tier': performance_tier,
                'recommended_max_violations': self._calculate_max_violations_for_system(
                    available_memory_gb, cpu_count
                ),
                'optimal_batch_size': self._calculate_optimal_batch_size(cpu_count),
                'can_handle_large_datasets': available_memory_gb > 4 and cpu_count >= 4
            }
            
        except Exception as e:
            print(f"系统能力评估失败: {str(e)}")
            return {
                'cpu_cores': 4,
                'total_memory_gb': 8,
                'available_memory_gb': 4,
                'performance_tier': 'medium',
                'recommended_max_violations': 10000,
                'optimal_batch_size': 1000,
                'can_handle_large_datasets': False
            }
    
    def _get_violation_count_thresholds(self) -> Dict:
        """获取基于违例数量的阈值
        
        Returns:
            Dict: 违例数量阈值
        """
        return {
            'small_dataset': 2000,      # < 2K violations
            'medium_dataset': 20000,    # 2K-20K violations  
            'large_dataset': 50000,     # 20K-50K violations
            'very_large_dataset': 100000, # > 50K violations
            'memory_warning': 10000,    # 内存使用警告阈值
            'performance_critical': 25000, # 性能严重阈值
            'ui_responsiveness_limit': 5000  # UI响应性限制
        }
    
    def _predict_load_time_by_violations(self, violation_count: int, system_caps: Dict) -> float:
        """基于违例数量预测加载时间
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力信息
            
        Returns:
            float: 预测的加载时间（秒）
        """
        # 基础处理时间（每个违例的处理时间）
        base_time_per_violation = 0.001  # 1ms per violation
        
        # 根据系统性能调整
        performance_multiplier = {
            'high': 0.5,
            'medium': 1.0,
            'low': 2.0
        }.get(system_caps.get('performance_tier', 'medium'), 1.0)
        
        # 计算基础处理时间
        processing_time = violation_count * base_time_per_violation * performance_multiplier
        
        # 添加I/O时间（基于违例数量）
        io_time = violation_count * 0.0005  # 0.5ms per violation for I/O
        
        # 添加UI渲染时间（如果使用标准渲染）
        ui_time = min(violation_count * 0.0002, 3.0)  # 最多3秒UI时间
        
        # 大数据集额外开销
        if violation_count > 20000:
            processing_time *= 1.5  # 大数据集处理开销
        
        return processing_time + io_time + ui_time
    
    def _recommend_strategy_by_violations(self, violation_count: int, system_caps: Dict) -> str:
        """基于违例数量推荐处理策略
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力信息
            
        Returns:
            str: 推荐的处理策略
        """
        thresholds = self._get_violation_count_thresholds()
        
        # 检查系统是否能处理大数据集
        can_handle_large = system_caps.get('can_handle_large_datasets', False)
        
        if violation_count > thresholds['very_large_dataset']:
            return "streaming_with_chunking" if can_handle_large else "memory_efficient_streaming"
        elif violation_count > thresholds['large_dataset']:
            return "high_performance_streaming" if can_handle_large else "chunked_processing"
        elif violation_count > thresholds['medium_dataset']:
            return "high_performance_async" if can_handle_large else "standard_async_chunked"
        elif violation_count > thresholds['small_dataset']:
            return "high_performance_async"
        else:
            return "standard_async"
    
    def _recommend_display_mode_by_violations(self, violation_count: int) -> str:
        """基于违例数量推荐显示模式
        
        Args:
            violation_count: 违例数量
            
        Returns:
            str: 推荐的显示模式
        """
        thresholds = self._get_violation_count_thresholds()
        
        if violation_count > thresholds['large_dataset']:
            return "paginated_virtual_table"
        elif violation_count > thresholds['medium_dataset']:
            return "virtual_table_with_lazy_loading"
        elif violation_count > thresholds['ui_responsiveness_limit']:
            return "high_performance_table"
        else:
            return "standard_table"
    
    def _predict_memory_requirements(self, violation_count: int) -> float:
        """预测内存需求
        
        Args:
            violation_count: 违例数量
            
        Returns:
            float: 预测的内存需求（MB）
        """
        # 基础内存需求（每个违例约1KB数据）
        base_memory_per_violation = 1  # KB
        base_memory_mb = violation_count * base_memory_per_violation / 1024
        
        # UI渲染内存开销
        ui_memory_mb = min(violation_count * 0.5 / 1024, 200)  # 最多200MB UI内存
        
        # 解析缓冲区内存
        buffer_memory_mb = min(violation_count * 0.2 / 1024, 100)  # 最多100MB缓冲区
        
        # 系统开销
        system_overhead_mb = 50
        
        total_memory = base_memory_mb + ui_memory_mb + buffer_memory_mb + system_overhead_mb
        
        # 大数据集额外开销
        if violation_count > 20000:
            total_memory *= 1.3
        
        return total_memory
    
    def _assess_performance_level_by_violations(self, violation_count: int, system_caps: Dict) -> str:
        """基于违例数量评估性能等级
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力信息
            
        Returns:
            str: 性能等级
        """
        thresholds = self._get_violation_count_thresholds()
        can_handle_large = system_caps.get('can_handle_large_datasets', False)
        
        if violation_count > thresholds['very_large_dataset']:
            return "critical" if not can_handle_large else "warning"
        elif violation_count > thresholds['performance_critical']:
            return "warning" if can_handle_large else "critical"
        elif violation_count > thresholds['medium_dataset']:
            return "moderate"
        elif violation_count > thresholds['small_dataset']:
            return "good"
        else:
            return "excellent"
    
    def _assess_system_performance_tier(self, cpu_count: int, cpu_freq, available_memory_gb: float) -> str:
        """评估系统性能等级
        
        Args:
            cpu_count: CPU核心数
            cpu_freq: CPU频率信息
            available_memory_gb: 可用内存（GB）
            
        Returns:
            str: 性能等级
        """
        score = 0
        
        # CPU评分
        if cpu_count >= 8:
            score += 3
        elif cpu_count >= 4:
            score += 2
        else:
            score += 1
        
        # 内存评分
        if available_memory_gb >= 8:
            score += 3
        elif available_memory_gb >= 4:
            score += 2
        else:
            score += 1
        
        # CPU频率评分
        if cpu_freq and cpu_freq.current >= 3000:
            score += 2
        elif cpu_freq and cpu_freq.current >= 2000:
            score += 1
        
        # 总分评级
        if score >= 7:
            return "high"
        elif score >= 4:
            return "medium"
        else:
            return "low"
    
    def _calculate_max_violations_for_system(self, available_memory_gb: float, cpu_count: int) -> int:
        """计算系统推荐的最大违例数量
        
        Args:
            available_memory_gb: 可用内存（GB）
            cpu_count: CPU核心数
            
        Returns:
            int: 推荐的最大违例数量
        """
        # 基于内存的限制（假设每个违例需要1KB内存）
        memory_limit = int(available_memory_gb * 1024 * 1024 * 0.5)  # 使用50%可用内存
        
        # 基于CPU的处理能力
        cpu_limit = cpu_count * 10000  # 每个核心处理10K违例
        
        # 返回较小值作为推荐上限
        return min(memory_limit, cpu_limit, 100000)  # 最大不超过100K
    
    def _calculate_optimal_batch_size(self, cpu_count: int) -> int:
        """计算最优批处理大小
        
        Args:
            cpu_count: CPU核心数
            
        Returns:
            int: 最优批处理大小
        """
        base_batch_size = 1000
        return min(base_batch_size * cpu_count, 10000)
    
    def _generate_optimization_suggestions(self, violation_count: int, 
                                         system_caps: Dict, performance_level: str) -> List[str]:
        """生成优化建议
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力信息
            performance_level: 性能等级
            
        Returns:
            List[str]: 优化建议列表
        """
        suggestions = []
        thresholds = self._get_violation_count_thresholds()
        
        # 基于违例数量的建议
        if violation_count > thresholds['very_large_dataset']:
            suggestions.extend([
                f"数据集非常大({violation_count:,}个违例)，强烈建议使用数据筛选",
                "启用流式处理模式以减少内存使用",
                "考虑分批处理或时间范围筛选"
            ])
        elif violation_count > thresholds['large_dataset']:
            suggestions.extend([
                f"数据集较大({violation_count:,}个违例)，建议使用高性能模式",
                "启用虚拟表格以提升UI响应速度"
            ])
        elif violation_count > thresholds['medium_dataset']:
            suggestions.append("建议启用分页显示以提升用户体验")
        
        # 基于系统能力的建议
        if not system_caps.get('can_handle_large_datasets', False):
            suggestions.extend([
                f"系统内存较低({system_caps.get('available_memory_gb', 0):.1f}GB)，建议启用内存优化模式",
                "考虑关闭其他应用程序以释放内存"
            ])
        
        if system_caps.get('cpu_cores', 4) < 4:
            suggestions.append("CPU核心数较少，建议降低并发处理数量")
        
        # 基于性能等级的建议
        if performance_level in ['critical', 'warning']:
            suggestions.extend([
                "当前配置可能导致性能问题，建议调整处理策略",
                "考虑使用更严格的数据筛选条件"
            ])
        
        return suggestions
    
    def select_intelligent_strategy(self, file_analysis: Dict) -> Dict:
        """智能策略选择
        
        Args:
            file_analysis: 文件分析结果
            
        Returns:
            Dict: 选择的策略配置
        """
        violation_count = file_analysis.get('estimated_violations', 0)
        system_caps = file_analysis.get('system_capabilities', {})
        performance_level = file_analysis.get('performance_level', 'good')
        
        # 基于违例数量的策略选择
        strategy_config = self._select_strategy_by_violation_count(
            violation_count, system_caps, performance_level
        )
        
        # 添加回退机制
        strategy_config['fallback_strategies'] = self._define_fallback_strategies(
            violation_count, system_caps
        )
        
        # 添加动态切换配置
        strategy_config['dynamic_switching'] = self._configure_dynamic_switching(
            violation_count, system_caps
        )
        
        # 添加策略验证和选择逻辑
        strategy_config['selection_metadata'] = {
            'violation_count': violation_count,
            'selection_reason': self._get_strategy_selection_reason(violation_count, system_caps),
            'confidence_score': self._calculate_strategy_confidence(violation_count, system_caps, performance_level),
            'alternative_strategies': self._get_alternative_strategies(violation_count, system_caps),
            'performance_prediction': self._predict_strategy_performance(strategy_config, violation_count, system_caps)
        }
        
        return strategy_config
    
    def _select_strategy_by_violation_count(self, violation_count: int, 
                                          system_caps: Dict, performance_level: str) -> Dict:
        """基于违例数量选择策略
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力
            performance_level: 性能等级
            
        Returns:
            Dict: 策略配置
        """
        thresholds = self._get_violation_count_thresholds()
        can_handle_large = system_caps.get('can_handle_large_datasets', False)
        
        if violation_count < thresholds['small_dataset']:
            # < 2K violations: 标准处理
            return {
                'strategy_name': 'standard_processing',
                'parser_type': 'standard_async',
                'display_mode': 'standard_table',
                'batch_size': 500,
                'use_streaming': False,
                'use_pagination': False,
                'memory_limit_mb': 100,
                'progress_interval': 1000,
                'gc_interval': 5000,
                'priority': 'user_experience'
            }
        
        elif violation_count < thresholds['medium_dataset']:
            # 2K-20K violations: 高性能处理
            return {
                'strategy_name': 'high_performance_processing',
                'parser_type': 'high_performance_async',
                'display_mode': 'high_performance_table' if can_handle_large else 'paginated_table',
                'batch_size': system_caps.get('optimal_batch_size', 2000),
                'use_streaming': False,
                'use_pagination': not can_handle_large,
                'page_size': 200 if not can_handle_large else 1000,
                'memory_limit_mb': 300 if can_handle_large else 150,
                'progress_interval': 5000,
                'gc_interval': 10000,
                'priority': 'balanced'
            }
        
        elif violation_count < 50000:
            # 20K-50K violations: 流式处理
            return {
                'strategy_name': 'streaming_processing',
                'parser_type': 'high_performance_streaming',
                'display_mode': 'virtual_table_with_lazy_loading',
                'batch_size': system_caps.get('optimal_batch_size', 5000),
                'use_streaming': True,
                'use_pagination': True,
                'page_size': 100,
                'memory_limit_mb': 500 if can_handle_large else 200,
                'progress_interval': 10000,
                'gc_interval': 20000,
                'chunk_size': 10000,
                'lazy_loading': True,
                'priority': 'memory_efficiency'
            }
        
        else:
            # > 50K violations: 超大数据集处理
            return {
                'strategy_name': 'ultra_large_dataset_processing',
                'parser_type': 'streaming_with_chunking',
                'display_mode': 'paginated_virtual_table',
                'batch_size': min(system_caps.get('optimal_batch_size', 10000), 10000),
                'use_streaming': True,
                'use_pagination': True,
                'page_size': 50,
                'memory_limit_mb': 800 if can_handle_large else 300,
                'progress_interval': 25000,
                'gc_interval': 50000,
                'chunk_size': 25000,
                'lazy_loading': True,
                'background_processing': True,
                'priority': 'stability'
            }
    
    def _define_fallback_strategies(self, violation_count: int, system_caps: Dict) -> List[Dict]:
        """定义回退策略
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力
            
        Returns:
            List[Dict]: 回退策略列表
        """
        fallback_strategies = []
        
        # 主策略失败时的回退选项
        if violation_count > 20000:
            # 大数据集的回退策略
            fallback_strategies.extend([
                {
                    'trigger': 'memory_pressure',
                    'strategy': {
                        'parser_type': 'memory_efficient_streaming',
                        'display_mode': 'paginated_virtual_table',
                        'batch_size': 1000,
                        'page_size': 25,
                        'memory_limit_mb': 150,
                        'aggressive_gc': True
                    }
                },
                {
                    'trigger': 'processing_timeout',
                    'strategy': {
                        'parser_type': 'chunked_processing',
                        'display_mode': 'simple_list',
                        'batch_size': 500,
                        'show_progress_only': True,
                        'defer_ui_updates': True
                    }
                },
                {
                    'trigger': 'ui_freeze',
                    'strategy': {
                        'parser_type': 'background_processing',
                        'display_mode': 'loading_placeholder',
                        'batch_size': 100,
                        'background_only': True,
                        'minimal_ui': True
                    }
                }
            ])
        
        elif violation_count > 2000:
            # 中等数据集的回退策略
            fallback_strategies.extend([
                {
                    'trigger': 'memory_pressure',
                    'strategy': {
                        'parser_type': 'standard_async',
                        'display_mode': 'paginated_table',
                        'batch_size': 1000,
                        'page_size': 100,
                        'memory_limit_mb': 100
                    }
                },
                {
                    'trigger': 'performance_degradation',
                    'strategy': {
                        'parser_type': 'chunked_processing',
                        'display_mode': 'standard_table',
                        'batch_size': 500,
                        'reduce_ui_updates': True
                    }
                }
            ])
        
        else:
            # 小数据集的回退策略
            fallback_strategies.append({
                'trigger': 'any_failure',
                'strategy': {
                    'parser_type': 'basic_sync',
                    'display_mode': 'simple_table',
                    'batch_size': 100,
                    'minimal_features': True
                }
            })
        
        return fallback_strategies
    
    def _configure_dynamic_switching(self, violation_count: int, system_caps: Dict) -> Dict:
        """配置动态策略切换
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力
            
        Returns:
            Dict: 动态切换配置
        """
        return {
            'enabled': True,
            'monitoring_interval': 2.0,  # 每2秒检查一次
            'switch_triggers': {
                'memory_usage_threshold': 0.8,  # 内存使用超过80%
                'processing_speed_threshold': 0.5,  # 处理速度低于预期50%
                'ui_response_threshold': 0.2,  # UI响应时间超过200ms
                'error_rate_threshold': 0.05  # 错误率超过5%
            },
            'switch_conditions': [
                {
                    'condition': 'memory_pressure_detected',
                    'action': 'switch_to_memory_efficient_mode',
                    'parameters': {
                        'reduce_batch_size': True,
                        'enable_aggressive_gc': True,
                        'switch_to_streaming': True
                    }
                },
                {
                    'condition': 'slow_processing_detected',
                    'action': 'optimize_for_speed',
                    'parameters': {
                        'increase_batch_size': True,
                        'reduce_ui_updates': True,
                        'defer_non_critical_operations': True
                    }
                },
                {
                    'condition': 'ui_freeze_detected',
                    'action': 'switch_to_background_processing',
                    'parameters': {
                        'move_to_background': True,
                        'show_progress_only': True,
                        'minimal_ui_updates': True
                    }
                }
            ],
            'rollback_conditions': {
                'performance_improved': True,
                'memory_pressure_relieved': True,
                'user_interaction_required': True
            },
            'max_switches_per_session': 3,  # 防止频繁切换
            'cooldown_period': 30.0  # 切换后的冷却期（秒）
        }
    
    def apply_strategy_with_fallback(self, strategy_config: Dict, 
                                   current_performance: Dict) -> Dict:
        """应用策略并处理回退
        
        Args:
            strategy_config: 策略配置
            current_performance: 当前性能指标
            
        Returns:
            Dict: 应用结果
        """
        try:
            # 检查是否需要触发回退
            fallback_trigger = self._check_fallback_triggers(
                strategy_config, current_performance
            )
            
            if fallback_trigger:
                # 应用回退策略
                fallback_strategy = self._select_fallback_strategy(
                    strategy_config['fallback_strategies'], fallback_trigger
                )
                
                if fallback_strategy:
                    return {
                        'status': 'fallback_applied',
                        'original_strategy': strategy_config['strategy_name'],
                        'fallback_strategy': fallback_strategy,
                        'trigger': fallback_trigger,
                        'config': fallback_strategy['strategy']
                    }
            
            # 应用主策略
            return {
                'status': 'primary_strategy_applied',
                'strategy': strategy_config['strategy_name'],
                'config': strategy_config
            }
            
        except Exception as e:
            print(f"策略应用失败: {str(e)}")
            # 应用最基础的回退策略
            return {
                'status': 'emergency_fallback',
                'config': {
                    'parser_type': 'basic_sync',
                    'display_mode': 'simple_table',
                    'batch_size': 100
                }
            }
    
    def _check_fallback_triggers(self, strategy_config: Dict, 
                               current_performance: Dict) -> Optional[str]:
        """检查是否需要触发回退
        
        Args:
            strategy_config: 策略配置
            current_performance: 当前性能指标
            
        Returns:
            Optional[str]: 触发的回退条件，如果没有则返回None
        """
        memory_usage = current_performance.get('memory_usage_percent', 0)
        processing_speed = current_performance.get('processing_speed', 1.0)
        ui_response_time = current_performance.get('ui_response_time', 0)
        error_rate = current_performance.get('error_rate', 0)
        
        # 检查内存压力
        if memory_usage > 85:
            return 'memory_pressure'
        
        # 检查处理超时
        if processing_speed < 0.3:  # 处理速度低于预期30%
            return 'processing_timeout'
        
        # 检查UI冻结
        if ui_response_time > 0.5:  # UI响应时间超过500ms
            return 'ui_freeze'
        
        # 检查错误率
        if error_rate > 0.1:  # 错误率超过10%
            return 'high_error_rate'
        
        return None
    
    def _select_fallback_strategy(self, fallback_strategies: List[Dict], 
                                trigger: str) -> Optional[Dict]:
        """选择合适的回退策略
        
        Args:
            fallback_strategies: 可用的回退策略列表
            trigger: 触发条件
            
        Returns:
            Optional[Dict]: 选择的回退策略
        """
        # 寻找匹配的回退策略
        for strategy in fallback_strategies:
            if strategy['trigger'] == trigger or strategy['trigger'] == 'any_failure':
                return strategy
        
        # 如果没有找到匹配的，返回最后一个（通常是最保守的）
        return fallback_strategies[-1] if fallback_strategies else None
    
    def _estimate_record_count(self, file_size: int) -> int:
        """估算记录数量
        
        基于5行每条违例记录的公式
        """
        # 估算总行数（假设平均每行50字节）
        avg_bytes_per_line = 50
        estimated_lines = file_size / avg_bytes_per_line
        
        # 基于5行每条违例记录计算
        estimated_violations = int(estimated_lines / 5)
        return estimated_violations
    
    def estimate_violation_count_with_sampling(self, file_path: str, sample_size: int = 1000) -> int:
        """通过文件采样更准确地估算违例数量
        
        基于5行每违例的假设进行估算
        
        Args:
            file_path: 文件路径
            sample_size: 采样行数
            
        Returns:
            int: 估算的违例数量
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                # 获取文件总行数
                total_lines = sum(1 for _ in f)
                
                if total_lines == 0:
                    return 0
                
                # 基于5行每违例的公式进行基础估算
                base_estimation = int(total_lines / 5)
                
                # 如果文件较小，直接返回基础估算
                if total_lines < sample_size:
                    return max(1, base_estimation)
                
                # 对于大文件，进行采样验证
                f.seek(0)
                sample_lines = []
                sample_step = max(1, total_lines // sample_size)
                
                for i, line in enumerate(f):
                    if i % sample_step == 0:
                        sample_lines.append(line.strip())
                    if len(sample_lines) >= sample_size:
                        break
                
                if not sample_lines:
                    return max(1, base_estimation)
                
                # 检测违例开始标记的密度
                violation_patterns = ['violation', 'error:', 'fail:', 'timing', 'setup', 'hold']
                violation_lines = 0
                
                for line in sample_lines:
                    line_lower = line.lower()
                    # 检查是否包含违例相关关键词
                    if any(pattern in line_lower for pattern in violation_patterns):
                        violation_lines += 1
                
                # 计算违例相关行的密度
                violation_density = violation_lines / len(sample_lines) if sample_lines else 0
                
                # 根据密度调整估算
                if violation_density > 0.8:
                    # 高密度：可能每行都是违例相关，估算偏高
                    adjusted_estimation = int(base_estimation * 0.8)
                elif violation_density > 0.4:
                    # 中等密度：接近5行每违例的假设
                    adjusted_estimation = base_estimation
                elif violation_density > 0.1:
                    # 低密度：可能有更多非违例行，估算偏低
                    adjusted_estimation = int(base_estimation * 1.2)
                else:
                    # 极低密度：可能不是标准违例文件格式
                    adjusted_estimation = int(base_estimation * 0.5)
                
                return max(1, adjusted_estimation)
                
        except Exception as e:
            print(f"文件采样估算失败: {str(e)}")
            # 回退到基于文件大小的估算
            file_size = os.path.getsize(file_path)
            return self._estimate_record_count(file_size)
    
    def _get_strategy_selection_reason(self, violation_count: int, system_caps: Dict) -> str:
        """获取策略选择原因
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力
            
        Returns:
            str: 选择原因
        """
        thresholds = self._get_violation_count_thresholds()
        can_handle_large = system_caps.get('can_handle_large_datasets', False)
        
        if violation_count < thresholds['small_dataset']:
            return f"小数据集({violation_count:,}个违例)，使用标准处理以获得最佳用户体验"
        elif violation_count < thresholds['medium_dataset']:
            reason = f"中等数据集({violation_count:,}个违例)，使用高性能处理"
            if not can_handle_large:
                reason += "，由于系统资源限制启用分页显示"
            return reason
        elif violation_count < 50000:
            reason = f"大数据集({violation_count:,}个违例)，使用流式处理"
            if not can_handle_large:
                reason += "，系统资源有限，采用内存优化策略"
            return reason
        else:
            reason = f"超大数据集({violation_count:,}个违例)，使用分块流式处理"
            if not can_handle_large:
                reason += "，系统资源不足，强制使用内存保护模式"
            return reason
    
    def _calculate_strategy_confidence(self, violation_count: int, system_caps: Dict, performance_level: str) -> float:
        """计算策略选择的置信度
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力
            performance_level: 性能等级
            
        Returns:
            float: 置信度分数 (0.0-1.0)
        """
        confidence = 0.8  # 基础置信度
        
        # 基于系统能力调整置信度
        if system_caps.get('can_handle_large_datasets', False):
            confidence += 0.1
        else:
            confidence -= 0.1
        
        # 基于性能等级调整
        performance_adjustments = {
            'excellent': 0.1,
            'good': 0.05,
            'moderate': 0.0,
            'warning': -0.1,
            'critical': -0.2
        }
        confidence += performance_adjustments.get(performance_level, 0.0)
        
        # 基于违例数量的复杂度调整
        thresholds = self._get_violation_count_thresholds()
        if violation_count > thresholds['very_large_dataset']:
            confidence -= 0.15  # 超大数据集降低置信度
        elif violation_count > thresholds['large_dataset']:
            confidence -= 0.05
        
        return max(0.0, min(1.0, confidence))
    
    def _get_alternative_strategies(self, violation_count: int, system_caps: Dict) -> List[Dict]:
        """获取备选策略
        
        Args:
            violation_count: 违例数量
            system_caps: 系统能力
            
        Returns:
            List[Dict]: 备选策略列表
        """
        alternatives = []
        thresholds = self._get_violation_count_thresholds()
        can_handle_large = system_caps.get('can_handle_large_datasets', False)
        
        if violation_count < thresholds['small_dataset']:
            # 小数据集的备选策略
            alternatives.extend([
                {
                    'name': 'high_performance_processing',
                    'reason': '如果需要更快的处理速度',
                    'trade_offs': '可能消耗更多内存'
                }
            ])
        
        elif violation_count < thresholds['medium_dataset']:
            # 中等数据集的备选策略
            alternatives.extend([
                {
                    'name': 'standard_processing',
                    'reason': '如果系统资源紧张',
                    'trade_offs': '处理速度可能较慢'
                },
                {
                    'name': 'streaming_processing',
                    'reason': '如果内存使用是主要关注点',
                    'trade_offs': 'UI响应可能稍慢'
                }
            ])
        
        elif violation_count < 50000:
            # 大数据集的备选策略
            alternatives.extend([
                {
                    'name': 'high_performance_processing',
                    'reason': '如果系统性能足够强',
                    'trade_offs': '内存使用量会显著增加',
                    'condition': 'requires_high_performance_system'
                },
                {
                    'name': 'ultra_large_dataset_processing',
                    'reason': '如果当前策略性能不佳',
                    'trade_offs': '用户体验可能受影响'
                }
            ])
        
        else:
            # 超大数据集的备选策略
            alternatives.extend([
                {
                    'name': 'streaming_processing',
                    'reason': '如果系统性能较好',
                    'trade_offs': '可能遇到内存压力',
                    'condition': 'requires_sufficient_memory'
                },
                {
                    'name': 'manual_chunking',
                    'reason': '如果自动处理失败',
                    'trade_offs': '需要用户手动干预'
                }
            ])
        
        return alternatives
    
    def _predict_strategy_performance(self, strategy_config: Dict, violation_count: int, system_caps: Dict) -> Dict:
        """预测策略性能
        
        Args:
            strategy_config: 策略配置
            violation_count: 违例数量
            system_caps: 系统能力
            
        Returns:
            Dict: 性能预测结果
        """
        strategy_name = strategy_config.get('strategy_name', 'unknown')
        
        # 基础性能预测
        base_predictions = {
            'standard_processing': {
                'load_time_factor': 1.0,
                'memory_factor': 1.0,
                'ui_responsiveness': 'excellent',
                'stability': 'high'
            },
            'high_performance_processing': {
                'load_time_factor': 0.6,
                'memory_factor': 1.5,
                'ui_responsiveness': 'good',
                'stability': 'high'
            },
            'streaming_processing': {
                'load_time_factor': 0.8,
                'memory_factor': 0.7,
                'ui_responsiveness': 'moderate',
                'stability': 'moderate'
            },
            'ultra_large_dataset_processing': {
                'load_time_factor': 1.2,
                'memory_factor': 0.5,
                'ui_responsiveness': 'limited',
                'stability': 'moderate'
            }
        }
        
        base_prediction = base_predictions.get(strategy_name, base_predictions['standard_processing'])
        
        # 计算具体的性能指标
        predicted_load_time = self._predict_load_time_by_violations(violation_count, system_caps) * base_prediction['load_time_factor']
        predicted_memory = self._predict_memory_requirements(violation_count) * base_prediction['memory_factor']
        
        # 系统能力调整
        if not system_caps.get('can_handle_large_datasets', False):
            predicted_load_time *= 1.3
            predicted_memory *= 0.8  # 内存优化模式
        
        return {
            'predicted_load_time_seconds': predicted_load_time,
            'predicted_memory_mb': predicted_memory,
            'ui_responsiveness': base_prediction['ui_responsiveness'],
            'stability_level': base_prediction['stability'],
            'success_probability': self._calculate_success_probability(strategy_config, violation_count, system_caps),
            'performance_score': self._calculate_performance_score(predicted_load_time, predicted_memory, base_prediction)
        }
    
    def _calculate_success_probability(self, strategy_config: Dict, violation_count: int, system_caps: Dict) -> float:
        """计算策略成功概率
        
        Args:
            strategy_config: 策略配置
            violation_count: 违例数量
            system_caps: 系统能力
            
        Returns:
            float: 成功概率 (0.0-1.0)
        """
        base_probability = 0.9
        
        # 基于违例数量调整
        thresholds = self._get_violation_count_thresholds()
        if violation_count > thresholds['very_large_dataset']:
            base_probability -= 0.2
        elif violation_count > thresholds['large_dataset']:
            base_probability -= 0.1
        
        # 基于系统能力调整
        if not system_caps.get('can_handle_large_datasets', False):
            base_probability -= 0.15
        
        # 基于策略复杂度调整
        strategy_complexity = {
            'standard_processing': 0.0,
            'high_performance_processing': -0.05,
            'streaming_processing': -0.1,
            'ultra_large_dataset_processing': -0.15
        }
        
        strategy_name = strategy_config.get('strategy_name', 'standard_processing')
        base_probability += strategy_complexity.get(strategy_name, 0.0)
        
        return max(0.1, min(1.0, base_probability))
    
    def _calculate_performance_score(self, load_time: float, memory_mb: float, base_prediction: Dict) -> int:
        """计算性能分数
        
        Args:
            load_time: 预测加载时间
            memory_mb: 预测内存使用
            base_prediction: 基础预测信息
            
        Returns:
            int: 性能分数 (0-100)
        """
        score = 100
        
        # 加载时间影响
        if load_time > 10:
            score -= 30
        elif load_time > 5:
            score -= 15
        elif load_time > 2:
            score -= 5
        
        # 内存使用影响
        if memory_mb > 1000:
            score -= 25
        elif memory_mb > 500:
            score -= 10
        elif memory_mb > 200:
            score -= 5
        
        # UI响应性影响
        ui_penalties = {
            'excellent': 0,
            'good': -5,
            'moderate': -15,
            'limited': -25,
            'poor': -40
        }
        score += ui_penalties.get(base_prediction['ui_responsiveness'], -10)
        
        # 稳定性影响
        stability_penalties = {
            'high': 0,
            'moderate': -10,
            'low': -20
        }
        score += stability_penalties.get(base_prediction['stability'], -15)
        
        return max(0, min(100, score))
    
    def monitor_strategy_performance(self, strategy_config: Dict, current_metrics: Dict) -> Dict:
        """监控策略性能并建议调整
        
        Args:
            strategy_config: 当前策略配置
            current_metrics: 当前性能指标
            
        Returns:
            Dict: 监控结果和建议
        """
        monitoring_result = {
            'status': 'monitoring',
            'current_performance': current_metrics,
            'strategy_effectiveness': 'good',
            'recommendations': [],
            'should_switch_strategy': False,
            'suggested_adjustments': {}
        }
        
        # 检查性能指标
        load_time = current_metrics.get('load_time', 0)
        memory_usage = current_metrics.get('memory_usage_mb', 0)
        ui_response_time = current_metrics.get('ui_response_time', 0)
        error_rate = current_metrics.get('error_rate', 0)
        
        # 评估策略效果
        effectiveness_score = 100
        
        # 加载时间评估
        predicted_load_time = strategy_config.get('selection_metadata', {}).get('performance_prediction', {}).get('predicted_load_time_seconds', 5)
        if load_time > predicted_load_time * 1.5:
            effectiveness_score -= 30
            monitoring_result['recommendations'].append("加载时间超出预期，建议优化批处理大小")
        
        # 内存使用评估
        predicted_memory = strategy_config.get('selection_metadata', {}).get('performance_prediction', {}).get('predicted_memory_mb', 200)
        if memory_usage > predicted_memory * 1.3:
            effectiveness_score -= 25
            monitoring_result['recommendations'].append("内存使用超出预期，建议启用内存优化模式")
        
        # UI响应性评估
        if ui_response_time > 0.2:
            effectiveness_score -= 20
            monitoring_result['recommendations'].append("UI响应较慢，建议减少UI更新频率")
        
        # 错误率评估
        if error_rate > 0.05:
            effectiveness_score -= 35
            monitoring_result['recommendations'].append("错误率较高，建议切换到更稳定的策略")
            monitoring_result['should_switch_strategy'] = True
        
        # 确定策略效果等级
        if effectiveness_score >= 80:
            monitoring_result['strategy_effectiveness'] = 'excellent'
        elif effectiveness_score >= 60:
            monitoring_result['strategy_effectiveness'] = 'good'
        elif effectiveness_score >= 40:
            monitoring_result['strategy_effectiveness'] = 'moderate'
        else:
            monitoring_result['strategy_effectiveness'] = 'poor'
            monitoring_result['should_switch_strategy'] = True
        
        # 生成调整建议
        if effectiveness_score < 70:
            monitoring_result['suggested_adjustments'] = self._generate_strategy_adjustments(
                strategy_config, current_metrics, effectiveness_score
            )
        
        return monitoring_result
    
    def _generate_strategy_adjustments(self, strategy_config: Dict, current_metrics: Dict, effectiveness_score: int) -> Dict:
        """生成策略调整建议
        
        Args:
            strategy_config: 当前策略配置
            current_metrics: 当前性能指标
            effectiveness_score: 效果分数
            
        Returns:
            Dict: 调整建议
        """
        adjustments = {}
        
        # 内存压力调整
        if current_metrics.get('memory_usage_mb', 0) > 500:
            adjustments['batch_size'] = max(100, strategy_config.get('batch_size', 1000) // 2)
            adjustments['enable_aggressive_gc'] = True
            adjustments['reduce_ui_updates'] = True
        
        # 加载时间调整
        if current_metrics.get('load_time', 0) > 10:
            adjustments['increase_batch_size'] = True
            adjustments['defer_ui_rendering'] = True
            adjustments['use_background_processing'] = True
        
        # UI响应性调整
        if current_metrics.get('ui_response_time', 0) > 0.2:
            adjustments['reduce_update_frequency'] = True
            adjustments['enable_virtual_scrolling'] = True
            adjustments['defer_non_critical_operations'] = True
        
        # 错误率调整
        if current_metrics.get('error_rate', 0) > 0.05:
            adjustments['switch_to_safer_parser'] = True
            adjustments['increase_error_handling'] = True
            adjustments['reduce_concurrency'] = True
        
        return adjustments
    
    def switch_strategy_dynamically(self, current_strategy: Dict, trigger_condition: str, current_metrics: Dict) -> Dict:
        """动态切换策略
        
        Args:
            current_strategy: 当前策略
            trigger_condition: 触发条件
            current_metrics: 当前性能指标
            
        Returns:
            Dict: 新策略配置
        """
        print(f"触发动态策略切换: {trigger_condition}")
        
        # 获取当前违例数量
        violation_count = current_strategy.get('selection_metadata', {}).get('violation_count', 1000)
        
        # 重新评估系统能力
        system_caps = self.assess_system_capabilities()
        
        # 根据触发条件选择新策略
        if trigger_condition == 'memory_pressure':
            new_strategy = self._select_memory_efficient_strategy(violation_count, system_caps)
        elif trigger_condition == 'processing_timeout':
            new_strategy = self._select_speed_optimized_strategy(violation_count, system_caps)
        elif trigger_condition == 'ui_freeze':
            new_strategy = self._select_ui_responsive_strategy(violation_count, system_caps)
        elif trigger_condition == 'high_error_rate':
            new_strategy = self._select_stable_strategy(violation_count, system_caps)
        else:
            # 默认回退到保守策略
            new_strategy = self._select_conservative_strategy(violation_count, system_caps)
        
        # 添加切换元数据
        new_strategy['switch_metadata'] = {
            'previous_strategy': current_strategy.get('strategy_name', 'unknown'),
            'switch_trigger': trigger_condition,
            'switch_time': time.time(),
            'switch_reason': f"由于{trigger_condition}触发策略切换",
            'expected_improvement': self._predict_switch_improvement(current_strategy, new_strategy, trigger_condition)
        }
        
        return new_strategy
    
    def _select_memory_efficient_strategy(self, violation_count: int, system_caps: Dict) -> Dict:
        """选择内存高效策略"""
        return {
            'strategy_name': 'memory_efficient_processing',
            'parser_type': 'memory_efficient_streaming',
            'display_mode': 'paginated_virtual_table',
            'batch_size': 500,
            'use_streaming': True,
            'use_pagination': True,
            'page_size': 25,
            'memory_limit_mb': 100,
            'aggressive_gc': True,
            'lazy_loading': True,
            'priority': 'memory_conservation'
        }
    
    def _select_speed_optimized_strategy(self, violation_count: int, system_caps: Dict) -> Dict:
        """选择速度优化策略"""
        return {
            'strategy_name': 'speed_optimized_processing',
            'parser_type': 'high_performance_async',
            'display_mode': 'background_processing',
            'batch_size': min(10000, system_caps.get('optimal_batch_size', 5000)),
            'use_streaming': False,
            'use_pagination': True,
            'page_size': 200,
            'memory_limit_mb': 600,
            'defer_ui_updates': True,
            'background_processing': True,
            'priority': 'processing_speed'
        }
    
    def _select_ui_responsive_strategy(self, violation_count: int, system_caps: Dict) -> Dict:
        """选择UI响应优化策略"""
        return {
            'strategy_name': 'ui_responsive_processing',
            'parser_type': 'chunked_async',
            'display_mode': 'progressive_loading',
            'batch_size': 200,
            'use_streaming': True,
            'use_pagination': True,
            'page_size': 50,
            'memory_limit_mb': 200,
            'ui_update_interval': 100,
            'progressive_rendering': True,
            'priority': 'user_experience'
        }
    
    def _select_stable_strategy(self, violation_count: int, system_caps: Dict) -> Dict:
        """选择稳定性优化策略"""
        return {
            'strategy_name': 'stable_processing',
            'parser_type': 'basic_sync',
            'display_mode': 'simple_table',
            'batch_size': 100,
            'use_streaming': False,
            'use_pagination': True,
            'page_size': 100,
            'memory_limit_mb': 150,
            'error_recovery': True,
            'conservative_mode': True,
            'priority': 'stability'
        }
    
    def _select_conservative_strategy(self, violation_count: int, system_caps: Dict) -> Dict:
        """选择保守策略"""
        return {
            'strategy_name': 'conservative_processing',
            'parser_type': 'basic_sync',
            'display_mode': 'minimal_table',
            'batch_size': 50,
            'use_streaming': False,
            'use_pagination': True,
            'page_size': 50,
            'memory_limit_mb': 100,
            'minimal_features': True,
            'safe_mode': True,
            'priority': 'reliability'
        }
    
    def _predict_switch_improvement(self, old_strategy: Dict, new_strategy: Dict, trigger: str) -> Dict:
        """预测策略切换的改进效果"""
        improvements = {
            'memory_pressure': {
                'memory_usage_reduction': '30-50%',
                'stability_improvement': 'high',
                'performance_trade_off': 'moderate_slowdown'
            },
            'processing_timeout': {
                'speed_improvement': '20-40%',
                'memory_usage_increase': 'moderate',
                'ui_responsiveness': 'may_decrease'
            },
            'ui_freeze': {
                'ui_responsiveness_improvement': 'significant',
                'user_experience': 'much_better',
                'processing_speed': 'may_decrease'
            },
            'high_error_rate': {
                'stability_improvement': 'high',
                'error_rate_reduction': '70-90%',
                'performance': 'conservative'
            }
        }
        
        return improvements.get(trigger, {
            'general_improvement': 'expected',
            'trade_offs': 'minimal'
        })
    
    def validate_estimation_accuracy(self, file_path: str, actual_count: int) -> Dict:
        """验证估算准确性
        
        Args:
            file_path: 文件路径
            actual_count: 实际违例数量
            
        Returns:
            Dict: 验证结果
        """
        try:
            # 基础估算
            file_size = os.path.getsize(file_path)
            basic_estimate = self._estimate_record_count(file_size)
            
            # 采样估算
            sampling_estimate = self.estimate_violation_count_with_sampling(file_path)
            
            # 计算准确性
            basic_accuracy = 1 - abs(basic_estimate - actual_count) / max(actual_count, 1)
            sampling_accuracy = 1 - abs(sampling_estimate - actual_count) / max(actual_count, 1)
            
            return {
                'file_path': file_path,
                'actual_count': actual_count,
                'basic_estimate': basic_estimate,
                'sampling_estimate': sampling_estimate,
                'basic_accuracy': max(0, basic_accuracy),
                'sampling_accuracy': max(0, sampling_accuracy),
                'basic_error_percent': abs(basic_estimate - actual_count) / max(actual_count, 1) * 100,
                'sampling_error_percent': abs(sampling_estimate - actual_count) / max(actual_count, 1) * 100,
                'recommended_method': 'sampling' if sampling_accuracy > basic_accuracy else 'basic'
            }
            
        except Exception as e:
            print(f"估算准确性验证失败: {str(e)}")
            return {}
    
    def _predict_load_time(self, file_size_mb: float, record_count: int) -> float:
        """预测加载时间
        
        基于经验公式和硬件性能
        """
        # 基础时间（文件I/O）
        base_time = file_size_mb * 0.1  # 每MB约0.1秒
        
        # 解析时间（CPU密集型）
        parse_time = record_count * 0.0001  # 每条记录约0.0001秒
        
        # UI渲染时间（如果使用标准表格）
        ui_time = min(record_count * 0.0005, 5.0)  # 每条记录约0.0005秒，最多5秒
        
        return base_time + parse_time + ui_time
    
    def _recommend_parsing_strategy(self, file_size_mb: float, record_count: int) -> str:
        """推荐解析策略"""
        if file_size_mb > 50 or record_count > 20000:
            return "high_performance_streaming"
        elif file_size_mb > 10 or record_count > 5000:
            return "high_performance_async"
        else:
            return "standard_async"
    
    def _recommend_display_mode(self, record_count: int) -> str:
        """推荐显示模式"""
        if record_count > 1000:
            return "high_performance_table"
        else:
            return "standard_table"
    
    def _assess_performance_level(self, file_size_mb: float, record_count: int) -> str:
        """评估性能等级"""
        if file_size_mb > 100 or record_count > 50000:
            return "critical"
        elif file_size_mb > 20 or record_count > 10000:
            return "warning"
        elif file_size_mb > 5 or record_count > 2000:
            return "moderate"
        else:
            return "good"
    
    def monitor_load_performance(self, start_time: float, end_time: float, 
                               record_count: int, memory_usage: float) -> Dict:
        """监控加载性能
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            record_count: 记录数量
            memory_usage: 内存使用量（MB）
            
        Returns:
            Dict: 性能监控结果
        """
        load_time = end_time - start_time
        
        # 更新统计信息
        self.stats['total_load_time'] += load_time
        self.stats['total_records_processed'] += record_count
        self.stats['peak_memory_usage'] = max(self.stats['peak_memory_usage'], memory_usage)
        
        # 性能评估
        performance_issues = []
        optimization_suggestions = []
        
        # 加载时间检查
        if load_time > self.thresholds['load_time_critical']:
            performance_issues.append(f"加载时间过长: {load_time:.2f}秒")
            optimization_suggestions.extend([
                "建议使用高性能解析器",
                "考虑分批处理大文件",
                "启用流式处理模式"
            ])
        elif load_time > self.thresholds['load_time_warning']:
            performance_issues.append(f"加载时间较长: {load_time:.2f}秒")
            optimization_suggestions.append("建议使用高性能模式")
        
        # 内存使用检查
        if memory_usage > self.thresholds['memory_critical']:
            performance_issues.append(f"内存使用过高: {memory_usage:.1f}MB")
            optimization_suggestions.extend([
                "启用内存优化模式",
                "使用分页显示",
                "考虑数据筛选"
            ])
        elif memory_usage > self.thresholds['memory_warning']:
            performance_issues.append(f"内存使用较高: {memory_usage:.1f}MB")
            optimization_suggestions.append("建议启用分页显示")
        
        # 记录数量检查
        if record_count > self.thresholds['record_count_critical']:
            performance_issues.append(f"数据量过大: {record_count:,}条记录")
            optimization_suggestions.extend([
                "强烈建议使用筛选功能",
                "启用虚拟滚动",
                "考虑数据分批处理"
            ])
        elif record_count > self.thresholds['record_count_high']:
            performance_issues.append(f"数据量较大: {record_count:,}条记录")
            optimization_suggestions.append("建议使用高性能表格")
        
        # 计算性能指标
        throughput = record_count / max(load_time, 0.001)
        memory_efficiency = record_count / max(memory_usage, 1)
        
        return {
            'load_time': load_time,
            'record_count': record_count,
            'memory_usage': memory_usage,
            'throughput': throughput,
            'memory_efficiency': memory_efficiency,
            'performance_issues': performance_issues,
            'optimization_suggestions': optimization_suggestions,
            'performance_score': self._calculate_performance_score(load_time, memory_usage, record_count)
        }
    
    def _calculate_performance_score(self, load_time: float, memory_usage: float, record_count: int) -> int:
        """计算性能评分（0-100）"""
        score = 100
        
        # 加载时间扣分
        if load_time > 10:
            score -= 40
        elif load_time > 5:
            score -= 20
        elif load_time > 2:
            score -= 10
        
        # 内存使用扣分
        if memory_usage > 500:
            score -= 30
        elif memory_usage > 300:
            score -= 15
        elif memory_usage > 150:
            score -= 5
        
        # 数据量扣分
        if record_count > 20000:
            score -= 20
        elif record_count > 10000:
            score -= 10
        elif record_count > 5000:
            score -= 5
        
        return max(0, score)
    
    def _monitor_performance(self):
        """定期性能监控"""
        try:
            # 获取当前进程信息
            process = psutil.Process()
            memory_info = process.memory_info()
            cpu_percent = process.cpu_percent()
            
            # 检查内存使用
            memory_mb = memory_info.rss / 1024 / 1024
            if memory_mb > self.thresholds['memory_critical']:
                self.performance_warning.emit(
                    "memory_critical", 
                    f"内存使用过高: {memory_mb:.1f}MB，建议重启插件或减少数据量"
                )
            
            # 检查CPU使用
            if cpu_percent > 80:
                self.performance_warning.emit(
                    "cpu_high", 
                    f"CPU使用率过高: {cpu_percent:.1f}%，可能影响系统响应"
                )
                
        except Exception as e:
            print(f"性能监控失败: {str(e)}")
    
    def get_optimization_recommendations(self, current_config: Dict) -> List[str]:
        """获取优化建议
        
        Args:
            current_config: 当前配置信息
            
        Returns:
            List[str]: 优化建议列表
        """
        recommendations = []
        
        # 基于当前配置的建议
        if current_config.get('use_standard_table', False) and current_config.get('record_count', 0) > 1000:
            recommendations.append("建议切换到高性能表格模式以提升显示速度")
        
        if current_config.get('page_size', 100) > 200:
            recommendations.append("建议减少分页大小以提升页面切换速度")
        
        if not current_config.get('use_async_parsing', True):
            recommendations.append("建议启用异步解析以避免界面卡死")
        
        # 基于历史性能的建议
        if self.stats['ui_freeze_count'] > 3:
            recommendations.append("检测到多次界面卡死，建议降低数据处理批次大小")
        
        if self.stats['peak_memory_usage'] > 400:
            recommendations.append("历史内存使用较高，建议启用内存优化模式")
        
        return recommendations
    
    def apply_auto_optimization(self, file_analysis: Dict) -> Dict:
        """应用自动优化
        
        Args:
            file_analysis: 文件分析结果
            
        Returns:
            Dict: 优化配置
        """
        config = {}
        
        # 根据性能等级自动配置
        performance_level = file_analysis.get('performance_level', 'good')
        
        if performance_level == 'critical':
            config.update({
                'use_high_performance_parser': True,
                'use_streaming_mode': True,
                'page_size': 50,
                'progress_interval': 100000,
                'gc_interval': 50000,
                'batch_size': 5000
            })
        elif performance_level == 'warning':
            config.update({
                'use_high_performance_parser': True,
                'use_streaming_mode': False,
                'page_size': 100,
                'progress_interval': 50000,
                'gc_interval': 100000,
                'batch_size': 2000
            })
        elif performance_level == 'moderate':
            config.update({
                'use_high_performance_parser': False,
                'page_size': 200,
                'progress_interval': 20000,
                'batch_size': 1000
            })
        else:
            config.update({
                'use_high_performance_parser': False,
                'page_size': 500,
                'progress_interval': 5000,
                'batch_size': 500
            })
        
        # 记录应用的优化
        self.stats['optimization_applied'].append({
            'timestamp': time.time(),
            'performance_level': performance_level,
            'config': config.copy()
        })
        
        return config
