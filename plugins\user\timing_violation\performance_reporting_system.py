"""
性能报告系统

提供详细的违例处理性能报告、策略比较和优化趋势分析。
"""

import json
import time
import statistics
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from datetime import datetime, timedelta
from PyQt5.QtCore import QObject, pyqtSignal

try:
    from .violation_performance_monitor import ViolationProcessingMetrics, PerformanceBaseline
except ImportError:
    # Fallback for direct execution
    from violation_performance_monitor import ViolationProcessingMetrics, PerformanceBaseline


@dataclass
class PerformanceReport:
    """性能报告数据结构"""
    report_id: str
    timestamp: float
    report_type: str  # 'session', 'comparison', 'trend', 'summary'
    violation_count: int
    duration_seconds: float
    
    # 核心性能指标
    violations_per_second: float
    avg_ui_response_time_ms: float
    peak_memory_usage_mb: float
    avg_cpu_usage_percent: float
    
    # 详细统计
    processing_efficiency: float  # 0-100%
    memory_efficiency: float      # 0-100%
    ui_responsiveness_score: float # 0-100%
    overall_performance_score: float # 0-100%
    
    # 策略信息
    parsing_strategy: str
    rendering_mode: str
    optimization_applied: List[str]
    
    # 问题和建议
    performance_issues: List[str]
    optimization_suggestions: List[str]
    
    # 比较数据（可选）
    baseline_comparison: Optional[Dict] = None
    strategy_comparison: Optional[Dict] = None
    
    # 趋势数据（可选）
    trend_analysis: Optional[Dict] = None


@dataclass
class StrategyPerformanceComparison:
    """策略性能比较数据"""
    strategy_name: str
    violation_count_range: Tuple[int, int]
    sample_count: int
    
    # 性能指标
    avg_violations_per_second: float
    avg_ui_response_time_ms: float
    avg_memory_usage_mb: float
    avg_load_time_seconds: float
    
    # 效率评分
    efficiency_score: float
    reliability_score: float
    resource_usage_score: float
    
    # 适用场景
    recommended_for: List[str]
    limitations: List[str]


class PerformanceReportingSystem(QObject):
    """性能报告系统"""
    
    # 信号定义
    report_generated = pyqtSignal(dict)  # 报告生成完成
    trend_analysis_updated = pyqtSignal(dict)  # 趋势分析更新
    performance_comparison_ready = pyqtSignal(dict)  # 性能比较就绪
    
    def __init__(self):
        super().__init__()
        
        # 报告存储
        self.performance_reports = deque(maxlen=1000)  # 限制内存使用
        self.strategy_performance_data = defaultdict(list)
        self.performance_history = defaultdict(deque)  # 按违例数量范围分组
        
        # 趋势分析数据
        self.trend_data = {
            'daily_performance': deque(maxlen=30),    # 30天数据
            'strategy_usage': defaultdict(int),       # 策略使用统计
            'performance_improvements': deque(maxlen=100),  # 性能改进记录
            'issue_patterns': defaultdict(list)       # 问题模式分析
        }
        
        # 性能基准数据
        self.performance_benchmarks = {
            'small_dataset': {'violations_per_second': 2000, 'ui_response_ms': 50, 'memory_mb': 100},
            'medium_dataset': {'violations_per_second': 1500, 'ui_response_ms': 80, 'memory_mb': 300},
            'large_dataset': {'violations_per_second': 1000, 'ui_response_ms': 100, 'memory_mb': 800}
        }
        
        # 报告配置
        self.report_config = {
            'include_detailed_metrics': True,
            'include_trend_analysis': True,
            'include_strategy_comparison': True,
            'performance_threshold_warnings': True,
            'auto_generate_suggestions': True
        }        

    def generate_session_report(self, session_data: Dict, 
                              performance_metrics: List[ViolationProcessingMetrics]) -> PerformanceReport:
        """生成会话性能报告
        
        Args:
            session_data: 会话数据
            performance_metrics: 性能指标列表
            
        Returns:
            PerformanceReport: 性能报告
        """
        report_id = f"session_{int(time.time() * 1000)}"
        
        # 计算核心性能指标
        violations_per_second = session_data.get('avg_violations_per_second', 0)
        ui_response_time = session_data.get('avg_ui_response_time_ms', 0)
        peak_memory = session_data.get('peak_memory_usage_mb', 0)
        avg_cpu = session_data.get('avg_cpu_usage_percent', 0)
        
        # 计算效率评分
        processing_efficiency = self._calculate_processing_efficiency(
            violations_per_second, session_data.get('violation_count', 0)
        )
        memory_efficiency = self._calculate_memory_efficiency(peak_memory)
        ui_responsiveness_score = self._calculate_ui_responsiveness_score(ui_response_time)
        overall_score = (processing_efficiency + memory_efficiency + ui_responsiveness_score) / 3
        
        # 分析性能问题
        performance_issues = self._analyze_performance_issues(session_data, performance_metrics)
        
        # 生成优化建议
        optimization_suggestions = self._generate_optimization_suggestions(
            session_data, performance_issues
        )
        
        # 获取基线比较数据
        baseline_comparison = self._get_baseline_comparison(session_data)
        
        # 创建报告
        report = PerformanceReport(
            report_id=report_id,
            timestamp=time.time(),
            report_type='session',
            violation_count=session_data.get('violation_count', 0),
            duration_seconds=session_data.get('duration_seconds', 0),
            violations_per_second=violations_per_second,
            avg_ui_response_time_ms=ui_response_time,
            peak_memory_usage_mb=peak_memory,
            avg_cpu_usage_percent=avg_cpu,
            processing_efficiency=processing_efficiency,
            memory_efficiency=memory_efficiency,
            ui_responsiveness_score=ui_responsiveness_score,
            overall_performance_score=overall_score,
            parsing_strategy=session_data.get('parsing_strategy', 'unknown'),
            rendering_mode=session_data.get('rendering_mode', 'unknown'),
            optimization_applied=session_data.get('optimizations_applied', []),
            performance_issues=performance_issues,
            optimization_suggestions=optimization_suggestions,
            baseline_comparison=baseline_comparison
        )
        
        # 存储报告
        self.performance_reports.append(report)
        
        # 更新趋势数据
        self._update_trend_data(report)
        
        # 发出信号
        self.report_generated.emit(asdict(report))
        
        return report
        
    def generate_strategy_comparison_report(self, strategies: List[str], 
                                          violation_count_range: Tuple[int, int]) -> Dict:
        """生成策略性能比较报告
        
        Args:
            strategies: 要比较的策略列表
            violation_count_range: 违例数量范围
            
        Returns:
            Dict: 策略比较报告
        """
        comparison_data = {}
        
        for strategy in strategies:
            strategy_metrics = self._get_strategy_performance_data(strategy, violation_count_range)
            
            if strategy_metrics:
                comparison = StrategyPerformanceComparison(
                    strategy_name=strategy,
                    violation_count_range=violation_count_range,
                    sample_count=len(strategy_metrics),
                    avg_violations_per_second=statistics.mean(
                        [m.violations_per_second for m in strategy_metrics]
                    ),
                    avg_ui_response_time_ms=statistics.mean(
                        [m.ui_response_time_ms for m in strategy_metrics]
                    ),
                    avg_memory_usage_mb=statistics.mean(
                        [m.memory_usage_mb for m in strategy_metrics]
                    ),
                    avg_load_time_seconds=statistics.mean(
                        [m.elapsed_time for m in strategy_metrics]
                    ),
                    efficiency_score=self._calculate_strategy_efficiency_score(strategy_metrics),
                    reliability_score=self._calculate_strategy_reliability_score(strategy_metrics),
                    resource_usage_score=self._calculate_resource_usage_score(strategy_metrics),
                    recommended_for=self._get_strategy_recommendations(strategy, strategy_metrics),
                    limitations=self._get_strategy_limitations(strategy, strategy_metrics)
                )
                
                comparison_data[strategy] = asdict(comparison)
        
        # 生成比较摘要
        comparison_report = {
            'report_id': f"comparison_{int(time.time() * 1000)}",
            'timestamp': time.time(),
            'violation_count_range': violation_count_range,
            'strategies_compared': strategies,
            'comparison_data': comparison_data,
            'recommendations': self._generate_strategy_recommendations(comparison_data),
            'best_strategy': self._determine_best_strategy(comparison_data),
            'performance_ranking': self._rank_strategies_by_performance(comparison_data)
        }
        
        # 发出信号
        self.performance_comparison_ready.emit(comparison_report)
        
        return comparison_report    
    
    def generate_trend_analysis_report(self, days: int = 7) -> Dict:
        """生成性能趋势分析报告
        
        Args:
            days: 分析天数
            
        Returns:
            Dict: 趋势分析报告
        """
        cutoff_time = time.time() - (days * 24 * 3600)
        
        # 获取时间范围内的报告
        recent_reports = [
            report for report in self.performance_reports
            if report.timestamp >= cutoff_time
        ]
        
        if not recent_reports:
            return {'error': 'No performance data available for the specified period'}
        
        # 按天分组数据
        daily_data = defaultdict(list)
        for report in recent_reports:
            day_key = datetime.fromtimestamp(report.timestamp).strftime('%Y-%m-%d')
            daily_data[day_key].append(report)
        
        # 计算趋势指标
        trend_metrics = {}
        for day, reports in daily_data.items():
            trend_metrics[day] = {
                'avg_violations_per_second': statistics.mean([r.violations_per_second for r in reports]),
                'avg_ui_response_time_ms': statistics.mean([r.avg_ui_response_time_ms for r in reports]),
                'avg_memory_usage_mb': statistics.mean([r.peak_memory_usage_mb for r in reports]),
                'avg_performance_score': statistics.mean([r.overall_performance_score for r in reports]),
                'total_violations_processed': sum([r.violation_count for r in reports]),
                'session_count': len(reports)
            }
        
        # 计算趋势方向
        performance_trend = self._calculate_performance_trend(trend_metrics)
        
        # 识别性能模式
        performance_patterns = self._identify_performance_patterns(recent_reports)
        
        # 生成趋势报告
        trend_report = {
            'report_id': f"trend_{int(time.time() * 1000)}",
            'timestamp': time.time(),
            'analysis_period_days': days,
            'total_sessions': len(recent_reports),
            'daily_metrics': trend_metrics,
            'performance_trend': performance_trend,
            'performance_patterns': performance_patterns,
            'improvement_opportunities': self._identify_improvement_opportunities(recent_reports),
            'regression_alerts': self._detect_performance_regressions(recent_reports),
            'optimization_impact': self._analyze_optimization_impact(recent_reports)
        }
        
        # 更新趋势数据
        self.trend_data['daily_performance'].append(trend_report)
        
        # 发出信号
        self.trend_analysis_updated.emit(trend_report)
        
        return trend_report
        
    def get_performance_summary(self, violation_count: int = None) -> Dict:
        """获取性能摘要
        
        Args:
            violation_count: 可选的违例数量过滤
            
        Returns:
            Dict: 性能摘要
        """
        if not self.performance_reports:
            return {'error': 'No performance data available'}
        
        # 过滤报告
        if violation_count:
            filtered_reports = [
                r for r in self.performance_reports
                if abs(r.violation_count - violation_count) / max(violation_count, 1) < 0.2  # 20%范围内
            ]
        else:
            filtered_reports = list(self.performance_reports)
        
        if not filtered_reports:
            return {'error': 'No matching performance data found'}
        
        # 计算摘要统计
        summary = {
            'total_sessions': len(filtered_reports),
            'avg_violations_per_second': statistics.mean([r.violations_per_second for r in filtered_reports]),
            'avg_ui_response_time_ms': statistics.mean([r.avg_ui_response_time_ms for r in filtered_reports]),
            'avg_memory_usage_mb': statistics.mean([r.peak_memory_usage_mb for r in filtered_reports]),
            'avg_performance_score': statistics.mean([r.overall_performance_score for r in filtered_reports]),
            'best_performance_score': max([r.overall_performance_score for r in filtered_reports]),
            'worst_performance_score': min([r.overall_performance_score for r in filtered_reports]),
            'common_issues': self._get_common_performance_issues(filtered_reports),
            'top_optimizations': self._get_top_optimization_suggestions(filtered_reports),
            'strategy_usage': self._get_strategy_usage_stats(filtered_reports)
        }
        
        return summary     
   
    def export_performance_data(self, format: str = 'json', 
                              include_raw_metrics: bool = False) -> str:
        """导出性能数据
        
        Args:
            format: 导出格式 ('json', 'csv')
            include_raw_metrics: 是否包含原始指标数据
            
        Returns:
            str: 导出的数据字符串
        """
        export_data = {
            'export_timestamp': time.time(),
            'total_reports': len(self.performance_reports),
            'reports': [asdict(report) for report in self.performance_reports]
        }
        
        if include_raw_metrics:
            export_data['trend_data'] = {
                'daily_performance': [asdict(d) for d in self.trend_data['daily_performance']],
                'strategy_usage': dict(self.trend_data['strategy_usage'])
            }
        
        if format == 'json':
            return json.dumps(export_data, indent=2, default=str)
        elif format == 'csv':
            return self._convert_to_csv(export_data)
        else:
            raise ValueError(f"Unsupported export format: {format}")
            
    def _calculate_processing_efficiency(self, violations_per_second: float, 
                                       violation_count: int) -> float:
        """计算处理效率评分"""
        if violation_count < 2000:
            benchmark = self.performance_benchmarks['small_dataset']['violations_per_second']
        elif violation_count < 20000:
            benchmark = self.performance_benchmarks['medium_dataset']['violations_per_second']
        else:
            benchmark = self.performance_benchmarks['large_dataset']['violations_per_second']
        
        efficiency = min(100, (violations_per_second / benchmark) * 100)
        return max(0, efficiency)
        
    def _calculate_memory_efficiency(self, memory_usage_mb: float) -> float:
        """计算内存效率评分"""
        # 基于1GB内存限制计算效率
        max_memory = 1000  # MB
        efficiency = max(0, 100 - (memory_usage_mb / max_memory) * 100)
        return min(100, efficiency)
        
    def _calculate_ui_responsiveness_score(self, ui_response_time_ms: float) -> float:
        """计算UI响应性评分"""
        # 基于100ms响应时间要求计算评分
        target_response_time = 100  # ms
        if ui_response_time_ms <= target_response_time:
            return 100
        else:
            # 响应时间超过目标时，评分递减
            penalty = min(100, (ui_response_time_ms - target_response_time) / target_response_time * 50)
            return max(0, 100 - penalty)
            
    def _analyze_performance_issues(self, session_data: Dict, 
                                  metrics: List[ViolationProcessingMetrics]) -> List[str]:
        """分析性能问题"""
        issues = []
        
        # 检查加载时间
        duration = session_data.get('duration_seconds', 0)
        if duration > 2.0:
            issues.append(f"Load time ({duration:.1f}s) exceeds 2 second threshold")
        elif duration > 1.0:
            issues.append(f"Load time ({duration:.1f}s) exceeds 1 second target")
        
        # 检查内存使用
        peak_memory = session_data.get('peak_memory_usage_mb', 0)
        if peak_memory > 1000:
            issues.append(f"Memory usage ({peak_memory:.0f}MB) exceeds 1GB limit")
        elif peak_memory > 800:
            issues.append(f"Memory usage ({peak_memory:.0f}MB) approaching 1GB limit")
        
        # 检查UI响应时间
        ui_response = session_data.get('avg_ui_response_time_ms', 0)
        if ui_response > 200:
            issues.append(f"UI response time ({ui_response:.0f}ms) critically slow")
        elif ui_response > 100:
            issues.append(f"UI response time ({ui_response:.0f}ms) exceeds 100ms target")
        
        # 检查处理速度
        violations_per_second = session_data.get('avg_violations_per_second', 0)
        violation_count = session_data.get('violation_count', 0)
        if violation_count > 0 and violations_per_second < 500:
            issues.append(f"Processing speed ({violations_per_second:.0f} violations/s) below minimum threshold")
        
        return issues  
      
    def _generate_optimization_suggestions(self, session_data: Dict, 
                                         issues: List[str]) -> List[str]:
        """生成优化建议"""
        suggestions = []
        violation_count = session_data.get('violation_count', 0)
        
        # 基于违例数量的建议
        if violation_count > 20000:
            suggestions.append("Consider using streaming parser for large datasets (>20K violations)")
            suggestions.append("Enable pagination with smaller page sizes for better UI responsiveness")
            suggestions.append("Use background prefetching to improve navigation performance")
        elif violation_count > 2000:
            suggestions.append("Use high-performance parser for medium datasets (2K-20K violations)")
            suggestions.append("Enable virtualized table rendering for better performance")
        
        # 基于性能问题的建议
        for issue in issues:
            if "Load time" in issue:
                suggestions.append("Enable file streaming and chunked processing")
                suggestions.append("Increase batch size for better throughput")
            elif "Memory usage" in issue:
                suggestions.append("Enable lazy loading and memory cleanup")
                suggestions.append("Reduce page size to lower memory footprint")
            elif "UI response time" in issue:
                suggestions.append("Enable UI virtualization and background processing")
                suggestions.append("Implement progressive rendering for large datasets")
            elif "Processing speed" in issue:
                suggestions.append("Switch to high-performance parsing strategy")
                suggestions.append("Optimize batch processing parameters")
        
        return list(set(suggestions))  # Remove duplicates
        
    def _get_baseline_comparison(self, session_data: Dict) -> Dict:
        """获取基线比较数据"""
        violation_count = session_data.get('violation_count', 0)
        
        if violation_count < 2000:
            benchmark = self.performance_benchmarks['small_dataset']
        elif violation_count < 20000:
            benchmark = self.performance_benchmarks['medium_dataset']
        else:
            benchmark = self.performance_benchmarks['large_dataset']
        
        return {
            'benchmark_violations_per_second': benchmark['violations_per_second'],
            'actual_violations_per_second': session_data.get('avg_violations_per_second', 0),
            'benchmark_ui_response_ms': benchmark['ui_response_ms'],
            'actual_ui_response_ms': session_data.get('avg_ui_response_time_ms', 0),
            'benchmark_memory_mb': benchmark['memory_mb'],
            'actual_memory_mb': session_data.get('peak_memory_usage_mb', 0)
        }
        
    def _update_trend_data(self, report: PerformanceReport):
        """更新趋势数据"""
        # 更新策略使用统计
        self.trend_data['strategy_usage'][report.parsing_strategy] += 1
        
        # 记录性能改进
        if report.overall_performance_score > 80:
            self.trend_data['performance_improvements'].append({
                'timestamp': report.timestamp,
                'score': report.overall_performance_score,
                'strategy': report.parsing_strategy,
                'violation_count': report.violation_count
            })
        
        # 记录问题模式
        for issue in report.performance_issues:
            self.trend_data['issue_patterns'][issue].append(report.timestamp)
            
    def _get_strategy_performance_data(self, strategy: str, 
                                     violation_count_range: Tuple[int, int]) -> List[ViolationProcessingMetrics]:
        """获取策略性能数据"""
        # 这里应该从实际的性能监控数据中获取
        # 暂时返回空列表，实际实现时需要连接到性能监控器
        return []
        
    def _calculate_strategy_efficiency_score(self, metrics: List[ViolationProcessingMetrics]) -> float:
        """计算策略效率评分"""
        if not metrics:
            return 0.0
        
        avg_violations_per_second = statistics.mean([m.violations_per_second for m in metrics])
        avg_ui_response = statistics.mean([m.ui_response_time_ms for m in metrics])
        
        # 综合评分：处理速度权重60%，UI响应性权重40%
        speed_score = min(100, (avg_violations_per_second / 1000) * 100)
        response_score = max(0, 100 - (avg_ui_response / 100) * 100)
        
        return speed_score * 0.6 + response_score * 0.4
        
    def _calculate_strategy_reliability_score(self, metrics: List[ViolationProcessingMetrics]) -> float:
        """计算策略可靠性评分"""
        if not metrics:
            return 0.0
        
        error_rate = sum(m.error_count for m in metrics) / len(metrics)
        reliability = max(0, 100 - error_rate * 20)  # 每个错误扣20分
        
        return reliability
        
    def _calculate_resource_usage_score(self, metrics: List[ViolationProcessingMetrics]) -> float:
        """计算资源使用评分"""
        if not metrics:
            return 0.0
        
        avg_memory = statistics.mean([m.memory_usage_mb for m in metrics])
        avg_cpu = statistics.mean([m.cpu_usage_percent for m in metrics])
        
        # 内存和CPU使用评分
        memory_score = max(0, 100 - (avg_memory / 1000) * 100)
        cpu_score = max(0, 100 - avg_cpu)
        
        return (memory_score + cpu_score) / 2     
   
    def _get_strategy_recommendations(self, strategy: str, 
                                   metrics: List[ViolationProcessingMetrics]) -> List[str]:
        """获取策略推荐场景"""
        recommendations = []
        
        if not metrics:
            return recommendations
        
        avg_violation_count = statistics.mean([m.violation_count for m in metrics])
        
        if strategy == 'standard':
            if avg_violation_count < 2000:
                recommendations.append("Small datasets (<2K violations)")
                recommendations.append("Quick analysis tasks")
        elif strategy == 'high_performance':
            if 2000 <= avg_violation_count < 20000:
                recommendations.append("Medium datasets (2K-20K violations)")
                recommendations.append("Regular analysis workflows")
        elif strategy == 'streaming':
            if avg_violation_count >= 20000:
                recommendations.append("Large datasets (>20K violations)")
                recommendations.append("Memory-constrained environments")
        
        return recommendations
        
    def _get_strategy_limitations(self, strategy: str, 
                                metrics: List[ViolationProcessingMetrics]) -> List[str]:
        """获取策略限制"""
        limitations = []
        
        if strategy == 'standard':
            limitations.append("Not suitable for large datasets")
            limitations.append("High memory usage with large files")
        elif strategy == 'high_performance':
            limitations.append("Higher CPU usage")
            limitations.append("Complex configuration requirements")
        elif strategy == 'streaming':
            limitations.append("Slower initial load time")
            limitations.append("Limited random access capabilities")
        
        return limitations
        
    def _generate_strategy_recommendations(self, comparison_data: Dict) -> List[str]:
        """生成策略推荐"""
        recommendations = []
        
        if not comparison_data:
            return recommendations
        
        # 找出最佳策略
        best_strategy = max(comparison_data.keys(), 
                          key=lambda k: comparison_data[k]['efficiency_score'])
        
        recommendations.append(f"Recommended strategy: {best_strategy}")
        
        # 基于效率评分的建议
        for strategy, data in comparison_data.items():
            if data['efficiency_score'] > 80:
                recommendations.append(f"{strategy} shows excellent performance")
            elif data['efficiency_score'] < 50:
                recommendations.append(f"{strategy} may need optimization")
        
        return recommendations
        
    def _determine_best_strategy(self, comparison_data: Dict) -> str:
        """确定最佳策略"""
        if not comparison_data:
            return "unknown"
        
        return max(comparison_data.keys(), 
                  key=lambda k: comparison_data[k]['efficiency_score'])
        
    def _rank_strategies_by_performance(self, comparison_data: Dict) -> List[str]:
        """按性能排序策略"""
        if not comparison_data:
            return []
        
        return sorted(comparison_data.keys(), 
                     key=lambda k: comparison_data[k]['efficiency_score'], 
                     reverse=True)
        
    def _calculate_performance_trend(self, trend_metrics: Dict) -> Dict:
        """计算性能趋势"""
        if len(trend_metrics) < 2:
            return {'trend': 'insufficient_data'}
        
        dates = sorted(trend_metrics.keys())
        scores = [trend_metrics[date]['avg_performance_score'] for date in dates]
        
        # 简单的线性趋势计算
        if len(scores) >= 2:
            trend_direction = 'improving' if scores[-1] > scores[0] else 'declining'
            trend_magnitude = abs(scores[-1] - scores[0])
        else:
            trend_direction = 'stable'
            trend_magnitude = 0
        
        return {
            'trend': trend_direction,
            'magnitude': trend_magnitude,
            'latest_score': scores[-1] if scores else 0,
            'score_change': scores[-1] - scores[0] if len(scores) >= 2 else 0
        }       
 
    def _identify_performance_patterns(self, reports: List[PerformanceReport]) -> Dict:
        """识别性能模式"""
        patterns = {
            'peak_hours': defaultdict(list),
            'violation_count_impact': {},
            'strategy_effectiveness': defaultdict(list)
        }
        
        for report in reports:
            # 按小时分组
            hour = datetime.fromtimestamp(report.timestamp).hour
            patterns['peak_hours'][hour].append(report.overall_performance_score)
            
            # 策略效果分析
            patterns['strategy_effectiveness'][report.parsing_strategy].append(
                report.overall_performance_score
            )
        
        # 计算平均值
        for hour, scores in patterns['peak_hours'].items():
            patterns['peak_hours'][hour] = statistics.mean(scores)
        
        for strategy, scores in patterns['strategy_effectiveness'].items():
            patterns['strategy_effectiveness'][strategy] = statistics.mean(scores)
        
        return patterns
        
    def _identify_improvement_opportunities(self, reports: List[PerformanceReport]) -> List[str]:
        """识别改进机会"""
        opportunities = []
        
        # 分析常见问题
        all_issues = []
        for report in reports:
            all_issues.extend(report.performance_issues)
        
        issue_counts = defaultdict(int)
        for issue in all_issues:
            issue_counts[issue] += 1
        
        # 找出最常见的问题
        common_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        
        for issue, count in common_issues:
            opportunities.append(f"Address common issue: {issue} (occurred {count} times)")
        
        return opportunities
        
    def _detect_performance_regressions(self, reports: List[PerformanceReport]) -> List[str]:
        """检测性能回归"""
        regressions = []
        
        if len(reports) < 5:
            return regressions
        
        # 比较最近和之前的性能
        recent_reports = reports[-5:]
        earlier_reports = reports[-10:-5] if len(reports) >= 10 else []
        
        if earlier_reports:
            recent_avg = statistics.mean([r.overall_performance_score for r in recent_reports])
            earlier_avg = statistics.mean([r.overall_performance_score for r in earlier_reports])
            
            if recent_avg < earlier_avg - 10:  # 性能下降超过10分
                regressions.append(f"Performance regression detected: {earlier_avg:.1f} → {recent_avg:.1f}")
        
        return regressions
        
    def _analyze_optimization_impact(self, reports: List[PerformanceReport]) -> Dict:
        """分析优化影响"""
        optimization_impact = defaultdict(list)
        
        for report in reports:
            for optimization in report.optimization_applied:
                optimization_impact[optimization].append(report.overall_performance_score)
        
        # 计算每种优化的平均效果
        impact_summary = {}
        for optimization, scores in optimization_impact.items():
            if scores:
                impact_summary[optimization] = {
                    'avg_score': statistics.mean(scores),
                    'usage_count': len(scores),
                    'effectiveness': 'high' if statistics.mean(scores) > 80 else 'medium' if statistics.mean(scores) > 60 else 'low'
                }
        
        return impact_summary
        
    def _get_common_performance_issues(self, reports: List[PerformanceReport]) -> List[str]:
        """获取常见性能问题"""
        all_issues = []
        for report in reports:
            all_issues.extend(report.performance_issues)
        
        issue_counts = defaultdict(int)
        for issue in all_issues:
            issue_counts[issue] += 1
        
        # 返回出现次数最多的前5个问题
        return [issue for issue, count in sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:5]]
        
    def _get_top_optimization_suggestions(self, reports: List[PerformanceReport]) -> List[str]:
        """获取热门优化建议"""
        all_suggestions = []
        for report in reports:
            all_suggestions.extend(report.optimization_suggestions)
        
        suggestion_counts = defaultdict(int)
        for suggestion in all_suggestions:
            suggestion_counts[suggestion] += 1
        
        # 返回出现次数最多的前5个建议
        return [suggestion for suggestion, count in sorted(suggestion_counts.items(), key=lambda x: x[1], reverse=True)[:5]]
        
    def _get_strategy_usage_stats(self, reports: List[PerformanceReport]) -> Dict:
        """获取策略使用统计"""
        strategy_counts = defaultdict(int)
        for report in reports:
            strategy_counts[report.parsing_strategy] += 1
        
        total_reports = len(reports)
        return {
            strategy: {
                'count': count,
                'percentage': (count / total_reports) * 100
            }
            for strategy, count in strategy_counts.items()
        }
        
    def _convert_to_csv(self, export_data: Dict) -> str:
        """转换为CSV格式"""
        # 简化的CSV转换实现
        csv_lines = []
        
        # CSV头部
        headers = [
            'timestamp', 'violation_count', 'violations_per_second', 
            'ui_response_time_ms', 'memory_usage_mb', 'performance_score',
            'parsing_strategy', 'rendering_mode'
        ]
        csv_lines.append(','.join(headers))
        
        # 数据行
        for report in export_data['reports']:
            row = [
                str(report.get('timestamp', '')),
                str(report.get('violation_count', '')),
                str(report.get('violations_per_second', '')),
                str(report.get('avg_ui_response_time_ms', '')),
                str(report.get('peak_memory_usage_mb', '')),
                str(report.get('overall_performance_score', '')),
                str(report.get('parsing_strategy', '')),
                str(report.get('rendering_mode', ''))
            ]
            csv_lines.append(','.join(row))
        
        return '\n'.join(csv_lines)