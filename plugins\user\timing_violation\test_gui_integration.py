#!/usr/bin/env python3
"""
测试GUI集成功能

模拟GUI环境测试Web展示集成功能。
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

def test_gui_web_integration():
    """测试GUI Web集成功能"""
    print("🧪 测试GUI Web集成功能")
    print("=" * 50)
    
    try:
        # 1. 测试导入
        print("1. 测试模块导入...")
        from gui_web_launcher import launch_web_display_for_gui
        from web_display_integration import get_web_display_integration
        print("✅ 模块导入成功")
        
        # 2. 测试数据生成
        print("\n2. 测试数据生成...")
        from gui_web_launcher import generate_web_data_for_gui
        
        # 模拟一些违例数据
        mock_violations = [
            {
                'id': 1,
                'num': 1,
                'hier': 'test.signal.path',
                'time_fs': 1000000,
                'check_info': 'Test violation',
                'status': 'confirmed',
                'confirmer': 'test_user',
                'result': 'pass',
                'reason': 'test reason',
                'confirmed_at': '2025-08-07',
                'corner': 'test_corner',
                'case': 'test_case',
                'source': 'gui_test'
            }
        ]
        
        success = generate_web_data_for_gui(mock_violations)
        if success:
            print("✅ 数据生成成功")
        else:
            print("❌ 数据生成失败")
            return False
        
        # 3. 测试文件生成
        print("\n3. 测试生成的文件...")
        web_dir = Path("../../../VIOLATION_CHECK/web_display")
        required_files = [
            "data/violations.json",
            "data/index.json",
            "standalone_test.html"
        ]
        
        all_files_exist = True
        for file_path in required_files:
            full_path = web_dir / file_path
            if full_path.exists():
                print(f"✅ {file_path} 存在")
            else:
                print(f"❌ {file_path} 不存在")
                all_files_exist = False
        
        if not all_files_exist:
            return False
        
        # 4. 测试Web集成管理器
        print("\n4. 测试Web集成管理器...")
        integration = get_web_display_integration()
        
        # 测试状态获取
        status = integration.get_server_status()
        print(f"✅ 服务器状态: {status}")
        
        print("\n🎉 所有测试通过！GUI Web集成功能正常。")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


def test_main_window_methods():
    """测试主窗口方法（模拟）"""
    print("\n🧪 测试主窗口方法")
    print("=" * 50)
    
    try:
        # 模拟主窗口类
        class MockMainWindow:
            def __init__(self):
                self.data_model = MockDataModel()
            
            def get_all_violations(self):
                """模拟获取所有违例数据"""
                return [
                    {
                        'id': 1,
                        'num': 1,
                        'hier': 'mock.test.signal',
                        'time_fs': 2000000,
                        'time_display': '2000000 FS',
                        'check_info': 'Mock violation for testing',
                        'status': 'confirmed',
                        'confirmer': 'mock_user',
                        'result': 'pass',
                        'reason': 'mock test',
                        'confirmed_at': '2025-08-07',
                        'corner': 'mock_corner',
                        'case_name': 'mock_case'
                    }
                ]
        
        class MockDataModel:
            def get_all_violations(self):
                return [
                    {
                        'id': 1,
                        'num': 1,
                        'hier': 'mock.test.signal',
                        'time_fs': 2000000,
                        'time_display': '2000000 FS',
                        'check_info': 'Mock violation for testing',
                        'status': 'confirmed',
                        'confirmer': 'mock_user',
                        'result': 'pass',
                        'reason': 'mock test',
                        'confirmed_at': '2025-08-07',
                        'corner': 'mock_corner',
                        'case_name': 'mock_case'
                    }
                ]
        
        # 测试模拟主窗口
        mock_window = MockMainWindow()
        violations = mock_window.get_all_violations()
        
        if violations and len(violations) > 0:
            print(f"✅ 模拟主窗口数据获取成功: {len(violations)} 条违例")
            print(f"   示例数据: {violations[0]['hier']}")
        else:
            print("❌ 模拟主窗口数据获取失败")
            return False
        
        # 测试Web集成
        from web_display_integration import get_web_display_integration
        integration = get_web_display_integration(mock_window)
        
        print("✅ Web集成管理器创建成功")
        
        print("\n🎉 主窗口方法测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 主窗口方法测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 GUI Web集成测试套件")
    print("=" * 60)
    
    success1 = test_gui_web_integration()
    success2 = test_main_window_methods()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    if success1 and success2:
        print("🎉 所有测试通过！GUI Web集成功能完全正常。")
        print("\n💡 使用说明:")
        print("   1. 在GUI中点击'网页显示'按钮")
        print("   2. 系统会自动生成数据并启动Web服务器")
        print("   3. 浏览器会自动打开显示违例数据")
        sys.exit(0)
    else:
        print("❌ 部分测试失败，请检查相关功能。")
        sys.exit(1)