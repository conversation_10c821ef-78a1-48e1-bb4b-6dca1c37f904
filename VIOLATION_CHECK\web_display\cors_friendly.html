<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时序违例数据展示 - CORS友好版</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            margin: 20px;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
        }
        .solution {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            margin: 20px;
            border-radius: 4px;
            border: 1px solid #c3e6cb;
        }
        .command {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #495057;
        }
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
        .button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>时序违例数据展示</h1>
            <p>CORS友好版 - 无需Web服务器</p>
        </div>
        
        <div class="warning">
            <h3>⚠️ CORS限制检测</h3>
            <p>检测到浏览器CORS策略阻止了数据加载。这是因为直接打开HTML文件（file://协议）时，浏览器不允许JavaScript加载本地JSON文件。</p>
        </div>
        
        <div class="solution">
            <h3>✅ 解决方案</h3>
            <p>请使用以下任一方法来正常查看数据：</p>
            
            <h4>方法1：启动Web服务器（推荐）</h4>
            <p>在命令行中执行：</p>
            <div class="command">python plugins/user/timing_violation/start_web_server.py</div>
            <p>然后访问：<a href="http://localhost:8000/standalone_test.html" target="_blank">http://localhost:8000/standalone_test.html</a></p>
            
            <h4>方法2：使用快速启动脚本</h4>
            <div class="command">python plugins/user/timing_violation/quick_start.py</div>
            
            <h4>方法3：在GUI中重新点击"网页显示"按钮</h4>
            <p>系统会自动启动Web服务器并打开浏览器。</p>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="file-status">检查中...</div>
                <div class="stat-label">数据文件状态</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="server-status">检查中...</div>
                <div class="stat-label">服务器状态</div>
            </div>
        </div>
        
        <div style="text-align: center; padding: 20px;">
            <a href="http://localhost:8000/standalone_test.html" class="button success" target="_blank">
                尝试访问Web服务器版本
            </a>
            <button class="button" onclick="checkDataFiles()">
                检查数据文件
            </button>
            <button class="button" onclick="showInstructions()">
                显示详细说明
            </button>
        </div>
        
        <div id="instructions" style="display: none; margin: 20px; padding: 15px; background: #e9ecef; border-radius: 4px;">
            <h3>详细说明</h3>
            <p><strong>为什么会出现CORS错误？</strong></p>
            <p>当您直接双击HTML文件打开时，浏览器使用file://协议。出于安全考虑，现代浏览器不允许file://协议的页面通过JavaScript的fetch API加载本地文件。</p>
            
            <p><strong>Web服务器的作用</strong></p>
            <p>Web服务器提供http://协议访问，绕过了CORS限制，让JavaScript可以正常加载JSON数据文件。</p>
            
            <p><strong>如何启动Web服务器？</strong></p>
            <ol>
                <li>打开命令行（CMD或PowerShell）</li>
                <li>导航到项目目录</li>
                <li>执行：<code>python plugins/user/timing_violation/start_web_server.py</code></li>
                <li>浏览器会自动打开，或手动访问显示的地址</li>
            </ol>
        </div>
    </div>

    <script>
        function checkDataFiles() {
            const fileStatus = document.getElementById('file-status');
            
            // 检查数据文件是否存在（通过尝试加载）
            const filesToCheck = [
                'data/violations.json',
                'data/index.json'
            ];
            
            let existingFiles = 0;
            let totalFiles = filesToCheck.length;
            
            filesToCheck.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            existingFiles++;
                        }
                        updateFileStatus(existingFiles, totalFiles);
                    })
                    .catch(() => {
                        updateFileStatus(existingFiles, totalFiles);
                    });
            });
        }
        
        function updateFileStatus(existing, total) {
            const fileStatus = document.getElementById('file-status');
            if (existing === total) {
                fileStatus.textContent = '✅ 完整';
                fileStatus.style.color = '#28a745';
            } else {
                fileStatus.textContent = `${existing}/${total}`;
                fileStatus.style.color = '#dc3545';
            }
        }
        
        function checkServerStatus() {
            const serverStatus = document.getElementById('server-status');
            
            // 检查常见端口的服务器状态
            const portsToCheck = [8000, 8001, 8002, 8003, 8004, 8005];
            let serverFound = false;
            
            portsToCheck.forEach(port => {
                fetch(`http://localhost:${port}/data/index.json`)
                    .then(response => {
                        if (response.ok && !serverFound) {
                            serverFound = true;
                            serverStatus.textContent = `✅ 端口${port}`;
                            serverStatus.style.color = '#28a745';
                            
                            // 添加访问链接
                            const linkDiv = document.createElement('div');
                            linkDiv.innerHTML = `<a href="http://localhost:${port}/standalone_test.html" target="_blank" class="button success">访问服务器</a>`;
                            document.querySelector('.stats').appendChild(linkDiv);
                        }
                    })
                    .catch(() => {
                        if (!serverFound) {
                            serverStatus.textContent = '❌ 未运行';
                            serverStatus.style.color = '#dc3545';
                        }
                    });
            });
        }
        
        function showInstructions() {
            const instructions = document.getElementById('instructions');
            instructions.style.display = instructions.style.display === 'none' ? 'block' : 'none';
        }
        
        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', function() {
            checkDataFiles();
            checkServerStatus();
        });
    </script>
</body>
</html>