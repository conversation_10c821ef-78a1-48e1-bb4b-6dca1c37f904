# 时序违例网页展示 - 快速启动指南

## 🎯 概述

本指南提供了时序违例网页展示功能的快速启动方法。系统已经过优化，支持数据库优先的数据组织策略，能够高效地按corner和case_name分组展示确认的违例数据。

## 🚀 一键启动

### 方法1：完整启动（推荐新用户）
```bash
# 自动生成数据并启动Web服务器
python plugins/user/timing_violation/quick_start.py
```

### 方法2：快速启动（数据已存在）
```bash
# 跳过数据生成，直接启动服务器
python plugins/user/timing_violation/quick_start.py --skip-generate
```

### 方法3：自定义端口
```bash
# 使用指定端口启动
python plugins/user/timing_violation/quick_start.py --port 8080
```

## 📋 分步操作

### 步骤1：环境检查
```bash
# 检查环境和数据文件状态
python plugins/user/timing_violation/quick_start.py --check-only
```

### 步骤2：生成数据
```bash
# 使用优化的数据生成脚本
python plugins/user/timing_violation/generate_optimized_web_data.py
```

### 步骤3：启动服务器
```bash
# 启动Web服务器
python plugins/user/timing_violation/start_web_server.py
```

## 🌐 访问地址

启动成功后，可以通过以下地址访问：

- **主页面**: http://localhost:8000
- **独立测试页面**: http://localhost:8000/standalone_test.html ⭐ **推荐**
- **数据加载测试**: http://localhost:8000/test_data_loading.html
- **简单测试**: http://localhost:8000/simple_test.html

## 📊 功能特性

### 数据组织优化
- ✅ **数据库优先策略** - 优先从SQLite数据库读取已确认的违例数据
- ✅ **智能数据分组** - 按corner和case_name进行高效分组
- ✅ **Excel备选方案** - 当数据库不可用时自动回退到Excel文件解析
- ✅ **数据验证** - 自动清理和标准化corner/case名称

### 网页界面功能
- 🔍 **多级过滤** - 支持按Corner、Case、状态进行过滤
- 📈 **实时统计** - 显示总违例数、确认数、Corner数、Case数
- 📱 **响应式设计** - 支持桌面和移动设备
- ⚡ **高性能展示** - 支持大数据集的虚拟滚动

### 数据源支持
- 🗄️ **SQLite数据库** - 主要数据源，支持复杂查询和关联
- 📊 **Excel文件** - 备选数据源，支持标准导出格式
- 🔄 **智能切换** - 自动检测可用数据源并选择最优策略

## 📁 生成的文件结构

```
VIOLATION_CHECK/web_display/
├── index.html                          # 主页面
├── standalone_test.html                 # 独立测试页面 ⭐
├── test_data_loading.html              # 数据加载测试页面
├── simple_test.html                    # 简单测试页面
├── css/                                # 样式文件
├── js/                                 # JavaScript文件
└── data/                               # 数据文件
    ├── violations.json                 # 统一违例数据 ⭐
    ├── index.json                      # 元数据和摘要
    ├── statistics.json                 # 详细统计
    ├── pagination_manifest.json        # 分页配置
    ├── corners/                        # Corner数据
    └── violations/                     # 分页违例数据
```

## 🔧 故障排除

### 问题1：数据加载失败
**症状**: 网页显示"加载数据失败"
**解决方案**:
```bash
# 1. 检查数据文件
python plugins/user/timing_violation/quick_start.py --check-only

# 2. 重新生成数据
python plugins/user/timing_violation/generate_optimized_web_data.py

# 3. 使用独立测试页面
# 访问: http://localhost:8000/standalone_test.html
```

### 问题2：端口被占用
**症状**: "端口已被占用"错误
**解决方案**:
```bash
# 使用其他端口
python plugins/user/timing_violation/quick_start.py --port 8080
```

### 问题3：CORS错误
**症状**: 浏览器控制台显示CORS错误
**解决方案**:
```bash
# 使用内置的Web服务器而不是直接打开HTML文件
python plugins/user/timing_violation/start_web_server.py
```

### 问题4：数据库连接失败
**症状**: 日志显示"Database connection failed"
**解决方案**:
```bash
# 检查数据库文件
ls -la VIOLATION_CHECK/timing_violations.db

# 使用Excel文件作为备选数据源
# 系统会自动回退到Excel文件解析
```

## 📈 性能优化

### 大数据集处理
- **自动分页**: 超过1000条记录自动启用分页
- **数据压缩**: 大文件自动压缩存储
- **虚拟滚动**: 前端虚拟滚动支持大量数据展示
- **智能缓存**: 客户端智能缓存减少重复加载

### 内存优化
- **流式处理**: 大数据集使用流式处理避免内存溢出
- **垃圾回收**: 自动垃圾回收机制
- **批处理**: 智能批处理大小调整

## 🎨 界面预览

### 主要功能区域
1. **统计卡片** - 显示总体数据统计
2. **过滤器** - Corner、Case、状态过滤
3. **数据表格** - 详细的违例信息展示
4. **分页控制** - 大数据集的分页导航

### 数据字段
- **序号** - 违例编号
- **层级路径** - 信号层级路径
- **时间** - 违例时间（fs）
- **检查信息** - 违例检查描述
- **状态** - 确认状态
- **确认人** - 确认人员
- **结果** - 确认结果
- **理由** - 确认理由
- **确认时间** - 确认时间戳
- **Corner** - 工艺角
- **Case** - 测试用例

## 🔄 数据更新

### 自动更新
```bash
# 重新生成最新数据
python plugins/user/timing_violation/generate_optimized_web_data.py

# 刷新浏览器页面即可看到更新
```

### 增量更新
系统支持增量数据更新，新的确认记录会自动包含在下次数据生成中。

## 📞 技术支持

### 日志文件
- `web_data_generation_optimized.log` - 数据生成日志
- 浏览器开发者工具控制台 - 前端错误日志

### 调试模式
```bash
# 启用详细日志
python plugins/user/timing_violation/generate_optimized_web_data.py --verbose
```

### 常用命令
```bash
# 完整重新开始
python plugins/user/timing_violation/quick_start.py

# 仅检查环境
python plugins/user/timing_violation/quick_start.py --check-only

# 自定义端口启动
python plugins/user/timing_violation/quick_start.py --port 8080 --no-browser
```

## 🎯 最佳实践

1. **首次使用** - 使用完整启动命令确保所有组件正常工作
2. **日常使用** - 使用快速启动跳过数据生成步骤
3. **数据更新** - 定期重新生成数据以获取最新的确认记录
4. **性能优化** - 对于大数据集，建议使用数据库模式而非Excel文件
5. **故障排除** - 优先使用独立测试页面进行问题诊断

---

## 🎉 快速开始

**最简单的启动方式**:
```bash
python plugins/user/timing_violation/quick_start.py
```

然后访问: http://localhost:8000/standalone_test.html

享受优化后的时序违例数据展示体验！