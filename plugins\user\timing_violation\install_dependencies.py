#!/usr/bin/env python3
"""
时序违例确认插件依赖安装脚本

检查并安装插件所需的依赖包。
"""

import sys
import subprocess
import importlib


def check_package(package_name, import_name=None):
    """检查包是否已安装
    
    Args:
        package_name: 包名称
        import_name: 导入名称（如果与包名不同）
    
    Returns:
        bool: 是否已安装
    """
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False


def install_package(package_name):
    """安装包
    
    Args:
        package_name: 包名称
    
    Returns:
        bool: 是否安装成功
    """
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False


def main():
    """主函数"""
    print("时序违例确认插件依赖检查")
    print("=" * 40)
    
    # 必需依赖
    required_packages = [
        ("PyQt5", "PyQt5"),
    ]
    
    # 可选依赖
    optional_packages = [
        ("openpyxl", "openpyxl"),
    ]
    
    # 检查必需依赖
    print("\n检查必需依赖:")
    missing_required = []
    
    for package_name, import_name in required_packages:
        if check_package(package_name, import_name):
            print(f"✓ {package_name} - 已安装")
        else:
            print(f"✗ {package_name} - 未安装")
            missing_required.append(package_name)
    
    # 检查可选依赖
    print("\n检查可选依赖:")
    missing_optional = []
    
    for package_name, import_name in optional_packages:
        if check_package(package_name, import_name):
            print(f"✓ {package_name} - 已安装")
        else:
            print(f"✗ {package_name} - 未安装")
            missing_optional.append(package_name)
    
    # 安装缺失的依赖
    if missing_required:
        print(f"\n发现缺失的必需依赖: {', '.join(missing_required)}")
        print("这些依赖是插件正常运行所必需的。")
        
        install_choice = input("是否现在安装？(y/n): ").lower().strip()
        if install_choice in ['y', 'yes']:
            print("\n安装必需依赖...")
            for package in missing_required:
                print(f"安装 {package}...")
                if install_package(package):
                    print(f"✓ {package} 安装成功")
                else:
                    print(f"✗ {package} 安装失败")
        else:
            print("跳过安装。注意：插件可能无法正常工作。")
    
    if missing_optional:
        print(f"\n发现缺失的可选依赖: {', '.join(missing_optional)}")
        print("这些依赖提供额外功能（如Excel导出）。")
        
        install_choice = input("是否现在安装？(y/n): ").lower().strip()
        if install_choice in ['y', 'yes']:
            print("\n安装可选依赖...")
            for package in missing_optional:
                print(f"安装 {package}...")
                if install_package(package):
                    print(f"✓ {package} 安装成功")
                else:
                    print(f"✗ {package} 安装失败")
        else:
            print("跳过安装。部分功能可能不可用。")
    
    # 最终检查
    print("\n" + "=" * 40)
    if not missing_required:
        print("✓ 所有必需依赖已满足，插件可以正常运行。")
    else:
        print("✗ 仍有必需依赖缺失，插件可能无法正常运行。")
    
    if not missing_optional:
        print("✓ 所有可选依赖已满足，所有功能可用。")
    else:
        print("⚠ 部分可选依赖缺失，某些功能可能不可用。")
    
    print("\n依赖检查完成。")


if __name__ == "__main__":
    main()
