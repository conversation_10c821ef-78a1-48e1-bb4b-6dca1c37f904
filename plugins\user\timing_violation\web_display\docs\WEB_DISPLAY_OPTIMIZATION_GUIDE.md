# 时序违例网页展示功能优化指南

## 概述

本指南介绍了时序违例插件中网页展示功能的优化实现，主要通过数据库优先的方式对数据进行规整（按corner、case_name分组），形成确认表格，并将解析VIOLATION_CHECK目录作为备选方案。

## 主要优化特性

### 1. 数据库优先策略

- **主要数据源**: SQLite数据库 (`timing_violations.db`)
- **备选数据源**: Excel文件 (`.xlsx`, `.xls`)
- **智能切换**: 自动检测可用数据源并选择最优策略

### 2. 高效数据组织

- **按corner分组**: 自动提取和组织所有corner信息
- **按case_name分组**: 在每个corner内按case进行二级分组
- **确认状态优先**: 优先显示已确认的违例记录
- **数据验证**: 自动清理和标准化corner/case名称

### 3. 性能优化

- **分页加载**: 大数据集自动分页，避免内存溢出
- **数据缓存**: 智能缓存机制，提高重复查询性能
- **压缩存储**: 大文件自动压缩，减少存储空间
- **虚拟滚动**: 前端虚拟滚动，支持大量数据展示

## 使用方法

### 1. 基本使用

```bash
# 使用优化的数据生成脚本
python generate_optimized_web_data.py

# 指定VIOLATION_CHECK目录
python generate_optimized_web_data.py --violation-check-dir ./VIOLATION_CHECK

# 启用详细日志
python generate_optimized_web_data.py --verbose
```

### 2. 编程接口使用

```python
from web_display.data_exporter import DataExporter
from web_display.utils.data_organizer import ViolationDataOrganizer

# 创建数据导出器（数据库优先）
exporter = DataExporter(
    violation_check_dir="VIOLATION_CHECK",
    enable_performance_monitoring=True
)

# 导出所有数据
success = exporter.export_all_data()

# 使用数据组织器
organizer = ViolationDataOrganizer()
organized_data = organizer.organize_violations_by_corner_case(violations)
```

### 3. 数据源配置

#### 数据库优先模式（推荐）

```python
# 数据库文件位置
database_path = "VIOLATION_CHECK/timing_violations.db"

# 数据库表结构
# - timing_violations: 主违例表
# - confirmation_records: 确认记录表（可选）
```

#### Excel文件备选模式

```python
# Excel文件位置
excel_files = "VIOLATION_CHECK/*.xlsx"

# 支持的Excel格式
# - 标准导出格式（从GUI导出）
# - 自定义格式（需要包含corner和case信息）
```

## 数据组织结构

### 1. 数据库查询优化

```sql
-- 优化的查询语句（双表结构）
SELECT 
    v.num, v.hier, v.time_ns, v.check_info,
    v.corner, v.case_name,
    c.status, c.confirmer, c.result, c.reason, c.confirmed_at
FROM timing_violations v
INNER JOIN confirmation_records c ON v.id = c.violation_id
WHERE c.status IN ('confirmed', 'Confirmed')
ORDER BY v.corner, v.case_name, v.num;

-- 优化的查询语句（单表结构）
SELECT *
FROM timing_violations
WHERE status LIKE '%confirmed%'
ORDER BY corner, case_name, num;
```

### 2. 数据组织结构

```json
{
  "corners": {
    "corner1": {
      "name": "corner1",
      "cases": {
        "case1": {
          "name": "case1",
          "violations": [...],
          "total_violations": 100,
          "confirmed_violations": 85
        }
      },
      "total_violations": 500,
      "confirmed_violations": 420
    }
  },
  "cases": {
    "case1": {
      "name": "case1",
      "total_violations": 200,
      "confirmed_violations": 180,
      "corners": ["corner1", "corner2"]
    }
  },
  "corner_case_mapping": {
    "corner1": ["case1", "case2"],
    "corner2": ["case1", "case3"]
  }
}
```

### 3. Web界面数据结构

```javascript
// 前端数据结构
{
  metadata: {
    total_violations: 1000,
    total_corners: 5,
    total_cases: 20,
    generated_at: "2024-01-01T12:00:00Z"
  },
  corners: ["corner1", "corner2", ...],
  cases: ["case1", "case2", ...],
  violations: [
    {
      num: 1,
      hier: "path/to/signal",
      time_ns: 1000000,
      check_info: "setup check",
      status: "confirmed",
      confirmer: "user1",
      result: "waived",
      reason: "design intent",
      confirmed_at: "2024-01-01",
      corner: "corner1",
      case: "case1"
    }
  ]
}
```

## 性能优化配置

### 1. 大数据集处理

```python
# 自动分页配置
max_records_per_file = 1000  # 小数据集
max_records_per_file = 2000  # 中等数据集
max_records_per_file = 5000  # 大数据集
max_records_per_file = 10000 # 超大数据集

# 压缩配置
use_compression = total_violations > 5000
```

### 2. 缓存策略

```javascript
// 前端缓存配置
cacheTimeout: 300000, // 5分钟
maxCacheSize: 100,    // 最大缓存条目数
virtualScrollBuffer: 50, // 虚拟滚动缓冲区
```

### 3. 内存优化

```python
# Python内存优化
gc_interval = 5000  # 垃圾回收间隔
batch_size = 1000   # 批处理大小
streaming_threshold = 10000  # 流式处理阈值
```

## 故障排除

### 1. 数据库连接问题

```bash
# 检查数据库文件
ls -la VIOLATION_CHECK/timing_violations.db

# 测试数据库连接
python -c "
from web_display.parsers.database_reader import DatabaseReader
reader = DatabaseReader('VIOLATION_CHECK/timing_violations.db')
print('Connection test:', reader.test_connection())
"
```

### 2. Excel文件解析问题

```bash
# 检查Excel文件
ls -la VIOLATION_CHECK/*.xlsx

# 测试Excel解析
python -c "
from web_display.parsers.excel_parser import ExcelParser
parser = ExcelParser()
violations = parser.parse_directory('VIOLATION_CHECK')
print(f'Parsed {len(violations)} violations')
"
```

### 3. 性能问题

```bash
# 启用性能监控
python generate_optimized_web_data.py --verbose

# 检查生成的性能报告
cat web_data_generation_optimized.log | grep "性能指标"
```

### 4. 常见错误解决

#### 数据库锁定错误
```bash
# 解决方案：关闭其他访问数据库的程序
lsof VIOLATION_CHECK/timing_violations.db
```

#### 内存不足错误
```bash
# 解决方案：调整批处理大小
export BATCH_SIZE=500
python generate_optimized_web_data.py
```

#### 文件权限错误
```bash
# 解决方案：修复文件权限
chmod -R 755 VIOLATION_CHECK/web_display/
```

## 最佳实践

### 1. 数据准备

- 确保数据库文件完整且未损坏
- 定期备份数据库文件
- 保持Excel文件格式一致性

### 2. 性能优化

- 对于大数据集（>10,000条记录），使用数据库模式
- 定期清理缓存文件
- 监控内存使用情况

### 3. 维护建议

- 定期更新web界面数据
- 监控数据生成日志
- 备份重要的配置文件

## 技术架构

### 1. 数据流

```
数据库/Excel文件 → 数据组织器 → JSON文件 → Web界面
     ↓              ↓           ↓         ↓
  原始数据      corner/case   分页数据   用户界面
                  分组        压缩存储    交互过滤
```

### 2. 组件关系

```
DataExporter (主导出器)
├── DatabaseReader (数据库读取)
├── ExcelParser (Excel解析)
├── ViolationDataOrganizer (数据组织)
└── FileUtils (文件工具)

Web界面
├── ViolationDataManager (数据管理)
├── 过滤器组件
├── 数据表组件
└── 统计组件
```

### 3. 扩展性

- 支持新的数据源类型
- 可配置的组织策略
- 插件化的过滤器
- 自定义的统计指标

## 更新日志

### v1.0.0 (优化版本)
- 实现数据库优先策略
- 添加高级数据组织器
- 优化大数据集处理
- 改进错误处理和恢复
- 增强性能监控

### 未来计划
- 支持实时数据更新
- 添加更多统计图表
- 实现数据导出功能
- 支持多用户协作