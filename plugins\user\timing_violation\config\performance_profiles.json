{"small_dataset": {"name": "small_dataset", "description": "小数据集优化配置 (< 2,000个违例)", "violation_count_range": [0, 2000], "parser_config": {"parser_type": "standard_async", "batch_size": 500, "use_streaming": false, "progress_interval": 1000, "gc_interval": 5000, "timeout_seconds": 30}, "display_config": {"display_mode": "standard_table", "use_pagination": false, "page_size": 1000, "virtual_scrolling": false, "lazy_loading": false, "row_height": 35, "column_auto_resize": true}, "memory_config": {"memory_limit_mb": 100, "enable_memory_monitoring": false, "aggressive_gc": false, "cache_size": 1000, "preload_data": true}, "ui_config": {"ui_update_interval": 100, "show_progress_bar": true, "enable_animations": true, "responsive_ui": true, "background_processing": false}, "optimization_config": {"priority": "user_experience", "enable_caching": true, "prefetch_data": false, "optimize_for": "responsiveness", "fallback_enabled": true}, "created_time": 1753855101.871588, "last_used_time": 0.0, "usage_count": 0, "performance_score": 0.0}, "medium_dataset": {"name": "medium_dataset", "description": "中等数据集优化配置 (2,000-20,000个违例)", "violation_count_range": [2000, 20000], "parser_config": {"parser_type": "high_performance_async", "batch_size": 2000, "use_streaming": false, "progress_interval": 5000, "gc_interval": 10000, "timeout_seconds": 60}, "display_config": {"display_mode": "high_performance_table", "use_pagination": true, "page_size": 200, "virtual_scrolling": true, "lazy_loading": true, "row_height": 35, "column_auto_resize": false}, "memory_config": {"memory_limit_mb": 300, "enable_memory_monitoring": true, "aggressive_gc": false, "cache_size": 5000, "preload_data": false}, "ui_config": {"ui_update_interval": 200, "show_progress_bar": true, "enable_animations": false, "responsive_ui": true, "background_processing": true}, "optimization_config": {"priority": "balanced", "enable_caching": true, "prefetch_data": true, "optimize_for": "balanced", "fallback_enabled": true}, "created_time": 1753855101.871588, "last_used_time": 0.0, "usage_count": 0, "performance_score": 0.0}, "large_dataset": {"name": "large_dataset", "description": "大数据集优化配置 (20,000-50,000个违例)", "violation_count_range": [20000, 50000], "parser_config": {"parser_type": "high_performance_streaming", "batch_size": 5000, "use_streaming": true, "progress_interval": 10000, "gc_interval": 20000, "timeout_seconds": 120, "chunk_size": 10000}, "display_config": {"display_mode": "virtual_table_with_lazy_loading", "use_pagination": true, "page_size": 100, "virtual_scrolling": true, "lazy_loading": true, "row_height": 35, "column_auto_resize": false}, "memory_config": {"memory_limit_mb": 500, "enable_memory_monitoring": true, "aggressive_gc": true, "cache_size": 2000, "preload_data": false}, "ui_config": {"ui_update_interval": 500, "show_progress_bar": true, "enable_animations": false, "responsive_ui": false, "background_processing": true}, "optimization_config": {"priority": "memory_efficiency", "enable_caching": false, "prefetch_data": false, "optimize_for": "memory", "fallback_enabled": true}, "created_time": 1753855101.871588, "last_used_time": 0.0, "usage_count": 0, "performance_score": 0.0}, "very_large_dataset": {"name": "very_large_dataset", "description": "超大数据集优化配置 (> 50,000个违例)", "violation_count_range": [50000, Infinity], "parser_config": {"parser_type": "streaming_with_chunking", "batch_size": 10000, "use_streaming": true, "progress_interval": 25000, "gc_interval": 50000, "timeout_seconds": 300, "chunk_size": 25000}, "display_config": {"display_mode": "paginated_virtual_table", "use_pagination": true, "page_size": 50, "virtual_scrolling": true, "lazy_loading": true, "row_height": 35, "column_auto_resize": false}, "memory_config": {"memory_limit_mb": 800, "enable_memory_monitoring": true, "aggressive_gc": true, "cache_size": 1000, "preload_data": false}, "ui_config": {"ui_update_interval": 1000, "show_progress_bar": true, "enable_animations": false, "responsive_ui": false, "background_processing": true}, "optimization_config": {"priority": "stability", "enable_caching": false, "prefetch_data": false, "optimize_for": "stability", "fallback_enabled": true}, "created_time": 1753855101.871588, "last_used_time": 0.0, "usage_count": 0, "performance_score": 0.0}, "balanced": {"name": "balanced", "description": "平衡配置 (适用于大多数情况)", "violation_count_range": [0, Infinity], "parser_config": {"parser_type": "adaptive", "batch_size": 2000, "use_streaming": "auto", "progress_interval": 5000, "gc_interval": 15000, "timeout_seconds": 90}, "display_config": {"display_mode": "adaptive", "use_pagination": "auto", "page_size": 150, "virtual_scrolling": "auto", "lazy_loading": "auto", "row_height": 35, "column_auto_resize": "auto"}, "memory_config": {"memory_limit_mb": 400, "enable_memory_monitoring": true, "aggressive_gc": "auto", "cache_size": 3000, "preload_data": "auto"}, "ui_config": {"ui_update_interval": 300, "show_progress_bar": true, "enable_animations": "auto", "responsive_ui": true, "background_processing": "auto"}, "optimization_config": {"priority": "balanced", "enable_caching": true, "prefetch_data": "auto", "optimize_for": "balanced", "fallback_enabled": true}, "created_time": 1753855101.871588, "last_used_time": 0.0, "usage_count": 0, "performance_score": 0.0}, "fast": {"name": "fast", "description": "快速配置 (优先处理速度)", "violation_count_range": [0, Infinity], "parser_config": {"parser_type": "high_performance_async", "batch_size": 5000, "use_streaming": false, "progress_interval": 10000, "gc_interval": 20000, "timeout_seconds": 60}, "display_config": {"display_mode": "high_performance_table", "use_pagination": true, "page_size": 300, "virtual_scrolling": false, "lazy_loading": false, "row_height": 30, "column_auto_resize": false}, "memory_config": {"memory_limit_mb": 800, "enable_memory_monitoring": false, "aggressive_gc": false, "cache_size": 10000, "preload_data": true}, "ui_config": {"ui_update_interval": 50, "show_progress_bar": false, "enable_animations": false, "responsive_ui": false, "background_processing": false}, "optimization_config": {"priority": "speed", "enable_caching": true, "prefetch_data": true, "optimize_for": "speed", "fallback_enabled": false}, "created_time": 1753855101.871588, "last_used_time": 0.0, "usage_count": 0, "performance_score": 0.0}, "memory_efficient": {"name": "memory_efficient", "description": "内存优化配置 (最小内存使用)", "violation_count_range": [0, Infinity], "parser_config": {"parser_type": "memory_efficient_streaming", "batch_size": 500, "use_streaming": true, "progress_interval": 2000, "gc_interval": 5000, "timeout_seconds": 180, "chunk_size": 1000}, "display_config": {"display_mode": "simple_table", "use_pagination": true, "page_size": 25, "virtual_scrolling": true, "lazy_loading": true, "row_height": 30, "column_auto_resize": false}, "memory_config": {"memory_limit_mb": 100, "enable_memory_monitoring": true, "aggressive_gc": true, "cache_size": 100, "preload_data": false}, "ui_config": {"ui_update_interval": 1000, "show_progress_bar": true, "enable_animations": false, "responsive_ui": false, "background_processing": true}, "optimization_config": {"priority": "memory", "enable_caching": false, "prefetch_data": false, "optimize_for": "memory", "fallback_enabled": true}, "created_time": 1753855101.871588, "last_used_time": 0.0, "usage_count": 0, "performance_score": 0.0}}