import gzip
import json
import os

# 手动解压缩所有文件
files_to_decompress = [
    'npg_f1_ffg_page_test_027_test.json.gz',
    'npg_f2_ffg_page_test_027_test.json.gz',
    'npg_f2_ssg_page_test_027_test_page1.json.gz',
    'npg_f2_ssg_page_test_027_test_page2.json.gz',
    'npg_f2_ssg_page_test_027_test_page3.json.gz',
    'npg_f2_ssg_page_test_027_test_page4.json.gz',
    'npg_f2_ssg_page_test_027_test_page5.json.gz',
    'npg_f2_ssg_page_test_027_test_page6.json.gz'
]

violations_dir = 'data/violations'

for gz_filename in files_to_decompress:
    gz_path = os.path.join(violations_dir, gz_filename)
    json_path = os.path.join(violations_dir, gz_filename.replace('.gz', ''))
    
    if os.path.exists(gz_path):
        try:
            with gzip.open(gz_path, 'rt', encoding='utf-8') as f_in:
                data = json.load(f_in)
            
            with open(json_path, 'w', encoding='utf-8') as f_out:
                json.dump(data, f_out, indent=2, ensure_ascii=False)
            
            print(f'✅ 解压缩成功: {gz_filename}')
        except Exception as e:
            print(f'❌ 解压缩失败 {gz_filename}: {e}')
    else:
        print(f'⚠️ 文件不存在: {gz_path}')

# 更新 pagination_manifest.json
manifest_path = 'data/pagination_manifest.json'
if os.path.exists(manifest_path):
    try:
        with open(manifest_path, 'r', encoding='utf-8') as f:
            manifest = json.load(f)
        
        # 更新压缩标志
        manifest['compressed'] = False
        
        # 更新文件名，移除.gz后缀
        for corner_case, info in manifest.get('corner_cases', {}).items():
            if 'files' in info:
                info['files'] = [f.replace('.gz', '') for f in info['files']]
        
        # 保存更新后的manifest
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2, ensure_ascii=False)
        
        print('✅ pagination_manifest.json 已更新')
        
    except Exception as e:
        print(f'❌ 更新manifest失败: {e}')

print('\n解压缩完成！现在刷新浏览器页面查看结果。')
