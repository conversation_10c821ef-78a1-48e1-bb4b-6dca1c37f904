"""
违例处理性能监控器

专门针对违例处理工作流程的实时性能监控，包括违例处理速度、UI响应性和性能基线建立。
"""

import time
import psutil
import threading
from typing import Dict, List, Optional, Callable, Any
from collections import deque, defaultdict
from dataclasses import dataclass, field
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThread
from PyQt5.QtWidgets import QApplication


@dataclass
class ViolationProcessingMetrics:
    """违例处理性能指标"""
    timestamp: float
    violation_count: int
    violations_per_second: float
    ui_response_time_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    processing_stage: str  # 'parsing', 'rendering', 'interaction', 'batch_operation'
    operation_type: str    # 'load', 'navigate', 'filter', 'confirm', 'export'
    elapsed_time: float
    error_count: int = 0
    cache_hit_ratio: float = 0.0
    
    # Violation-specific metrics
    violation_complexity_score: float = 0.0  # Based on data complexity
    batch_size: int = 0
    page_size: int = 0
    
    # UI responsiveness metrics
    scroll_lag_ms: float = 0.0
    click_response_ms: float = 0.0
    render_time_ms: float = 0.0


@dataclass
class PerformanceBaseline:
    """性能基线数据"""
    violation_count_range: tuple  # (min, max) violation count
    avg_violations_per_second: float
    avg_ui_response_time_ms: float
    avg_memory_usage_mb: float
    avg_load_time_seconds: float
    sample_count: int
    last_updated: float
    
    # Performance targets based on requirements
    target_violations_per_second: float = field(default=1000.0)
    target_ui_response_ms: float = field(default=100.0)  # Requirement 2.3
    target_load_time_seconds: float = field(default=1.0)  # Requirement 2.5


class ViolationPerformanceMonitor(QObject):
    """违例处理性能监控器"""
    
    # 信号定义
    performance_metrics_updated = pyqtSignal(dict)  # 性能指标更新
    performance_warning = pyqtSignal(str, dict)     # 性能警告
    baseline_established = pyqtSignal(str, dict)    # 基线建立
    ui_responsiveness_alert = pyqtSignal(float)     # UI响应性警报
    
    def __init__(self):
        super().__init__()
        
        # 性能监控状态
        self.monitoring_active = False
        self.current_operation = None
        self.operation_start_time = 0
        
        # 性能指标历史记录（使用deque限制内存使用）
        self.metrics_history = deque(maxlen=1000)
        self.ui_response_history = deque(maxlen=500)
        
        # 性能基线数据（按违例数量范围分组）
        self.performance_baselines = {
            'small': PerformanceBaseline((0, 2000), 0, 0, 0, 0, 0, 0),      # <2K violations
            'medium': PerformanceBaseline((2000, 20000), 0, 0, 0, 0, 0, 0), # 2K-20K violations  
            'large': PerformanceBaseline((20000, float('inf')), 0, 0, 0, 0, 0, 0)  # >20K violations
        }
        
        # 实时监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._collect_real_time_metrics)
        
        # UI响应性监控
        self.ui_monitor_timer = QTimer()
        self.ui_monitor_timer.timeout.connect(self._monitor_ui_responsiveness)
        
        # 性能阈值配置（基于需求文档）
        self.performance_thresholds = {
            'ui_response_warning_ms': 100,      # Requirement 2.3: <100ms response time
            'ui_response_critical_ms': 200,     # Critical threshold
            'load_time_warning_s': 1.0,         # Requirement 2.5: <1s page load
            'load_time_critical_s': 2.0,        # Critical threshold  
            'violations_per_second_min': 500,   # Minimum acceptable processing speed
            'memory_usage_warning_mb': 800,     # Requirement 3.1: <1GB memory
            'memory_usage_critical_mb': 1000,   # Critical threshold
            'cpu_usage_warning_percent': 80,
            'error_rate_warning_percent': 1.0
        }
        
        # 当前监控会话数据
        self.current_session = {
            'start_time': 0,
            'violation_count': 0,
            'operation_type': '',
            'processing_stage': '',
            'metrics_collected': 0,
            'errors_encountered': 0
        }
        
        # UI交互监控
        self.ui_interaction_tracker = {
            'last_click_time': 0,
            'last_scroll_time': 0,
            'pending_ui_operations': 0,
            'ui_freeze_detected': False
        }
        
        # 线程安全锁
        self._metrics_lock = threading.Lock()
        
    def start_monitoring_session(self, operation_type: str, violation_count: int, 
                                processing_stage: str = 'unknown') -> str:
        """开始性能监控会话
        
        Args:
            operation_type: 操作类型 ('load', 'navigate', 'filter', 'confirm', 'export')
            violation_count: 违例数量
            processing_stage: 处理阶段 ('parsing', 'rendering', 'interaction', 'batch_operation')
            
        Returns:
            str: 会话ID
        """
        session_id = f"{operation_type}_{int(time.time() * 1000)}"
        
        with self._metrics_lock:
            self.monitoring_active = True
            self.current_operation = operation_type
            self.operation_start_time = time.time()
            
            self.current_session = {
                'session_id': session_id,
                'start_time': self.operation_start_time,
                'violation_count': violation_count,
                'operation_type': operation_type,
                'processing_stage': processing_stage,
                'metrics_collected': 0,
                'errors_encountered': 0
            }
        
        # 开始实时监控
        self.monitor_timer.start(100)  # 每100ms收集一次指标
        self.ui_monitor_timer.start(50)  # 每50ms监控UI响应性
        
        print(f"Performance monitoring started for {operation_type} with {violation_count} violations")
        return session_id
        
    def stop_monitoring_session(self) -> Dict:
        """停止性能监控会话并返回会话摘要
        
        Returns:
            Dict: 会话性能摘要
        """
        if not self.monitoring_active:
            return {}
            
        with self._metrics_lock:
            self.monitoring_active = False
            
            # Safely stop timers (may hang in test environments)
            try:
                self.monitor_timer.stop()
            except Exception as e:
                print(f"Warning: Could not stop monitor timer: {e}")
            
            try:
                self.ui_monitor_timer.stop()
            except Exception as e:
                print(f"Warning: Could not stop UI monitor timer: {e}")
            
            session_duration = time.time() - self.operation_start_time
            
            # 计算会话摘要
            session_summary = self._calculate_session_summary(session_duration)
            
            # 更新性能基线
            self._update_performance_baseline(session_summary)
            
            # 检查性能问题
            self._analyze_session_performance(session_summary)
            
        print(f"Performance monitoring stopped. Duration: {session_duration:.3f}s")
        return session_summary
        
    def track_violations_per_second(self, processed_violations: int, elapsed_time: float):
        """跟踪违例处理速度
        
        Args:
            processed_violations: 已处理的违例数量
            elapsed_time: 经过的时间（秒）
        """
        if elapsed_time > 0:
            violations_per_second = processed_violations / elapsed_time
            
            # 检查是否低于最低要求
            if violations_per_second < self.performance_thresholds['violations_per_second_min']:
                self.performance_warning.emit(
                    'low_processing_speed',
                    {
                        'violations_per_second': violations_per_second,
                        'threshold': self.performance_thresholds['violations_per_second_min'],
                        'processed_violations': processed_violations,
                        'elapsed_time': elapsed_time
                    }
                )
                
    def track_ui_response_time(self, operation: str, response_time_ms: float):
        """跟踪UI响应时间
        
        Args:
            operation: 操作类型
            response_time_ms: 响应时间（毫秒）
        """
        with self._metrics_lock:
            self.ui_response_history.append({
                'timestamp': time.time(),
                'operation': operation,
                'response_time_ms': response_time_ms
            })
        
        # 检查响应时间阈值
        if response_time_ms > self.performance_thresholds['ui_response_critical_ms']:
            self.ui_responsiveness_alert.emit(response_time_ms)
            self.performance_warning.emit(
                'ui_response_critical',
                {
                    'operation': operation,
                    'response_time_ms': response_time_ms,
                    'threshold': self.performance_thresholds['ui_response_critical_ms']
                }
            )
        elif response_time_ms > self.performance_thresholds['ui_response_warning_ms']:
            self.performance_warning.emit(
                'ui_response_warning',
                {
                    'operation': operation,
                    'response_time_ms': response_time_ms,
                    'threshold': self.performance_thresholds['ui_response_warning_ms']
                }
            )
            
    def establish_performance_baseline(self, violation_count: int, force_update: bool = False):
        """建立或更新性能基线
        
        Args:
            violation_count: 违例数量
            force_update: 是否强制更新基线
        """
        baseline_category = self._get_baseline_category(violation_count)
        baseline = self.performance_baselines[baseline_category]
        
        # 检查是否需要更新基线
        if baseline.sample_count == 0 or force_update or \
           (time.time() - baseline.last_updated) > 3600:  # 1小时更新一次
            
            # 收集最近的性能数据
            recent_metrics = self._get_recent_metrics_for_violation_count(violation_count)
            
            if recent_metrics:
                updated_baseline = self._calculate_baseline_from_metrics(
                    recent_metrics, violation_count
                )
                
                self.performance_baselines[baseline_category] = updated_baseline
                
                self.baseline_established.emit(
                    baseline_category,
                    {
                        'violation_count_range': updated_baseline.violation_count_range,
                        'avg_violations_per_second': updated_baseline.avg_violations_per_second,
                        'avg_ui_response_time_ms': updated_baseline.avg_ui_response_time_ms,
                        'sample_count': updated_baseline.sample_count
                    }
                )
                
                print(f"Performance baseline established for {baseline_category} "
                      f"({violation_count} violations)")
                      
    def get_performance_baseline(self, violation_count: int) -> PerformanceBaseline:
        """获取指定违例数量的性能基线
        
        Args:
            violation_count: 违例数量
            
        Returns:
            PerformanceBaseline: 性能基线数据
        """
        baseline_category = self._get_baseline_category(violation_count)
        return self.performance_baselines[baseline_category]
        
    def _collect_real_time_metrics(self):
        """收集实时性能指标"""
        if not self.monitoring_active:
            return
            
        try:
            current_time = time.time()
            elapsed_time = current_time - self.operation_start_time
            
            # 收集系统指标
            memory_info = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info().rss / (1024 ** 2)  # MB
            cpu_usage = process.cpu_percent()
            
            # 创建性能指标对象
            metrics = ViolationProcessingMetrics(
                timestamp=current_time,
                violation_count=self.current_session['violation_count'],
                violations_per_second=self._calculate_current_violations_per_second(),
                ui_response_time_ms=self._get_average_ui_response_time(),
                memory_usage_mb=process_memory,
                cpu_usage_percent=cpu_usage,
                processing_stage=self.current_session['processing_stage'],
                operation_type=self.current_session['operation_type'],
                elapsed_time=elapsed_time,
                error_count=self.current_session['errors_encountered']
            )
            
            with self._metrics_lock:
                self.metrics_history.append(metrics)
                self.current_session['metrics_collected'] += 1
            
            # 发出性能指标更新信号
            self.performance_metrics_updated.emit({
                'violations_per_second': metrics.violations_per_second,
                'ui_response_time_ms': metrics.ui_response_time_ms,
                'memory_usage_mb': metrics.memory_usage_mb,
                'cpu_usage_percent': metrics.cpu_usage_percent,
                'elapsed_time': metrics.elapsed_time
            })
            
            # 检查性能阈值
            self._check_performance_thresholds(metrics)
            
        except Exception as e:
            print(f"Error collecting performance metrics: {e}")
            self.current_session['errors_encountered'] += 1
            
    def _monitor_ui_responsiveness(self):
        """监控UI响应性"""
        if not self.monitoring_active:
            return
            
        try:
            app = QApplication.instance()
            if app:
                # 检查事件队列长度（间接指标）
                events_pending = app.hasPendingEvents()
                
                if events_pending and not self.ui_interaction_tracker['ui_freeze_detected']:
                    # 检测可能的UI冻结
                    current_time = time.time()
                    if (current_time - self.ui_interaction_tracker['last_click_time']) > 0.5:
                        self.ui_interaction_tracker['ui_freeze_detected'] = True
                        self.performance_warning.emit(
                            'ui_freeze_detected',
                            {'timestamp': current_time}
                        )
                else:
                    self.ui_interaction_tracker['ui_freeze_detected'] = False
                    
        except Exception as e:
            print(f"Error monitoring UI responsiveness: {e}")
            
    def _calculate_current_violations_per_second(self) -> float:
        """计算当前违例处理速度"""
        if not self.metrics_history:
            return 0.0
            
        # 使用最近的指标计算平均速度
        recent_metrics = list(self.metrics_history)[-10:]  # 最近10个指标
        if len(recent_metrics) < 2:
            return 0.0
            
        total_violations = sum(m.violation_count for m in recent_metrics)
        total_time = recent_metrics[-1].elapsed_time - recent_metrics[0].elapsed_time
        
        return total_violations / total_time if total_time > 0 else 0.0
        
    def _get_average_ui_response_time(self) -> float:
        """获取平均UI响应时间"""
        if not self.ui_response_history:
            return 0.0
            
        recent_responses = list(self.ui_response_history)[-20:]  # 最近20个响应
        if not recent_responses:
            return 0.0
            
        total_time = sum(r['response_time_ms'] for r in recent_responses)
        return total_time / len(recent_responses)
        
    def _get_baseline_category(self, violation_count: int) -> str:
        """根据违例数量获取基线类别"""
        if violation_count < 2000:
            return 'small'
        elif violation_count < 20000:
            return 'medium'
        else:
            return 'large'
            
    def _get_recent_metrics_for_violation_count(self, violation_count: int) -> List[ViolationProcessingMetrics]:
        """获取指定违例数量范围的最近性能指标"""
        category = self._get_baseline_category(violation_count)
        baseline = self.performance_baselines[category]
        min_count, max_count = baseline.violation_count_range
        
        with self._metrics_lock:
            return [
                m for m in self.metrics_history
                if min_count <= m.violation_count < max_count
            ]
            
    def _calculate_baseline_from_metrics(self, metrics: List[ViolationProcessingMetrics], 
                                       violation_count: int) -> PerformanceBaseline:
        """从性能指标计算基线"""
        if not metrics:
            category = self._get_baseline_category(violation_count)
            return self.performance_baselines[category]
            
        avg_violations_per_second = sum(m.violations_per_second for m in metrics) / len(metrics)
        avg_ui_response_time = sum(m.ui_response_time_ms for m in metrics) / len(metrics)
        avg_memory_usage = sum(m.memory_usage_mb for m in metrics) / len(metrics)
        avg_load_time = sum(m.elapsed_time for m in metrics) / len(metrics)
        
        category = self._get_baseline_category(violation_count)
        baseline_range = self.performance_baselines[category].violation_count_range
        
        return PerformanceBaseline(
            violation_count_range=baseline_range,
            avg_violations_per_second=avg_violations_per_second,
            avg_ui_response_time_ms=avg_ui_response_time,
            avg_memory_usage_mb=avg_memory_usage,
            avg_load_time_seconds=avg_load_time,
            sample_count=len(metrics),
            last_updated=time.time()
        )
        
    def _calculate_session_summary(self, session_duration: float) -> Dict:
        """计算会话性能摘要"""
        with self._metrics_lock:
            session_metrics = [
                m for m in self.metrics_history
                if m.timestamp >= self.operation_start_time
            ]
            
        if not session_metrics:
            return {}
            
        return {
            'session_id': self.current_session.get('session_id', ''),
            'operation_type': self.current_session['operation_type'],
            'violation_count': self.current_session['violation_count'],
            'duration_seconds': session_duration,
            'avg_violations_per_second': sum(m.violations_per_second for m in session_metrics) / len(session_metrics),
            'avg_ui_response_time_ms': sum(m.ui_response_time_ms for m in session_metrics) / len(session_metrics),
            'peak_memory_usage_mb': max(m.memory_usage_mb for m in session_metrics),
            'avg_cpu_usage_percent': sum(m.cpu_usage_percent for m in session_metrics) / len(session_metrics),
            'total_errors': self.current_session['errors_encountered'],
            'metrics_collected': len(session_metrics)
        }
        
    def _update_performance_baseline(self, session_summary: Dict):
        """更新性能基线"""
        violation_count = session_summary.get('violation_count', 0)
        if violation_count > 0:
            self.establish_performance_baseline(violation_count)
            
    def _analyze_session_performance(self, session_summary: Dict):
        """分析会话性能并发出警告"""
        # 检查加载时间
        duration = session_summary.get('duration_seconds', 0)
        if duration > self.performance_thresholds['load_time_critical_s']:
            self.performance_warning.emit('load_time_critical', session_summary)
        elif duration > self.performance_thresholds['load_time_warning_s']:
            self.performance_warning.emit('load_time_warning', session_summary)
            
        # 检查内存使用
        peak_memory = session_summary.get('peak_memory_usage_mb', 0)
        if peak_memory > self.performance_thresholds['memory_usage_critical_mb']:
            self.performance_warning.emit('memory_usage_critical', session_summary)
        elif peak_memory > self.performance_thresholds['memory_usage_warning_mb']:
            self.performance_warning.emit('memory_usage_warning', session_summary)
            
        # 检查UI响应时间
        avg_ui_response = session_summary.get('avg_ui_response_time_ms', 0)
        if avg_ui_response > self.performance_thresholds['ui_response_critical_ms']:
            self.performance_warning.emit('ui_response_critical', session_summary)
        elif avg_ui_response > self.performance_thresholds['ui_response_warning_ms']:
            self.performance_warning.emit('ui_response_warning', session_summary)
            
    def _check_performance_thresholds(self, metrics: ViolationProcessingMetrics):
        """检查性能阈值"""
        # 检查内存使用
        if metrics.memory_usage_mb > self.performance_thresholds['memory_usage_critical_mb']:
            self.performance_warning.emit(
                'memory_usage_critical',
                {'memory_usage_mb': metrics.memory_usage_mb}
            )
            
        # 检查CPU使用
        if metrics.cpu_usage_percent > self.performance_thresholds['cpu_usage_warning_percent']:
            self.performance_warning.emit(
                'cpu_usage_high',
                {'cpu_usage_percent': metrics.cpu_usage_percent}
            )
            
    def get_current_performance_summary(self) -> Dict:
        """获取当前性能摘要"""
        if not self.monitoring_active:
            return {}
            
        current_time = time.time()
        session_duration = current_time - self.operation_start_time
        
        return self._calculate_session_summary(session_duration)
        
    def get_performance_history(self, limit: int = 100) -> List[Dict]:
        """获取性能历史记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List[Dict]: 性能历史记录
        """
        with self._metrics_lock:
            recent_metrics = list(self.metrics_history)[-limit:]
            
        return [
            {
                'timestamp': m.timestamp,
                'violation_count': m.violation_count,
                'violations_per_second': m.violations_per_second,
                'ui_response_time_ms': m.ui_response_time_ms,
                'memory_usage_mb': m.memory_usage_mb,
                'cpu_usage_percent': m.cpu_usage_percent,
                'operation_type': m.operation_type,
                'processing_stage': m.processing_stage
            }
            for m in recent_metrics
        ]