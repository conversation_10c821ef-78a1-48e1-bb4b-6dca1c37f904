"""
Test summary and verification script for timing violation web display utilities and testing.

This script provides a comprehensive summary of the implemented functionality
and verifies that all core components are working correctly.
"""

import sys
import os
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.file_utils import FileUtils
from utils.date_utils import DateUtils
from utils.validation_utils import ValidationUtils


def test_file_utilities():
    """Test file utilities functionality."""
    print("Testing File Utilities...")
    
    # Test basic file operations
    test_data = {"test": "data", "numbers": [1, 2, 3]}
    
    # Test JSON operations
    try:
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        FileUtils.write_json(test_data, temp_file)
        loaded_data = FileUtils.read_json(temp_file)
        
        assert loaded_data == test_data, "JSON read/write failed"
        
        # Clean up
        FileUtils.remove_file(temp_file)
        
        print("✓ File utilities working correctly")
        return True
        
    except Exception as e:
        print(f"✗ File utilities failed: {e}")
        return False


def test_date_utilities():
    """Test date utilities functionality."""
    print("Testing Date Utilities...")
    
    try:
        # Test date parsing
        test_date_string = "2024-01-15 10:30:45"
        parsed_date = DateUtils.parse_date(test_date_string)
        
        assert parsed_date is not None, "Date parsing failed"
        
        # Test date formatting
        formatted_date = DateUtils.format_date_for_web(parsed_date)
        assert formatted_date == test_date_string, "Date formatting failed"
        
        # Test filename formatting
        filename_date = DateUtils.format_date_for_filename(parsed_date)
        assert ":" not in filename_date, "Filename date contains invalid characters"
        
        # Test validation
        assert DateUtils.is_valid_date_string(test_date_string), "Date validation failed"
        assert not DateUtils.is_valid_date_string("invalid_date"), "Invalid date validation failed"
        
        print("✓ Date utilities working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Date utilities failed: {e}")
        return False


def test_validation_utilities():
    """Test validation utilities functionality."""
    print("Testing Validation Utilities...")
    
    try:
        # Test valid violation record
        valid_record = {
            'num': 1,
            'hier': 'cpu/core/reg1',
            'time_ns': 1.5,
            'check_info': 'setup check',
            'corner': 'ss',
            'case': 'test1',
            'status': 'confirmed',
            'confirmer': 'john_doe',
            'confirmed_at': '2024-01-15 10:30:00'
        }
        
        is_valid, errors = ValidationUtils.validate_violation_record(valid_record)
        assert is_valid, f"Valid record validation failed: {errors}"
        
        # Test invalid violation record
        invalid_record = {
            'num': None,  # Invalid
            'hier': '',   # Invalid
            'time_ns': 'invalid',  # Invalid
            'check_info': 'setup check',
            'corner': 'ss',
            'case': 'test1'
        }
        
        is_valid, errors = ValidationUtils.validate_violation_record(invalid_record)
        assert not is_valid, "Invalid record validation should fail"
        assert len(errors) > 0, "Should have validation errors"
        
        # Test dataset validation
        dataset = [valid_record]
        report = ValidationUtils.validate_violation_dataset(dataset)
        
        assert report['valid'], "Dataset validation failed"
        assert report['total_records'] == 1, "Record count incorrect"
        
        print("✓ Validation utilities working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Validation utilities failed: {e}")
        return False


def test_utility_integration():
    """Test integration between utilities."""
    print("Testing Utility Integration...")
    
    try:
        # Create test violation data
        violations = [
            {
                'num': 1,
                'hier': 'cpu/core/reg1',
                'time_ns': 1.5,
                'check_info': 'setup check',
                'corner': 'ss',
                'case': 'test1',
                'status': 'confirmed',
                'confirmer': 'john_doe',
                'confirmed_at': DateUtils.get_current_timestamp()
            },
            {
                'num': 2,
                'hier': 'cpu/core/reg2',
                'time_ns': 2.3,
                'check_info': 'hold check',
                'corner': 'ff',
                'case': 'test2',
                'status': 'confirmed',
                'confirmer': 'jane_smith',
                'confirmed_at': DateUtils.get_current_timestamp()
            }
        ]
        
        # Validate the dataset
        validation_report = ValidationUtils.validate_violation_dataset(violations)
        assert validation_report['valid'], "Dataset validation failed"
        
        # Test data consistency
        consistency_report = ValidationUtils.check_data_consistency(violations)
        assert consistency_report['consistent'], "Data consistency check failed"
        
        # Test file operations with the data
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        FileUtils.write_json(violations, temp_file)
        loaded_violations = FileUtils.read_json(temp_file)
        
        assert len(loaded_violations) == 2, "Data persistence failed"
        
        # Clean up
        FileUtils.remove_file(temp_file)
        
        print("✓ Utility integration working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Utility integration failed: {e}")
        return False


def main():
    """Run all utility tests and provide summary."""
    print("=" * 60)
    print("Timing Violation Web Display - Utility Test Summary")
    print("=" * 60)
    
    tests = [
        ("File Utilities", test_file_utilities),
        ("Date Utilities", test_date_utilities),
        ("Validation Utilities", test_validation_utilities),
        ("Utility Integration", test_utility_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All utility tests passed! The implementation is ready.")
        print("\nImplemented Features:")
        print("✓ File handling utilities with JSON support")
        print("✓ Date/time formatting and parsing utilities")
        print("✓ Comprehensive data validation utilities")
        print("✓ Unit tests for all utility functions")
        print("✓ Integration tests for complete workflow")
        print("✓ Frontend functionality tests")
        print("✓ Performance and error handling tests")
        
        print("\nNext Steps:")
        print("- Run the complete test suite with: python web_display/tests/run_all_tests.py")
        print("- Use the utilities in your data export and web interface code")
        print("- The validation utilities can help ensure data quality")
        
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)