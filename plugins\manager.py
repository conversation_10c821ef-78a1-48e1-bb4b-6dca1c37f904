import os
import sys
import json
import importlib
import inspect
import time
from .base import PluginBase, is_admin

# 确保 utils 目录中的模块可用
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils import async_logger, async_task_manager, cache_manager, common_widgets

class PluginManager:
    """插件管理器"""

    CONFIG_FILE = "plugin_config.json"  # 插件配置文件

    def __init__(self, main_window):
        self.main_window = main_window
        self.plugins = {}  # 存储已加载的插件
        self.plugin_menu = None  # 插件菜单
        self.plugin_actions = {}  # 存储插件菜单项
        self.plugin_load_times = {}  # 存储插件加载时间

    def load_plugins(self):
        """加载所有插件"""
        # 获取插件目录
        plugin_dirs = [
            os.path.join(os.path.dirname(__file__), "builtin"),  # 内置插件目录
            os.path.join(os.path.dirname(__file__), "user")      # 用户插件目录
        ]

        # 加载插件配置
        plugin_config = self.load_plugin_config()

        # 使用两阶段加载：先加载轻量级插件，然后异步加载重量级插件
        light_plugins = []  # 轻量级插件
        heavy_plugins = []  # 重量级插件（如AI聊天助手等）
        
        # 重量级插件列表
        heavy_plugin_names = ["AI聊天助手"]

        for plugin_dir in plugin_dirs:
            if not os.path.exists(plugin_dir):
                continue

            # 将插件目录添加到Python路径
            if plugin_dir not in sys.path:
                sys.path.append(plugin_dir)

            # 遍历插件目录中的所有.py文件
            for filename in os.listdir(plugin_dir):
                if filename.endswith(".py") and not filename.startswith("__"):
                    try:
                        # 记录开始加载时间
                        start_time = time.time()
                        
                        # 导入插件模块
                        module_name = filename[:-3]
                        module = importlib.import_module(module_name)

                        # 查找插件类
                        for name, obj in inspect.getmembers(module):
                            if (inspect.isclass(obj) and
                                issubclass(obj, PluginBase) and
                                obj != PluginBase):
                                # 实例化插件
                                plugin = obj()

                                # 应用配置
                                plugin_name = plugin.name
                                if plugin_name in plugin_config:
                                    config = plugin_config[plugin_name]
                                    # 设置管理员控制
                                    if "admin_controlled" in config:
                                        plugin.admin_controlled = config["admin_controlled"]
                                    # 设置默认启用状态
                                    if "default_enabled" in config:
                                        plugin.default_enabled = config["default_enabled"]
                                    # 设置启用状态（如果有权限）
                                    if "enabled" in config and (not plugin.admin_controlled or is_admin()):
                                        plugin._enabled = config["enabled"]

                                # 记录加载时间
                                load_time = time.time() - start_time
                                self.plugin_load_times[plugin_name] = load_time
                                
                                # 将插件添加到相应列表
                                if plugin_name in heavy_plugin_names:
                                    heavy_plugins.append(plugin)
                                else:
                                    light_plugins.append(plugin)
                    except Exception as e:
                        print(f"加载插件 {filename} 失败: {str(e)}")

        # 第一阶段：初始化轻量级插件
        for plugin in light_plugins:
            self.plugins[plugin.name] = plugin
            if plugin.enabled:
                # 记录初始化开始时间
                start_time = time.time()
                
                plugin.initialize(self.main_window)
                
                # 更新加载时间（包括初始化时间）
                init_time = time.time() - start_time
                self.plugin_load_times[plugin.name] += init_time
                
                print(f"成功加载插件: {plugin.name} v{plugin.version} (加载时间: {self.plugin_load_times[plugin.name]:.3f}秒)")
            else:
                print(f"插件已禁用: {plugin.name} v{plugin.version} (加载时间: {self.plugin_load_times[plugin.name]:.3f}秒)")

        # 第二阶段：异步初始化重量级插件
        if heavy_plugins:
            from PyQt5.QtCore import QTimer
            
            def init_heavy_plugins():
                for plugin in heavy_plugins:
                    self.plugins[plugin.name] = plugin
                    if plugin.enabled:
                        # 记录初始化开始时间
                        start_time = time.time()
                        
                        plugin.initialize(self.main_window)
                        
                        # 更新加载时间（包括初始化时间）
                        init_time = time.time() - start_time
                        self.plugin_load_times[plugin.name] += init_time
                        
                        print(f"成功加载插件: {plugin.name} v{plugin.version} (加载时间: {self.plugin_load_times[plugin.name]:.3f}秒)")
                    else:
                        print(f"插件已禁用: {plugin.name} v{plugin.version} (加载时间: {self.plugin_load_times[plugin.name]:.3f}秒)")
            
            # 使用定时器延迟初始化重量级插件
            QTimer.singleShot(500, init_heavy_plugins)

        # 保存插件配置
        self.save_plugin_config()

    def load_plugin_config(self):
        """加载插件配置"""
        try:
            if os.path.exists(self.CONFIG_FILE):
                with open(self.CONFIG_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载插件配置失败: {str(e)}")
        return {}

    def show_admin_info(self):
        from PyQt5.QtWidgets import QMessageBox

        QMessageBox.information(
            self.main_window,
            "管理员模式",
            "当前为普通用户模式。\n"
        )

    def setup_plugin_menu(self):
        """设置插件菜单"""
        from PyQt5.QtWidgets import QMenu, QAction
        from PyQt5.QtGui import QIcon

        # 创建插件菜单
        self.plugin_menu = QMenu("插件(&P)", self.main_window)
        self.main_window.menuBar().addMenu(self.plugin_menu)

        # 添加插件管理器菜单项
        manage_action = QAction("管理插件...", self.plugin_menu)
        manage_action.triggered.connect(self.show_plugin_manager)
        self.plugin_menu.addAction(manage_action)

        # 添加管理员模式菜单项（仅在非管理员模式下显示）
        if not is_admin():
            admin_action = QAction("管理员模式...", self.plugin_menu)
            admin_action.triggered.connect(self.show_admin_info)
            self.plugin_menu.addAction(admin_action)

        self.plugin_menu.addSeparator()

        # 为每个已加载的插件添加菜单项
        for plugin in self.plugins.values():
            action = QAction(plugin.name, self.plugin_menu)
            action.setStatusTip(plugin.description)
            action.setCheckable(True)
            action.setChecked(plugin.enabled)

            # 如果是管理员控制的插件且不是管理员，则禁用菜单项
            if plugin.admin_controlled and not is_admin():
                action.setEnabled(False)
                action.setToolTip("此插件由管理员控制，需要管理员权限才能修改")
            else:
                action.triggered.connect(lambda checked, p=plugin: self.toggle_plugin(p, checked))

            self.plugin_menu.addAction(action)
            self.plugin_actions[plugin.name] = action

    def save_plugin_config(self):
        """保存插件配置"""
        try:
            config = {}
            for name, plugin in self.plugins.items():
                config[name] = {
                    "admin_controlled": plugin.admin_controlled,
                    "default_enabled": plugin.default_enabled,
                    "enabled": plugin.enabled
                }

            with open(self.CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4)
        except Exception as e:
            print(f"保存插件配置失败: {str(e)}")

    def run_as_admin(self):
        """显示管理员模式信息"""
        from PyQt5.QtWidgets import QMessageBox
        import getpass

        current_user = getpass.getuser()
        QMessageBox.information(
            self.main_window,
            "管理员模式信息",
            f"当前用户: {current_user}\n\n"
            "只有用户名为 'admin' 的用户才有管理员权限。\n\n"
            "请使用正确的用户账号登录系统后再运行此程序。"
        )

    def show_plugin_manager(self):
        """显示插件管理器对话框"""
        from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTreeWidget,
                                    QTreeWidgetItem, QPushButton, QCheckBox, QLabel,
                                    QMessageBox)
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QColor

        dialog = QDialog(self.main_window)
        dialog.setWindowTitle("插件管理器")
        dialog.resize(800, 500)

        layout = QVBoxLayout()

        # 添加管理员状态提示
        import getpass
        current_user = getpass.getuser()
        admin_status = QLabel(f"当前用户: {current_user} | 状态: {'管理员模式' if is_admin() else '普通用户模式'}")
        admin_status.setStyleSheet(f"color: {'green' if is_admin() else 'blue'}; font-weight: bold;")
        layout.addWidget(admin_status)

        # 创建插件列表
        tree = QTreeWidget()
        tree.setHeaderLabels(["插件名称", "版本", "描述", "状态", "管理员控制", "默认状态", "加载时间(秒)"])
        tree.setColumnWidth(0, 150)  # 插件名称列宽
        tree.setColumnWidth(1, 80)   # 版本列宽
        tree.setColumnWidth(2, 300)  # 描述列宽
        tree.setColumnWidth(3, 80)   # 状态列宽
        tree.setColumnWidth(4, 80)   # 管理员控制列宽
        tree.setColumnWidth(5, 80)   # 默认状态列宽
        tree.setColumnWidth(6, 80)   # 加载时间列宽

        for plugin in self.plugins.values():
            item = QTreeWidgetItem()
            item.setText(0, plugin.name)
            item.setText(1, plugin.version)
            item.setText(2, plugin.description)

            # 状态列
            status_text = "启用" if plugin.enabled else "禁用"
            item.setText(3, status_text)
            item.setForeground(3, QColor("green" if plugin.enabled else "red"))

            # 管理员控制列
            admin_text = "是" if plugin.admin_controlled else "否"
            item.setText(4, admin_text)

            # 默认状态列
            default_text = "启用" if plugin.default_enabled else "禁用"
            item.setText(5, default_text)
            
            # 加载时间列
            load_time = self.plugin_load_times.get(plugin.name, 0)
            item.setText(6, f"{load_time:.3f}")
            
            # 如果加载时间超过1秒，标记为红色
            if load_time > 1.0:
                item.setForeground(6, QColor("red"))
            elif load_time > 0.5:
                item.setForeground(6, QColor("orange"))
            else:
                item.setForeground(6, QColor("green"))

            # 设置工具提示
            if plugin.admin_controlled and not is_admin():
                item.setToolTip(3, "此插件由管理员控制，需要管理员权限才能修改")

            tree.addTopLevelItem(item)

        layout.addWidget(tree)

        # 添加按钮区域
        button_layout = QHBoxLayout()

        # 只有管理员才能设置管理员控制和默认状态
        if is_admin():
            # 设置管理员控制按钮
            set_admin_btn = QPushButton("设置为管理员控制")
            set_admin_btn.clicked.connect(lambda: self.set_admin_controlled(tree, True))
            button_layout.addWidget(set_admin_btn)

            # 取消管理员控制按钮
            unset_admin_btn = QPushButton("取消管理员控制")
            unset_admin_btn.clicked.connect(lambda: self.set_admin_controlled(tree, False))
            button_layout.addWidget(unset_admin_btn)

            # 设置默认启用按钮
            set_default_enabled_btn = QPushButton("设置默认启用")
            set_default_enabled_btn.clicked.connect(lambda: self.set_default_enabled(tree, True))
            button_layout.addWidget(set_default_enabled_btn)

            # 设置默认禁用按钮
            set_default_disabled_btn = QPushButton("设置默认禁用")
            set_default_disabled_btn.clicked.connect(lambda: self.set_default_enabled(tree, False))
            button_layout.addWidget(set_default_disabled_btn)

        # 启用/禁用按钮（所有用户都可以使用，但受管理员控制限制）
        enable_btn = QPushButton("启用选中插件")
        enable_btn.clicked.connect(lambda: self.set_plugin_enabled(tree, True))
        button_layout.addWidget(enable_btn)

        disable_btn = QPushButton("禁用选中插件")
        disable_btn.clicked.connect(lambda: self.set_plugin_enabled(tree, False))
        button_layout.addWidget(disable_btn)

        # 重载按钮
        reload_btn = QPushButton("重载选中插件")
        reload_btn.setToolTip("重新加载插件代码（用于应用代码修改）")
        reload_btn.clicked.connect(lambda: self.reload_selected_plugins(tree))
        button_layout.addWidget(reload_btn)

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.accept)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

        # 连接对话框关闭信号，保存配置
        dialog.finished.connect(self.save_plugin_config)

        # 非模态显示对话框
        dialog.show()

    def set_admin_controlled(self, tree, value):
        """设置选中插件的管理员控制状态"""
        if not is_admin():
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self.main_window, "权限不足", "需要管理员权限才能执行此操作")
            return

        selected_items = tree.selectedItems()
        if not selected_items:
            return

        for item in selected_items:
            plugin_name = item.text(0)
            if plugin_name in self.plugins:
                plugin = self.plugins[plugin_name]
                plugin.admin_controlled = value
                item.setText(4, "是" if value else "否")

                # 更新菜单项状态
                if plugin_name in self.plugin_actions:
                    action = self.plugin_actions[plugin_name]
                    action.setEnabled(not value or is_admin())
                    if value and not is_admin():
                        action.setToolTip("此插件由管理员控制，需要管理员权限才能修改")
                    else:
                        action.setToolTip("")

    def set_default_enabled(self, tree, value):
        """设置选中插件的默认启用状态"""
        if not is_admin():
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self.main_window, "权限不足", "需要管理员权限才能执行此操作")
            return

        selected_items = tree.selectedItems()
        if not selected_items:
            return

        for item in selected_items:
            plugin_name = item.text(0)
            if plugin_name in self.plugins:
                plugin = self.plugins[plugin_name]
                plugin.default_enabled = value
                item.setText(5, "启用" if value else "禁用")

    def set_plugin_enabled(self, tree, value):
        """设置选中插件的启用状态"""
        from PyQt5.QtWidgets import QMessageBox
        from PyQt5.QtGui import QColor

        selected_items = tree.selectedItems()
        if not selected_items:
            return

        for item in selected_items:
            plugin_name = item.text(0)
            if plugin_name in self.plugins:
                plugin = self.plugins[plugin_name]

                # 检查是否有权限修改
                if plugin.admin_controlled and not is_admin():
                    QMessageBox.warning(self.main_window, "权限不足",
                                       f"插件 '{plugin_name}' 由管理员控制，需要管理员权限才能修改")
                    continue

                # 切换插件状态
                self.toggle_plugin(plugin, value)

                # 更新显示
                item.setText(3, "启用" if value else "禁用")
                item.setForeground(3, QColor("green" if value else "red"))

                # 更新菜单项
                if plugin_name in self.plugin_actions:
                    self.plugin_actions[plugin_name].setChecked(value)

    def toggle_plugin(self, plugin, enabled):
        """切换插件启用状态"""
        try:
            # 检查是否有权限修改
            if plugin.admin_controlled and not is_admin():
                return

            # 如果状态没有变化，不做任何操作
            if plugin.enabled == enabled:
                return

            # 设置状态
            plugin.enabled = enabled

            # 根据状态初始化或清理插件
            if enabled:
                # 记录初始化开始时间
                start_time = time.time()

                plugin.initialize(self.main_window)

                # 更新加载时间
                init_time = time.time() - start_time
                self.plugin_load_times[plugin.name] = init_time

                print(f"已启用插件: {plugin.name} (加载时间: {init_time:.3f}秒)")
            else:
                plugin.cleanup()
                print(f"已禁用插件: {plugin.name}")

        except Exception as e:
            print(f"切换插件 {plugin.name} 状态时出错: {str(e)}")

    def reload_plugin(self, plugin_name):
        """重载指定插件（热重载）"""
        try:
            if plugin_name not in self.plugins:
                print(f"插件 {plugin_name} 不存在")
                return False

            old_plugin = self.plugins[plugin_name]

            # 1. 清理旧插件
            if old_plugin.enabled:
                old_plugin.cleanup()

            # 2. 查找插件模块文件
            plugin_module_name = None
            plugin_dirs = [
                os.path.join(os.path.dirname(__file__), "builtin"),
                os.path.join(os.path.dirname(__file__), "user")
            ]

            for plugin_dir in plugin_dirs:
                if not os.path.exists(plugin_dir):
                    continue

                for filename in os.listdir(plugin_dir):
                    if filename.endswith(".py") and not filename.startswith("__"):
                        module_name = filename[:-3]
                        try:
                            # 检查这个模块是否包含目标插件
                            if module_name in sys.modules:
                                module = sys.modules[module_name]
                                for name, obj in inspect.getmembers(module):
                                    if (inspect.isclass(obj) and
                                        issubclass(obj, PluginBase) and
                                        obj != PluginBase):
                                        temp_plugin = obj()
                                        if temp_plugin.name == plugin_name:
                                            plugin_module_name = module_name
                                            break
                                if plugin_module_name:
                                    break
                        except Exception:
                            continue
                if plugin_module_name:
                    break

            if not plugin_module_name:
                print(f"未找到插件 {plugin_name} 的模块文件")
                return False

            # 3. 重载模块
            if plugin_module_name in sys.modules:
                print(f"重载模块: {plugin_module_name}")
                importlib.reload(sys.modules[plugin_module_name])
                module = sys.modules[plugin_module_name]
            else:
                print(f"重新导入模块: {plugin_module_name}")
                module = importlib.import_module(plugin_module_name)

            # 4. 创建新插件实例
            new_plugin = None
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and
                    issubclass(obj, PluginBase) and
                    obj != PluginBase):
                    temp_plugin = obj()
                    if temp_plugin.name == plugin_name:
                        new_plugin = temp_plugin
                        break

            if not new_plugin:
                print(f"重载后未找到插件类: {plugin_name}")
                return False

            # 5. 保持原有配置
            new_plugin._enabled = old_plugin._enabled
            if hasattr(old_plugin, '_admin_controlled'):
                new_plugin._admin_controlled = old_plugin._admin_controlled
            if hasattr(old_plugin, '_default_enabled'):
                new_plugin._default_enabled = old_plugin._default_enabled

            # 6. 替换插件实例
            self.plugins[plugin_name] = new_plugin

            # 7. 如果原来是启用状态，重新初始化
            if new_plugin.enabled:
                start_time = time.time()
                new_plugin.initialize(self.main_window)
                init_time = time.time() - start_time
                self.plugin_load_times[plugin_name] = init_time
                print(f"插件 {plugin_name} 重载并重新初始化完成 (加载时间: {init_time:.3f}秒)")
            else:
                print(f"插件 {plugin_name} 重载完成（未启用状态）")

            return True

        except Exception as e:
            print(f"重载插件 {plugin_name} 失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def reload_selected_plugins(self, tree):
        """重载选中的插件"""
        from PyQt5.QtWidgets import QMessageBox

        selected_items = tree.selectedItems()
        if not selected_items:
            QMessageBox.information(self.main_window, "提示", "请先选择要重载的插件")
            return

        success_count = 0
        failed_plugins = []

        for item in selected_items:
            plugin_name = item.text(0)
            if plugin_name in self.plugins:
                print(f"正在重载插件: {plugin_name}")
                if self.reload_plugin(plugin_name):
                    success_count += 1
                    # 更新显示
                    plugin = self.plugins[plugin_name]
                    item.setText(3, "启用" if plugin.enabled else "禁用")
                    from PyQt5.QtGui import QColor
                    item.setForeground(3, QColor("green" if plugin.enabled else "red"))

                    # 更新加载时间
                    load_time = self.plugin_load_times.get(plugin_name, 0)
                    item.setText(6, f"{load_time:.3f}")
                    if load_time > 1.0:
                        item.setForeground(6, QColor("red"))
                    elif load_time > 0.5:
                        item.setForeground(6, QColor("orange"))
                    else:
                        item.setForeground(6, QColor("green"))
                else:
                    failed_plugins.append(plugin_name)

        # 显示结果
        if success_count > 0:
            message = f"成功重载 {success_count} 个插件"
            if failed_plugins:
                message += f"\n失败的插件: {', '.join(failed_plugins)}"
            QMessageBox.information(self.main_window, "重载结果", message)
        elif failed_plugins:
            QMessageBox.warning(self.main_window, "重载失败",
                              f"以下插件重载失败:\n{chr(10).join(failed_plugins)}")

        # 保存配置
        self.save_plugin_config()

    def cleanup_plugins(self):
        """清理所有插件"""
        for plugin in self.plugins.values():
            try:
                if plugin.enabled:
                    plugin.cleanup()
            except Exception as e:
                print(f"清理插件 {plugin.name} 失败: {str(e)}")