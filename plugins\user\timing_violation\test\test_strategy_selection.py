#!/usr/bin/env python3
"""
测试智能策略选择功能

验证基于违例数量的策略选择、回退机制和动态切换功能。
"""

import sys
import os
import tempfile
from typing import Dict

# 添加插件路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from performance_optimizer import PerformanceOptimizer
from strategy_manager import IntelligentStrategyManager, StrategyType, TriggerCondition


def create_test_file(violation_count: int) -> str:
    """创建测试文件
    
    Args:
        violation_count: 违例数量
        
    Returns:
        str: 测试文件路径
    """
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False)
    
    # 写入测试数据（每个违例5行，更接近真实格式）
    for i in range(violation_count):
        temp_file.write(f"Violation {i+1}: Setup time violation\n")
        temp_file.write(f"  Instance: /test/hierarchy/path/instance_{i}\n")
        temp_file.write(f"  Time: {i * 1000}fs\n")
        temp_file.write(f"  Check: setup_check (required: 100fs, actual: {50 + i}fs)\n")
        temp_file.write(f"  Status: FAIL\n")
    
    temp_file.close()
    return temp_file.name


def test_violation_count_estimation():
    """测试违例数量估算"""
    print("=== 测试违例数量估算 ===")
    
    optimizer = PerformanceOptimizer()
    
    # 测试不同大小的文件
    test_cases = [100, 1000, 5000, 15000, 30000]
    
    for violation_count in test_cases:
        test_file = create_test_file(violation_count)
        
        try:
            # 使用采样方法估算
            estimated = optimizer.estimate_violation_count_with_sampling(test_file)
            accuracy = abs(estimated - violation_count) / violation_count * 100
            
            print(f"实际违例数: {violation_count:,}, 估算违例数: {estimated:,}, 准确率: {100-accuracy:.1f}%")
            
            # 验证估算准确性（允许30%误差，因为采样估算有一定不确定性）
            assert accuracy <= 30, f"估算准确性不足: {accuracy:.1f}%"
            
        finally:
            os.unlink(test_file)
    
    print("违例数量估算测试通过 ✓\n")


def test_strategy_selection_by_violation_count():
    """测试基于违例数量的策略选择"""
    print("=== 测试基于违例数量的策略选择 ===")
    
    optimizer = PerformanceOptimizer()
    strategy_manager = IntelligentStrategyManager(optimizer)
    
    # 模拟系统能力
    system_capabilities = {
        'cpu_cores': 8,
        'total_memory_gb': 16,
        'available_memory_gb': 8,
        'can_handle_large_datasets': True,
        'optimal_batch_size': 2000
    }
    
    # 测试不同违例数量的策略选择
    test_cases = [
        (500, StrategyType.STANDARD),           # < 2K violations
        (5000, StrategyType.HIGH_PERFORMANCE),  # 2K-20K violations
        (25000, StrategyType.STREAMING),        # 20K-50K violations
        (75000, StrategyType.ULTRA_LARGE)       # > 50K violations
    ]
    
    for violation_count, expected_strategy in test_cases:
        strategy_config = strategy_manager.select_strategy_by_violation_count(
            violation_count, system_capabilities
        )
        
        print(f"违例数量: {violation_count:,}")
        print(f"  选择策略: {strategy_config.strategy_name}")
        print(f"  期望策略: {expected_strategy.value}")
        print(f"  选择原因: {strategy_config.selection_reason}")
        print(f"  置信度: {strategy_config.confidence_score:.2f}")
        print()
        
        # 验证策略选择正确性
        assert strategy_config.strategy_name == expected_strategy.value, \
            f"策略选择错误: 期望 {expected_strategy.value}, 实际 {strategy_config.strategy_name}"
    
    print("策略选择测试通过 ✓\n")


def test_system_capability_adjustment():
    """测试系统能力调整"""
    print("=== 测试系统能力调整 ===")
    
    optimizer = PerformanceOptimizer()
    strategy_manager = IntelligentStrategyManager(optimizer)
    
    # 低性能系统
    low_performance_system = {
        'cpu_cores': 2,
        'total_memory_gb': 4,
        'available_memory_gb': 2,
        'can_handle_large_datasets': False,
        'optimal_batch_size': 500
    }
    
    # 高性能系统
    high_performance_system = {
        'cpu_cores': 16,
        'total_memory_gb': 32,
        'available_memory_gb': 16,
        'can_handle_large_datasets': True,
        'optimal_batch_size': 5000
    }
    
    violation_count = 10000  # 中等数据集
    
    # 测试低性能系统
    low_perf_strategy = strategy_manager.select_strategy_by_violation_count(
        violation_count, low_performance_system
    )
    
    # 测试高性能系统
    high_perf_strategy = strategy_manager.select_strategy_by_violation_count(
        violation_count, high_performance_system
    )
    
    print(f"违例数量: {violation_count:,}")
    print(f"低性能系统策略: {low_perf_strategy.strategy_name}")
    print(f"  批处理大小: {low_perf_strategy.batch_size}")
    print(f"  内存限制: {low_perf_strategy.memory_limit_mb}MB")
    print(f"高性能系统策略: {high_perf_strategy.strategy_name}")
    print(f"  批处理大小: {high_perf_strategy.batch_size}")
    print(f"  内存限制: {high_perf_strategy.memory_limit_mb}MB")
    print()
    
    # 验证系统能力调整
    assert low_perf_strategy.batch_size <= high_perf_strategy.batch_size, \
        "低性能系统应该使用更小的批处理大小"
    assert low_perf_strategy.memory_limit_mb <= high_perf_strategy.memory_limit_mb, \
        "低性能系统应该使用更小的内存限制"
    
    print("系统能力调整测试通过 ✓\n")


def test_fallback_mechanism():
    """测试回退机制"""
    print("=== 测试回退机制 ===")
    
    optimizer = PerformanceOptimizer()
    strategy_manager = IntelligentStrategyManager(optimizer)
    
    system_capabilities = {
        'cpu_cores': 4,
        'total_memory_gb': 8,
        'available_memory_gb': 4,
        'can_handle_large_datasets': True,
        'optimal_batch_size': 1000
    }
    
    # 选择一个高性能策略
    violation_count = 10000
    original_strategy = strategy_manager.select_strategy_by_violation_count(
        violation_count, system_capabilities
    )
    
    print(f"原始策略: {original_strategy.strategy_name}")
    
    # 测试不同的回退触发条件
    test_triggers = [
        ('memory_pressure', '内存压力'),
        ('processing_timeout', '处理超时'),
        ('ui_freeze', 'UI冻结'),
        ('high_error_rate', '高错误率')
    ]
    
    for trigger, description in test_triggers:
        current_metrics = {
            'memory_usage_mb': 900 if trigger == 'memory_pressure' else 200,
            'processing_speed': 0.2 if trigger == 'processing_timeout' else 1.0,
            'ui_response_time': 0.6 if trigger == 'ui_freeze' else 0.1,
            'error_rate': 0.15 if trigger == 'high_error_rate' else 0.01
        }
        
        fallback_strategy = strategy_manager.trigger_fallback(trigger, current_metrics)
        
        if fallback_strategy:
            print(f"触发条件: {description}")
            print(f"  回退策略: {fallback_strategy.strategy_name}")
            print(f"  选择原因: {fallback_strategy.selection_reason}")
            print()
            
            # 验证回退策略不同于原始策略
            assert fallback_strategy.strategy_name != original_strategy.strategy_name, \
                f"回退策略应该不同于原始策略"
        else:
            print(f"触发条件 {description} 未找到合适的回退策略")
    
    print("回退机制测试通过 ✓\n")


def test_dynamic_switching():
    """测试动态策略切换"""
    print("=== 测试动态策略切换 ===")
    
    optimizer = PerformanceOptimizer()
    strategy_manager = IntelligentStrategyManager(optimizer)
    
    system_capabilities = {
        'cpu_cores': 4,
        'total_memory_gb': 8,
        'available_memory_gb': 4,
        'can_handle_large_datasets': True,
        'optimal_batch_size': 1000
    }
    
    # 选择初始策略
    violation_count = 15000
    initial_strategy = strategy_manager.select_strategy_by_violation_count(
        violation_count, system_capabilities
    )
    
    print(f"初始策略: {initial_strategy.strategy_name}")
    
    # 模拟性能问题并触发动态切换
    current_metrics = {
        'memory_usage_percent': 90,  # 高内存使用
        'processing_speed': 0.2,     # 低处理速度
        'ui_response_time': 0.6,     # 慢UI响应
        'error_rate': 0.02
    }
    
    new_strategy = strategy_manager.switch_strategy_dynamically(
        initial_strategy.__dict__, 'memory_pressure', current_metrics
    )
    
    print(f"切换后策略: {new_strategy['strategy_name']}")
    print(f"切换原因: {new_strategy['switch_metadata']['switch_reason']}")
    print(f"预期改进: {new_strategy['switch_metadata']['expected_improvement']}")
    print()
    
    # 验证策略已切换
    assert new_strategy['strategy_name'] != initial_strategy.strategy_name, \
        "动态切换应该选择不同的策略"
    
    print("动态策略切换测试通过 ✓\n")


def test_comprehensive_file_analysis():
    """测试综合文件分析"""
    print("=== 测试综合文件分析 ===")
    
    optimizer = PerformanceOptimizer()
    
    # 创建不同大小的测试文件
    test_cases = [1000, 5000, 20000, 50000]
    
    for violation_count in test_cases:
        test_file = create_test_file(violation_count)
        
        try:
            # 执行综合文件分析
            analysis = optimizer.analyze_file_performance(test_file)
            
            print(f"文件分析结果 (违例数: {violation_count:,}):")
            print(f"  估算违例数: {analysis.get('estimated_violations', 0):,}")
            print(f"  推荐策略: {analysis.get('recommended_strategy', 'unknown')}")
            print(f"  推荐显示: {analysis.get('recommended_display', 'unknown')}")
            print(f"  性能等级: {analysis.get('performance_level', 'unknown')}")
            print(f"  预测加载时间: {analysis.get('predicted_load_time', 0):.2f}秒")
            print(f"  内存需求: {analysis.get('memory_requirements_mb', 0):.1f}MB")
            
            suggestions = analysis.get('optimization_suggestions', [])
            if suggestions:
                print("  优化建议:")
                for suggestion in suggestions[:3]:  # 只显示前3个建议
                    print(f"    - {suggestion}")
            print()
            
            # 验证分析结果合理性
            assert analysis.get('estimated_violations', 0) > 0, "应该估算出违例数量"
            assert analysis.get('recommended_strategy'), "应该推荐策略"
            assert analysis.get('performance_level'), "应该评估性能等级"
            
        finally:
            os.unlink(test_file)
    
    print("综合文件分析测试通过 ✓\n")


def main():
    """运行所有测试"""
    print("开始测试智能策略选择功能...\n")
    
    try:
        test_violation_count_estimation()
        test_strategy_selection_by_violation_count()
        test_system_capability_adjustment()
        test_fallback_mechanism()
        test_dynamic_switching()
        test_comprehensive_file_analysis()
        
        print("🎉 所有测试通过！智能策略选择功能实现正确。")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())