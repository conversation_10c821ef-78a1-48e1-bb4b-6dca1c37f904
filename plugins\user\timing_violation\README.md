# 后仿时序违例确认插件

## 概述

后仿时序违例确认插件是RunSim GUI的一个专业插件，用于分析和确认后仿真时序违例。该插件提供了完整的时序违例管理流程，包括日志解析、自动确认、手动确认、历史记录管理和导出功能。

## 功能特性

### 1. 文件选择与解析
- 支持选择vio_summary.log文件
- 自动解析时序违例条目
- 支持多种时间单位（FS、PS、NS）
- 异步解析，避免GUI冻结

### 2. 自动确认功能
- 根据复位时间自动确认违例
- 自动标记复位期间的时序违例
- 统一的自动确认备注

### 3. 手动确认功能
- 逐一确认违例记录
- 支持确认人、确认结果、确认理由输入
- 批量确认功能
- 编辑已确认记录

### 4. 历史记录功能
- SQLite数据库存储确认记录
- 自动匹配历史模式
- 智能建议确认信息
- 减少重复确认工作

### 5. 用例信息识别
- 自动解析用例名称和corner信息
- 支持标准目录格式：{case_name}_{corner}
- Corner下拉选择支持
- 支持的corner类型完整

### 6. 导出功能
- Excel格式导出
- CSV格式导出
- 完整的确认清单信息
- 自定义导出路径

### 7. 现代化UI
- 与RunSim GUI主题一致
- 响应式布局设计
- 实时进度显示
- 状态栏信息

## 安装与使用

### 安装要求
- Python 3.8+
- PyQt5 5.15+
- SQLite3（Python内置）
- openpyxl（可选，用于Excel导出）

### 使用步骤

1. **启动插件**
   - 在RunSim GUI中点击"工具"菜单
   - 选择"后仿时序违例确认工具"

2. **选择日志文件**
   - 点击"选择文件"按钮
   - 选择vio_summary.log文件
   - 插件会自动解析用例信息

3. **设置复位时间**
   - 在"复位时间(ns)"输入框中输入复位时间
   - 默认值为1000ns

4. **自动确认**
   - 点击"自动确认"按钮
   - 系统会自动确认复位期间的违例

5. **手动确认**
   - 对于待确认的违例，点击"确认"按钮
   - 填写确认人、确认结果、确认理由
   - 系统会保存历史模式供下次使用

6. **导出结果**
   - 点击"导出Excel"或"导出CSV"
   - 选择保存路径
   - 导出完整的确认清单

## 文件格式

### vio_summary.log格式
```
------------------------------------------------------------
NUM    : 1
Hier   : tb_top.xxx.xxx
Time   : 1523423 FS
Check  : setup( posedge xxx, xxx )
------------------------------------------------------------
NUM    : 2
Hier   : tb_top.xxx.xxx
Time   : 1523423 FS
Check  : hold(posedge xxx, xxx)
------------------------------------------------------------
```

### 支持的Corner类型
- npg_f1_ssg, npg_f2_ssg, npg_f3_ssg, npg_f4_ssg, npg_f5_ssg, npg_f6_ssg, npg_f7_ssg
- npg_f1_ffg, npg_f2_ffg, npg_f3_ffg, npg_f4_ffg, npg_f5_ffg, npg_f6_ffg, npg_f7_ffg
- npg_f1_tt, npg_f2_tt, npg_f3_tt

## 数据库结构

插件使用SQLite数据库存储数据，位于`VIOLATION_CHECK/timing_violations.db`。

### 主要表结构
- `timing_violations`: 时序违例记录
- `confirmation_records`: 确认记录
- `violation_patterns`: 历史匹配模式

## 技术特性

### 跨平台兼容性
- 支持Windows和Linux
- 统一的文件路径处理
- 跨平台字体设置

### 性能优化
- 异步文件解析
- 数据库连接池
- 智能缓存机制

### 错误处理
- 完善的异常处理
- 用户友好的错误提示
- 详细的日志记录

## 开发信息

### 版本历史
- v1.0.0: 初始版本，包含所有基础功能

### 开发团队
- RunSim GUI Team

### 许可证
- 遵循RunSim GUI项目许可证

## 故障排除

### 常见问题

1. **文件解析失败**
   - 检查文件格式是否正确
   - 确保文件编码为UTF-8
   - 验证文件路径是否正确

2. **数据库错误**
   - 检查VIOLATION_CHECK目录权限
   - 确保磁盘空间充足
   - 重启插件重新初始化数据库

3. **导出失败**
   - 安装openpyxl库：`pip install openpyxl`
   - 检查导出路径权限
   - 确保文件未被其他程序占用

### 调试模式
插件会在控制台输出详细的调试信息，包括：
- 文件解析进度
- 数据库操作状态
- 错误详情和堆栈跟踪

## 联系支持

如有问题或建议，请联系RunSim GUI开发团队。
